apply plugin: 'com.android.library'
apply from: '../shared/keepUnitySymbols.gradle'

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation(name: 'lightwebviewsdk-release', ext:'aar')
    implementation(name: 'lightwebviewunity-release', ext:'aar')
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.core:core:1.9.0'
    implementation 'androidx.games:games-activity:3.0.5'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
}

android {
    namespace "com.unity3d.player"
    ndkPath "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK"
    ndkVersion "27.2.12479018"
    compileSdk 35
    buildToolsVersion = "34.0.0"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    defaultConfig {
        consumerProguardFiles "proguard-unity.txt"
        versionName "0.1.0"
        minSdk 23
        targetSdk 35
        versionCode 1

        ndk {
            abiFilters "armeabi-v7a", "arm64-v8a"
            debugSymbolLevel "full"
        }

        externalNativeBuild {
            cmake {
                arguments "-DANDROID_STL=c++_shared", "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"
            }
        }
    }

    lint {
        abortOnError false
    }

    androidResources {
        ignoreAssetsPattern = "!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"
        noCompress = ['.unity3d', '.ress', '.resource', '.obb', '.bundle', '.unityexp'] + unityStreamingAssets.tokenize(', ')
    }

    packaging {
        jniLibs {
            useLegacyPackaging true
        }
    }
}


android.externalNativeBuild {
    cmake {
        version "3.22.1"
        // Workaround for CMake long path issue on Windows, place CMake intermediate files next to Unity project
        buildStagingDirectory "${unityProjectPath}/.utmp"
        path "src/main/cpp/CMakeLists.txt"
    }
}
android.buildFeatures {
    prefab true
}