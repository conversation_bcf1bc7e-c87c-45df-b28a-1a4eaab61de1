<?xml version='1.0' encoding='UTF-8'?>
<androidx.constraintlayout>
  <constraintlayout versions="1.1.0,1.1.1,1.1.2,1.1.3,2.0.0-alpha1,2.0.0-alpha2,2.0.0-alpha3,2.0.0-alpha4,2.0.0-alpha5,2.0.0-beta1,2.0.0-beta2,2.0.0-beta3,2.0.0-beta4,2.0.0-beta5,2.0.0-beta6,2.0.0-beta7,2.0.0-beta8,2.0.0-rc1,2.0.0,2.0.1,2.0.2,2.0.3,2.0.4,2.1.0-alpha1,2.1.0-alpha2,2.1.0-beta01,2.1.0-beta02,2.1.0-rc01,2.1.0,2.1.1,2.1.2,2.1.3,2.1.4,2.2.0-alpha01,2.2.0-alpha02,2.2.0-alpha03,2.2.0-alpha04,2.2.0-alpha05,2.2.0-alpha06,2.2.0-alpha07,2.2.0-alpha08,2.2.0-alpha09,2.2.0-alpha10,2.2.0-alpha11,2.2.0-alpha12,2.2.0-alpha13,2.2.0-alpha14,2.2.0-beta01,2.2.0-rc01,2.2.0,2.2.1"/>
  <constraintlayout-compose versions="1.0.0-alpha01,1.0.0-alpha02,1.0.0-alpha03,1.0.0-alpha04,1.0.0-alpha05,1.0.0-alpha06,1.0.0-alpha07,1.0.0-alpha08,1.0.0-beta01,1.0.0-beta02,1.0.0-rc01,1.0.0-rc02,1.0.0,1.0.1,1.1.0-alpha01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-alpha08,1.1.0-alpha09,1.1.0-alpha10,1.1.0-alpha11,1.1.0-alpha12,1.1.0-alpha13,1.1.0-alpha14,1.1.0-beta01,1.1.0-rc01,1.1.0,1.1.1"/>
  <constraintlayout-compose-android versions="1.1.0-alpha10,1.1.0-alpha11,1.1.0-alpha12,1.1.0-alpha13,1.1.0-alpha14,1.1.0-beta01,1.1.0-rc01,1.1.0,1.1.1"/>
  <constraintlayout-core versions="1.0.0-alpha1,1.0.0-alpha2,1.0.0-alpha3,1.0.0-beta01,1.0.0-beta02,1.0.0-beta03,1.0.0-beta04,1.0.0,1.0.1,1.0.2,1.0.3,1.0.4,1.1.0-alpha01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-alpha08,1.1.0-alpha09,1.1.0-alpha10,1.1.0-alpha11,1.1.0-alpha12,1.1.0-alpha13,1.1.0-alpha14,1.1.0-beta01,1.1.0-rc01,1.1.0,1.1.1"/>
  <constraintlayout-solver versions="1.1.0,1.1.1,1.1.2,1.1.3,2.0.0-alpha1,2.0.0-alpha2,2.0.0-alpha3,2.0.0-alpha4,2.0.0-alpha5,2.0.0-beta1,2.0.0-beta2,2.0.0-beta3,2.0.0-beta4,2.0.0-beta5,2.0.0-beta6,2.0.0-beta7,2.0.0-beta8,2.0.0-rc1,2.0.0,2.0.1,2.0.2,2.0.3,2.0.4"/>
</androidx.constraintlayout>
