{"info": {"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, "cxxBuildFolder": "/Users/<USER>/Desktop/WebBrowser/.utmp/RelWithDebInfo/215e5a2o/armeabi-v7a", "soFolder": "/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/215e5a2o/obj/armeabi-v7a", "soRepublishFolder": "/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cmake/release/obj/armeabi-v7a", "abiPlatformVersion": 23, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DANDROID_STL=c++_shared", "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"], "cFlagsList": [], "cppFlagsList": [], "variantName": "release", "isDebuggableEnabled": false, "validAbiList": ["armeabi-v7a", "arm64-v8a"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "/Users/<USER>/Desktop/WebBrowser/.utmp", "intermediatesBaseFolder": "/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates", "intermediatesFolder": "/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx", "gradleModulePathName": ":unityLibrary", "moduleRootFolder": "/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary", "moduleBuildFile": "/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build.gradle", "makeFile": "/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK", "ndkFolderBeforeSymLinking": "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK", "ndkVersion": "27.2.12479018", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "riscv64", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 35, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34, "VanillaIceCream": 35}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "riscv64", "bitness": 64, "isDefault": false, "isDeprecated": false, "architecture": "riscv64", "triple": "riscv64-linux-android", "llvmTriple": "riscv64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/android.toolchain.cmake", "cmake": {"cmakeExe": "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/cmake", "cmakeVersionFromDsl": "3.22.1"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/arm-linux-androideabi/libc++_shared.so", "arm64-v8a": "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/libc++_shared.so", "riscv64": "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/riscv64-linux-android/libc++_shared.so", "x86": "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/i686-linux-android/libc++_shared.so", "x86_64": "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/x86_64-linux-android/libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle", "sdkFolder": "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/SDK", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": true}, "outputOptions": [], "ninjaExe": "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ninja", "hasBuildTimeInformation": true}, "prefabClassPaths": ["/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.prefab/cli/2.1.0/aa32fec809c44fa531f01dcfb739b5b3304d3050/cli-2.1.0-all.jar"], "prefabPackages": ["/Users/<USER>/.gradle/caches/8.11/transforms/cf11df13d2fd9a509170edfac64a04e6/transformed/jetified-games-activity-3.0.5/prefab"], "prefabPackageConfigurations": [], "stlType": "c++_shared", "optimizationTag": "RelWithDebInfo"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "/Users/<USER>/Desktop/WebBrowser/.utmp/RelWithDebInfo/215e5a2o/prefab/armeabi-v7a", "isActiveAbi": true, "fullConfigurationHash": "215e5a2o1f71q6532c1a722a1r59695m342d5g5f1614383m3z6r662q4n3q72", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.7.2.\n#   - $NDK is the path to NDK 27.2.12479018.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-H$PROJECT/unityLibrary/src/main/cpp\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=23\n-DANDROID_PLATFORM=android-23\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=$PROJECT/unityLibrary/build/intermediates/cxx/RelWithDebInfo/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=$PROJECT/unityLibrary/build/intermediates/cxx/RelWithDebInfo/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=RelWithDebInfo\n-DCMAKE_FIND_ROOT_PATH=/Users/<USER>/Desktop/WebBrowser/.utmp/RelWithDebInfo/$HASH/prefab/$ABI/prefab\n-B/Users/<USER>/Desktop/WebBrowser/.utmp/RelWithDebInfo/$HASH/$ABI\n-GNinja\n-DANDROID_STL=c++_shared\n-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON", "configurationArguments": ["-H/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=23", "-DANDROID_PLATFORM=android-23", "-DANDROID_ABI=armeabi-v7a", "-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a", "-DANDROID_NDK=/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK", "-DCMAKE_ANDROID_NDK=/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK", "-DCMAKE_TOOLCHAIN_FILE=/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ninja", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/215e5a2o/obj/armeabi-v7a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/215e5a2o/obj/armeabi-v7a", "-DCMAKE_BUILD_TYPE=RelWithDebInfo", "-DCMAKE_FIND_ROOT_PATH=/Users/<USER>/Desktop/WebBrowser/.utmp/RelWithDebInfo/215e5a2o/prefab/armeabi-v7a/prefab", "-B/Users/<USER>/Desktop/WebBrowser/.utmp/RelWithDebInfo/215e5a2o/armeabi-v7a", "-<PERSON><PERSON><PERSON><PERSON>", "-DANDROID_STL=c++_shared", "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"], "stlLibraryFile": "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/arm-linux-androideabi/libc++_shared.so", "intermediatesParentFolder": "/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/215e5a2o"}