<lint-module
    format="1"
    dir="/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary"
    name=":unityLibrary"
    type="LIBRARY"
    maven="Gradle:unityLibrary:unspecified"
    agpVersion="8.7.2"
    buildFolder="build"
    bootClassPath="/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/SDK/platforms/android-35/android.jar:/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/SDK/build-tools/34.0.0/core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
