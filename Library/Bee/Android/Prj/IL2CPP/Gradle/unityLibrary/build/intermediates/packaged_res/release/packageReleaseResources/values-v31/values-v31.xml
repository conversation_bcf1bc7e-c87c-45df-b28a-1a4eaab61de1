<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="BaseUnityGameActivityTheme" parent="Theme.AppCompat.Light.NoActionBar">
    <item name="android:windowSplashScreenAnimatedIcon">@color/staticSplashScreenBackgroundColor</item>
    <item name="android:windowSplashScreenBackground">@color/staticSplashScreenBackgroundColor</item>
    <item name="android:windowSplashScreenIconBackgroundColor">@color/staticSplashScreenBackgroundColor</item>
    <item name="android:windowSplashScreenAnimationDuration">0</item>
  </style>
    <style name="BaseUnityTheme" parent="android:Theme.Holo.Light.NoActionBar.Fullscreen">
    <item name="android:windowSplashScreenAnimatedIcon">@color/staticSplashScreenBackgroundColor</item>
    <item name="android:windowSplashScreenBackground">@color/staticSplashScreenBackgroundColor</item>
    <item name="android:windowSplashScreenIconBackgroundColor">@color/staticSplashScreenBackgroundColor</item>
    <item name="android:windowSplashScreenAnimationDuration">0</item>
  </style>
</resources>