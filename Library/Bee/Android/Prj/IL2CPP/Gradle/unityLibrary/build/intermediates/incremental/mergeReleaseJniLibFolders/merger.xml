<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs"><file name="armeabi-v7a/libmain.so" path="/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/armeabi-v7a/libmain.so"/><file name="armeabi-v7a/libil2cpp.so" path="/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/armeabi-v7a/libil2cpp.so"/><file name="armeabi-v7a/libunity.so" path="/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/armeabi-v7a/libunity.so"/><file name="armeabi-v7a/lib_burst_generated.so" path="/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/armeabi-v7a/lib_burst_generated.so"/><file name="arm64-v8a/libmain.so" path="/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libmain.so"/><file name="arm64-v8a/libil2cpp.so" path="/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libil2cpp.so"/><file name="arm64-v8a/libunity.so" path="/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libunity.so"/><file name="arm64-v8a/lib_burst_generated.so" path="/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/lib_burst_generated.so"/></source></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/release/jniLibs"/></dataSet></merger>