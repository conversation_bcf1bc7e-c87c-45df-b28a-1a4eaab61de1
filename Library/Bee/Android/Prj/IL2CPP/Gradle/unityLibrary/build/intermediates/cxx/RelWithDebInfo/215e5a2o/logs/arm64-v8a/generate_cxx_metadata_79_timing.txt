# C/C++ build system timings
generate_cxx_metadata
  [gap of 62ms]
  create-invalidation-state 14ms
  generate-prefab-packages
    [gap of 48ms]
    exec-prefab 1876ms
    [gap of 86ms]
  generate-prefab-packages completed in 2010ms
  execute-generate-process
    exec-configure 1658ms
    [gap of 362ms]
  execute-generate-process completed in 2023ms
  [gap of 18ms]
  remove-unexpected-so-files 27ms
  [gap of 133ms]
  write-metadata-json-to-file 44ms
generate_cxx_metadata completed in 4401ms

