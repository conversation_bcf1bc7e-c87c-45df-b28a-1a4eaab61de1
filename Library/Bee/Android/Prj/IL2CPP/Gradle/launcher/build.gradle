apply plugin: 'com.android.application'
apply from: 'setupSymbols.gradle'
apply from: '../shared/keepUnitySymbols.gradle'

dependencies {
    implementation project(':unityLibrary')
}

android {
    namespace "com.webview.pryze"
    ndkPath "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK"
    ndkVersion "27.2.12479018"
    compileSdk 35
    buildToolsVersion = "34.0.0"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    defaultConfig {
        applicationId "com.webview.pryze"
        versionName "0.1.0"
        minSdk 23
        targetSdk 35
        versionCode 1

        ndk {
            abiFilters "armeabi-v7a", "arm64-v8a"
            debugSymbolLevel "full"
        }
    }

    lint {
        abortOnError false
    }

    androidResources {
        ignoreAssetsPattern = "!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"
        noCompress = ['.unity3d', '.ress', '.resource', '.obb', '.bundle', '.unityexp'] + unityStreamingAssets.tokenize(', ')
    }

    packaging {
        jniLibs {
            useLegacyPackaging true
        }
    }

    buildTypes {
        debug {
            minifyEnabled = false
            proguardFiles getDefaultProguardFile('proguard-android.txt')
            jniDebuggable = true
            signingConfig signingConfigs.debug
        }

        release {
            minifyEnabled = false
            proguardFiles getDefaultProguardFile('proguard-android.txt')
            signingConfig signingConfigs.debug
        }
    }

    bundle {
        language {
            enableSplit = false
        }

        density {
            enableSplit = false
        }

        abi {
            enableSplit = true
        }

        texture {
            enableSplit = true
        }
    }
}