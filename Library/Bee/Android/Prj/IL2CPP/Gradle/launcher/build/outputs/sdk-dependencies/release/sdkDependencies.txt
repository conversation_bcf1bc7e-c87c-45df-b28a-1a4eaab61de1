# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    artifactId: "lightwebviewsdk-release"
  }
  digests {
    sha256: "\323\266\005\215\241\177J\277\341\361\302\362y\351\033\034\276F\250\t!\262&\005\234.\016\370%\225b\""
  }
}
library {
  maven_library {
    artifactId: "lightwebviewunity-release"
  }
  digests {
    sha256: "?\330\020\236\335\344\247D\035u\023\357\256\317\r\355\371n\316\210M\230\230\242\017t\371\206[\235\361\177"
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.6.1"
  }
  digests {
    sha256: "~\245W;\223\253\253\323\27521$Q\306\352H\246b\260:\024\r\332\201\256\276uwj \244\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.6.0"
  }
  digests {
    sha256: "\177\br>\312\276\373\246\026\326\fqK\016\23210\033\324\320y/\314yF\301G\234W\375-("
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.3.0"
  }
  digests {
    sha256: "\227\334E\257\357\343\241\344!\332B\270\266\351\371\004\221G|E\374ax >:^\212\005\356\205S"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.9.0"
  }
  digests {
    sha256: "\213\332>\343\250\210\207\325Ofy\373kl\327\210b\237s#J\311\034\213\276\331$\347!\354\205\270"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.3.0"
  }
  digests {
    sha256: "\253\375)\310Un[\3202Z\237v\232\271\351\321T\377JU\025\304v\315\325\242\250([\033\031\334"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.7.10"
  }
  digests {
    sha256: "\347q\376t%\n\224>\217cFq2\001\377\035\214\271\\:]\032\221\242+e\251\340Oj\211\001"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.7.10"
  }
  digests {
    sha256: "\031\361\002\357\351b\237\216\253\3068S\255\025\3053\344|G\371\037\312\t(\\[\336\206\345\237\221\324"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "13.0"
  }
  digests {
    sha256: "\254\342\241\r\310\342\325\3754\222^\312\300>I\210\262\300\370Qe\f\224\270\316\364\233\241\275\021\024x"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.0.0"
  }
  digests {
    sha256: "U\225\244\016\'\212{9\372x\240\224\220\343\327\363\372\251\\{\001DqH\2758\265\255\340`\\5"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.5.1"
  }
  digests {
    sha256: "3\260\327=\302\360(\374\3535\231\272\312\276V<=\266\322o5\023\330\211YXcSjJ\310\300"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.1.0"
  }
  digests {
    sha256: "\376\0227\277\002\235\006>\177)\3769\256\257s\357t\310\260\243e\204\206\374)\323\305C&e8\211"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.1.0"
  }
  digests {
    sha256: "\335wa[\323\335\'Z\373\021\266-\362[\256F\261\vJ\021|\323yC\257E\275\313\370uXR"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.5.1"
  }
  digests {
    sha256: " \255\025 \366%\317E^j\375r\220\230\203\006\323\251\210n\372\231>\b`\373\253\364\273?{\332"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.9.0"
  }
  digests {
    sha256: "\025B\241\337{\351\b\311_\356\221\270\333\300?\331t\365?\021\330J\205\330\201\371ZRU\034@Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.5.1"
  }
  digests {
    sha256: "\024\242}_\270\241Ck\033}\354\030\276\272\246l\203\f\333\274\216(\250\034\345\370[|3\263\256\235"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.5.1"
  }
  digests {
    sha256: "\204\201\024\037\227\360\346!=\323?\314\211\247\204\304\275\021\246\377}Gy\241\317j\016\237\275\277$\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.5.1"
  }
  digests {
    sha256: "\356y!\003\312$\213\372\361P\304Z\223\207\036L\367\350\316\272\271\220\340\366/}\345\324\377/ \237"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.0"
  }
  digests {
    sha256: "-\345(\326\211\216\225\357\002\r\"\331\377\337\235\037w\313\335\223\371-9\337\252]\\C\260\303\021\310"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.6.1"
  }
  digests {
    sha256: "\226\036\275\350\023\207y\242\231C\f\243%\250n(\304\220Rz\207\272Q\203b\372E\304L~~\225"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.6.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.6.1"
  }
  digests {
    sha256: ":\223\377\320R\204FC\300\376\371P\256Ux\333G\314\313\351\347\027mh\0233\030.#+\260\361"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.6.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.6.0"
  }
  digests {
    sha256: "\253\005G\304\225\2252\024\245\362\262\201P\001ON\002\0236x\325+w\327cu\352#^D=\275"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.6.0"
  }
  digests {
    sha256: "\207\r5\375&k-\257d\301\b\017\345\030$\323\303h\367\231S\204\250\327\305\374/\334@\353{:"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\224\002D,\334ZC\317b\373\024\370\317\230\3063B\324\331\331\270\005\310\003<l\367\350\002t\232\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.2.0"
  }
  digests {
    sha256: "\363\032\006\301P\354\2600s\365Zo{\vt\242@\246\250\327\'\301L\347g&\320 W\r\372\214"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.4.1"
  }
  digests {
    sha256: "\333d\233>\372$\343\020R\024S\020\260\002\333\221\3324k?\211\300\223\3548\303\004m\264^yN"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.2.0"
  }
  digests {
    sha256: "\177\372MFM\235\262Y\374\240\315\265\017\275J\266=hr\274\332YF\213\237uUPL}Z\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.3.6"
  }
  digests {
    sha256: "\022\360\203\033O\b\t-]\332\',\031#\301\032\002/\362\f\357\376\323\350\001u\036!\273\215\034\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.0.0"
  }
  digests {
    sha256: "\310&\t\316\330\304\230\360\247\001\243\017\266w\033\267H\b`\332\356\204\330.\n\201\356\206\355\367\2729"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.games"
    artifactId: "games-activity"
    version: "3.0.5"
  }
  digests {
    sha256: "a\237\324y\a\353\035\264)N\341\267p\257\220\030\035D\244\035T\347\245UE~=\222\357\343\306\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.1.4"
  }
  digests {
    sha256: "\r\367\024\300\265\036Tq\016\277tn\264i\3233\027k\273<\262\237\200w]\303\312N\263\026%\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.0.4"
  }
  digests {
    sha256: ">G\177M\3421\345\213%\365\251\222\363\276E\351}3,4\243\232\236>}Kx\256\n\302%o"
  }
  repo_index {
  }
}
library {
  digests {
    sha256: "\035\"4\233\216?<\241\3438\252\031Cg\201Q\336zj\244N\233\263\206\033\207\217\f\274\005\200O"
  }
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 30
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 18
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 13
  library_dep_index: 19
  library_dep_index: 45
  library_dep_index: 22
  library_dep_index: 8
  library_dep_index: 30
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 29
  library_dep_index: 8
}
library_dependencies {
  library_index: 5
  library_dep_index: 4
}
library_dependencies {
  library_index: 6
  library_dep_index: 4
  library_dep_index: 7
  library_dep_index: 5
  library_dep_index: 11
  library_dep_index: 13
  library_dep_index: 17
  library_dep_index: 18
}
library_dependencies {
  library_index: 7
  library_dep_index: 8
}
library_dependencies {
  library_index: 8
  library_dep_index: 9
  library_dep_index: 10
}
library_dependencies {
  library_index: 11
  library_dep_index: 12
  library_dep_index: 4
}
library_dependencies {
  library_index: 13
  library_dep_index: 4
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 16
}
library_dependencies {
  library_index: 14
  library_dep_index: 4
}
library_dependencies {
  library_index: 15
  library_dep_index: 4
  library_dep_index: 14
}
library_dependencies {
  library_index: 16
  library_dep_index: 4
}
library_dependencies {
  library_index: 17
  library_dep_index: 4
  library_dep_index: 5
}
library_dependencies {
  library_index: 18
  library_dep_index: 4
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 6
}
library_dependencies {
  library_index: 19
  library_dep_index: 4
  library_dep_index: 8
  library_dep_index: 20
}
library_dependencies {
  library_index: 20
  library_dep_index: 4
  library_dep_index: 18
  library_dep_index: 21
  library_dep_index: 19
  library_dep_index: 22
  library_dep_index: 8
  library_dep_index: 23
}
library_dependencies {
  library_index: 21
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 16
}
library_dependencies {
  library_index: 22
  library_dep_index: 4
  library_dep_index: 14
  library_dep_index: 16
  library_dep_index: 8
}
library_dependencies {
  library_index: 23
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 27
}
library_dependencies {
  library_index: 24
  library_dep_index: 25
}
library_dependencies {
  library_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 9
}
library_dependencies {
  library_index: 26
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
}
library_dependencies {
  library_index: 27
  library_dep_index: 8
  library_dep_index: 28
}
library_dependencies {
  library_index: 28
  library_dep_index: 8
}
library_dependencies {
  library_index: 29
  library_dep_index: 4
}
library_dependencies {
  library_index: 30
  library_dep_index: 4
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 2
}
library_dependencies {
  library_index: 31
  library_dep_index: 4
  library_dep_index: 6
  library_dep_index: 5
}
library_dependencies {
  library_index: 32
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 5
}
library_dependencies {
  library_index: 33
  library_dep_index: 4
}
library_dependencies {
  library_index: 34
  library_dep_index: 4
}
library_dependencies {
  library_index: 35
  library_dep_index: 4
  library_dep_index: 6
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 4
  library_dep_index: 6
}
library_dependencies {
  library_index: 37
  library_dep_index: 4
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 38
  library_dep_index: 39
}
library_dependencies {
  library_index: 38
  library_dep_index: 13
  library_dep_index: 39
  library_dep_index: 4
}
library_dependencies {
  library_index: 39
  library_dep_index: 4
  library_dep_index: 29
}
library_dependencies {
  library_index: 40
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 37
}
library_dependencies {
  library_index: 41
  library_dep_index: 4
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 3
  library_dep_index: 21
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 7
}
library_dependencies {
  library_index: 42
  library_dep_index: 4
  library_dep_index: 6
  library_dep_index: 36
}
library_dependencies {
  library_index: 43
  library_dep_index: 4
  library_dep_index: 6
  library_dep_index: 44
  library_dep_index: 19
}
library_dependencies {
  library_index: 44
  library_dep_index: 15
  library_dep_index: 21
  library_dep_index: 14
}
library_dependencies {
  library_index: 45
  library_dep_index: 4
}
library_dependencies {
  library_index: 47
  library_dep_index: 2
  library_dep_index: 6
  library_dep_index: 48
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 1
  dependency_index: 2
  dependency_index: 6
  dependency_index: 46
  dependency_index: 47
  dependency_index: 49
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
