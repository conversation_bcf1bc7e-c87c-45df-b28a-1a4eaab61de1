<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.webview.pryze"
    android:installLocation="preferExternal"
    android:versionCode="1"
    android:versionName="0.1.0" >

    <uses-sdk
        android:minSdkVersion="23"
        android:targetSdkVersion="35" />

    <supports-screens
        android:anyDensity="true"
        android:largeScreens="true"
        android:normalScreens="true"
        android:smallScreens="true"
        android:xlargeScreens="true" />

    <uses-permission android:name="android.permission.INTERNET" />

    <uses-feature android:glEsVersion="0x00030000" />
    <uses-feature
        android:name="android.hardware.vulkan.version"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen.multitouch"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen.multitouch.distinct"
        android:required="false" />

    <permission
        android:name="com.webview.pryze.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.webview.pryze.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:configChanges="orientation|keyboardHidden|screenSize"
        android:enableOnBackInvokedCallback="false"
        android:extractNativeLibs="true"
        android:icon="@mipmap/app_icon"
        android:label="@string/app_name" >
        <meta-data
            android:name="unity.splash-mode"
            android:value="0" />
        <meta-data
            android:name="unity.splash-enable"
            android:value="True" />
        <meta-data
            android:name="unity.launch-fullscreen"
            android:value="True" />
        <meta-data
            android:name="unity.render-outside-safearea"
            android:value="True" />
        <meta-data
            android:name="notch.config"
            android:value="portrait|landscape" />
        <meta-data
            android:name="unity.auto-report-fully-drawn"
            android:value="true" />
        <meta-data
            android:name="unity.auto-set-game-state"
            android:value="true" />
        <meta-data
            android:name="unity.strip-engine-code"
            android:value="true" />

        <activity
            android:name="com.unity3d.player.UnityPlayerGameActivity"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
            android:enabled="true"
            android:exported="true"
            android:hardwareAccelerated="false"
            android:launchMode="singleTask"
            android:resizeableActivity="true"
            android:screenOrientation="reverseLandscape"
            android:theme="@style/BaseUnityGameActivityTheme" >
            <intent-filter>
                <category android:name="android.intent.category.LAUNCHER" />

                <action android:name="android.intent.action.MAIN" />
            </intent-filter>

            <meta-data
                android:name="unityplayer.UnityActivity"
                android:value="true" />
            <meta-data
                android:name="android.app.lib_name"
                android:value="game" />
            <meta-data
                android:name="WindowManagerPreference:FreeformWindowSize"
                android:value="@string/FreeformWindowSize_maximize" />
            <meta-data
                android:name="WindowManagerPreference:FreeformWindowOrientation"
                android:value="@string/FreeformWindowOrientation_landscape" />
            <meta-data
                android:name="notch_support"
                android:value="true" />
        </activity>
        <activity
            android:name="com.lightwebviewsdk.WebviewActivity"
            android:exported="false"
            android:launchMode="standard"
            android:theme="@android:style/Theme.NoTitleBar" >
        </activity>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.webview.pryze.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
        </provider>
    </application>

</manifest>