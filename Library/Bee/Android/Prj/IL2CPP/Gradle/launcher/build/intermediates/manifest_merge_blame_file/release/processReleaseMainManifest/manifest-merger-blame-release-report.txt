1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.webview.pryze"
4    android:installLocation="preferExternal"
5    android:versionCode="1"
6    android:versionName="0.1.0" >
7
8    <uses-sdk
9        android:minSdkVersion="23"
10        android:targetSdkVersion="35" />
11
12    <supports-screens
12-->/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml:4:3-163
13        android:anyDensity="true"
13-->/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml:4:135-160
14        android:largeScreens="true"
14-->/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml:4:78-105
15        android:normalScreens="true"
15-->/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml:4:49-77
16        android:smallScreens="true"
16-->/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml:4:21-48
17        android:xlargeScreens="true" />
17-->/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml:4:106-134
18
19    <uses-permission android:name="android.permission.INTERNET" />
19-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:8:5-67
19-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:8:22-64
20
21    <uses-feature android:glEsVersion="0x00030000" />
21-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:10:5-54
21-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:10:19-51
22    <uses-feature
22-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:11:5-13:36
23        android:name="android.hardware.vulkan.version"
23-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:12:9-55
24        android:required="false" />
24-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:13:9-33
25    <uses-feature
25-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:14:5-16:36
26        android:name="android.hardware.touchscreen"
26-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:15:9-52
27        android:required="false" />
27-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:16:9-33
28    <uses-feature
28-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:17:5-19:36
29        android:name="android.hardware.touchscreen.multitouch"
29-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:18:9-63
30        android:required="false" />
30-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:19:9-33
31    <uses-feature
31-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:20:5-22:36
32        android:name="android.hardware.touchscreen.multitouch.distinct"
32-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:21:9-72
33        android:required="false" />
33-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:22:9-33
34
35    <permission
35-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.11/transforms/01b0759c45be5acd57b988c62a807a3d/transformed/core-1.9.0/AndroidManifest.xml:22:5-24:47
36        android:name="com.webview.pryze.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
36-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.11/transforms/01b0759c45be5acd57b988c62a807a3d/transformed/core-1.9.0/AndroidManifest.xml:23:9-81
37        android:protectionLevel="signature" />
37-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.11/transforms/01b0759c45be5acd57b988c62a807a3d/transformed/core-1.9.0/AndroidManifest.xml:24:9-44
38
39    <uses-permission android:name="com.webview.pryze.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
39-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.11/transforms/01b0759c45be5acd57b988c62a807a3d/transformed/core-1.9.0/AndroidManifest.xml:26:5-97
39-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.11/transforms/01b0759c45be5acd57b988c62a807a3d/transformed/core-1.9.0/AndroidManifest.xml:26:22-94
40
41    <application
41-->/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml:3:3-83
42        android:allowBackup="true"
42-->[:lightwebviewsdk-release:] /Users/<USER>/.gradle/caches/8.11/transforms/907504033a149dc2a8bd2318a93877dd/transformed/jetified-lightwebviewsdk-release/AndroidManifest.xml:12:9-35
43        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
43-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.11/transforms/01b0759c45be5acd57b988c62a807a3d/transformed/core-1.9.0/AndroidManifest.xml:28:18-86
44        android:configChanges="orientation|keyboardHidden|screenSize"
44-->[:lightwebviewsdk-release:] /Users/<USER>/.gradle/caches/8.11/transforms/907504033a149dc2a8bd2318a93877dd/transformed/jetified-lightwebviewsdk-release/AndroidManifest.xml:13:9-70
45        android:enableOnBackInvokedCallback="false"
45-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:25:9-52
46        android:extractNativeLibs="true"
47        android:icon="@mipmap/app_icon"
47-->/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml:3:16-47
48        android:label="@string/app_name" >
48-->/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml:3:48-80
49        <meta-data
49-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:27:9-29:33
50            android:name="unity.splash-mode"
50-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:28:13-45
51            android:value="0" />
51-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:29:13-30
52        <meta-data
52-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:30:9-32:36
53            android:name="unity.splash-enable"
53-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:31:13-47
54            android:value="True" />
54-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:32:13-33
55        <meta-data
55-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:33:9-35:36
56            android:name="unity.launch-fullscreen"
56-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:34:13-51
57            android:value="True" />
57-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:35:13-33
58        <meta-data
58-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:36:9-38:36
59            android:name="unity.render-outside-safearea"
59-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:37:13-57
60            android:value="True" />
60-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:38:13-33
61        <meta-data
61-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:39:9-41:50
62            android:name="notch.config"
62-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:40:13-40
63            android:value="portrait|landscape" />
63-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:41:13-47
64        <meta-data
64-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:42:9-44:36
65            android:name="unity.auto-report-fully-drawn"
65-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:43:13-57
66            android:value="true" />
66-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:44:13-33
67        <meta-data
67-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:45:9-47:36
68            android:name="unity.auto-set-game-state"
68-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:46:13-53
69            android:value="true" />
69-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:47:13-33
70        <meta-data
70-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:48:9-50:36
71            android:name="unity.strip-engine-code"
71-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:49:13-51
72            android:value="true" />
72-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:50:13-33
73
74        <activity
74-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:52:9-83:20
75            android:name="com.unity3d.player.UnityPlayerGameActivity"
75-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:53:13-70
76            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
76-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:54:13-194
77            android:enabled="true"
77-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:55:13-35
78            android:exported="true"
78-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:56:13-36
79            android:hardwareAccelerated="false"
79-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:57:13-48
80            android:launchMode="singleTask"
80-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:58:13-44
81            android:resizeableActivity="true"
81-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:59:13-46
82            android:screenOrientation="reverseLandscape"
82-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:60:13-57
83            android:theme="@style/BaseUnityGameActivityTheme" >
83-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:61:13-62
84            <intent-filter>
84-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:62:13-66:29
85                <category android:name="android.intent.category.LAUNCHER" />
85-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:63:17-77
85-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:63:27-74
86
87                <action android:name="android.intent.action.MAIN" />
87-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:65:17-69
87-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:65:25-66
88            </intent-filter>
89
90            <meta-data
90-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:68:13-70:40
91                android:name="unityplayer.UnityActivity"
91-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:69:17-57
92                android:value="true" />
92-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:70:17-37
93            <meta-data
93-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:71:13-73:40
94                android:name="android.app.lib_name"
94-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:72:17-52
95                android:value="game" />
95-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:73:17-37
96            <meta-data
96-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:74:13-76:71
97                android:name="WindowManagerPreference:FreeformWindowSize"
97-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:75:17-74
98                android:value="@string/FreeformWindowSize_maximize" />
98-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:76:17-68
99            <meta-data
99-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:77:13-79:79
100                android:name="WindowManagerPreference:FreeformWindowOrientation"
100-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:78:17-81
101                android:value="@string/FreeformWindowOrientation_landscape" />
101-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:79:17-76
102            <meta-data
102-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:80:13-82:40
103                android:name="notch_support"
103-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:81:17-45
104                android:value="true" />
104-->[:unityLibrary] /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:82:17-37
105        </activity>
106        <activity
106-->[:lightwebviewsdk-release:] /Users/<USER>/.gradle/caches/8.11/transforms/907504033a149dc2a8bd2318a93877dd/transformed/jetified-lightwebviewsdk-release/AndroidManifest.xml:14:9-19:20
107            android:name="com.lightwebviewsdk.WebviewActivity"
107-->[:lightwebviewsdk-release:] /Users/<USER>/.gradle/caches/8.11/transforms/907504033a149dc2a8bd2318a93877dd/transformed/jetified-lightwebviewsdk-release/AndroidManifest.xml:15:13-63
108            android:exported="false"
108-->[:lightwebviewsdk-release:] /Users/<USER>/.gradle/caches/8.11/transforms/907504033a149dc2a8bd2318a93877dd/transformed/jetified-lightwebviewsdk-release/AndroidManifest.xml:16:13-37
109            android:launchMode="standard"
109-->[:lightwebviewsdk-release:] /Users/<USER>/.gradle/caches/8.11/transforms/907504033a149dc2a8bd2318a93877dd/transformed/jetified-lightwebviewsdk-release/AndroidManifest.xml:17:13-42
110            android:theme="@android:style/Theme.NoTitleBar" >
110-->[:lightwebviewsdk-release:] /Users/<USER>/.gradle/caches/8.11/transforms/907504033a149dc2a8bd2318a93877dd/transformed/jetified-lightwebviewsdk-release/AndroidManifest.xml:18:13-60
111        </activity>
112
113        <provider
113-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11/transforms/506e0923b2edf699a568247bbabd40eb/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
114            android:name="androidx.startup.InitializationProvider"
114-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11/transforms/506e0923b2edf699a568247bbabd40eb/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:25:13-67
115            android:authorities="com.webview.pryze.androidx-startup"
115-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11/transforms/506e0923b2edf699a568247bbabd40eb/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:26:13-68
116            android:exported="false" >
116-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11/transforms/506e0923b2edf699a568247bbabd40eb/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:27:13-37
117            <meta-data
117-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11/transforms/506e0923b2edf699a568247bbabd40eb/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
118                android:name="androidx.emoji2.text.EmojiCompatInitializer"
118-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11/transforms/506e0923b2edf699a568247bbabd40eb/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:30:17-75
119                android:value="androidx.startup" />
119-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11/transforms/506e0923b2edf699a568247bbabd40eb/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:31:17-49
120            <meta-data
120-->[androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/8.11/transforms/2be88b33c7c135c3fb5a1d54448b4c97/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:31:13-33:52
121                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
121-->[androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/8.11/transforms/2be88b33c7c135c3fb5a1d54448b4c97/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:32:17-78
122                android:value="androidx.startup" />
122-->[androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/8.11/transforms/2be88b33c7c135c3fb5a1d54448b4c97/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:33:17-49
123        </provider>
124    </application>
125
126</manifest>
