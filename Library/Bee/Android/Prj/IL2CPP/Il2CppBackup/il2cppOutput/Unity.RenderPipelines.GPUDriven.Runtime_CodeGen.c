﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void EmbeddedAttribute__ctor_mC74BAF246B6DBDC3E01CA6C01E9F99BEBFB85292 (void);
extern void IsUnmanagedAttribute__ctor_mECABE2CB94F3EB55D9EBAB15A7E05DF541B049E3 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m1EDC75FA86A9139DD4A9DB0DAEFD85A1B786E080 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mBD0A412FCC9806186BDA6EE066831333F28A3414 (void);
extern void AABB_get_min_m209EF3ECD01E859258E1BDE8780CA4C25276DF70 (void);
extern void AABB_get_max_mCD29B3A2C2DEA60BBF50091D8E3C938511D82B08 (void);
extern void AABB_ToString_mB0C529C757F63FD0F78316C90D2B86206159B678 (void);
extern void AABB_RotateExtents_mE4E40331E25B5F3310112548831B83F096C98E06 (void);
extern void AABB_Transform_m1B125B36873D7F03DCBB8817DE20E81FD17C149C (void);
extern void AABBExtensions_ToAABB_m367412CA9980E495609B7267A2CC04AE39AF2402 (void);
extern void AABBExtensions_ToBounds_m7CAFEDB45226C652830072153F7236BA97C6C520 (void);
extern void BatchLayer__ctor_m5B65286582FB01A261387F6EB54330C8E47C3CFD (void);
extern void DisallowGPUDrivenRendering_get_applyToChildrenRecursively_m3E929193F5CE5D66B2FE31D776EC2A2F2116CAA9 (void);
extern void DisallowGPUDrivenRendering_set_applyToChildrenRecursively_m68D41A53534B142755C97A1E90F85707979EF47E (void);
extern void DisallowGPUDrivenRendering_OnEnable_m249A20499E8492DEEBAA51A2F682BAF5832200BE (void);
extern void DisallowGPUDrivenRendering_OnDisable_m1F1E89249A83BC47FE22BBA3AFBB3E45AA8A9CA5 (void);
extern void DisallowGPUDrivenRendering_AllowGPUDrivenRendering_m20DF3D8C370F5104515C565084A83B476625F356 (void);
extern void DisallowGPUDrivenRendering_AllowGPUDrivenRenderingRecursively_m6A9F55C1FE2690255B488CBD65D4EC7A088795DB (void);
extern void DisallowGPUDrivenRendering_OnValidate_m6F127CE57094B0CAE61BA8B4918EECB80E37240D (void);
extern void DisallowGPUDrivenRendering__ctor_mE6D1309170C6045938779078895FBDC316CD22C8 (void);
extern void DisallowSmallMeshCulling_get_applyToChildrenRecursively_m07F3D5D527D2DEF50D7B02D214383B8AF78C4C64 (void);
extern void DisallowSmallMeshCulling_set_applyToChildrenRecursively_m938F4AF22EDE3BBF3FA63774848026C0D1BE66B7 (void);
extern void DisallowSmallMeshCulling_OnEnable_m794055654CE760B4FD1780DC20C0C94C84A99A87 (void);
extern void DisallowSmallMeshCulling_OnDisable_m75A11291A610DED6EADAF48AF38509DE765AA25F (void);
extern void DisallowSmallMeshCulling_AllowSmallMeshCulling_m8D38B7DB8F8A05A7839BC00395B7137C4688E996 (void);
extern void DisallowSmallMeshCulling_AllowSmallMeshCullingRecursively_m8D9B550BDF92C3C920B34AC0DDC65D5B095F9D22 (void);
extern void DisallowSmallMeshCulling_OnValidate_mF3C1B33DE96CA700BC01B5311F80AE4E99F29DA0 (void);
extern void DisallowSmallMeshCulling__ctor_m8EE3BCDB2DE6243C9C844C82F454D0845738A51D (void);
extern void DebugDisplayGPUResidentDrawer_get_displayBatcherStats_m210D40F2C66835ADDD79B906A5266E0F233D3C34 (void);
extern void DebugDisplayGPUResidentDrawer_set_displayBatcherStats_m59E27BDE577C32E337F723FEF8F63C6BC686C662 (void);
extern void DebugDisplayGPUResidentDrawer_GetOccluderViewInstanceID_m726FCBE5E8C19295040CED7A6F87E7F31DCC3CE8 (void);
extern void DebugDisplayGPUResidentDrawer_get_occlusionTestOverlayEnable_m9A235C5BC833535F37EF6521C8201C3CE29C51A5 (void);
extern void DebugDisplayGPUResidentDrawer_set_occlusionTestOverlayEnable_mA2EB2A26999F2D9AF42AA8E3E4636C26C2B742EB (void);
extern void DebugDisplayGPUResidentDrawer_get_occlusionTestOverlayCountVisible_m634A2AC553EFFABBE7867FF3F849B56854132881 (void);
extern void DebugDisplayGPUResidentDrawer_set_occlusionTestOverlayCountVisible_mB528723B0E9B869C1A316E1E96A992559BF6F7ED (void);
extern void DebugDisplayGPUResidentDrawer_get_overrideOcclusionTestToAlwaysPass_m540F53888B63B89065870B56B8981D23580A82E3 (void);
extern void DebugDisplayGPUResidentDrawer_set_overrideOcclusionTestToAlwaysPass_m20C4D9DC1E966A9C4C4D0D466F4BD38ADD30AE13 (void);
extern void DebugDisplayGPUResidentDrawer_GetInstanceCullerViewStats_m0FBAB4D8A7F7B2AD56BABE57E5E0648B5686A85C (void);
extern void DebugDisplayGPUResidentDrawer_GetInstanceOcclusionEventStats_m602D50534C866EEB3661DA78C183640EE3224B94 (void);
extern void DebugDisplayGPUResidentDrawer_GetOccluderStats_mC14EA7475AC1AFC3C3252E433ADB1C537CD934A4 (void);
extern void DebugDisplayGPUResidentDrawer_GetOcclusionContextsCounts_m60D0D1EBF103C8A3199281D3DC5CD2C318F98CE2 (void);
extern void DebugDisplayGPUResidentDrawer_GetInstanceCullerViewCount_m92894DC66DDDCC9E86096BBE082D1D306C2A085A (void);
extern void DebugDisplayGPUResidentDrawer_GetInstanceOcclusionEventCount_mD84250B5B4DF8C05FAC630C7E60E9E27125E14A8 (void);
extern void DebugDisplayGPUResidentDrawer_AddInstanceCullerViewDataRow_m9F1A1FB82CC3344A6A3A2DC88D8EF67A67F8A767 (void);
extern void DebugDisplayGPUResidentDrawer_OccluderVersionString_m7714E7A750D1B3965E3E07AC82B75EDCFFC0A265 (void);
extern void DebugDisplayGPUResidentDrawer_OcclusionTestString_m1B455D1A286D34A76D5A3F9F0583852B93AEDB4B (void);
extern void DebugDisplayGPUResidentDrawer_VisibleInstancesString_m379FD46F020F09A4F6104D0B903B87E6D5CF440E (void);
extern void DebugDisplayGPUResidentDrawer_CulledInstancesString_mD981EA1AFC5F7D9E5FE201A576EF96E47FD545E7 (void);
extern void DebugDisplayGPUResidentDrawer_AddInstanceOcclusionPassDataRow_mD27E269D6934863E7BD1A75433C1DD6F6B080A9B (void);
extern void DebugDisplayGPUResidentDrawer_AddOcclusionContextDataRow_mDCD25714EE18DC98C484BC29D6780364F36C371B (void);
extern void DebugDisplayGPUResidentDrawer_get_AreAnySettingsActive_m749C09E9C318C2B5073709110F7D2DEFA890D144 (void);
extern void DebugDisplayGPUResidentDrawer_get_IsPostProcessingAllowed_m18A00DF3C6845F6EB6E85FEF8FAFD3A4458B4DE3 (void);
extern void DebugDisplayGPUResidentDrawer_get_IsLightingActive_m043DDEA409C65069FE6EB254A58195135068A9A1 (void);
extern void DebugDisplayGPUResidentDrawer_TryGetScreenClearColor_m8EF35A1BD3CE911D205A4031178454C2354F76FB (void);
extern void DebugDisplayGPUResidentDrawer_UnityEngine_Rendering_IDebugDisplaySettingsData_CreatePanel_m742018A2D4F79F94FD7B9A29434307A7519FD155 (void);
extern void DebugDisplayGPUResidentDrawer__ctor_mC29313F658266A745F986BBC51F16C4AF1287947 (void);
extern void Strings__cctor_m68ACF98790846338FA3596EE3124ECAFDFC10204 (void);
extern void SettingsPanel_get_PanelName_m90BADF6418AA88C7F59BA9D1A3AFA28D7665CC93 (void);
extern void SettingsPanel_get_Flags_m748B011E300968BF170B06B15BCA80C9B4A7EF80 (void);
extern void SettingsPanel__ctor_mC4E295FA1EE20556ED1C2F8876B443177EF1250E (void);
extern void SettingsPanel_AddInstanceCullingStatsWidget_mF9C247F7AF56C7FDF3B0D459DF7387E1C209332B (void);
extern void SettingsPanel_AddOcclusionContextStatsWidget_mBBDAD684776194C51A2E3010D8A59938BD7F746B (void);
extern void U3CU3Ec__cctor_mA36DB0581BF5A4E8003BF3379BC4A176BBB178B0 (void);
extern void U3CU3Ec__ctor_m795644A9581215B808B0A8ACF217AF65CA840E5E (void);
extern void U3CU3Ec_U3C_ctorU3Eb__4_0_m6433C603F60BAA5065A77094C19BB86C7F301E66 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__4_1_m700B2928DE73FF7DEF6A82F44CC8BBCC2124D2BA (void);
extern void U3CU3Ec_U3C_ctorU3Eb__4_2_mA9D2E15C3916863E032D292E2D4215957F09F6AC (void);
extern void U3CU3Ec_U3C_ctorU3Eb__4_16_m24CC97757460BA4C7D937415ED3A4B0E5F2D2511 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__4_17_m4B4D802EB8D30CAC663D4F3F84BA09EB588781A2 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__4_24_mFE4098833C8C3D728134A8714C6470ABDF7B5425 (void);
extern void U3CU3Ec_U3CAddInstanceCullingStatsWidgetU3Eb__5_1_m21D3F1B6499B429C40E3AFE8E19BBB3D23B775E6 (void);
extern void U3CU3Ec_U3CAddOcclusionContextStatsWidgetU3Eb__6_1_m00E24C8B8F792462905FFCD595B4E88335BAC41F (void);
extern void U3CU3Ec__DisplayClass4_0__ctor_m4B6078FB02177992D2D89D03034193B119FCC121 (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__3_m09F899326DEB7BC43FDA83BD3BD0933D8BF7F3C7 (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__4_mCE08B782017CDB1C40C62EAB6EDB582D97CD5123 (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__5_mE0392013001AC3B5B6F6E927E0BD1860BDBBF804 (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__6_mC55B110181918CCD37536EE778E6B7529B42C847 (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__7_m19308DA906FD7430AB4AC38DF6D9498F0EBCC74C (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__8_mD92DF44421F9DC7B7E522574E2710F6A5C36343B (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__9_mF9E585B1AC3AB4A1888A365B823DA2EBC930561A (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__10_m2076D6E428469C3DEA95C8D8ABF9C5D6490F6016 (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__11_m8E660F32E291ECAD40101E871D1DCD46505404E4 (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__12_m5B550E5C3EFAE4FB4F90F4D93494C0CD20B5B2C5 (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__13_m67A22BA78A4E106D7D26373185CE1E4D3DDCE61A (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__14_m90443DF357AD717A59E5B152B935A0B6FD4B3D2D (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__15_mB3AE67259D20D9E1FB23112977F28C6C83AD8FE2 (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__18_m5659B758462B6421D0C403F466CE324073390081 (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__19_mB54A648694706FD2A9D5BA8F313520AFADEE4827 (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__20_m6FD3989F5AAC6FF35A3DCCC385AAC6A30A5FDF8E (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__21_m256066D0FADEFCDE94027FDFE5AD2DBB417FCCE0 (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__22_mFBEF78A6BCEEC69B688E85C3E8CBB8490CB61EA1 (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__23_mCC74DDDC57694ECDD68316C373AD2E5D7959D2A5 (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__25_m04E4D66D56588388AFED0AFBE7E0BE534FD46D24 (void);
extern void U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__26_m77486D483224008476C5144215DAA92B2A555BA0 (void);
extern void U3CU3Ec__DisplayClass5_0__ctor_mF3D08B015A3B6ECA3D2EC8E9824AB1808DA40D07 (void);
extern void U3CU3Ec__DisplayClass5_0_U3CAddInstanceCullingStatsWidgetU3Eb__0_mAFCC6F5239982E03A4CD3B1910A434D9ABBAF259 (void);
extern void U3CU3Ec__DisplayClass5_0_U3CAddInstanceCullingStatsWidgetU3Eb__2_mA787F433219B34806F931F1610D41ADA4EB0325E (void);
extern void U3CU3Ec__DisplayClass5_0_U3CAddInstanceCullingStatsWidgetU3Eb__3_m15303C44E35730C25B1342370B74ED28764B85AD (void);
extern void U3CU3Ec__DisplayClass6_0__ctor_m6ADCBA286E64868206185185B5EA12F646D6B302 (void);
extern void U3CU3Ec__DisplayClass6_0_U3CAddOcclusionContextStatsWidgetU3Eb__0_m7D8DB169EB50BF8D498D565117D77ED9BBEE5786 (void);
extern void U3CU3Ec__DisplayClass29_0__ctor_m9E9E0D304D4857CCC41C81920D8F9B2426B21022 (void);
extern void U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__0_mC0E8B5021FEC842FDA982AF122F22BB6340D35E9 (void);
extern void U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__1_m9AE4D495A32845644E11FB2B895DA5E75F8635F8 (void);
extern void U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__2_m95BA865F2DE7F6DBDD8FCEFE47701070C5AE3F98 (void);
extern void U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__3_m58F09A1804446AF0F5931AC7196D7BAC039F3EDB (void);
extern void U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__4_m3747952920734A24FC5B1F9DCFE309747F9C4C4D (void);
extern void U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__5_m74C8AB5E55872D1160D6F08B78F48718683BF499 (void);
extern void U3CU3Ec__DisplayClass34_0__ctor_m51E792F40771F45B88ABD1EE79EF175CCE76CD48 (void);
extern void U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__0_m27FB30AE227BED3DFA61625B55C8EBE3082D04C9 (void);
extern void U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__1_m1E2FEB11D28E7B45D586D165C7B58E8C799C2989 (void);
extern void U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__2_m4652608ED9683E21291FE5A1F074C1DC6235627B (void);
extern void U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__3_m497AA999AF8B5BAC82C1131C80256A58497A0190 (void);
extern void U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__4_m94EE7C495806664154E3E3BDC30518483010AEF4 (void);
extern void U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__5_m8BF230F649C38952F63793E85645803C3E72E468 (void);
extern void U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__6_mAA756A602DE4CAA7F4F3ABF323D5D6A41FADF620 (void);
extern void U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__7_m111803CA6822911B48D35155FF2DE904987938BF (void);
extern void U3CU3Ec__DisplayClass35_0__ctor_mD214B5A8783451B94F4590004B1FE5F08EC6D092 (void);
extern void U3CU3Ec__DisplayClass35_0_U3CAddOcclusionContextDataRowU3Eb__0_m4C70823339569EBDDFBBE48D1E3EA1501DADE74D (void);
extern void U3CU3Ec__DisplayClass35_0_U3CAddOcclusionContextDataRowU3Eb__1_m28A3FB09CA136E909008FFC0989CF96328E6EBC6 (void);
extern void U3CU3Ec__DisplayClass35_0_U3CAddOcclusionContextDataRowU3Eb__2_m711316387059484363D585616C8D01248FCA7EED (void);
extern void U3CU3Ec__DisplayClass35_0_U3CAddOcclusionContextDataRowU3Eb__3_mA0FC30C2542A04A04E4E3C75BFDF01746FCCAF7B (void);
extern void Line_LineOfPlaneIntersectingPlane_m0ED39FE7E09D672A03C93AA51883CECB1FFF2EF8 (void);
extern void Line_PlaneContainingLineAndPoint_m59813FE596901FCDFCA7E64A83841D14AB030402 (void);
extern void Line_PlaneContainingLineWithNormalPerpendicularToVector_m34DB45E98946F82ECB81242D6CE074AEF43E0CF0 (void);
extern void ReceiverPlanes_IsSignBitSet_m115EC872F6B26CBF654D849659242E76E7850A09 (void);
extern void ReceiverPlanes_LightFacingFrustumPlaneSubArray_m995D1E681E0ED63DFA8489C582A2FCBAF1C9356B (void);
extern void ReceiverPlanes_SilhouettePlaneSubArray_m851F735918EE9297C64815C42AAD446BCBE55F4B (void);
extern void ReceiverPlanes_CreateEmptyForTesting_m31C17EE66110F80DE225AC49E8AC79ED4D838ADB (void);
extern void ReceiverPlanes_Dispose_mE53DCBAD81DDB002760FC5661F3B26330B62ACF4 (void);
extern void ReceiverPlanes_Create_mB53449C117AA6108D5F00315F573CE710945A81F (void);
extern void FrustumPlaneCuller_Dispose_m802E9F4605F17D55D46C6C0D308830667DE0A08B (void);
extern void FrustumPlaneCuller_Create_m4DD061D5BCFFFDBE074596E2373150D4787331F3 (void);
extern void FrustumPlaneCuller_ComputeSplitVisibilityMask_mCFC42DB8BADE15AB4A87F4BC60A3B5D7507745DC (void);
extern void PlanePacket4__ctor_m1B67A6C35FB1B6B6BE21D76B496DA3531F636CC7 (void);
extern void ReceiverSphereCuller_CreateEmptyForTesting_mC63AFF1A7B2CF647E5A605E41125BA5E5533AE0B (void);
extern void ReceiverSphereCuller_Dispose_mFCC83320D29032DBC7A3173ADFE8F764A472FFA5 (void);
extern void ReceiverSphereCuller_UseReceiverPlanes_mEA2EE8FE32563F3FBE40D6AADA9724719186674E (void);
extern void ReceiverSphereCuller_Create_m63514E053CF0DD1CD37702E56C84F4F0408A8B00 (void);
extern void ReceiverSphereCuller_DistanceUntilCylinderFullyCrossesPlane_m1E5B038B031E5090A38EE0FF6EFC405E4BDD63E1 (void);
extern void ReceiverSphereCuller_ComputeSplitVisibilityMask_m1188332DCE2CBF80B1A431AFB1BD49955898936F (void);
extern void GPUResidentBatcher_get_batchersContext_m832BD0381846BE2E11D425CE1319953A411D64CE (void);
extern void GPUResidentBatcher_get_occlusionCullingCommon_mCC135850F6B339139CBA5E60715E4B059195CBC7 (void);
extern void GPUResidentBatcher_get_instanceCullingBatcher_m9E7BF9BA5E63072AB043C86836AC4C036AD5D465 (void);
extern void GPUResidentBatcher__ctor_mE43D45A4CCB93B7DD21D5FB49C4D6BA16DEC72B2 (void);
extern void GPUResidentBatcher_Dispose_m027050D276B285BED80401912FB6B3D0BADC90AD (void);
extern void GPUResidentBatcher_OnBeginContextRendering_mFCE0FA21DAB11BC5B770CB5C63F14C32B2ECC30F (void);
extern void GPUResidentBatcher_OnEndContextRendering_mDC1CCC712A83C48E61D34E56BE21B5458C77BB61 (void);
extern void GPUResidentBatcher_OnBeginCameraRendering_m818CA5BB64950FFCF566414C70CC5D8B6D96A922 (void);
extern void GPUResidentBatcher_OnEndCameraRendering_mC31C1F8395CEED9B4AD6321DACC36E9115666885 (void);
extern void GPUResidentBatcher_UpdateFrame_mB0B6728FE5D08E785FD7747D9AF4D065705D944D (void);
extern void GPUResidentBatcher_DestroyMaterials_m0BEE5AE70E666A3E35E16EA8D941480E1351A243 (void);
extern void GPUResidentBatcher_DestroyDrawInstances_m9513A335709809ED850D1AF76E2A6FFCB8707AF9 (void);
extern void GPUResidentBatcher_DestroyMeshes_m4B3BF017904878023774926DE457D2476283B094 (void);
extern void GPUResidentBatcher_FreeRendererGroupInstances_mB2929BF0DE2234256EB75CC078DE8441CA9594E9 (void);
extern void GPUResidentBatcher_InstanceOcclusionTest_mCDEFBDADDB90AA06A2E1394B5B80E807CBB5AA43 (void);
extern void GPUResidentBatcher_UpdateInstanceOccluders_m5570E1F11C12314DD68EA4A1B9B4EE0473FD6A18 (void);
extern void GPUResidentBatcher_UpdateRenderers_m08752E00B533B9709C0EF2892D843BF10D24920B (void);
extern void GPUResidentBatcher_SchedulePackedMaterialCacheUpdate_mB5D6DC79D682FEAA1A5F68BEB685132B1F549881 (void);
extern void GPUResidentBatcher_PostCullBeginCameraRendering_m9C006EAC8C65FBC1B7DD3F9F4123E071EC1D2F44 (void);
extern void GPUResidentBatcher_OnSetupAmbientProbe_mDCAD4C9AE21158471F846321EA2A58DBDA1914A6 (void);
extern void GPUResidentBatcher_UpdateRendererInstancesAndBatches_m7CC59149342BCCB17DE20FCD2BF0294D613B48B9 (void);
extern void GPUResidentBatcher_UpdateRendererBatches_m7FDCEBC6D9743BA42ED99E545EBBF438702B56DC (void);
extern void GPUResidentBatcher_OnFinishedCulling_m3894D01381A5290564E27DC8315623AD4B21975D (void);
extern void GPUResidentBatcher_ProcessTrees_mA20D5412045E8AE5E485DE8F50B350F0A88538B7 (void);
extern void GPUResidentBatcher_UpdateSpeedTreeWindAndUploadWindParamsToGPU_m1E26807F0F67557341970AC983776178A0E90490 (void);
extern void GPUResidentDrawer_get_instance_m142CE6BEC88AA7FA34052B0138128C3B944FEBDD (void);
extern void GPUResidentDrawer_IsInstanceOcclusionCullingEnabled_m03F098AAAA5FCB8140B53C641EB2B0381669BC8E (void);
extern void GPUResidentDrawer_PostCullBeginCameraRendering_m3EB60CDFBF342ABD0B11B30439BB01B7CD6F1F77 (void);
extern void GPUResidentDrawer_OnSetupAmbientProbe_mF67DC77B41AD752F71A25EA4221AA3180AA236CC (void);
extern void GPUResidentDrawer_InstanceOcclusionTest_m0DD4F0A4685967C617984FBCE5A0B99A35790AFE (void);
extern void GPUResidentDrawer_UpdateInstanceOccluders_mD2F1BAB128CEB6B6A731FEA876A1E08A31C98B30 (void);
extern void GPUResidentDrawer_ReinitializeIfNeeded_mE8A70A9A6B9C8D4A341552E05D95E4D74B7D68D5 (void);
extern void GPUResidentDrawer_RenderDebugOcclusionTestOverlay_m17664202C62084572F6037B5F1C61E2FE8C0BFD0 (void);
extern void GPUResidentDrawer_RenderDebugOccluderOverlay_mB7819CD0C90F839351CE854B2DD297D14F5F830B (void);
extern void GPUResidentDrawer_GetDebugStats_m857EE673158C860D3471D0CC6203B60D0BC98B4D (void);
extern void GPUResidentDrawer_InsertIntoPlayerLoop_mBDEE8B11EE73F12439561D73E4A2A3C8D6861007 (void);
extern void GPUResidentDrawer_RemoveFromPlayerLoop_m18F5D085D7C67EEA7EFDB5ABC52AB8C343CA5CAF (void);
extern void GPUResidentDrawer_IsEnabled_m03CFAB6E2CE8D71361F5223C940F4C0A785A1116 (void);
extern void GPUResidentDrawer_GetGlobalSettingsFromRPAsset_m4710F7D4D983AEB1ADD00A40E7E4068C330C9A41 (void);
extern void GPUResidentDrawer_IsForcedOnViaCommandLine_mC2F9713BA2691A02C23E22E59DDC30E41289539F (void);
extern void GPUResidentDrawer_IsOcclusionForcedOnViaCommandLine_mA0CD2D200E26C820386D92E58645EF2FF0B02FDA (void);
extern void GPUResidentDrawer_Reinitialize_m542E6537EC9C14A35291824BA6798D5D0D747190 (void);
extern void GPUResidentDrawer_CleanUp_mF773237C2F3AEF0251249FFD56C02F7A650EE9C2 (void);
extern void GPUResidentDrawer_Recreate_m09E096E3492D77EE4A3D0D070FA53D2017AD6874 (void);
extern void GPUResidentDrawer_get_batcher_m03715B9C280D664F90B0B1F592D9C3ADD212F9F3 (void);
extern void GPUResidentDrawer_get_settings_m3F0472441E9F1191B0E0FC43B6D8BBF004EAF3C6 (void);
extern void GPUResidentDrawer__ctor_m3B65B01D5C54231BF2D7C4C65B4FA11DDA8CCA1A (void);
extern void GPUResidentDrawer_Dispose_mD5709371AD0309C33F25511B22C7C1DCD6AC234D (void);
extern void GPUResidentDrawer_OnSceneLoaded_m4C2686BC1182C9327DBA362D670CB30601292F5A (void);
extern void GPUResidentDrawer_PostPostLateUpdateStatic_m2322043F8B8734792788BF29370233B9BFFBFF7F (void);
extern void GPUResidentDrawer_OnBeginContextRendering_m383B0726811F68E670753BDD5F4EE772DE4593C0 (void);
extern void GPUResidentDrawer_OnEndContextRendering_m4E432047D9A70FD9FE8718FB195A9477A657857A (void);
extern void GPUResidentDrawer_OnBeginCameraRendering_mDCB0CA5A9CB1F1BA25DB7214D3BD75975AA2B705 (void);
extern void GPUResidentDrawer_OnEndCameraRendering_m1F301D2884DCFB5ADD5CB533FEC803B898EDC690 (void);
extern void GPUResidentDrawer_PostPostLateUpdate_m94401477ABD5387DBAAD1D6A1CC39E59AE1E2EEB (void);
extern void GPUResidentDrawer_ProcessMaterials_m5F9A91DF336FD3CA1CFD81608475C39F85FD89D5 (void);
extern void GPUResidentDrawer_ProcessMeshes_m254F0CB1EB50564FF5FCC3CDD00420784BB45C04 (void);
extern void GPUResidentDrawer_ProcessLODGroups_mFDBD9F9CD5F13FDE5AEC4817644BC1BEF6D71D7C (void);
extern void GPUResidentDrawer_ProcessRendererMaterialChanges_m4883F6718DF27F989C1C1EE43581646928ED9EE3 (void);
extern void GPUResidentDrawer_ProcessRenderers_m381BBD34CA5CDCABD580BD2464CBA98ABDBC9E87 (void);
extern void GPUResidentDrawer_TransformInstances_m20475CB2F2401DD9A54661E4EA63ACC2A5D72B49 (void);
extern void GPUResidentDrawer_FreeInstances_mDCFFEBAB0E14151660E84E08BEC9AF91F149F611 (void);
extern void GPUResidentDrawer_FreeRendererGroupInstances_m038FBB93EAF3C06AA447CBBC04E2F9CEC8675814 (void);
extern void GPUResidentDrawer_AppendNewInstance_mEB1EA724EECB7E96FB4A378582A650ABBD8E635E (void);
extern void GPUResidentDrawer_ScheduleQueryRendererGroupInstancesJob_mCC2105095C5D0AB94F74B0DF5033C72BF8F64E21 (void);
extern void GPUResidentDrawer_ScheduleQueryRendererGroupInstancesJob_m059C106F6838578EB820B0854214AD8E52414C43 (void);
extern void GPUResidentDrawer_ScheduleQueryRendererGroupInstancesJob_mC87594A6FDDAC454AA8ED09E3F975236FDE87F01 (void);
extern void GPUResidentDrawer_ScheduleQueryMeshInstancesJob_m63420C906AFFC9D2431B6FD4B71BEBC19F6B902C (void);
extern void GPUResidentDrawer_ClassifyMaterials_m07623E4BDE7E899A29EB6E9A9DA2B9FA78ACC722 (void);
extern void GPUResidentDrawer_FindUnsupportedRenderers_m1C0CB02546C7733938983343B8668CAC84E60772 (void);
extern void GPUResidentDrawer_GetMaterialsWithChangedPackedMaterial_m112BC45C2CEA7D3B26EDE1B217430AA49E5EE0E8 (void);
extern void GPUResidentDrawer_FindRenderersFromMaterials_m08545CCE43F5D5D5492189DDD6D506E98AB208B5 (void);
extern void GPUResidentDrawer_IsProjectSupported_m8070103F51F3975E8D573377D445553710EBA457 (void);
extern void GPUResidentDrawer_IsProjectSupported_m4F5CDE5F81A0BE7CF42E37F1545E85A8C5A07DE3 (void);
extern void GPUResidentDrawer_IsGPUResidentDrawerSupportedBySRP_m24CCB3D5623CD94D8DA06D20CE59806BC9D35922 (void);
extern void GPUResidentDrawer_LogMessage_m7FF3E65D1A87DF183F9D29254AE637D842B88D41 (void);
extern void ClassifyMaterialsJob_Execute_m94CBFED114435BCA576F6A9344BD213DCAC28DA6 (void);
extern void FindUnsupportedRenderersJob_Execute_m69B13CFCB49D64142693707BD2ABEE5BA954B26E (void);
extern void FindRenderersFromMaterialJob_Execute_mBEE77639F82D94D447BBCD74866B3A1FC106CDC3 (void);
extern void GetMaterialsWithChangedPackedMaterialJob_Execute_mDCCF53C7F3BF79C9F789EEE83784BC5763F6413F (void);
extern void Strings__cctor_m40AEC7C35446DC97C6BA1EFB06EA5B4F5CAADAB4 (void);
extern void DebugRendererBatcherStats__ctor_mAE82DDFAB36DFF39F4B28D1D43B5896042B250B0 (void);
extern void DebugRendererBatcherStats_Dispose_m76B62E89A85CAA6D7108200B3C4BDAF9DB4832E7 (void);
extern void GPUResidentDrawerResources_UnityEngine_Rendering_IRenderPipelineGraphicsSettings_get_version_m136CCE7FDEBC9741FDCAB827F74D51A7B7DF6E07 (void);
extern void GPUResidentDrawerResources_get_instanceDataBufferCopyKernels_m4821367D99C54C654F6FC0F677D53038EACAEB40 (void);
extern void GPUResidentDrawerResources_set_instanceDataBufferCopyKernels_m126688C4BE7907A678DE23AF2E3332DD8A34827A (void);
extern void GPUResidentDrawerResources_get_instanceDataBufferUploadKernels_mA900FCDAA87450DEBC3C134E015FA14685ADA9EA (void);
extern void GPUResidentDrawerResources_set_instanceDataBufferUploadKernels_m54B6A238CECBBA7093D5D1FEDA9B2C8E56332678 (void);
extern void GPUResidentDrawerResources_get_transformUpdaterKernels_mCA7A4849EFDC13E448339A6AEC42065FDAB5C63C (void);
extern void GPUResidentDrawerResources_set_transformUpdaterKernels_mC87744E70D4108F953B0C637C483AA5ABE685709 (void);
extern void GPUResidentDrawerResources_get_windDataUpdaterKernels_m2C5FADD001A37D11A324FE865E925CD9A5501315 (void);
extern void GPUResidentDrawerResources_set_windDataUpdaterKernels_m7828D337715FCE91AFEC15C34DAC0F61753A725F (void);
extern void GPUResidentDrawerResources_get_occluderDepthPyramidKernels_m7006886C18CF45076331E4B6114CA37A3CE69532 (void);
extern void GPUResidentDrawerResources_set_occluderDepthPyramidKernels_m9103835307DA4F40F0439903A0E7DF5C8712B704 (void);
extern void GPUResidentDrawerResources_get_instanceOcclusionCullingKernels_m0096BB5665B29E5552385CC7C4990DDF95C6EDB1 (void);
extern void GPUResidentDrawerResources_set_instanceOcclusionCullingKernels_m06138322ED0CC1A22774E0D41C74B4CE691BFFEE (void);
extern void GPUResidentDrawerResources_get_occlusionCullingDebugKernels_m8B7B3517326F40890A0935A0DC1DD55C8B14F164 (void);
extern void GPUResidentDrawerResources_set_occlusionCullingDebugKernels_m94C497F616A9D90161CFEA216A07029CC55D0D27 (void);
extern void GPUResidentDrawerResources_get_debugOcclusionTestPS_m0A869F58FF84A5B43E925DBE72A100212D672BF2 (void);
extern void GPUResidentDrawerResources_set_debugOcclusionTestPS_m2B0F9F3EC01C30490B37C40D1BACDB2919E88ACD (void);
extern void GPUResidentDrawerResources_get_debugOccluderPS_m476766B8038CC61693711BEAB81BD5B65C95D9DD (void);
extern void GPUResidentDrawerResources_set_debugOccluderPS_m4A969CD0B817583E1A089F6C636A28F8F9F32835 (void);
extern void GPUResidentDrawerResources__ctor_m9A9FBC773137C24968523F68F1ED3D55922BAF1C (void);
extern void OcclusionTestMethods_GetBatchLayerMask_m1CC038C215B2531DDD0A4C8AF03E2DC518A43D09 (void);
extern void OcclusionCullingSettings__ctor_mE814849AC60B1DC1AC17E02F1AF2128DF38FE95A (void);
extern void OccluderSubviewUpdate__ctor_m482C60BE94ECE0B1F15E930936345D9F60E399A8 (void);
extern void OccluderParameters__ctor_mBBA1CD9856BD109C9F1D18715B28E2DA95CE83D9 (void);
extern void IGPUResidentRenderPipeline_ReinitializeGPUResidentDrawer_m6D9AB828C92C7E97A8B441028C3056A905005E3F (void);
extern void IGPUResidentRenderPipeline_IsGPUResidentDrawerSupportedBySRP_mFED552B69E782E4125B03C0EC1B2007FEB023553 (void);
extern void IGPUResidentRenderPipeline_IsGPUResidentDrawerSupportedBySRP_mAC46B52099ED2E34F12F8B7E802DC67E0113A0A9 (void);
extern void IGPUResidentRenderPipeline_IsGPUResidentDrawerSupportedByProjectConfiguration_m5CE31EEE661C5F8B23636A4F3452CEA8A7AC0B66 (void);
extern void IGPUResidentRenderPipeline_IsGPUResidentDrawerEnabled_m4B17CE9EBDCEADAB2C6378F8DACE655817DD1757 (void);
extern void RangeKey_Equals_m05E612C122D91758588CEB7529751045E6F09493 (void);
extern void RangeKey_GetHashCode_mC38845B6A1CC657D6F6B1149E7448AA6A3EF3257 (void);
extern void DrawKey_Equals_mBA2838871085C8944B81322DC6E851F4273E8E91 (void);
extern void DrawKey_GetHashCode_m6E3C5F3D42D02D8035AA7E7B5501FDBC1551F4E5 (void);
extern void BinningConfig_get_visibilityConfigCount_m0B698DBD65F8147B56C14CA7CFB741EAB701B3B9 (void);
extern void CullingJob_PackFloatToUint8_m7E24A8A8334FF990F67C75BCE8BB991037645230 (void);
extern void CullingJob_CalculateLODVisibility_mEBC1FA7E67148AD8CCB4D54FBAB3CF92DDF72A0E (void);
extern void CullingJob_CalculateVisibilityMask_m285DA7E28A8582C95BAE2C6794C34FC3F8F195ED (void);
extern void CullingJob_Execute_m172CC218FA8F7198BD69C1C87294FD90D94DB884 (void);
extern void AllocateBinsPerBatch_IsInstanceFlipped_m5ED8B8D355142D10DB3983D15A256B5DBD4D6ABA (void);
extern void AllocateBinsPerBatch_Execute_m8244D8F2860ED03B023A6D348D9471F2AF76E0F1 (void);
extern void PrefixSumDrawsAndInstances_Execute_m8EADFE8897EAE00866B67F8AE64CA774EE33B5F9 (void);
extern void DrawCommandOutputPerBatch_EncodeGPUInstanceIndexAndCrossFade_m6EFB0D6BB29BF2F65B66913BD2F82E4364ED8830 (void);
extern void DrawCommandOutputPerBatch_IsInstanceFlipped_m1AE2BD53A7D17783F262A94539417F423FD4CF79 (void);
extern void DrawCommandOutputPerBatch_Execute_mF995D35E2E640A3172F4BC62E37BF103CFBBBBA7 (void);
extern void CompactVisibilityMasksJob_Execute_m7D5B39DEFEC4944D35111B4B7D22267147B4AB1E (void);
extern void InstanceCullerSplitDebugArray_get_Counters_mBDB8C933AEEB9F934132442CA20FC54A9E9A01AB (void);
extern void InstanceCullerSplitDebugArray_Init_m0E461744D899802E8123D32B79AD2EEC7FF1ED66 (void);
extern void InstanceCullerSplitDebugArray_Dispose_m0FA405A9BA291E560088D998A71EE3FEF95100C1 (void);
extern void InstanceCullerSplitDebugArray_TryAddSplits_m8F0CAC17CB3DA747A187760D3961255C1E95BDF5 (void);
extern void InstanceCullerSplitDebugArray_AddSync_m05D0080857890E3C8E46730B1D4DC17E4FB71D90 (void);
extern void InstanceCullerSplitDebugArray_MoveToDebugStatsAndClear_mAA78838CCD17348C01F115022EF530C3F5B98AD4 (void);
extern void InstanceOcclusionEventDebugArray_get_CounterBuffer_mF25E7B744518F980443AFCF79E48EA3CCF852D04 (void);
extern void InstanceOcclusionEventDebugArray_Init_mF8AEF95440DA22ACC2DB5717CEBD9C8B8B3797D5 (void);
extern void InstanceOcclusionEventDebugArray_Dispose_m2D57744F19C60D3E9B6EFDF81BB08498853E37C1 (void);
extern void InstanceOcclusionEventDebugArray_TryAdd_m2FD080E0DDCA404C8C362A5933EC852A9CD5F109 (void);
extern void InstanceOcclusionEventDebugArray_MoveToDebugStatsAndClear_mFEF02F07D15F2F2942FABFD0C7C577FAB1859977 (void);
extern void Info_HasVersion_m9E077F93A4F89E728DF4FA88764ABF91C732D960 (void);
extern void InstanceCuller_Init_mDBBD40C7316EBE16746FACA43170B7E1001EC67F (void);
extern void InstanceCuller_CreateFrustumCullingJob_m073380FE489347D6F8CFB772AE33D0599532F54A (void);
extern void InstanceCuller_ComputeWorstCaseDrawCommandCount_mF1277DD5CAC20EEE023EA894E84D7C1ADD7B180D (void);
extern void InstanceCuller_CreateCullJobTree_m2E52805D7C465D995C4A87D156710394A4BB0EDF (void);
extern void InstanceCuller_CreateCompactedVisibilityMaskJob_m3A2F38959953B889E5E7456DE426ABA7134DFB9B (void);
extern void InstanceCuller_InstanceOccludersUpdated_mCA7D8171B44CF76E6A48314D7F97F2B698DCCE05 (void);
extern void InstanceCuller_DisposeCompactVisibilityMasks_mE05A5A23578160F5FDE1F8F2E59B7B8E2819BC76 (void);
extern void InstanceCuller_DisposeSceneViewHiddenBits_mB8548C25259A3AE546942634429EF581779992BD (void);
extern void InstanceCuller_GetCompactedVisibilityMasks_mED15105E7567F79042B542B56B4DE2ABEDE14C26 (void);
extern void InstanceCuller_InstanceOcclusionTest_m8E12352E27A10F62B9C737C3C7D0394DBC416F15 (void);
extern void InstanceCuller_EnsureValidOcclusionTestResults_m22DA66230772FAE8FA78B7E2B3839AB414BE8A22 (void);
extern void InstanceCuller_AddOcclusionCullingDispatch_mDA6F922B0734577B8F921E9AE4C02CEE4F8A32A5 (void);
extern void InstanceCuller_FlushDebugCounters_m1036A68627B296B0CE88254424A25EEA430BCCE6 (void);
extern void InstanceCuller_OnBeginSceneViewCameraRendering_m9F271E3A4A411E077FC767C85C1813F41C9ADA3D (void);
extern void InstanceCuller_OnEndSceneViewCameraRendering_m01D33D11A4853EB0397A2996F10795662828C2F8 (void);
extern void InstanceCuller_UpdateFrame_m4273286746BAF0CADC1665DB994C64B47FB32F42 (void);
extern void InstanceCuller_OnBeginCameraRendering_mB7CDBC0AD43EEC324B9584B96C25DF3B911EC907 (void);
extern void InstanceCuller_OnEndCameraRendering_mF0DF0261BF1978BAFAC10F5F9DB533FC86B8089B (void);
extern void InstanceCuller_Dispose_m7C8B413AE9FD5A5C4EAD81A22D2D4C1F2E6C368F (void);
extern void ShaderIDs__cctor_mBB02927959BF46D5749EF53F56D62D1C6736FEF2 (void);
extern void SetupCullingJobInput_Execute_mDE838174B1F7960CBAD7010EB7FB70CCA168E977 (void);
extern void InstanceOcclusionTestPassData__ctor_m73F58387AFEBAB53120CC7AEF289EC50A82AFC8C (void);
extern void U3CU3Ec__cctor_m5495172327D2C037C7A396EB43420AD62D33BEBE (void);
extern void U3CU3Ec__ctor_mBE453CFF0731A8063B406EBCFB4E99157CE39C80 (void);
extern void U3CU3Ec_U3CInstanceOcclusionTestU3Eb__26_0_mC8CC22455E7B2DCCE2556B495FB257AB2CB3B04B (void);
extern void OnCullingCompleteCallback__ctor_m77440340DEF8EC177F2367F9CDFB4C7039B109CD (void);
extern void OnCullingCompleteCallback_Invoke_mC230E2F011722DC958EFACC67609C75FFB0A54C8 (void);
extern void OnCullingCompleteCallback_BeginInvoke_mAA8DD729BE78EF7C06DC33D3714B306D8793E492 (void);
extern void OnCullingCompleteCallback_EndInvoke_m2ABE1C345A8D041A537AD05BE2B8632D872865A0 (void);
extern void InstanceCullingBatcherDesc_NewDefault_mC543DB9EBF6418504763D8C66FCD457AC5A8B9AF (void);
extern void PrefixSumDrawInstancesJob_Execute_m6384DA1CB6A1E053F358640B9E2FE7F378677771 (void);
extern void BuildDrawListsJob_IncrementCounter_m2198A8B6F4713D070C44BF162EEAC564C15A120F (void);
extern void BuildDrawListsJob_Execute_m731DC4DAB67702B447641FF630CD1C60A596636E (void);
extern void FindDrawInstancesJob_Execute_m011CC04D8F9DA0E7E3F04783AA60964898BEDC1D (void);
extern void FindMaterialDrawInstancesJob_Execute_m0A8C723C617595319217DB5AFD151AC28F7759A0 (void);
extern void RemoveDrawInstanceIndicesJob_RemoveDrawRange_m57FBE6346CD44B5B864E6CA89A0549D9C880E652 (void);
extern void RemoveDrawInstanceIndicesJob_RemoveDrawBatch_mF0A59FED062CDA68A971249F539ACDB5D90DB5A5 (void);
extern void RemoveDrawInstanceIndicesJob_Execute_m37A6D16D906E5C1A7C3340E64C3ADAB45607EFCC (void);
extern void UpdatePackedMaterialDataCacheJob_ProcessMaterial_mCFF81645A1DB74D08DAAD17D679FA00A306E57E3 (void);
extern void UpdatePackedMaterialDataCacheJob_Execute_m117BE8C4EEAC8E86F3445464E84ECB6A954A3EBB (void);
extern void CreateDrawBatchesJob_EditDrawRange_mC8D762596291034355C1B116E92CAF83C5051F96 (void);
extern void CreateDrawBatchesJob_EditDrawBatch_mAF28C16786B3B72E623304CCAD2B3DF57FDB15E5 (void);
extern void CreateDrawBatchesJob_ProcessRenderer_m520CC5A20C49B3A0A91F8A06F8D954DD8D860FE0 (void);
extern void CreateDrawBatchesJob_Execute_m28C7A7916BC78A53B2A02EAF503B94E85BD03F4A (void);
extern void CPUDrawInstanceData_get_drawInstances_m5B182A75D8968C69C7EFF2CE773D0029E7412D68 (void);
extern void CPUDrawInstanceData_get_batchHash_m407E1D37D4A5D8BDE76DE45CF33C53643DA47C12 (void);
extern void CPUDrawInstanceData_get_drawBatches_m6E62CEC9E106C2CB84B185517A04105E77D875F6 (void);
extern void CPUDrawInstanceData_get_rangeHash_mF69F0BF1AFACDC62B7466A99F24DBE460E4BB9B6 (void);
extern void CPUDrawInstanceData_get_drawRanges_m980D02096E99CCD5299D5B06AAEC78E986A92754 (void);
extern void CPUDrawInstanceData_get_drawBatchIndices_m414A7D8E605519E4B6F5F3B1020BE3B21F991E29 (void);
extern void CPUDrawInstanceData_get_drawInstanceIndices_m22402BAB913DE193060C175FF253FC5C3D8D0D56 (void);
extern void CPUDrawInstanceData_get_valid_m224B4D6D342C4EAC30B8F8A5A34837827DEDCE3E (void);
extern void CPUDrawInstanceData_Initialize_m1D889D823B6B72F24F03385CBC492D3B18B510E4 (void);
extern void CPUDrawInstanceData_Dispose_m0F6E3A9F7C46E04F0C1A5073F3C3039BD1E29D3B (void);
extern void CPUDrawInstanceData_RebuildDrawListsIfNeeded_m8CE9E2B0870BADE0F90B83C54ED6A206B39F5248 (void);
extern void CPUDrawInstanceData_DestroyDrawInstanceIndices_m6B1D8988D3CE889D903F736E9814EAFFCCC35FC3 (void);
extern void CPUDrawInstanceData_DestroyDrawInstances_m6410B734F4067FA0A56142576467F65ACAF30343 (void);
extern void CPUDrawInstanceData_DestroyMaterialDrawInstances_mC3646BA15537CA5C257F05E06AC409B4ACA4E167 (void);
extern void CPUDrawInstanceData_NeedsRebuild_m0F632EDB133A8FFB533DB9A5BBCC199DEB9AC11C (void);
extern void CPUDrawInstanceData__ctor_m21E4C7096825C181C31F9F5A8600D2CF96F5B239 (void);
extern void InstanceCullingBatcher_get_batchMaterialHash_mF1798E2B3C1C885996C171F3F4EDFB7DFDC53151 (void);
extern void InstanceCullingBatcher_get_packedMaterialHash_m12837A329EFD8A76B3E25C2140F516E2847570EC (void);
extern void InstanceCullingBatcher__ctor_m98D5EFDC5F3AF7FF61DEE777748DBD66758A239B (void);
extern void InstanceCullingBatcher_get_culler_mBFCD2ACBB0F3D4A650233F186A5EB98D47A714D4 (void);
extern void InstanceCullingBatcher_Dispose_m6A9FED11F52B4FD30BAF1ECE6676B853B9BCCA42 (void);
extern void InstanceCullingBatcher_GetBatchID_m149BEE4723E1E52B23E9B63AE33960431ADAE18C (void);
extern void InstanceCullingBatcher_UpdateInstanceDataBufferLayoutVersion_mFFB18677B049FC0438D8169800EE48CE19594268 (void);
extern void InstanceCullingBatcher_GetDrawInstanceData_mED58B161705DB4D131F341DB74C65163A6920ABA (void);
extern void InstanceCullingBatcher_OnPerformCulling_mC82A6CB4199689882BEFB834A6E8CA9DFEFB02DD (void);
extern void InstanceCullingBatcher_OnFinishedCulling_mA2C92EE1760B83D8F49B13F81E6736E488ACBF70 (void);
extern void InstanceCullingBatcher_DestroyDrawInstances_m53512A8BD8DF31ADE89587FD55D5B4226F9F6A44 (void);
extern void InstanceCullingBatcher_DestroyMaterials_mF017E0AF2451934C0B8D3D9F7457FCDB3908F087 (void);
extern void InstanceCullingBatcher_DestroyMeshes_m7CB28EB447BDD8A7EF2780BB3BA51A16239B91BD (void);
extern void InstanceCullingBatcher_PostCullBeginCameraRendering_m8C7F421728438AB4A12BF864206727A08A2C30D6 (void);
extern void InstanceCullingBatcher_RegisterBatchMeshes_m59D3A05E2598309513C7DD095CD30F64C33C6214 (void);
extern void InstanceCullingBatcher_RegisterBatchMaterials_m14CAC0F48DECBDF0DFAF932DEB94E8522E3F9BD7 (void);
extern void InstanceCullingBatcher_SchedulePackedMaterialCacheUpdate_m22B6C778DE0258BCEA6BFBB9F9278637010B5A0C (void);
extern void InstanceCullingBatcher_BuildBatch_m425E597AED2AEC32D3E4FD0A9458F29695EABF9B (void);
extern void InstanceCullingBatcher_InstanceOccludersUpdated_mB9D7CECE86473174B52A1D76E73DC546738C9A44 (void);
extern void InstanceCullingBatcher_UpdateFrame_mD1B5D19FB7AB428A0E766E54E1E58821EF647457 (void);
extern void InstanceCullingBatcher_GetCompactedVisibilityMasks_m56336BD173368549DEDC4D743382E3311D29B144 (void);
extern void InstanceCullingBatcher_OnEndContextRendering_mB4D41FEA107B3D5ECF9AF02F3937726021141A26 (void);
extern void InstanceCullingBatcher_OnBeginCameraRendering_mE389E1056E80885B9816E9231F9C3C2A42B72221 (void);
extern void InstanceCullingBatcher_OnEndCameraRendering_m189059E45FB5732F03BAFABF3421CDD68D81DEB1 (void);
extern void GPUInstanceComponentDesc__ctor_m3D6BFA5A8BBF78939717DABD22CDEDE234780EE0 (void);
extern void GPUInstanceDataBuffer_NextVersion_m87213EAF8B57C72440D656BD00E246106CD9404F (void);
extern void GPUInstanceDataBuffer_get_valid_mBB8F7F2B22AA1450AD0944A8364F19025D0687F1 (void);
extern void GPUInstanceDataBuffer_CPUInstanceToGPUInstance_m1177E7984777339744077A74E281D84B17704592 (void);
extern void GPUInstanceDataBuffer_GetPropertyIndex_mF39E38B5B13B5BF4E45934C274E076B4401656DA (void);
extern void GPUInstanceDataBuffer_GetGpuAddress_m478AE68E3BE3FC5076A3D8C1D9F2CC20E11FD7EF (void);
extern void GPUInstanceDataBuffer_GetGpuAddress_mCDCEF5E738A3FE9E217D94ECA43A2AE5A6380225 (void);
extern void GPUInstanceDataBuffer_CPUInstanceToGPUInstance_m3C4863A5F6AC91EA128DE5FDD296B3159CCE218C (void);
extern void GPUInstanceDataBuffer_GPUInstanceToCPUInstance_m6861C120BA15E1AA9BB2116DDBF09CC4A68BC039 (void);
extern void GPUInstanceDataBuffer_CPUInstanceArrayToGPUInstanceArray_mE3CD8040A3236B9CA56E2CB69B90C68CB1BE42A3 (void);
extern void GPUInstanceDataBuffer_Dispose_m338824ADC36E89D59E8D1EC451F00A78337A4165 (void);
extern void GPUInstanceDataBuffer_AsReadOnly_m7E7EAB66B500E1CAA7AEB2C2F7CAEBE40CCE729F (void);
extern void GPUInstanceDataBuffer__ctor_m62C97070C67C69A70905B44F586178FEFB54C95E (void);
extern void ReadOnly__ctor_mF532002BB9D890FF1F0582B28EE81FFF81C4843D (void);
extern void ReadOnly_CPUInstanceToGPUInstance_m01CDC3239F33A9D1A861D1816930C4A7E71440E5 (void);
extern void ReadOnly_CPUInstanceArrayToGPUInstanceArray_m246175FBDE0C68B01CF4B8B0F73087368CA7ACF3 (void);
extern void ConvertCPUInstancesToGPUInstancesJob_Execute_m16B5AACD9BEEFFA2046C2E1999F8C4AA7FF2B1D9 (void);
extern void GPUInstanceDataBufferBuilder_CreateMetadataValue_m50A13411B4799C2C197B9C4EFD3EED2BB4C25036 (void);
extern void GPUInstanceDataBufferBuilder_AddComponent_m26713FB7D74F3F47052241EEDE3D95D1502C35E8 (void);
extern void GPUInstanceDataBufferBuilder_Build_mDEF5AC49115B5D3CD195C5802389B93AE1C25C8C (void);
extern void GPUInstanceDataBufferBuilder_Dispose_m1869839D0122EAE7FE7A7F9FE5356CCDE50D6636 (void);
extern void GPUInstanceDataBufferUploader__ctor_mAD57A016FC2E0940D4A469F418E161A1DCA8CC8C (void);
extern void GPUInstanceDataBufferUploader_GetUploadBufferPtr_m0CB3210A88F79E17329FDE29AD57C33C975D55DD (void);
extern void GPUInstanceDataBufferUploader_GetUIntPerInstance_mE171D8F73FE5CDE5D3F83C730F31877B8649A59C (void);
extern void GPUInstanceDataBufferUploader_GetParamUIntOffset_mF231C740B0554E7A82228DAEA5FE5F6BEBD978FD (void);
extern void GPUInstanceDataBufferUploader_AllocateUploadHandles_mAFB40A36A5FDAF5C560EB04B822012E57219A607 (void);
extern void GPUInstanceDataBufferUploader_SubmitToGpu_m352C6DB1B32BBB2D141C76ECCB19F8FF55940105 (void);
extern void GPUInstanceDataBufferUploader_SubmitToGpu_m6037322A91258909D757C712390F08395A36CF9C (void);
extern void GPUInstanceDataBufferUploader_Dispose_m0D8CBA64EB57BF6554F97E74B238F0A159B574B5 (void);
extern void UploadKernelIDs__cctor_m5DABBDCC0EDE576865CC927D7810EE469972169A (void);
extern void GPUResources_LoadShaders_m9ACB6FC1CAC3C07D223AB04073FEFEBCD55F5CDA (void);
extern void GPUResources_CreateResources_m07CBEC1195D765927E59B74DDFF7402B3608E22D (void);
extern void GPUResources_Dispose_m2D71F420E19EDC6E791D5FD0BDAA81501C6017E6 (void);
extern void WriteInstanceDataParameterJob_Execute_mFBD8B3E368892E290A864D47C75EC3958286BC25 (void);
extern void GPUInstanceDataBufferGrower__ctor_m2555453CA56595D3963837F7226E4F85D11379B1 (void);
extern void GPUInstanceDataBufferGrower_SubmitToGpu_mF6B94F8512B09B0E57404C840A3C22D621C8EE9A (void);
extern void GPUInstanceDataBufferGrower_Dispose_m24A1DE3F1FE59498294C9845783B541018F54BE3 (void);
extern void CopyInstancesKernelIDs__cctor_mE2A43876DE96902483CD0D2EFE3D31E698AA4715 (void);
extern void GPUResources_LoadShaders_m4C5A24A42FDCD3D04EBB7C2342E23D14748CBB89 (void);
extern void GPUResources_CreateResources_mD345D83E87BDDAF07F3A8628B9E1F1860BEC40C3 (void);
extern void GPUResources_Dispose_mBB023C03E82397C5CF141105338158EEDA6841AF (void);
extern void InstanceHandle_get_index_mD1303DDB5E62977480B92E3DCECC534B1465CB94 (void);
extern void InstanceHandle_set_index_mC56210BD1B26D7B86BCD9BF2CE5341188DC1A234 (void);
extern void InstanceHandle_get_instanceIndex_mD7BAC819F382904784AC46D1F6A631B4A21CA8B5 (void);
extern void InstanceHandle_get_type_mAC674106B77C6008F547FBAB2FE02F263D3FA40C (void);
extern void InstanceHandle_get_valid_m5629806E66255D75C3B1ED5826B0DA6D13B9E0CE (void);
extern void InstanceHandle_Create_mDF57F601CCEF1ED2DBFD880416FE0B5EB625DB2B (void);
extern void InstanceHandle_FromInt_m501BC299814E873C1040C63575F9391327992272 (void);
extern void InstanceHandle_Equals_mB7F950B845D19368217960B6FE7DBEA51B013C9E (void);
extern void InstanceHandle_CompareTo_mC8763D64CE799F220739F8D3880F0725ADA9A93D (void);
extern void InstanceHandle_GetHashCode_m256554043D1CEC35A1670DA2BDD39B453A4985D9 (void);
extern void InstanceHandle__cctor_m482B79BDF36DE1A1A2BDB9C9D97F597DE7ED7F77 (void);
extern void SharedInstanceHandle_get_index_mA8EF25F39F6B747A2984740DC8A6A7F7F3B31606 (void);
extern void SharedInstanceHandle_set_index_mBD1FDAC50BC4D743EF40A7195E2DF1E3B4BC1840 (void);
extern void SharedInstanceHandle_get_valid_m723EEF4B52F015C0A6EC4BB621966C396311C865 (void);
extern void SharedInstanceHandle_Equals_m8318CF0264558ADD64222B3A2593EACCED56BFE0 (void);
extern void SharedInstanceHandle_CompareTo_m31419B958041797369105E6105847547F39F98C4 (void);
extern void SharedInstanceHandle_GetHashCode_m5B97E179A78BD59969291F66E286E00873FC120D (void);
extern void SharedInstanceHandle__cctor_m1F42E7568683FEA4B78D0F18E1F5707420D6644E (void);
extern void GPUInstanceIndex_get_index_m0EDBD4FD5FC090990E2A24DCEBB5346B260C919D (void);
extern void GPUInstanceIndex_set_index_m24EF3293A5E4CA20F4186F53B459500CDAE40687 (void);
extern void GPUInstanceIndex_get_valid_m5DBD00DEF2B390C4DB2E0B41686B6A09A064D27D (void);
extern void GPUInstanceIndex_Equals_mEF7254FCD24AAD9A15FD3D1555306A5F1C908D23 (void);
extern void GPUInstanceIndex_CompareTo_m4CEFE1A49A0D625417B0123D3CA3AE385B785DCF (void);
extern void GPUInstanceIndex_GetHashCode_mD3CFF7872E06CD6F455F81B019F03672C213D970 (void);
extern void GPUInstanceIndex__cctor_mBE79D3EFAD002DBED18492AEDE53BD38FCA142E7 (void);
extern void InstanceAllocator_get_length_m762D1C624FF0B3D9F1DFED48EEA65465932C49DC (void);
extern void InstanceAllocator_set_length_m02BBC305E32676069A1D7855C5FF776FD7F24172 (void);
extern void InstanceAllocator_get_valid_m86EC99BF39D782407913E9BAC1DB2D7CBA1FD797 (void);
extern void InstanceAllocator_Initialize_m299908C0326706B4BF44DDEC7CDA3D1074347F01 (void);
extern void InstanceAllocator_Dispose_mB710FC50CF870DBD3B226AE4B28057364D67237A (void);
extern void InstanceAllocator_AllocateInstance_m846D1D4EA89D654F8AD4272579E3A7591FE155AB (void);
extern void InstanceAllocator_FreeInstance_m4017808CB3B95F6FC5079EFDCC7CA13217C42E3C (void);
extern void InstanceAllocator_GetNumAllocated_mC907C85B73DDEAFAE3606B4099F5C7E808DA8C18 (void);
extern void InstanceAllocators_Initialize_m1C63A61426D12441A3C4EABEC29B392FC93E90D9 (void);
extern void InstanceAllocators_Dispose_m0B2675D6FA6E95D9B9CC4617A94F408D9E80F5F7 (void);
extern void InstanceAllocators_GetInstanceAllocator_mF5B156CDAEDEEC2849590A544A5D99E691D0E37F (void);
extern void InstanceAllocators_GetInstanceHandlesLength_mBFEDAC598685E4B38B1E67DECA4E75B8E345ACE0 (void);
extern void InstanceAllocators_GetInstancesLength_m7F121F6138F0713B01D5379DCA36255DF631D1C3 (void);
extern void InstanceAllocators_AllocateInstance_m1BA734BD0D1B20F166DEBCCEE8AC5B2B0911D9A4 (void);
extern void InstanceAllocators_FreeInstance_m0EBB63A12A593C580C54770B70A6032C55237AF6 (void);
extern void InstanceAllocators_AllocateSharedInstance_mD61A96D871FD88F2826B307B6BBF976223E075CF (void);
extern void InstanceAllocators_FreeSharedInstance_m0B4E4FDCEA191C4829065D705C241F8667B658D5 (void);
extern void CPUInstanceData_get_instancesLength_mEF0DF28231C26F0C2E7EDA6A7115A51F51E5109C (void);
extern void CPUInstanceData_set_instancesLength_mA37EF6CFF372799E7F182362873F19898021B186 (void);
extern void CPUInstanceData_get_instancesCapacity_mB1E847621B15F7559FC491B24F54ABA253A73249 (void);
extern void CPUInstanceData_set_instancesCapacity_m2BC970C6B7AA237310D79F3994737B40F962EE1D (void);
extern void CPUInstanceData_get_handlesLength_mA9DC002A27BBD94B09B5944B84C6F47C30692F48 (void);
extern void CPUInstanceData_Initialize_mCB28B3C43BE61390B76A1B9C1C79BBFA33A33FD0 (void);
extern void CPUInstanceData_Dispose_mA6CD2735982440ECA65880116C2D214E97ADFC20 (void);
extern void CPUInstanceData_Grow_m54A454E32FB7C164E757F05A453DB71BBF6E1907 (void);
extern void CPUInstanceData_AddUnsafe_m1F5C8C517A8258670937584F1DD66E9D47724BC2 (void);
extern void CPUInstanceData_InstanceToIndex_m9D4701DC598D9046898239AF94F4E87F6A0F14EE (void);
extern void CPUInstanceData_IndexToInstance_m3C95DBA9646291FACE0234AC811183928C232321 (void);
extern void CPUInstanceData_IsValidInstance_m5B3FF0F8456CF25EE209E68A230D594BB5DB36A3 (void);
extern void CPUInstanceData_IsFreeInstanceHandle_mEEBB421BA3391592FEF206095FBBC6A4BF6D9589 (void);
extern void CPUInstanceData_IsValidIndex_m4BE52A79DE124AA4872297A76A0FEA1D114CA010 (void);
extern void CPUInstanceData_GetFreeInstancesCount_m1F0B5BDAFE8DC1DB434FC5682705494F3EDFE10C (void);
extern void CPUInstanceData_EnsureFreeInstances_mB7036C8793D1CB89C1C371A605581CBB4E177484 (void);
extern void CPUInstanceData_AddNoGrow_mEB9B4B4BCDB7C718DA14898D903B5E0D45DE0455 (void);
extern void CPUInstanceData_Add_m0736A5D9F85B1EC37E061411CC00335DFCC44D5A (void);
extern void CPUInstanceData_Remove_m599064EAE344F332CFE4CD00C9714E72623D6877 (void);
extern void CPUInstanceData_Set_m47C0F837DC4AFF38FE4A50E426FFE05969B3FC19 (void);
extern void CPUInstanceData_SetDefault_mDE2B13667675847C626111DE77EDE63FE25B5E42 (void);
extern void CPUInstanceData_Get_SharedInstance_mE5ED4915B38984690F4AB5089634500FBCB6C3CB (void);
extern void CPUInstanceData_Get_LocalToWorldIsFlipped_m19881EEED025A0FC45721F63DFCCC1C462F93107 (void);
extern void CPUInstanceData_Get_WorldAABB_mF9F14AE6B71FECD3BF51252AB06781687C9531EB (void);
extern void CPUInstanceData_Get_TetrahedronCacheIndex_m7D09D9EBD350591CCD9A948AF1F55DA98F85D1B6 (void);
extern void CPUInstanceData_Get_WorldBounds_m59B12DD936A814F2E056E962A4889CD3FD473417 (void);
extern void CPUInstanceData_Get_MovedInCurrentFrame_m58A543967F67149E003AD3FE6FE4C45F9E87C869 (void);
extern void CPUInstanceData_Get_MovedInPreviousFrame_m9A0B9988AF8004828D13C96F89981F3C6AB15C29 (void);
extern void CPUInstanceData_Get_VisibleInPreviousFrame_m3E67A47CD7703E4345BEE5A7068855FE6A589BB5 (void);
extern void CPUInstanceData_Set_SharedInstance_mECEE018E02FA2F6502B59197A9AEF35FA2D35081 (void);
extern void CPUInstanceData_Set_LocalToWorldIsFlipped_mB2B243184FD3C74C54A5D8EC74542BC0E019EF29 (void);
extern void CPUInstanceData_Set_WorldAABB_m56D85CE12B7C589B053C471E803090DF9CA37073 (void);
extern void CPUInstanceData_Set_TetrahedronCacheIndex_mAC10B658EB1AB6EF405BC32A6DE8E88F7AA45AD5 (void);
extern void CPUInstanceData_Set_MovedInCurrentFrame_mB2081F967C2A8B6E6AC80F2E91DB3FD70851346F (void);
extern void CPUInstanceData_Set_MovedInPreviousFrame_m096AF4BED24D559F13122047DF89E56C3C431B4D (void);
extern void CPUInstanceData_Set_VisibleInPreviousFrame_m53D87829915906D1DD8E19F1F8D51631E9C02620 (void);
extern void CPUInstanceData_AsReadOnly_m90E90427C88E3F72D123C44F0E7957BACDDA8306 (void);
extern void ReadOnly_get_handlesLength_m50556F07B2F7144758202F76E202ED795E715DE9 (void);
extern void ReadOnly_get_instancesLength_m0E85E521D7DB61518C6988B08646386F20A17307 (void);
extern void ReadOnly__ctor_mFAEAA65432C0711F606AC98FD2320032B60A3309 (void);
extern void ReadOnly_InstanceToIndex_mA64D7447F6466C660DE83765B1B42673D922CCE2 (void);
extern void ReadOnly_IndexToInstance_m5BB2CA43E37DBA390AB7F29E6B5357A05226EE1B (void);
extern void ReadOnly_IsValidInstance_m560D63CCD78D3661349A6134A30A29EFEDD3C30A (void);
extern void ReadOnly_IsValidIndex_m2E036F94E4DFD41DF1CCA4148915594457CF0A9B (void);
extern void CPUSharedInstanceData_get_instancesLength_m6C9D2E7F861FF43E23759E8816653646F41038B6 (void);
extern void CPUSharedInstanceData_set_instancesLength_m62AE1E8890A80D570FD3D11CD9E7C76B9D19124F (void);
extern void CPUSharedInstanceData_get_instancesCapacity_mE40EF07D597175079E8DB6639E444A8346BD5C67 (void);
extern void CPUSharedInstanceData_set_instancesCapacity_mEBAB461BBCD3CD8127252B3F6156431A24943A69 (void);
extern void CPUSharedInstanceData_get_handlesLength_mF238FD08383695F2D7F707F2FA87279FE939CE61 (void);
extern void CPUSharedInstanceData_Initialize_m0D70CFA6688D2B1A165CDB20AB1760CBB4604A6E (void);
extern void CPUSharedInstanceData_Dispose_m37AA8B81C3E42B81EC8E948B3F3DF9197B75E48C (void);
extern void CPUSharedInstanceData_Grow_m0AED29A47C913CD92A9EA6281FABBBE76C4C26F3 (void);
extern void CPUSharedInstanceData_AddUnsafe_mD66018426B29B8FC77CBA218E04D6470D3A08B69 (void);
extern void CPUSharedInstanceData_SharedInstanceToIndex_m8D1697AC229D8D0D83FA702F6F440132FA123282 (void);
extern void CPUSharedInstanceData_IndexToSharedInstance_m7E58052D57FC86E8F4C0571D1213F2D4774BD323 (void);
extern void CPUSharedInstanceData_InstanceToIndex_mA58DA1C16D52FECF5ED6238B168203837D75F87C (void);
extern void CPUSharedInstanceData_IsValidInstance_m33E12C093A6F0194B4D388782B15C2DD64616384 (void);
extern void CPUSharedInstanceData_IsFreeInstanceHandle_m972EDE9AF42DAADE66DAA3B1E1A8F8A85B594BFA (void);
extern void CPUSharedInstanceData_IsValidIndex_m24116C260518DD680C3CDBD5BB1797B7764DDBCD (void);
extern void CPUSharedInstanceData_GetFreeInstancesCount_mC18CC336EB3ED96DA46A39019C480485B58A26F5 (void);
extern void CPUSharedInstanceData_EnsureFreeInstances_m9276B1E0AE6DE0DFAE5F6FA4AFFB7DD9E6939CB0 (void);
extern void CPUSharedInstanceData_AddNoGrow_mAF3FBC643ABC12255E182EE8F8D26024AD8BBACD (void);
extern void CPUSharedInstanceData_Add_m3AFDBA31C2E77E106BD970496C5F0EFBDCB3FCD3 (void);
extern void CPUSharedInstanceData_Remove_m1BA13D76461FC093E7667EA3746FC4A6C3E99EF9 (void);
extern void CPUSharedInstanceData_Get_RendererGroupID_m09F08F1763DD649A37D88B169CA50B2BD4F71768 (void);
extern void CPUSharedInstanceData_Get_MeshID_m70DD07FFBE12B43BA2546547FD26BC268E28EF43 (void);
extern void CPUSharedInstanceData_Get_LocalAABB_mB7D5A9EA9CD43BBEE3E938D31500A518FD3C02D7 (void);
extern void CPUSharedInstanceData_Get_Flags_m4F22F6B4DB27966ED5F633612B135EDCD274E341 (void);
extern void CPUSharedInstanceData_Get_LODGroupAndMask_m4A750431421271D4D2960E84D4B3AD83F96A8A42 (void);
extern void CPUSharedInstanceData_Get_GameObjectLayer_m75D2D7344A04236BFAE513142B29AB6CC8E7CB4D (void);
extern void CPUSharedInstanceData_Get_RefCount_mE216F6749BB1B06E795C52C2433EE85E6C57DC01 (void);
extern void CPUSharedInstanceData_Get_MaterialIDs_mCE6EC8119990B7324D473B6DFB27B91A076404FC (void);
extern void CPUSharedInstanceData_Set_RendererGroupID_m06EFE6568E7FABED464A0A091DB7D8DE05FC8719 (void);
extern void CPUSharedInstanceData_Set_MeshID_m97990780D2C7B92B6A146C6FB243D08D2674EAA9 (void);
extern void CPUSharedInstanceData_Set_LocalAABB_mFA0C8491B2FCD9E1F5BB25FF598C7520D689D08C (void);
extern void CPUSharedInstanceData_Set_Flags_m9C1CE3AC11B09B3B2C9F65422B2FD31D23C49F4D (void);
extern void CPUSharedInstanceData_Set_LODGroupAndMask_mD27421C024B1779830BEFA1274063B72D7C182CA (void);
extern void CPUSharedInstanceData_Set_GameObjectLayer_m97AEE383F753133901141CCD0040B7D2C2E41EEC (void);
extern void CPUSharedInstanceData_Set_RefCount_m7C5AF143DDC85304B2840A688BB2DD095A03B7CC (void);
extern void CPUSharedInstanceData_Set_MaterialIDs_m3C8A0B397DBC9FEFF9661015CC32232A4910D812 (void);
extern void CPUSharedInstanceData_Set_m5E3312BE2456604D50D30AA09F973F7534EF7197 (void);
extern void CPUSharedInstanceData_SetDefault_m9241E530C50F996744FC4691DCD87C0E30602237 (void);
extern void CPUSharedInstanceData_AsReadOnly_mC186E7142A7E53BFE6D90BB0AB1841600BE7CBF8 (void);
extern void ReadOnly_get_handlesLength_mEAE03E557C81624E1F2CD280610A867E764EE456 (void);
extern void ReadOnly_get_instancesLength_m82B5F22A5EE9DF8AB94546D3B739C1EB36AD38D6 (void);
extern void ReadOnly__ctor_mDBC17F900B61DCF328F4BE2CDF0464E97F44614D (void);
extern void ReadOnly_SharedInstanceToIndex_m7CD21D39C3285388B428D1CDCAA9AFB10A0178B4 (void);
extern void ReadOnly_IndexToSharedInstance_mC97CAB3F6DB3C92BCB528F85F5B9E42728DE3389 (void);
extern void ReadOnly_IsValidSharedInstance_mC2056D855CF0ED6FA050810CE1035680DC453A1A (void);
extern void ReadOnly_IsValidIndex_mA6B2DAE40129D5F001698A34D413EE0CEB24E61D (void);
extern void ReadOnly_InstanceToIndex_mE76C9EBF3ACCFDD5D73B3A99925CAE88DE2DDBE8 (void);
extern void SmallIntegerArray_get_Valid_m717ECD9B73FD5A373F40B0A623CB2C8E188012C0 (void);
extern void SmallIntegerArray_set_Valid_mF9C378DA26AB93E1AB38CCD7371CA23ABB6779D3 (void);
extern void SmallIntegerArray__ctor_mD7860879B9A093BE8CD0CFD815D125FDB746090E (void);
extern void SmallIntegerArray_get_Item_m81A85EC298981F31BB405CA783D90BB96697C2DE (void);
extern void SmallIntegerArray_set_Item_mFEF4007D7D6DA125990D22E32B34ADCBF7056EA2 (void);
extern void SmallIntegerArray_Dispose_m11D070ED0582D69C7F631CAF8C44F93183D73579 (void);
extern void EditorInstanceDataArrays_Initialize_m9893832043B3485AD59127B93D0872405CF77AFC (void);
extern void EditorInstanceDataArrays_Dispose_m16E99865063B2150E7BEFCE3BAE55831D9532B3B (void);
extern void EditorInstanceDataArrays_Grow_m57AFD6C22AA320DA1E98981C894EEB1F96C0B041 (void);
extern void EditorInstanceDataArrays_Remove_mC3970E725DCDCEA7B636C2DCB4732C432787AE35 (void);
extern void EditorInstanceDataArrays_SetDefault_mBB385CE94C9E46ADADB174CEFB393C2A19245724 (void);
extern void ReadOnly__ctor_m2029FDC41CDEC78F717316642DB33534904EC6E2 (void);
extern void PackedMatrix_FromMatrix4x4_mD8BF568A72FAFAA50614FD1F9DA6A7F257CB3E77 (void);
extern void PackedMatrix_FromFloat4x4_m2EEC3F97BB2E382DD01F31AE83B11D73F2579A9D (void);
extern void InstanceDataSystem_get_hasBoundingSpheres_mE95EB4398294EC395CE2A5A16F5D86EF8D86AFBF (void);
extern void InstanceDataSystem_get_instanceData_mCB763544E2728F9E48CEF5CB5284044D1C61CDF1 (void);
extern void InstanceDataSystem_get_sharedInstanceData_m917A6760CCBBEBB27FECC0035926431ED41D1BDF (void);
extern void InstanceDataSystem_get_aliveInstances_m24552E5DB0DD7022BEBC44E99BAD4E5B91C3FD89 (void);
extern void InstanceDataSystem__ctor_m5F7B568C5D6BF6507682A782B497C5DF9AF288E7 (void);
extern void InstanceDataSystem_Dispose_mD8F0ABE86EC7824BD24020C924702A073024A5FC (void);
extern void InstanceDataSystem_GetMaxInstancesOfType_mD0C2B5D78BAA3DF5116E66D663F0AB88A1267928 (void);
extern void InstanceDataSystem_GetAliveInstancesOfType_mACA5AF484D118330CACC8C0D919BAFDDA30D43FA (void);
extern void InstanceDataSystem_EnsureIndexQueueBufferCapacity_mEC63AEE12228511E02036542B749925C591E4190 (void);
extern void InstanceDataSystem_EnsureProbeBuffersCapacity_m5C8E2190B2C827606936372E985463BC746A65D2 (void);
extern void InstanceDataSystem_EnsureTransformBuffersCapacity_m8101997E233AADA2DFCA0C139B74927BAD65C221 (void);
extern void InstanceDataSystem_ScheduleInterpolateProbesAndUpdateTetrahedronCache_m138513395BC490C04056D11A8AE9A4017E69092D (void);
extern void InstanceDataSystem_DispatchProbeUpdateCommand_m89CB692F574017CDA489FFDC50B7D021F9BE624A (void);
extern void InstanceDataSystem_DispatchMotionUpdateCommand_m6CAB421EB2033825CFE9FF8C0A3F13FF6849BD53 (void);
extern void InstanceDataSystem_DispatchTransformUpdateCommand_m408F702045F0792E5FAB3D30089984D2AC68492F (void);
extern void InstanceDataSystem_DispatchWindDataCopyHistoryCommand_m3CE9A16E6EDD1B23B2A1844EB0C8FE63297FCF44 (void);
extern void InstanceDataSystem_UpdateInstanceMotionsData_m81BC58CE2698369C68C0E7AC1543A7AE4CD871FA (void);
extern void InstanceDataSystem_UpdateInstanceTransformsData_mA9273FAEEACA70AB121D953179312125AD328FCC (void);
extern void InstanceDataSystem_UpdateInstanceProbesData_m1CD19D71D15B03FC82F0D5434D43872B6482AEE2 (void);
extern void InstanceDataSystem_UpdateInstanceWindDataHistory_m9E2E361D86A93AEC4256E9E45E6FF8C25DDEF97E (void);
extern void InstanceDataSystem_ReallocateAndGetInstances_mD8B36795100226FED3AFE497FC9DED84FF4A6476 (void);
extern void InstanceDataSystem_FreeRendererGroupInstances_mDB237F9840CA6B5121A30D5238DEFCBBE2DC7B78 (void);
extern void InstanceDataSystem_FreeInstances_m1FCCBE915D86469CC20E2C01AE6FB341734F2AF9 (void);
extern void InstanceDataSystem_ScheduleUpdateInstanceDataJob_mEB4A7B9A770F619108268D0B11ABE99DCEFAC479 (void);
extern void InstanceDataSystem_UpdateAllInstanceProbes_m2544131305465C5C6DE3956ACE326DC2B9DB05AF (void);
extern void InstanceDataSystem_InitializeInstanceTransforms_mF2F8A8EEDBFFA25647574740B190DD2899B5B0F8 (void);
extern void InstanceDataSystem_UpdateInstanceTransforms_m7A0057B405E3D12CFF3EB78FCB3BE1D1593A0E43 (void);
extern void InstanceDataSystem_UpdateInstanceMotions_mDCDA88917F5E5B6CC8D8FCFB50744E529C11CDFF (void);
extern void InstanceDataSystem_ScheduleQueryRendererGroupInstancesJob_m4C0025CA86226F2D5A23C721CA42E7E8DF4C30B4 (void);
extern void InstanceDataSystem_ScheduleQueryRendererGroupInstancesJob_m04F92151A520DC0AF8F1FB4B7AFA040C0F625D0E (void);
extern void InstanceDataSystem_ScheduleQueryRendererGroupInstancesJob_mB490AA73553E991D855BFC67D1622FE3AC8C098E (void);
extern void InstanceDataSystem_ScheduleQuerySortedMeshInstancesJob_m04115ECA07C31067F98B727EE322A1786C70175C (void);
extern void InstanceDataSystem_ScheduleCollectInstancesLODGroupAndMasksJob_m9A29F99524770324E8E2896B54E5C08FF4A0979E (void);
extern void InstanceDataSystem_InternalSanityCheckStates_m972099150EFFB0CFB52E22F42C4E216C1B012A9B (void);
extern void InstanceDataSystem_GetVisibleTreeInstances_m215114432B8645A102573A589C21C9925471A451 (void);
extern void InstanceDataSystem_UpdatePerFrameInstanceVisibility_m1C6A42FA01165B8F7D05C4179DD093BE19AA4512 (void);
extern void InstanceTransformUpdateIDs__cctor_m22E50C74A91C8F98F112D7D4E8AD2D3CA77829C5 (void);
extern void InstanceWindDataUpdateIDs__cctor_mDCE66DBD25DE0B17A7C41D329C4006A0AC407C09 (void);
extern void QueryRendererGroupInstancesCountJob_Execute_m78EBD1A4D27FC820DB994D726466672AE6698114 (void);
extern void ComputeInstancesOffsetAndResizeInstancesArrayJob_Execute_m7278A70AAF09ACD7DE06A58BBE53617ED59B3178 (void);
extern void QueryRendererGroupInstancesJob_Execute_m689B8C96E8D3739314972C71A89F43C5DD8DD5EC (void);
extern void QueryRendererGroupInstancesMultiJob_Execute_m3ED5F46C8E2BBCFD2302EBF30F55D07CB4292593 (void);
extern void QuerySortedMeshInstancesJob_Execute_m0B30D3DBCCB8FE5833D677F735387912A448CCDF (void);
extern void CalculateInterpolatedLightAndOcclusionProbesBatchJob_Execute_mB29926232C46662441F96B9405E9BE4D3A84109B (void);
extern void ScatterTetrahedronCacheIndicesJob_Execute_m157E3BAA3AA5106CD5629211DB4DF10C941B0942 (void);
extern void TransformUpdateJob_Execute_m0C99BC8B89AD60C1B495E471F9F02FBEEF7F8342 (void);
extern void ProbesUpdateJob_Execute_mFACBB95DE46DB9C879C2EE745CE37C4AEFB69AD6 (void);
extern void MotionUpdateJob_Execute_m266055F93AABA39FD53E7EDBD6BAFE55F586891B (void);
extern void ReallocateInstancesJob_Execute_m8E05DC4EC3877696439237F2B9485F5218445828 (void);
extern void FreeInstancesJob_Execute_mAED5669D78B3996A3298A55A6FF8625F90E6BADF (void);
extern void FreeRendererGroupInstancesJob_Execute_m475670DFB6FB0B8F6EFD0FA9ACD4CD17EFF326BD (void);
extern void UpdateRendererInstancesJob_Execute_m93597D2184F6460DD4610AD8E47868B1BA87A11C (void);
extern void CollectInstancesLODGroupsAndMasksJob_Execute_m2EFF9AB4F3476E491E51143F7F60F79020A0F780 (void);
extern void GetVisibleNonProcessedTreeInstancesJob_Execute_mEFBAD1E57760EF2F4C2A4F4E6F6F40E6261C284A (void);
extern void UpdateCompactedInstanceVisibilityJob_Execute_mDFAB7C16E38743439A74CCA9AA310B383D2BA55B (void);
extern void InstanceTypeInfo__cctor_m3B458B3343FBC2FD6C40D4FB48ED7A275F513140 (void);
extern void InstanceTypeInfo_InitParentTypes_m871A556C0C4609293ABEEBF110E20DD63FCD084C (void);
extern void InstanceTypeInfo_InitChildTypes_m172E3471BA713B1205C2B5BD08FC4E1D1518D41E (void);
extern void InstanceTypeInfo_GetMaxChildTypeRecursively_m145FABD376E475D616CC58209E97D94E586BC247 (void);
extern void InstanceTypeInfo_FlattenChildInstanceTypes_m815D9E228DD3FF4C86C5A233A038D551FBC28AC8 (void);
extern void InstanceTypeInfo_ValidateTypeRelationsAreCorrectlySorted_mD653B8D9D7F845BE0747448F358E441ADBB6D893 (void);
extern void InstanceTypeInfo_GetParentType_m4EFCC55DA43E58978C6A983D91BCB6FAAF147529 (void);
extern void InstanceTypeInfo_GetChildTypes_mA2B041904C54BC841991C707960DEF5842EA5093 (void);
extern void InstanceNumInfo_InitDefault_m8CC8A9E8EBF0EF16311F391E0312C46A7219A920 (void);
extern void InstanceNumInfo__ctor_m0C64766A7024C367CB84BA96F01861E951351650 (void);
extern void InstanceNumInfo__ctor_mCFED34B4FC73F15366339611E22502A8366B016C (void);
extern void InstanceNumInfo_GetInstanceNum_m42CC8341EB7A73444DB20B9B64ACF5377CA7CE19 (void);
extern void InstanceNumInfo_GetInstanceNumIncludingChildren_m45CB40CE2452143C10B4FFC0F05FA7751E416368 (void);
extern void InstanceNumInfo_GetTotalInstanceNum_m55D6043571FB3440889F98A7D638F6A43D060982 (void);
extern void OccluderDerivedData_FromParameters_mB285C6B3E3FBFB06A8E38D196D06D45FE722D88D (void);
extern void OccluderHandles_IsValid_m9FF256155930C1B40D4195C263AF38FCEB1B7904 (void);
extern void OccluderHandles_UseForOcclusionTest_m8E9F3CDFD218EC3C5A70DA375F156DC75CB2CAAD (void);
extern void OccluderHandles_UseForOccluderUpdate_m4376DEB9151FDF5678FDFE2ED525A3B3AC31AF03 (void);
extern void IndirectBufferAllocInfo_IsEmpty_m47785BE361D9B989BE0455AD1884831AB99E4009 (void);
extern void IndirectBufferAllocInfo_IsWithinLimits_mE8807A3A2DDE4F3E3E9091E2441D847F4BFC4067 (void);
extern void IndirectBufferAllocInfo_GetExtraDrawInfoSlotIndex_m2EAD4191867631265610C43212C34A56A2DAD969 (void);
extern void IndirectBufferContext__ctor_mC7F33D8E6E9DECDB5A167C9CFD68F1CBB6E7D7F5 (void);
extern void IndirectBufferContext_Matches_m40E2A7974D4B205D4BB770D4A68635D99FE4C721 (void);
extern void OccluderContext_get_subviewCount_m49FDF73077D5C9F1789BA0C35A39A7F78FCBF414 (void);
extern void OccluderContext_IsSubviewValid_m88BE2A076AC851E9D11AB02B30ECE80A7E4D6BE0 (void);
extern void OccluderContext_get_depthBufferSizeInOccluderPixels_mF734AB99EBF484188554B86CB2E07048E6138C36 (void);
extern void OccluderContext_Dispose_mF02789AFBB76CD0F4491CDD8A83BCD15938F22D8 (void);
extern void OccluderContext_UpdateMipBounds_m71B416ED1B6D8827B30F8B1C1E69870328E6FCD9 (void);
extern void OccluderContext_AllocateTexturesIfNecessary_m4F0340120C018852B2A697194F87A6C539D6C127 (void);
extern void OccluderContext_SetKeyword_m57CB9C813FA45672B4E4EAD297757E2C427EE0EE (void);
extern void OccluderContext_SetupFarDepthPyramidConstants_m69DC95369344843C149515C9A2A9312DD0E99571 (void);
extern void OccluderContext_CreateFarDepthPyramid_mAE9A7D75C802A5AB3F91ED35C2BF61DA52C0AB0F (void);
extern void OccluderContext_Import_m59CDBBE79F1A96B17BE009D5D561E35D69DA19D7 (void);
extern void OccluderContext_PrepareOccluders_m558D900C293C248A3CEE8FE6640C98873039DF1D (void);
extern void OccluderContext_GetDebugOutput_m0B03B350C81D50A7DA8CEB969E70AF0F782675C5 (void);
extern void ShaderIDs__cctor_mAF5448F8A3480811300797984917EC0136A2EEAE (void);
extern void InstanceOcclusionTestSubviewSettings_FromSpan_m23AA5216F285965B59FD98CCF986ABB9A0C527C5 (void);
extern void IndirectBufferContextHandles_UseForOcclusionTest_m098E1C673230461EF27D4BC76A7186A5DADEEDAB (void);
extern void IndirectBufferContextStorage_get_instanceBuffer_mD965845788AD240262E1C46BB4BA405B73C8EA27 (void);
extern void IndirectBufferContextStorage_get_instanceInfoBuffer_mC14C040CC88B2F11AFE1301EB1FB9C70E396E8E5 (void);
extern void IndirectBufferContextStorage_get_argsBuffer_m81BE13A707FAF6B8F9361AEBCDD9CBA69611C334 (void);
extern void IndirectBufferContextStorage_get_drawInfoBuffer_m0168780ED5CFCB3613F04F2C00561471352A5790 (void);
extern void IndirectBufferContextStorage_get_visibleInstanceBufferHandle_mBD86573F23F6B0F8BF6C46C2996C449E571DC87F (void);
extern void IndirectBufferContextStorage_get_indirectArgsBufferHandle_m7DF465FC2B94D42E1ABE0EE29E0EE4A1C850E925 (void);
extern void IndirectBufferContextStorage_ImportBuffers_mB024C5F1494D76DFEC07CDE6065AC7ACBB35E265 (void);
extern void IndirectBufferContextStorage_get_instanceInfoGlobalArray_m6D87D347531D8E3C00FB0967CCAEE69FED54FC41 (void);
extern void IndirectBufferContextStorage_get_drawInfoGlobalArray_mF90CEAECE8B83E043E462A4495421A574CC257D4 (void);
extern void IndirectBufferContextStorage_get_allocationCounters_mF22684F6D0214AE0755E3DFA8BA047C9E63C70FC (void);
extern void IndirectBufferContextStorage_Init_mE1D9E857917AF5759F2E08D6B008C991CE5558AF (void);
extern void IndirectBufferContextStorage_AllocateInstanceBuffers_m83064D5ABC47473BE4547D8CDB301CC989362AD4 (void);
extern void IndirectBufferContextStorage_FreeInstanceBuffers_mE035A24BD6179866735657D3D74C416AD39C597D (void);
extern void IndirectBufferContextStorage_AllocateDrawBuffers_mCB3267B00E8328A5031AAB859542061D665CE0A0 (void);
extern void IndirectBufferContextStorage_FreeDrawBuffers_mAB74E77AD8B6348D492585D0ACB5AE42C2CEB171 (void);
extern void IndirectBufferContextStorage_Dispose_mD7278C175C4C23BCB9190D68CE8343B543A24302 (void);
extern void IndirectBufferContextStorage_SyncContexts_mB63C2465AF6D851CA68041FA1C12E24F24874A32 (void);
extern void IndirectBufferContextStorage_ResetAllocators_m327B8163AEEAFE360011EAEBFBD80C96C6403467 (void);
extern void IndirectBufferContextStorage_GrowBuffers_mAB6F1A326FB73F38678D592C809333FAE5A25D44 (void);
extern void IndirectBufferContextStorage_ClearContextsAndGrowBuffers_m0B059924A2DB871C3C80B03DAF54A47C8A47E573 (void);
extern void IndirectBufferContextStorage_TryAllocateContext_mC8D472C07C986BDCFC55CF8C4CE324230A38618C (void);
extern void IndirectBufferContextStorage_TryGetContextIndex_m848B7C96E09D5CE2C6D12221D0B78EA4DCE77EF1 (void);
extern void IndirectBufferContextStorage_GetAllocInfoSubArray_m053D08700A1B5D4DB1AAF31B508E33E9FF418A4D (void);
extern void IndirectBufferContextStorage_GetAllocInfo_m9BF31C85DBB94B0D0F1B82AF66DA8493AB1B2595 (void);
extern void IndirectBufferContextStorage_CopyFromStaging_mE99297FD5192A7F628FD7DC704E877280DB03891 (void);
extern void IndirectBufferContextStorage_GetLimits_m2E8DDC54719944794D7342725107396F59D5CF71 (void);
extern void IndirectBufferContextStorage_GetBufferContext_mD76FD0DD95CEE4D7D9B62F81573C4A05A5519941 (void);
extern void IndirectBufferContextStorage_SetBufferContext_mEC8E692A99E595592E12F55F94B7E234A340DC2A (void);
extern void UpdateLODGroupTransformJob_Execute_m10AD64037A0D9FFAB351270A412B4B4342A6B8C5 (void);
extern void AllocateOrGetLODGroupDataInstancesJob_Execute_mF977F85F444872F9F0E507A0DADE7F2550050CA7 (void);
extern void UpdateLODGroupDataJob_Execute_m37868AFCE3AFCC80D46C8156CA2713096FE5EC3D (void);
extern void FreeLODGroupDataJob_Execute_mABCBCD7B1C65E4A56F7F608884D1F9FB4FA1AAF7 (void);
extern void LODGroupDataPool_get_lodGroupDataHash_m62280E732F32C5C35B2DADCD304E46662939F045 (void);
extern void LODGroupDataPool_get_lodGroupCullingData_m9D4FE39BAD4D72923936ABBBEBEEF7F2F3131865 (void);
extern void LODGroupDataPool_get_crossfadedRendererCount_m1851897792114FF4241A4099060D707ECAD45334 (void);
extern void LODGroupDataPool_get_activeLodGroupCount_m97904EE5C95159152B6C0A1ABC068C06B5079CD4 (void);
extern void LODGroupDataPool__ctor_m41A2B2D9392893C14F8F1CC08EDE34AE43CDBC8C (void);
extern void LODGroupDataPool_Dispose_m018568FAFC3BCCE2F577FC92B6A3223CC585AA91 (void);
extern void LODGroupDataPool_UpdateLODGroupTransformData_mA548FB2A357D0A1CF586FBD7B3D04B928BCE005A (void);
extern void LODGroupDataPool_UpdateLODGroupData_mF09A39F868F16124B4F2503B1F725FE54AE7A96B (void);
extern void LODGroupDataPool_FreeLODGroupData_m900936DC26BBC6F1ABF60871DAF69D93FB79C900 (void);
extern void LodGroupShaderIDs__cctor_m317D81DF99F20606D1C93B871FD9CE2083C6C42A (void);
extern void LODGroupRenderingUtils_CalculateFOVHalfAngle_m419B49ED9977DF739E0E1E39573AC58D40C261BB (void);
extern void LODGroupRenderingUtils_CalculateScreenRelativeMetric_m3FBB837700CB21D0CE4207EAF4EC7727E9471BBF (void);
extern void LODGroupRenderingUtils_CalculatePerspectiveDistance_mCAF3705B907D10EF33B1FBC16B63C6E31E9EF722 (void);
extern void LODGroupRenderingUtils_CalculateSqrPerspectiveDistance_m4D8787AC075B2D624D9594A40CF26541FF3261AD (void);
extern void LODGroupRenderingUtils_GetWorldReferencePoint_m52B6D8ACD8B14067173FA1CE5C9DB3BC69142F0A (void);
extern void LODGroupRenderingUtils_GetWorldSpaceScale_m8716B8D01BB1BA0E22EDEBADCD7ACE00F7C2F5EE (void);
extern void LODGroupRenderingUtils_GetWorldSpaceSize_m88BB3F93D7EB22A6D89863821D5B13ED0F9D9104 (void);
extern void LODGroupRenderingUtils_CalculateLODDistance_m8E6D74E4D1F593158767E34CE33DC2379ED13B86 (void);
extern void OcclusionTestComputeShader_Init_mC423CA21A41E4B44DB2715096F04D7498A80E960 (void);
extern void SilhouettePlaneCache_Init_mF14F33F1C7D6CD3704478C92314526747ABDFF0C (void);
extern void SilhouettePlaneCache_Dispose_m52386469CD058770AAD5B4E19ADF603598BECEC5 (void);
extern void SilhouettePlaneCache_Update_m08599EDAF7CC1D053E0E54A8DF15F55E1E0B6342 (void);
extern void SilhouettePlaneCache_FreeUnusedSlots_m2249464604B48996B77945B9BBCDBDCECD2074C2 (void);
extern void SilhouettePlaneCache_GetSubArray_m0527F754CEEB54300C083A4BDAE9B56D026DA63C (void);
extern void Slot__ctor_m0E99B79099FDB0098404A2FD223A9C029CFFF5D1 (void);
extern void OcclusionCullingCommon_Init_mB12BBAEE22EA6EA4C93640CF113484C45AB21128 (void);
extern void OcclusionCullingCommon_UseOcclusionDebug_mB3DD90044DC771F1A74BDEAC59C921AE66E9311D (void);
extern void OcclusionCullingCommon_PrepareCulling_mB0789630787C7E0CD26370E649348B1C2E368B0C (void);
extern void OcclusionCullingCommon_SetDepthPyramid_mD653D7921DC4590B1E5DDC848F3B3DDF10D15D07 (void);
extern void OcclusionCullingCommon_SetDebugPyramid_m7DB573CC2B23E59F6E09FE953D2953447FB4D8BE (void);
extern void OcclusionCullingCommon_RenderDebugOcclusionTestOverlay_mFC06DC3F4302109DCBCE0016F77FDC7221C0F850 (void);
extern void OcclusionCullingCommon_RenderDebugOccluderOverlay_mDCEE8545488D66BAFEEC82CA0A5B078EF76F1719 (void);
extern void OcclusionCullingCommon_DispatchDebugClear_mD07E3E63ABEB291DB36385737735511B88AD3AC2 (void);
extern void OcclusionCullingCommon_PrepareOccluders_mB04E538ADB8D350F2F77C2B0AEB3235B5537C78A (void);
extern void OcclusionCullingCommon_CreateFarDepthPyramid_mA599495FF407F8137E6B40745EFA5296FD390859 (void);
extern void OcclusionCullingCommon_UpdateInstanceOccluders_m66590207897221E9FA80265BBEB4E9E40708646D (void);
extern void OcclusionCullingCommon_UpdateSilhouettePlanes_m4576EBD18929EC7B7AAA98EA599CEB053033161E (void);
extern void OcclusionCullingCommon_GetOcclusionTestDebugOutput_m3F8B14753A940E66F3378EE0A13B467CD5B54163 (void);
extern void OcclusionCullingCommon_UpdateOccluderStats_mFCE4F68D13AD834D837ACC6CF5818BB454DEB374 (void);
extern void OcclusionCullingCommon_HasOccluderContext_m24FD8FB63CF4F73E28369A7C5E4AB1A4B0C6EF90 (void);
extern void OcclusionCullingCommon_GetOccluderContext_m5FA55C98ABA809491877468967428AEA6ED50AA9 (void);
extern void OcclusionCullingCommon_UpdateFrame_m62E1615FE4BB0184C70EF0D5A1B5341A9E6B439E (void);
extern void OcclusionCullingCommon_NewContext_m192A0843FCB88873DB0DBC0D30E85E34D9CD3724 (void);
extern void OcclusionCullingCommon_DeleteContext_mD0DD525EF7A79EDEC506F1FD27762960E7A9D773 (void);
extern void OcclusionCullingCommon_Dispose_mA5C16ABDC8FFDCBDF1B0BBDAAF046EB707CAB0BE (void);
extern void OcclusionCullingCommon__ctor_m3B0C90E1EF8186EB97881C43D58E13303CACED1C (void);
extern void OcclusionCullingCommon__cctor_m65EF7B748745B32F17F979959B56ABA54B68E19D (void);
extern void OcclusionCullingCommon_U3CRenderDebugOcclusionTestOverlayU3Eb__29_1_m9B31475AE7F1F1FB5043C7E6AE2AB37D0D901037 (void);
extern void ShaderIDs__cctor_mC4B7BFD4D1A496F04AC567A1D343648AF9932CDD (void);
extern void OcclusionTestOverlaySetupPassData__ctor_m319029C880BDA7B70BBB48CCC52A6DEEE84BC7AA (void);
extern void OcclusionTestOverlayPassData__ctor_m0D63CEF912BF6F987D0718384ED42945529D5FE0 (void);
extern void OccluderOverlayPassData__ctor_m2BBEDE9EE87B99D51BD3A55ADE85B0FF7191D88E (void);
extern void UpdateOccludersPassData__ctor_m780741CED9AA7DEA6E7F15F1125830643B0940A5 (void);
extern void U3CU3Ec__cctor_m69E4B9D0362E234583DB9D7CC8D28B7B958F008D (void);
extern void U3CU3Ec__ctor_m7AF3A5B26F1D35F52C4E1518DCB55AF32705CA12 (void);
extern void U3CU3Ec_U3CRenderDebugOcclusionTestOverlayU3Eb__29_0_m6B98C8D250CCC733E809FCD7A6BEF46BE6416D27 (void);
extern void U3CU3Ec_U3CRenderDebugOccluderOverlayU3Eb__32_0_m000074A9983218A19ECAA6BBF27D4DE6F0CEC6EC (void);
extern void U3CU3Ec_U3CUpdateInstanceOccludersU3Eb__37_0_m07755DD078337F25892B35E882F36CF2D77C600B (void);
extern void OcclusionCullingCommonShaderVariables__ctor_m6098CCD0E939B2F9DE8715FF129DAE892745C610 (void);
extern void RenderersBatchersContextDesc_NewDefault_m60D7888149F2142AA66FECE97FDB3D098A3EC7DA (void);
extern void RenderersBatchersContext_get_renderersParameters_mFAD826F98D88258ACEB3BAAB3BCA506E9DA8C54E (void);
extern void RenderersBatchersContext_get_gpuInstanceDataBuffer_m58A374780F991800398A3A5C43B900F17E49CA78 (void);
extern void RenderersBatchersContext_get_activeLodGroupCount_m67F17132BF666120D9AD4C4AF2B5332DA3C3B3E8 (void);
extern void RenderersBatchersContext_get_defaultDescriptions_m9E1716E5F3F0528BCEF408D48F5114377A4449D5 (void);
extern void RenderersBatchersContext_get_defaultMetadata_m2A1B42632AB9F8A3B0E162912B6E1E542AC52A78 (void);
extern void RenderersBatchersContext_get_lodGroupCullingData_m71D5CF459C1ED069E69F643AEF402CE8684925ED (void);
extern void RenderersBatchersContext_get_instanceDataBufferVersion_m9F26A5C73EE9A8C8848F3163AAF3B50FBC96EFE3 (void);
extern void RenderersBatchersContext_get_instanceDataBufferLayoutVersion_m633BE48CAD9AA78DD46E20B2208647B3A94D992D (void);
extern void RenderersBatchersContext_get_crossfadedRendererCount_m6D1B5BC5F0E565A3A4CFE7AB9BFF448DFE7BF58A (void);
extern void RenderersBatchersContext_get_cachedAmbientProbe_m122AB618901D9C67E31A3E1994C09FAE04AEAFE1 (void);
extern void RenderersBatchersContext_get_hasBoundingSpheres_mA6745C1F53546E926C85BC0B69E1E176E5C07B54 (void);
extern void RenderersBatchersContext_get_instanceData_mA110F9896EEF3B8277350408C9554A9CA4BBAA1F (void);
extern void RenderersBatchersContext_get_sharedInstanceData_m657B7F8E58C1857C9A941039A9C87EDEE14BE073 (void);
extern void RenderersBatchersContext_get_instanceDataBuffer_m085CC45CC334F7C4AFFC82F08FE9041267BC3FC0 (void);
extern void RenderersBatchersContext_get_aliveInstances_m464BB51D736CC6E53816E92B54FA52E20A6AB992 (void);
extern void RenderersBatchersContext_get_smallMeshScreenPercentage_m20E6B516780C91E3EFFF054223A2AD8259D67CEA (void);
extern void RenderersBatchersContext_get_resources_m384802C47C8866FE84F3D19892ED70D03CAD5CF2 (void);
extern void RenderersBatchersContext_get_occlusionCullingCommon_mB5106ABB84E6D34B14EBA467B292E39DDCB60C1D (void);
extern void RenderersBatchersContext_get_debugStats_m26AAE0C2CF41DBE02DD210D1FDDB808F8A88CB87 (void);
extern void RenderersBatchersContext__ctor_m0284FF6010F6BE127276B918BCB7F8D488D82C33 (void);
extern void RenderersBatchersContext_Dispose_mD6CFED69D7F9007FBA28516C2A6CCD9394D1FC3E (void);
extern void RenderersBatchersContext_GetMaxInstancesOfType_mEF99113F1507ABC8426119B2F16B92114F19E1B3 (void);
extern void RenderersBatchersContext_GetAliveInstancesOfType_mAB16FC96B0BC9357E0DC9FA279AD4844AE0BBD60 (void);
extern void RenderersBatchersContext_GrowInstanceBuffer_m72EEF32E7D68892D6B6C686290FB074274AF33AD (void);
extern void RenderersBatchersContext_EnsureInstanceBufferCapacity_mE609DC40C454449FDFCD61C0347BF4F4C7CFC395 (void);
extern void RenderersBatchersContext_UpdateLODGroupData_mC3BBC143D600124BC3536CAFE8ADA3D80B9F4E1E (void);
extern void RenderersBatchersContext_TransformLODGroupData_m910C251DDACF06457FAB5E90FFE94CB76C84004E (void);
extern void RenderersBatchersContext_DestroyLODGroups_m2F2BB8BC930C966F0C1FD6392D669D26B2967675 (void);
extern void RenderersBatchersContext_UpdateLODGroups_mCC2A5E08EF4A3A88B195D71F252997FAE8255490 (void);
extern void RenderersBatchersContext_ReallocateAndGetInstances_m75003DE54327AFC9FC9226F543E2AA42ED4CA436 (void);
extern void RenderersBatchersContext_ScheduleUpdateInstanceDataJob_m52A9965BBC3ACB0F00144C8D39E46478543B623B (void);
extern void RenderersBatchersContext_FreeRendererGroupInstances_m1BBD1A75AFD3CED5F347ED940D15EF20D303EA17 (void);
extern void RenderersBatchersContext_FreeInstances_m8D8AFCF6F9AD2F684CBFCD5B9126C77B9BA856E0 (void);
extern void RenderersBatchersContext_ScheduleQueryRendererGroupInstancesJob_mEE8968FD91E2F49D2AE33D4A3D0E8C745FF489E4 (void);
extern void RenderersBatchersContext_ScheduleQueryRendererGroupInstancesJob_m41084D427885CB440E7ACDD227EF915E37B24FA3 (void);
extern void RenderersBatchersContext_ScheduleQueryRendererGroupInstancesJob_m07B14A5915E64E55DB6DEE709DABD8446F320E13 (void);
extern void RenderersBatchersContext_ScheduleQueryMeshInstancesJob_mEF8DDD764AFBFBFA23B708EF823511E8CD748966 (void);
extern void RenderersBatchersContext_ChangeInstanceBufferVersion_m8BDA9E1B471724D930283E832CDC1C4D1172499C (void);
extern void RenderersBatchersContext_CreateDataBufferUploader_mE83CA3760B15FBD7BD9E8166D38C01ACA6DC4385 (void);
extern void RenderersBatchersContext_SubmitToGpu_m7D51CAAFDF4D04FDB49B81F907ADA5C0023909BF (void);
extern void RenderersBatchersContext_SubmitToGpu_m522529681D96803ECD637E642083FD54D8FBAAB6 (void);
extern void RenderersBatchersContext_InitializeInstanceTransforms_m3346F85D05A58656054559FF0D221E5F5D42C813 (void);
extern void RenderersBatchersContext_UpdateInstanceTransforms_m83DE2D5F845C8D5C10B3E6B809BE32E00E1607AE (void);
extern void RenderersBatchersContext_UpdateAmbientProbeAndGpuBuffer_m9635A08E6A72E53938EA2C332B7F37BFD6925535 (void);
extern void RenderersBatchersContext_UpdateInstanceWindDataHistory_m08DA4EE6C170DEA9C8A9B876071CEB4804438173 (void);
extern void RenderersBatchersContext_UpdateInstanceMotions_m597C9A66CF49C8F6A010D5D7D0E866657DA207ED (void);
extern void RenderersBatchersContext_TransformLODGroups_mB0CB4CD84FB8FF1E35821FD3CB869166ED7D5B7D (void);
extern void RenderersBatchersContext_UpdatePerFrameInstanceVisibility_mBD8E7669A22B6C1D47BD0BF3BDC5E22BDD16FBB2 (void);
extern void RenderersBatchersContext_ScheduleCollectInstancesLODGroupAndMasksJob_mD6FC667C7E0C513173E0720521FD54C3A385737A (void);
extern void RenderersBatchersContext_GetRendererInstanceHandle_mF6127D9881FD12DFF2E5AB4132343A50E46E3FE3 (void);
extern void RenderersBatchersContext_GetVisibleTreeInstances_m5C91EC91858A7EA240EF72E870C8C6A14D1FCC7F (void);
extern void RenderersBatchersContext_GetInstanceDataBuffer_m7164DAD5855B34AA94DC599A67E3FCC547C6FC1E (void);
extern void RenderersBatchersContext_UpdateFrame_mCFA782A62647ADD043E3247EFF36079A2426DAD4 (void);
extern void RenderersParameters_CreateInstanceDataBuffer_m945CE4EF304375414A46DDED06474BFC3132D971 (void);
extern void RenderersParameters__ctor_mD3445734819B50610F768B8E6EF49822D5ABEE8A (void);
extern void RenderersParameters__cctor_m8D5D5734DCF7E98603C17A197EC062D2B1D88F05 (void);
extern void RenderersParameters_U3C_ctorU3Eg__GetParamInfoU7C14_0_mD43A1760BB14DE3AF585F6E664A7641CA2E4560F (void);
extern void ParamNames__cctor_mAEF822BDB14694895783B71D8EACF1EEC9B15C91 (void);
extern void ParamInfo_get_valid_m198E32DB1AAD1F43EAD6964E4EF79E79D078AF7D (void);
extern void ParallelBitArray_get_Length_m82FE0E2AC9FAB29DA67E28FFAEA04EB642955B08 (void);
extern void ParallelBitArray_get_IsCreated_m31B6CFD3C95548F523C3D074463B827B7CD7A535 (void);
extern void ParallelBitArray__ctor_m7BB9EA31D2DF48FD4BE5D8773C539A76C5D4E6E4 (void);
extern void ParallelBitArray_Dispose_m24CBECA125F3D0090E9786E6AF56CB2E6DE452C6 (void);
extern void ParallelBitArray_Dispose_m3806D56E9788D3C69BD6C6985C6E2F949A89F9B7 (void);
extern void ParallelBitArray_Resize_mA268182EEF9B41198BFC3780A74CF12D9B232011 (void);
extern void ParallelBitArray_Set_mEDB30931801E1F71F11F62CCB23F3537EEE0F1EA (void);
extern void ParallelBitArray_Get_m250C22A6191BAF4C5B314EFAF451E391D251F2B0 (void);
extern void ParallelBitArray_GetChunk_m7303392F0138448DF74E7A709F38B500B9461ED2 (void);
extern void ParallelBitArray_SetChunk_m48943193199714BCF0925DD8E14C8EB651885629 (void);
extern void ParallelBitArray_InterlockedReadChunk_m19B068CDEB9686FE2DD8A42BF99D2011EEAFA84F (void);
extern void ParallelBitArray_InterlockedOrChunk_m51A85AD8A6A6FFD317303DEABA6B9797B79CE658 (void);
extern void ParallelBitArray_ChunkCount_mE27E6F3D861AF09C5B31BD7F3964796B26A9C3B5 (void);
extern void ParallelBitArray_GetSubArray_m45E14868BB90EC98D0467ABDA3DAD1BD4BFC49DD (void);
extern void ParallelBitArray_GetBitsArray_m31F100FDDB1EA0FE4E1592768ED14B843D0DD73D (void);
extern void ParallelBitArray_FillZeroes_mDAE3DE6ACB91DE00B5DC5D50415B7A0F938A08BB (void);
extern void ParallelSortExtensions_ParallelSort_m237D06D0D0DA504CE809A6FF2D2CEF9CE0221A08 (void);
extern void ParallelSortExtensions_U3CParallelSortU3Eg__SwapU7C2_0_mDD868A15D4BFD33E6DFF6107497D4EB6EE040E16 (void);
extern void RadixSortBucketCountJob_Execute_mF3ADEB0523C3DE92CB5CFEF01B65E72C9AB7C023 (void);
extern void RadixSortBatchPrefixSumJob_AtomicIncrement_m89775B1090C6296097B6445BC76D2C6BE88F199E (void);
extern void RadixSortBatchPrefixSumJob_JobIndexPrefixSum_m9C47BE4B67FCFF29A8FA94D39589F9B9A2840EE3 (void);
extern void RadixSortBatchPrefixSumJob_Execute_m434849692F7D93EF83545890B59FC96BF14AED93 (void);
extern void RadixSortPrefixSumJob_Execute_mF1969BD6160F81BA429AF74A8944935FC83BC551 (void);
extern void RadixSortBucketSortJob_Execute_mDB0AB3CD468DA898E41CBF3E9EF5BE26AD26E4D2 (void);
extern void __JobReflectionRegistrationOutput__15867191014387474753_CreateJobReflectionData_m61B92F5EF70DF366B7640CA4487293699C2E2A18 (void);
extern void __JobReflectionRegistrationOutput__15867191014387474753_EarlyInit_mCAFBD6F04F7737F01B0CA94B81910948BEB121CB (void);
static Il2CppMethodPointer s_methodPointers[839] = 
{
	EmbeddedAttribute__ctor_mC74BAF246B6DBDC3E01CA6C01E9F99BEBFB85292,
	IsUnmanagedAttribute__ctor_mECABE2CB94F3EB55D9EBAB15A7E05DF541B049E3,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m1EDC75FA86A9139DD4A9DB0DAEFD85A1B786E080,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mBD0A412FCC9806186BDA6EE066831333F28A3414,
	AABB_get_min_m209EF3ECD01E859258E1BDE8780CA4C25276DF70,
	AABB_get_max_mCD29B3A2C2DEA60BBF50091D8E3C938511D82B08,
	AABB_ToString_mB0C529C757F63FD0F78316C90D2B86206159B678,
	AABB_RotateExtents_mE4E40331E25B5F3310112548831B83F096C98E06,
	AABB_Transform_m1B125B36873D7F03DCBB8817DE20E81FD17C149C,
	AABBExtensions_ToAABB_m367412CA9980E495609B7267A2CC04AE39AF2402,
	AABBExtensions_ToBounds_m7CAFEDB45226C652830072153F7236BA97C6C520,
	BatchLayer__ctor_m5B65286582FB01A261387F6EB54330C8E47C3CFD,
	DisallowGPUDrivenRendering_get_applyToChildrenRecursively_m3E929193F5CE5D66B2FE31D776EC2A2F2116CAA9,
	DisallowGPUDrivenRendering_set_applyToChildrenRecursively_m68D41A53534B142755C97A1E90F85707979EF47E,
	DisallowGPUDrivenRendering_OnEnable_m249A20499E8492DEEBAA51A2F682BAF5832200BE,
	DisallowGPUDrivenRendering_OnDisable_m1F1E89249A83BC47FE22BBA3AFBB3E45AA8A9CA5,
	DisallowGPUDrivenRendering_AllowGPUDrivenRendering_m20DF3D8C370F5104515C565084A83B476625F356,
	DisallowGPUDrivenRendering_AllowGPUDrivenRenderingRecursively_m6A9F55C1FE2690255B488CBD65D4EC7A088795DB,
	DisallowGPUDrivenRendering_OnValidate_m6F127CE57094B0CAE61BA8B4918EECB80E37240D,
	DisallowGPUDrivenRendering__ctor_mE6D1309170C6045938779078895FBDC316CD22C8,
	DisallowSmallMeshCulling_get_applyToChildrenRecursively_m07F3D5D527D2DEF50D7B02D214383B8AF78C4C64,
	DisallowSmallMeshCulling_set_applyToChildrenRecursively_m938F4AF22EDE3BBF3FA63774848026C0D1BE66B7,
	DisallowSmallMeshCulling_OnEnable_m794055654CE760B4FD1780DC20C0C94C84A99A87,
	DisallowSmallMeshCulling_OnDisable_m75A11291A610DED6EADAF48AF38509DE765AA25F,
	DisallowSmallMeshCulling_AllowSmallMeshCulling_m8D38B7DB8F8A05A7839BC00395B7137C4688E996,
	DisallowSmallMeshCulling_AllowSmallMeshCullingRecursively_m8D9B550BDF92C3C920B34AC0DDC65D5B095F9D22,
	DisallowSmallMeshCulling_OnValidate_mF3C1B33DE96CA700BC01B5311F80AE4E99F29DA0,
	DisallowSmallMeshCulling__ctor_m8EE3BCDB2DE6243C9C844C82F454D0845738A51D,
	DebugDisplayGPUResidentDrawer_get_displayBatcherStats_m210D40F2C66835ADDD79B906A5266E0F233D3C34,
	DebugDisplayGPUResidentDrawer_set_displayBatcherStats_m59E27BDE577C32E337F723FEF8F63C6BC686C662,
	DebugDisplayGPUResidentDrawer_GetOccluderViewInstanceID_m726FCBE5E8C19295040CED7A6F87E7F31DCC3CE8,
	DebugDisplayGPUResidentDrawer_get_occlusionTestOverlayEnable_m9A235C5BC833535F37EF6521C8201C3CE29C51A5,
	DebugDisplayGPUResidentDrawer_set_occlusionTestOverlayEnable_mA2EB2A26999F2D9AF42AA8E3E4636C26C2B742EB,
	DebugDisplayGPUResidentDrawer_get_occlusionTestOverlayCountVisible_m634A2AC553EFFABBE7867FF3F849B56854132881,
	DebugDisplayGPUResidentDrawer_set_occlusionTestOverlayCountVisible_mB528723B0E9B869C1A316E1E96A992559BF6F7ED,
	DebugDisplayGPUResidentDrawer_get_overrideOcclusionTestToAlwaysPass_m540F53888B63B89065870B56B8981D23580A82E3,
	DebugDisplayGPUResidentDrawer_set_overrideOcclusionTestToAlwaysPass_m20C4D9DC1E966A9C4C4D0D466F4BD38ADD30AE13,
	DebugDisplayGPUResidentDrawer_GetInstanceCullerViewStats_m0FBAB4D8A7F7B2AD56BABE57E5E0648B5686A85C,
	DebugDisplayGPUResidentDrawer_GetInstanceOcclusionEventStats_m602D50534C866EEB3661DA78C183640EE3224B94,
	DebugDisplayGPUResidentDrawer_GetOccluderStats_mC14EA7475AC1AFC3C3252E433ADB1C537CD934A4,
	DebugDisplayGPUResidentDrawer_GetOcclusionContextsCounts_m60D0D1EBF103C8A3199281D3DC5CD2C318F98CE2,
	DebugDisplayGPUResidentDrawer_GetInstanceCullerViewCount_m92894DC66DDDCC9E86096BBE082D1D306C2A085A,
	DebugDisplayGPUResidentDrawer_GetInstanceOcclusionEventCount_mD84250B5B4DF8C05FAC630C7E60E9E27125E14A8,
	DebugDisplayGPUResidentDrawer_AddInstanceCullerViewDataRow_m9F1A1FB82CC3344A6A3A2DC88D8EF67A67F8A767,
	DebugDisplayGPUResidentDrawer_OccluderVersionString_m7714E7A750D1B3965E3E07AC82B75EDCFFC0A265,
	DebugDisplayGPUResidentDrawer_OcclusionTestString_m1B455D1A286D34A76D5A3F9F0583852B93AEDB4B,
	DebugDisplayGPUResidentDrawer_VisibleInstancesString_m379FD46F020F09A4F6104D0B903B87E6D5CF440E,
	DebugDisplayGPUResidentDrawer_CulledInstancesString_mD981EA1AFC5F7D9E5FE201A576EF96E47FD545E7,
	DebugDisplayGPUResidentDrawer_AddInstanceOcclusionPassDataRow_mD27E269D6934863E7BD1A75433C1DD6F6B080A9B,
	DebugDisplayGPUResidentDrawer_AddOcclusionContextDataRow_mDCD25714EE18DC98C484BC29D6780364F36C371B,
	DebugDisplayGPUResidentDrawer_get_AreAnySettingsActive_m749C09E9C318C2B5073709110F7D2DEFA890D144,
	DebugDisplayGPUResidentDrawer_get_IsPostProcessingAllowed_m18A00DF3C6845F6EB6E85FEF8FAFD3A4458B4DE3,
	DebugDisplayGPUResidentDrawer_get_IsLightingActive_m043DDEA409C65069FE6EB254A58195135068A9A1,
	DebugDisplayGPUResidentDrawer_TryGetScreenClearColor_m8EF35A1BD3CE911D205A4031178454C2354F76FB,
	DebugDisplayGPUResidentDrawer_UnityEngine_Rendering_IDebugDisplaySettingsData_CreatePanel_m742018A2D4F79F94FD7B9A29434307A7519FD155,
	DebugDisplayGPUResidentDrawer__ctor_mC29313F658266A745F986BBC51F16C4AF1287947,
	Strings__cctor_m68ACF98790846338FA3596EE3124ECAFDFC10204,
	SettingsPanel_get_PanelName_m90BADF6418AA88C7F59BA9D1A3AFA28D7665CC93,
	SettingsPanel_get_Flags_m748B011E300968BF170B06B15BCA80C9B4A7EF80,
	SettingsPanel__ctor_mC4E295FA1EE20556ED1C2F8876B443177EF1250E,
	SettingsPanel_AddInstanceCullingStatsWidget_mF9C247F7AF56C7FDF3B0D459DF7387E1C209332B,
	SettingsPanel_AddOcclusionContextStatsWidget_mBBDAD684776194C51A2E3010D8A59938BD7F746B,
	U3CU3Ec__cctor_mA36DB0581BF5A4E8003BF3379BC4A176BBB178B0,
	U3CU3Ec__ctor_m795644A9581215B808B0A8ACF217AF65CA840E5E,
	U3CU3Ec_U3C_ctorU3Eb__4_0_m6433C603F60BAA5065A77094C19BB86C7F301E66,
	U3CU3Ec_U3C_ctorU3Eb__4_1_m700B2928DE73FF7DEF6A82F44CC8BBCC2124D2BA,
	U3CU3Ec_U3C_ctorU3Eb__4_2_mA9D2E15C3916863E032D292E2D4215957F09F6AC,
	U3CU3Ec_U3C_ctorU3Eb__4_16_m24CC97757460BA4C7D937415ED3A4B0E5F2D2511,
	U3CU3Ec_U3C_ctorU3Eb__4_17_m4B4D802EB8D30CAC663D4F3F84BA09EB588781A2,
	U3CU3Ec_U3C_ctorU3Eb__4_24_mFE4098833C8C3D728134A8714C6470ABDF7B5425,
	U3CU3Ec_U3CAddInstanceCullingStatsWidgetU3Eb__5_1_m21D3F1B6499B429C40E3AFE8E19BBB3D23B775E6,
	U3CU3Ec_U3CAddOcclusionContextStatsWidgetU3Eb__6_1_m00E24C8B8F792462905FFCD595B4E88335BAC41F,
	U3CU3Ec__DisplayClass4_0__ctor_m4B6078FB02177992D2D89D03034193B119FCC121,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__3_m09F899326DEB7BC43FDA83BD3BD0933D8BF7F3C7,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__4_mCE08B782017CDB1C40C62EAB6EDB582D97CD5123,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__5_mE0392013001AC3B5B6F6E927E0BD1860BDBBF804,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__6_mC55B110181918CCD37536EE778E6B7529B42C847,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__7_m19308DA906FD7430AB4AC38DF6D9498F0EBCC74C,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__8_mD92DF44421F9DC7B7E522574E2710F6A5C36343B,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__9_mF9E585B1AC3AB4A1888A365B823DA2EBC930561A,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__10_m2076D6E428469C3DEA95C8D8ABF9C5D6490F6016,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__11_m8E660F32E291ECAD40101E871D1DCD46505404E4,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__12_m5B550E5C3EFAE4FB4F90F4D93494C0CD20B5B2C5,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__13_m67A22BA78A4E106D7D26373185CE1E4D3DDCE61A,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__14_m90443DF357AD717A59E5B152B935A0B6FD4B3D2D,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__15_mB3AE67259D20D9E1FB23112977F28C6C83AD8FE2,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__18_m5659B758462B6421D0C403F466CE324073390081,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__19_mB54A648694706FD2A9D5BA8F313520AFADEE4827,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__20_m6FD3989F5AAC6FF35A3DCCC385AAC6A30A5FDF8E,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__21_m256066D0FADEFCDE94027FDFE5AD2DBB417FCCE0,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__22_mFBEF78A6BCEEC69B688E85C3E8CBB8490CB61EA1,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__23_mCC74DDDC57694ECDD68316C373AD2E5D7959D2A5,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__25_m04E4D66D56588388AFED0AFBE7E0BE534FD46D24,
	U3CU3Ec__DisplayClass4_0_U3C_ctorU3Eb__26_m77486D483224008476C5144215DAA92B2A555BA0,
	U3CU3Ec__DisplayClass5_0__ctor_mF3D08B015A3B6ECA3D2EC8E9824AB1808DA40D07,
	U3CU3Ec__DisplayClass5_0_U3CAddInstanceCullingStatsWidgetU3Eb__0_mAFCC6F5239982E03A4CD3B1910A434D9ABBAF259,
	U3CU3Ec__DisplayClass5_0_U3CAddInstanceCullingStatsWidgetU3Eb__2_mA787F433219B34806F931F1610D41ADA4EB0325E,
	U3CU3Ec__DisplayClass5_0_U3CAddInstanceCullingStatsWidgetU3Eb__3_m15303C44E35730C25B1342370B74ED28764B85AD,
	U3CU3Ec__DisplayClass6_0__ctor_m6ADCBA286E64868206185185B5EA12F646D6B302,
	U3CU3Ec__DisplayClass6_0_U3CAddOcclusionContextStatsWidgetU3Eb__0_m7D8DB169EB50BF8D498D565117D77ED9BBEE5786,
	U3CU3Ec__DisplayClass29_0__ctor_m9E9E0D304D4857CCC41C81920D8F9B2426B21022,
	U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__0_mC0E8B5021FEC842FDA982AF122F22BB6340D35E9,
	U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__1_m9AE4D495A32845644E11FB2B895DA5E75F8635F8,
	U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__2_m95BA865F2DE7F6DBDD8FCEFE47701070C5AE3F98,
	U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__3_m58F09A1804446AF0F5931AC7196D7BAC039F3EDB,
	U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__4_m3747952920734A24FC5B1F9DCFE309747F9C4C4D,
	U3CU3Ec__DisplayClass29_0_U3CAddInstanceCullerViewDataRowU3Eb__5_m74C8AB5E55872D1160D6F08B78F48718683BF499,
	U3CU3Ec__DisplayClass34_0__ctor_m51E792F40771F45B88ABD1EE79EF175CCE76CD48,
	U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__0_m27FB30AE227BED3DFA61625B55C8EBE3082D04C9,
	U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__1_m1E2FEB11D28E7B45D586D165C7B58E8C799C2989,
	U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__2_m4652608ED9683E21291FE5A1F074C1DC6235627B,
	U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__3_m497AA999AF8B5BAC82C1131C80256A58497A0190,
	U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__4_m94EE7C495806664154E3E3BDC30518483010AEF4,
	U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__5_m8BF230F649C38952F63793E85645803C3E72E468,
	U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__6_mAA756A602DE4CAA7F4F3ABF323D5D6A41FADF620,
	U3CU3Ec__DisplayClass34_0_U3CAddInstanceOcclusionPassDataRowU3Eb__7_m111803CA6822911B48D35155FF2DE904987938BF,
	U3CU3Ec__DisplayClass35_0__ctor_mD214B5A8783451B94F4590004B1FE5F08EC6D092,
	U3CU3Ec__DisplayClass35_0_U3CAddOcclusionContextDataRowU3Eb__0_m4C70823339569EBDDFBBE48D1E3EA1501DADE74D,
	U3CU3Ec__DisplayClass35_0_U3CAddOcclusionContextDataRowU3Eb__1_m28A3FB09CA136E909008FFC0989CF96328E6EBC6,
	U3CU3Ec__DisplayClass35_0_U3CAddOcclusionContextDataRowU3Eb__2_m711316387059484363D585616C8D01248FCA7EED,
	U3CU3Ec__DisplayClass35_0_U3CAddOcclusionContextDataRowU3Eb__3_mA0FC30C2542A04A04E4E3C75BFDF01746FCCAF7B,
	Line_LineOfPlaneIntersectingPlane_m0ED39FE7E09D672A03C93AA51883CECB1FFF2EF8,
	Line_PlaneContainingLineAndPoint_m59813FE596901FCDFCA7E64A83841D14AB030402,
	Line_PlaneContainingLineWithNormalPerpendicularToVector_m34DB45E98946F82ECB81242D6CE074AEF43E0CF0,
	ReceiverPlanes_IsSignBitSet_m115EC872F6B26CBF654D849659242E76E7850A09,
	ReceiverPlanes_LightFacingFrustumPlaneSubArray_m995D1E681E0ED63DFA8489C582A2FCBAF1C9356B,
	ReceiverPlanes_SilhouettePlaneSubArray_m851F735918EE9297C64815C42AAD446BCBE55F4B,
	ReceiverPlanes_CreateEmptyForTesting_m31C17EE66110F80DE225AC49E8AC79ED4D838ADB,
	ReceiverPlanes_Dispose_mE53DCBAD81DDB002760FC5661F3B26330B62ACF4,
	ReceiverPlanes_Create_mB53449C117AA6108D5F00315F573CE710945A81F,
	FrustumPlaneCuller_Dispose_m802E9F4605F17D55D46C6C0D308830667DE0A08B,
	FrustumPlaneCuller_Create_m4DD061D5BCFFFDBE074596E2373150D4787331F3,
	FrustumPlaneCuller_ComputeSplitVisibilityMask_mCFC42DB8BADE15AB4A87F4BC60A3B5D7507745DC,
	PlanePacket4__ctor_m1B67A6C35FB1B6B6BE21D76B496DA3531F636CC7,
	ReceiverSphereCuller_CreateEmptyForTesting_mC63AFF1A7B2CF647E5A605E41125BA5E5533AE0B,
	ReceiverSphereCuller_Dispose_mFCC83320D29032DBC7A3173ADFE8F764A472FFA5,
	ReceiverSphereCuller_UseReceiverPlanes_mEA2EE8FE32563F3FBE40D6AADA9724719186674E,
	ReceiverSphereCuller_Create_m63514E053CF0DD1CD37702E56C84F4F0408A8B00,
	ReceiverSphereCuller_DistanceUntilCylinderFullyCrossesPlane_m1E5B038B031E5090A38EE0FF6EFC405E4BDD63E1,
	ReceiverSphereCuller_ComputeSplitVisibilityMask_m1188332DCE2CBF80B1A431AFB1BD49955898936F,
	GPUResidentBatcher_get_batchersContext_m832BD0381846BE2E11D425CE1319953A411D64CE,
	GPUResidentBatcher_get_occlusionCullingCommon_mCC135850F6B339139CBA5E60715E4B059195CBC7,
	GPUResidentBatcher_get_instanceCullingBatcher_m9E7BF9BA5E63072AB043C86836AC4C036AD5D465,
	GPUResidentBatcher__ctor_mE43D45A4CCB93B7DD21D5FB49C4D6BA16DEC72B2,
	GPUResidentBatcher_Dispose_m027050D276B285BED80401912FB6B3D0BADC90AD,
	GPUResidentBatcher_OnBeginContextRendering_mFCE0FA21DAB11BC5B770CB5C63F14C32B2ECC30F,
	GPUResidentBatcher_OnEndContextRendering_mDC1CCC712A83C48E61D34E56BE21B5458C77BB61,
	GPUResidentBatcher_OnBeginCameraRendering_m818CA5BB64950FFCF566414C70CC5D8B6D96A922,
	GPUResidentBatcher_OnEndCameraRendering_mC31C1F8395CEED9B4AD6321DACC36E9115666885,
	GPUResidentBatcher_UpdateFrame_mB0B6728FE5D08E785FD7747D9AF4D065705D944D,
	GPUResidentBatcher_DestroyMaterials_m0BEE5AE70E666A3E35E16EA8D941480E1351A243,
	GPUResidentBatcher_DestroyDrawInstances_m9513A335709809ED850D1AF76E2A6FFCB8707AF9,
	GPUResidentBatcher_DestroyMeshes_m4B3BF017904878023774926DE457D2476283B094,
	GPUResidentBatcher_FreeRendererGroupInstances_mB2929BF0DE2234256EB75CC078DE8441CA9594E9,
	GPUResidentBatcher_InstanceOcclusionTest_mCDEFBDADDB90AA06A2E1394B5B80E807CBB5AA43,
	GPUResidentBatcher_UpdateInstanceOccluders_m5570E1F11C12314DD68EA4A1B9B4EE0473FD6A18,
	GPUResidentBatcher_UpdateRenderers_m08752E00B533B9709C0EF2892D843BF10D24920B,
	GPUResidentBatcher_SchedulePackedMaterialCacheUpdate_mB5D6DC79D682FEAA1A5F68BEB685132B1F549881,
	GPUResidentBatcher_PostCullBeginCameraRendering_m9C006EAC8C65FBC1B7DD3F9F4123E071EC1D2F44,
	GPUResidentBatcher_OnSetupAmbientProbe_mDCAD4C9AE21158471F846321EA2A58DBDA1914A6,
	GPUResidentBatcher_UpdateRendererInstancesAndBatches_m7CC59149342BCCB17DE20FCD2BF0294D613B48B9,
	GPUResidentBatcher_UpdateRendererBatches_m7FDCEBC6D9743BA42ED99E545EBBF438702B56DC,
	GPUResidentBatcher_OnFinishedCulling_m3894D01381A5290564E27DC8315623AD4B21975D,
	GPUResidentBatcher_ProcessTrees_mA20D5412045E8AE5E485DE8F50B350F0A88538B7,
	GPUResidentBatcher_UpdateSpeedTreeWindAndUploadWindParamsToGPU_m1E26807F0F67557341970AC983776178A0E90490,
	GPUResidentDrawer_get_instance_m142CE6BEC88AA7FA34052B0138128C3B944FEBDD,
	GPUResidentDrawer_IsInstanceOcclusionCullingEnabled_m03F098AAAA5FCB8140B53C641EB2B0381669BC8E,
	GPUResidentDrawer_PostCullBeginCameraRendering_m3EB60CDFBF342ABD0B11B30439BB01B7CD6F1F77,
	GPUResidentDrawer_OnSetupAmbientProbe_mF67DC77B41AD752F71A25EA4221AA3180AA236CC,
	GPUResidentDrawer_InstanceOcclusionTest_m0DD4F0A4685967C617984FBCE5A0B99A35790AFE,
	GPUResidentDrawer_UpdateInstanceOccluders_mD2F1BAB128CEB6B6A731FEA876A1E08A31C98B30,
	GPUResidentDrawer_ReinitializeIfNeeded_mE8A70A9A6B9C8D4A341552E05D95E4D74B7D68D5,
	GPUResidentDrawer_RenderDebugOcclusionTestOverlay_m17664202C62084572F6037B5F1C61E2FE8C0BFD0,
	GPUResidentDrawer_RenderDebugOccluderOverlay_mB7819CD0C90F839351CE854B2DD297D14F5F830B,
	GPUResidentDrawer_GetDebugStats_m857EE673158C860D3471D0CC6203B60D0BC98B4D,
	GPUResidentDrawer_InsertIntoPlayerLoop_mBDEE8B11EE73F12439561D73E4A2A3C8D6861007,
	GPUResidentDrawer_RemoveFromPlayerLoop_m18F5D085D7C67EEA7EFDB5ABC52AB8C343CA5CAF,
	GPUResidentDrawer_IsEnabled_m03CFAB6E2CE8D71361F5223C940F4C0A785A1116,
	GPUResidentDrawer_GetGlobalSettingsFromRPAsset_m4710F7D4D983AEB1ADD00A40E7E4068C330C9A41,
	GPUResidentDrawer_IsForcedOnViaCommandLine_mC2F9713BA2691A02C23E22E59DDC30E41289539F,
	GPUResidentDrawer_IsOcclusionForcedOnViaCommandLine_mA0CD2D200E26C820386D92E58645EF2FF0B02FDA,
	GPUResidentDrawer_Reinitialize_m542E6537EC9C14A35291824BA6798D5D0D747190,
	GPUResidentDrawer_CleanUp_mF773237C2F3AEF0251249FFD56C02F7A650EE9C2,
	GPUResidentDrawer_Recreate_m09E096E3492D77EE4A3D0D070FA53D2017AD6874,
	GPUResidentDrawer_get_batcher_m03715B9C280D664F90B0B1F592D9C3ADD212F9F3,
	GPUResidentDrawer_get_settings_m3F0472441E9F1191B0E0FC43B6D8BBF004EAF3C6,
	GPUResidentDrawer__ctor_m3B65B01D5C54231BF2D7C4C65B4FA11DDA8CCA1A,
	GPUResidentDrawer_Dispose_mD5709371AD0309C33F25511B22C7C1DCD6AC234D,
	GPUResidentDrawer_OnSceneLoaded_m4C2686BC1182C9327DBA362D670CB30601292F5A,
	GPUResidentDrawer_PostPostLateUpdateStatic_m2322043F8B8734792788BF29370233B9BFFBFF7F,
	GPUResidentDrawer_OnBeginContextRendering_m383B0726811F68E670753BDD5F4EE772DE4593C0,
	GPUResidentDrawer_OnEndContextRendering_m4E432047D9A70FD9FE8718FB195A9477A657857A,
	GPUResidentDrawer_OnBeginCameraRendering_mDCB0CA5A9CB1F1BA25DB7214D3BD75975AA2B705,
	GPUResidentDrawer_OnEndCameraRendering_m1F301D2884DCFB5ADD5CB533FEC803B898EDC690,
	GPUResidentDrawer_PostPostLateUpdate_m94401477ABD5387DBAAD1D6A1CC39E59AE1E2EEB,
	GPUResidentDrawer_ProcessMaterials_m5F9A91DF336FD3CA1CFD81608475C39F85FD89D5,
	GPUResidentDrawer_ProcessMeshes_m254F0CB1EB50564FF5FCC3CDD00420784BB45C04,
	GPUResidentDrawer_ProcessLODGroups_mFDBD9F9CD5F13FDE5AEC4817644BC1BEF6D71D7C,
	GPUResidentDrawer_ProcessRendererMaterialChanges_m4883F6718DF27F989C1C1EE43581646928ED9EE3,
	GPUResidentDrawer_ProcessRenderers_m381BBD34CA5CDCABD580BD2464CBA98ABDBC9E87,
	GPUResidentDrawer_TransformInstances_m20475CB2F2401DD9A54661E4EA63ACC2A5D72B49,
	GPUResidentDrawer_FreeInstances_mDCFFEBAB0E14151660E84E08BEC9AF91F149F611,
	GPUResidentDrawer_FreeRendererGroupInstances_m038FBB93EAF3C06AA447CBBC04E2F9CEC8675814,
	GPUResidentDrawer_AppendNewInstance_mEB1EA724EECB7E96FB4A378582A650ABBD8E635E,
	GPUResidentDrawer_ScheduleQueryRendererGroupInstancesJob_mCC2105095C5D0AB94F74B0DF5033C72BF8F64E21,
	GPUResidentDrawer_ScheduleQueryRendererGroupInstancesJob_m059C106F6838578EB820B0854214AD8E52414C43,
	GPUResidentDrawer_ScheduleQueryRendererGroupInstancesJob_mC87594A6FDDAC454AA8ED09E3F975236FDE87F01,
	GPUResidentDrawer_ScheduleQueryMeshInstancesJob_m63420C906AFFC9D2431B6FD4B71BEBC19F6B902C,
	GPUResidentDrawer_ClassifyMaterials_m07623E4BDE7E899A29EB6E9A9DA2B9FA78ACC722,
	GPUResidentDrawer_FindUnsupportedRenderers_m1C0CB02546C7733938983343B8668CAC84E60772,
	GPUResidentDrawer_GetMaterialsWithChangedPackedMaterial_m112BC45C2CEA7D3B26EDE1B217430AA49E5EE0E8,
	GPUResidentDrawer_FindRenderersFromMaterials_m08545CCE43F5D5D5492189DDD6D506E98AB208B5,
	GPUResidentDrawer_IsProjectSupported_m8070103F51F3975E8D573377D445553710EBA457,
	GPUResidentDrawer_IsProjectSupported_m4F5CDE5F81A0BE7CF42E37F1545E85A8C5A07DE3,
	GPUResidentDrawer_IsGPUResidentDrawerSupportedBySRP_m24CCB3D5623CD94D8DA06D20CE59806BC9D35922,
	GPUResidentDrawer_LogMessage_m7FF3E65D1A87DF183F9D29254AE637D842B88D41,
	ClassifyMaterialsJob_Execute_m94CBFED114435BCA576F6A9344BD213DCAC28DA6,
	FindUnsupportedRenderersJob_Execute_m69B13CFCB49D64142693707BD2ABEE5BA954B26E,
	FindRenderersFromMaterialJob_Execute_mBEE77639F82D94D447BBCD74866B3A1FC106CDC3,
	GetMaterialsWithChangedPackedMaterialJob_Execute_mDCCF53C7F3BF79C9F789EEE83784BC5763F6413F,
	Strings__cctor_m40AEC7C35446DC97C6BA1EFB06EA5B4F5CAADAB4,
	DebugRendererBatcherStats__ctor_mAE82DDFAB36DFF39F4B28D1D43B5896042B250B0,
	DebugRendererBatcherStats_Dispose_m76B62E89A85CAA6D7108200B3C4BDAF9DB4832E7,
	GPUResidentDrawerResources_UnityEngine_Rendering_IRenderPipelineGraphicsSettings_get_version_m136CCE7FDEBC9741FDCAB827F74D51A7B7DF6E07,
	GPUResidentDrawerResources_get_instanceDataBufferCopyKernels_m4821367D99C54C654F6FC0F677D53038EACAEB40,
	GPUResidentDrawerResources_set_instanceDataBufferCopyKernels_m126688C4BE7907A678DE23AF2E3332DD8A34827A,
	GPUResidentDrawerResources_get_instanceDataBufferUploadKernels_mA900FCDAA87450DEBC3C134E015FA14685ADA9EA,
	GPUResidentDrawerResources_set_instanceDataBufferUploadKernels_m54B6A238CECBBA7093D5D1FEDA9B2C8E56332678,
	GPUResidentDrawerResources_get_transformUpdaterKernels_mCA7A4849EFDC13E448339A6AEC42065FDAB5C63C,
	GPUResidentDrawerResources_set_transformUpdaterKernels_mC87744E70D4108F953B0C637C483AA5ABE685709,
	GPUResidentDrawerResources_get_windDataUpdaterKernels_m2C5FADD001A37D11A324FE865E925CD9A5501315,
	GPUResidentDrawerResources_set_windDataUpdaterKernels_m7828D337715FCE91AFEC15C34DAC0F61753A725F,
	GPUResidentDrawerResources_get_occluderDepthPyramidKernels_m7006886C18CF45076331E4B6114CA37A3CE69532,
	GPUResidentDrawerResources_set_occluderDepthPyramidKernels_m9103835307DA4F40F0439903A0E7DF5C8712B704,
	GPUResidentDrawerResources_get_instanceOcclusionCullingKernels_m0096BB5665B29E5552385CC7C4990DDF95C6EDB1,
	GPUResidentDrawerResources_set_instanceOcclusionCullingKernels_m06138322ED0CC1A22774E0D41C74B4CE691BFFEE,
	GPUResidentDrawerResources_get_occlusionCullingDebugKernels_m8B7B3517326F40890A0935A0DC1DD55C8B14F164,
	GPUResidentDrawerResources_set_occlusionCullingDebugKernels_m94C497F616A9D90161CFEA216A07029CC55D0D27,
	GPUResidentDrawerResources_get_debugOcclusionTestPS_m0A869F58FF84A5B43E925DBE72A100212D672BF2,
	GPUResidentDrawerResources_set_debugOcclusionTestPS_m2B0F9F3EC01C30490B37C40D1BACDB2919E88ACD,
	GPUResidentDrawerResources_get_debugOccluderPS_m476766B8038CC61693711BEAB81BD5B65C95D9DD,
	GPUResidentDrawerResources_set_debugOccluderPS_m4A969CD0B817583E1A089F6C636A28F8F9F32835,
	GPUResidentDrawerResources__ctor_m9A9FBC773137C24968523F68F1ED3D55922BAF1C,
	OcclusionTestMethods_GetBatchLayerMask_m1CC038C215B2531DDD0A4C8AF03E2DC518A43D09,
	OcclusionCullingSettings__ctor_mE814849AC60B1DC1AC17E02F1AF2128DF38FE95A,
	OccluderSubviewUpdate__ctor_m482C60BE94ECE0B1F15E930936345D9F60E399A8,
	OccluderParameters__ctor_mBBA1CD9856BD109C9F1D18715B28E2DA95CE83D9,
	NULL,
	NULL,
	NULL,
	IGPUResidentRenderPipeline_ReinitializeGPUResidentDrawer_m6D9AB828C92C7E97A8B441028C3056A905005E3F,
	IGPUResidentRenderPipeline_IsGPUResidentDrawerSupportedBySRP_mFED552B69E782E4125B03C0EC1B2007FEB023553,
	IGPUResidentRenderPipeline_IsGPUResidentDrawerSupportedBySRP_mAC46B52099ED2E34F12F8B7E802DC67E0113A0A9,
	IGPUResidentRenderPipeline_IsGPUResidentDrawerSupportedByProjectConfiguration_m5CE31EEE661C5F8B23636A4F3452CEA8A7AC0B66,
	IGPUResidentRenderPipeline_IsGPUResidentDrawerEnabled_m4B17CE9EBDCEADAB2C6378F8DACE655817DD1757,
	RangeKey_Equals_m05E612C122D91758588CEB7529751045E6F09493,
	RangeKey_GetHashCode_mC38845B6A1CC657D6F6B1149E7448AA6A3EF3257,
	DrawKey_Equals_mBA2838871085C8944B81322DC6E851F4273E8E91,
	DrawKey_GetHashCode_m6E3C5F3D42D02D8035AA7E7B5501FDBC1551F4E5,
	BinningConfig_get_visibilityConfigCount_m0B698DBD65F8147B56C14CA7CFB741EAB701B3B9,
	CullingJob_PackFloatToUint8_m7E24A8A8334FF990F67C75BCE8BB991037645230,
	CullingJob_CalculateLODVisibility_mEBC1FA7E67148AD8CCB4D54FBAB3CF92DDF72A0E,
	CullingJob_CalculateVisibilityMask_m285DA7E28A8582C95BAE2C6794C34FC3F8F195ED,
	CullingJob_Execute_m172CC218FA8F7198BD69C1C87294FD90D94DB884,
	AllocateBinsPerBatch_IsInstanceFlipped_m5ED8B8D355142D10DB3983D15A256B5DBD4D6ABA,
	AllocateBinsPerBatch_Execute_m8244D8F2860ED03B023A6D348D9471F2AF76E0F1,
	PrefixSumDrawsAndInstances_Execute_m8EADFE8897EAE00866B67F8AE64CA774EE33B5F9,
	DrawCommandOutputPerBatch_EncodeGPUInstanceIndexAndCrossFade_m6EFB0D6BB29BF2F65B66913BD2F82E4364ED8830,
	DrawCommandOutputPerBatch_IsInstanceFlipped_m1AE2BD53A7D17783F262A94539417F423FD4CF79,
	DrawCommandOutputPerBatch_Execute_mF995D35E2E640A3172F4BC62E37BF103CFBBBBA7,
	CompactVisibilityMasksJob_Execute_m7D5B39DEFEC4944D35111B4B7D22267147B4AB1E,
	InstanceCullerSplitDebugArray_get_Counters_mBDB8C933AEEB9F934132442CA20FC54A9E9A01AB,
	InstanceCullerSplitDebugArray_Init_m0E461744D899802E8123D32B79AD2EEC7FF1ED66,
	InstanceCullerSplitDebugArray_Dispose_m0FA405A9BA291E560088D998A71EE3FEF95100C1,
	InstanceCullerSplitDebugArray_TryAddSplits_m8F0CAC17CB3DA747A187760D3961255C1E95BDF5,
	InstanceCullerSplitDebugArray_AddSync_m05D0080857890E3C8E46730B1D4DC17E4FB71D90,
	InstanceCullerSplitDebugArray_MoveToDebugStatsAndClear_mAA78838CCD17348C01F115022EF530C3F5B98AD4,
	InstanceOcclusionEventDebugArray_get_CounterBuffer_mF25E7B744518F980443AFCF79E48EA3CCF852D04,
	InstanceOcclusionEventDebugArray_Init_mF8AEF95440DA22ACC2DB5717CEBD9C8B8B3797D5,
	InstanceOcclusionEventDebugArray_Dispose_m2D57744F19C60D3E9B6EFDF81BB08498853E37C1,
	InstanceOcclusionEventDebugArray_TryAdd_m2FD080E0DDCA404C8C362A5933EC852A9CD5F109,
	InstanceOcclusionEventDebugArray_MoveToDebugStatsAndClear_mFEF02F07D15F2F2942FABFD0C7C577FAB1859977,
	Info_HasVersion_m9E077F93A4F89E728DF4FA88764ABF91C732D960,
	InstanceCuller_Init_mDBBD40C7316EBE16746FACA43170B7E1001EC67F,
	InstanceCuller_CreateFrustumCullingJob_m073380FE489347D6F8CFB772AE33D0599532F54A,
	InstanceCuller_ComputeWorstCaseDrawCommandCount_mF1277DD5CAC20EEE023EA894E84D7C1ADD7B180D,
	InstanceCuller_CreateCullJobTree_m2E52805D7C465D995C4A87D156710394A4BB0EDF,
	InstanceCuller_CreateCompactedVisibilityMaskJob_m3A2F38959953B889E5E7456DE426ABA7134DFB9B,
	InstanceCuller_InstanceOccludersUpdated_mCA7D8171B44CF76E6A48314D7F97F2B698DCCE05,
	InstanceCuller_DisposeCompactVisibilityMasks_mE05A5A23578160F5FDE1F8F2E59B7B8E2819BC76,
	InstanceCuller_DisposeSceneViewHiddenBits_mB8548C25259A3AE546942634429EF581779992BD,
	InstanceCuller_GetCompactedVisibilityMasks_mED15105E7567F79042B542B56B4DE2ABEDE14C26,
	InstanceCuller_InstanceOcclusionTest_m8E12352E27A10F62B9C737C3C7D0394DBC416F15,
	InstanceCuller_EnsureValidOcclusionTestResults_m22DA66230772FAE8FA78B7E2B3839AB414BE8A22,
	InstanceCuller_AddOcclusionCullingDispatch_mDA6F922B0734577B8F921E9AE4C02CEE4F8A32A5,
	InstanceCuller_FlushDebugCounters_m1036A68627B296B0CE88254424A25EEA430BCCE6,
	InstanceCuller_OnBeginSceneViewCameraRendering_m9F271E3A4A411E077FC767C85C1813F41C9ADA3D,
	InstanceCuller_OnEndSceneViewCameraRendering_m01D33D11A4853EB0397A2996F10795662828C2F8,
	InstanceCuller_UpdateFrame_m4273286746BAF0CADC1665DB994C64B47FB32F42,
	InstanceCuller_OnBeginCameraRendering_mB7CDBC0AD43EEC324B9584B96C25DF3B911EC907,
	InstanceCuller_OnEndCameraRendering_mF0DF0261BF1978BAFAC10F5F9DB533FC86B8089B,
	InstanceCuller_Dispose_m7C8B413AE9FD5A5C4EAD81A22D2D4C1F2E6C368F,
	ShaderIDs__cctor_mBB02927959BF46D5749EF53F56D62D1C6736FEF2,
	SetupCullingJobInput_Execute_mDE838174B1F7960CBAD7010EB7FB70CCA168E977,
	InstanceOcclusionTestPassData__ctor_m73F58387AFEBAB53120CC7AEF289EC50A82AFC8C,
	U3CU3Ec__cctor_m5495172327D2C037C7A396EB43420AD62D33BEBE,
	U3CU3Ec__ctor_mBE453CFF0731A8063B406EBCFB4E99157CE39C80,
	U3CU3Ec_U3CInstanceOcclusionTestU3Eb__26_0_mC8CC22455E7B2DCCE2556B495FB257AB2CB3B04B,
	OnCullingCompleteCallback__ctor_m77440340DEF8EC177F2367F9CDFB4C7039B109CD,
	OnCullingCompleteCallback_Invoke_mC230E2F011722DC958EFACC67609C75FFB0A54C8,
	OnCullingCompleteCallback_BeginInvoke_mAA8DD729BE78EF7C06DC33D3714B306D8793E492,
	OnCullingCompleteCallback_EndInvoke_m2ABE1C345A8D041A537AD05BE2B8632D872865A0,
	InstanceCullingBatcherDesc_NewDefault_mC543DB9EBF6418504763D8C66FCD457AC5A8B9AF,
	PrefixSumDrawInstancesJob_Execute_m6384DA1CB6A1E053F358640B9E2FE7F378677771,
	BuildDrawListsJob_IncrementCounter_m2198A8B6F4713D070C44BF162EEAC564C15A120F,
	BuildDrawListsJob_Execute_m731DC4DAB67702B447641FF630CD1C60A596636E,
	FindDrawInstancesJob_Execute_m011CC04D8F9DA0E7E3F04783AA60964898BEDC1D,
	FindMaterialDrawInstancesJob_Execute_m0A8C723C617595319217DB5AFD151AC28F7759A0,
	NULL,
	NULL,
	RemoveDrawInstanceIndicesJob_RemoveDrawRange_m57FBE6346CD44B5B864E6CA89A0549D9C880E652,
	RemoveDrawInstanceIndicesJob_RemoveDrawBatch_mF0A59FED062CDA68A971249F539ACDB5D90DB5A5,
	RemoveDrawInstanceIndicesJob_Execute_m37A6D16D906E5C1A7C3340E64C3ADAB45607EFCC,
	UpdatePackedMaterialDataCacheJob_ProcessMaterial_mCFF81645A1DB74D08DAAD17D679FA00A306E57E3,
	UpdatePackedMaterialDataCacheJob_Execute_m117BE8C4EEAC8E86F3445464E84ECB6A954A3EBB,
	CreateDrawBatchesJob_EditDrawRange_mC8D762596291034355C1B116E92CAF83C5051F96,
	CreateDrawBatchesJob_EditDrawBatch_mAF28C16786B3B72E623304CCAD2B3DF57FDB15E5,
	CreateDrawBatchesJob_ProcessRenderer_m520CC5A20C49B3A0A91F8A06F8D954DD8D860FE0,
	CreateDrawBatchesJob_Execute_m28C7A7916BC78A53B2A02EAF503B94E85BD03F4A,
	CPUDrawInstanceData_get_drawInstances_m5B182A75D8968C69C7EFF2CE773D0029E7412D68,
	CPUDrawInstanceData_get_batchHash_m407E1D37D4A5D8BDE76DE45CF33C53643DA47C12,
	CPUDrawInstanceData_get_drawBatches_m6E62CEC9E106C2CB84B185517A04105E77D875F6,
	CPUDrawInstanceData_get_rangeHash_mF69F0BF1AFACDC62B7466A99F24DBE460E4BB9B6,
	CPUDrawInstanceData_get_drawRanges_m980D02096E99CCD5299D5B06AAEC78E986A92754,
	CPUDrawInstanceData_get_drawBatchIndices_m414A7D8E605519E4B6F5F3B1020BE3B21F991E29,
	CPUDrawInstanceData_get_drawInstanceIndices_m22402BAB913DE193060C175FF253FC5C3D8D0D56,
	CPUDrawInstanceData_get_valid_m224B4D6D342C4EAC30B8F8A5A34837827DEDCE3E,
	CPUDrawInstanceData_Initialize_m1D889D823B6B72F24F03385CBC492D3B18B510E4,
	CPUDrawInstanceData_Dispose_m0F6E3A9F7C46E04F0C1A5073F3C3039BD1E29D3B,
	CPUDrawInstanceData_RebuildDrawListsIfNeeded_m8CE9E2B0870BADE0F90B83C54ED6A206B39F5248,
	CPUDrawInstanceData_DestroyDrawInstanceIndices_m6B1D8988D3CE889D903F736E9814EAFFCCC35FC3,
	CPUDrawInstanceData_DestroyDrawInstances_m6410B734F4067FA0A56142576467F65ACAF30343,
	CPUDrawInstanceData_DestroyMaterialDrawInstances_mC3646BA15537CA5C257F05E06AC409B4ACA4E167,
	CPUDrawInstanceData_NeedsRebuild_m0F632EDB133A8FFB533DB9A5BBCC199DEB9AC11C,
	CPUDrawInstanceData__ctor_m21E4C7096825C181C31F9F5A8600D2CF96F5B239,
	InstanceCullingBatcher_get_batchMaterialHash_mF1798E2B3C1C885996C171F3F4EDFB7DFDC53151,
	InstanceCullingBatcher_get_packedMaterialHash_m12837A329EFD8A76B3E25C2140F516E2847570EC,
	InstanceCullingBatcher__ctor_m98D5EFDC5F3AF7FF61DEE777748DBD66758A239B,
	InstanceCullingBatcher_get_culler_mBFCD2ACBB0F3D4A650233F186A5EB98D47A714D4,
	InstanceCullingBatcher_Dispose_m6A9FED11F52B4FD30BAF1ECE6676B853B9BCCA42,
	InstanceCullingBatcher_GetBatchID_m149BEE4723E1E52B23E9B63AE33960431ADAE18C,
	InstanceCullingBatcher_UpdateInstanceDataBufferLayoutVersion_mFFB18677B049FC0438D8169800EE48CE19594268,
	InstanceCullingBatcher_GetDrawInstanceData_mED58B161705DB4D131F341DB74C65163A6920ABA,
	InstanceCullingBatcher_OnPerformCulling_mC82A6CB4199689882BEFB834A6E8CA9DFEFB02DD,
	InstanceCullingBatcher_OnFinishedCulling_mA2C92EE1760B83D8F49B13F81E6736E488ACBF70,
	InstanceCullingBatcher_DestroyDrawInstances_m53512A8BD8DF31ADE89587FD55D5B4226F9F6A44,
	InstanceCullingBatcher_DestroyMaterials_mF017E0AF2451934C0B8D3D9F7457FCDB3908F087,
	InstanceCullingBatcher_DestroyMeshes_m7CB28EB447BDD8A7EF2780BB3BA51A16239B91BD,
	InstanceCullingBatcher_PostCullBeginCameraRendering_m8C7F421728438AB4A12BF864206727A08A2C30D6,
	InstanceCullingBatcher_RegisterBatchMeshes_m59D3A05E2598309513C7DD095CD30F64C33C6214,
	InstanceCullingBatcher_RegisterBatchMaterials_m14CAC0F48DECBDF0DFAF932DEB94E8522E3F9BD7,
	InstanceCullingBatcher_SchedulePackedMaterialCacheUpdate_m22B6C778DE0258BCEA6BFBB9F9278637010B5A0C,
	InstanceCullingBatcher_BuildBatch_m425E597AED2AEC32D3E4FD0A9458F29695EABF9B,
	InstanceCullingBatcher_InstanceOccludersUpdated_mB9D7CECE86473174B52A1D76E73DC546738C9A44,
	InstanceCullingBatcher_UpdateFrame_mD1B5D19FB7AB428A0E766E54E1E58821EF647457,
	InstanceCullingBatcher_GetCompactedVisibilityMasks_m56336BD173368549DEDC4D743382E3311D29B144,
	InstanceCullingBatcher_OnEndContextRendering_mB4D41FEA107B3D5ECF9AF02F3937726021141A26,
	InstanceCullingBatcher_OnBeginCameraRendering_mE389E1056E80885B9816E9231F9C3C2A42B72221,
	InstanceCullingBatcher_OnEndCameraRendering_m189059E45FB5732F03BAFABF3421CDD68D81DEB1,
	GPUInstanceComponentDesc__ctor_m3D6BFA5A8BBF78939717DABD22CDEDE234780EE0,
	GPUInstanceDataBuffer_NextVersion_m87213EAF8B57C72440D656BD00E246106CD9404F,
	GPUInstanceDataBuffer_get_valid_mBB8F7F2B22AA1450AD0944A8364F19025D0687F1,
	GPUInstanceDataBuffer_CPUInstanceToGPUInstance_m1177E7984777339744077A74E281D84B17704592,
	GPUInstanceDataBuffer_GetPropertyIndex_mF39E38B5B13B5BF4E45934C274E076B4401656DA,
	GPUInstanceDataBuffer_GetGpuAddress_m478AE68E3BE3FC5076A3D8C1D9F2CC20E11FD7EF,
	GPUInstanceDataBuffer_GetGpuAddress_mCDCEF5E738A3FE9E217D94ECA43A2AE5A6380225,
	GPUInstanceDataBuffer_CPUInstanceToGPUInstance_m3C4863A5F6AC91EA128DE5FDD296B3159CCE218C,
	GPUInstanceDataBuffer_GPUInstanceToCPUInstance_m6861C120BA15E1AA9BB2116DDBF09CC4A68BC039,
	GPUInstanceDataBuffer_CPUInstanceArrayToGPUInstanceArray_mE3CD8040A3236B9CA56E2CB69B90C68CB1BE42A3,
	GPUInstanceDataBuffer_Dispose_m338824ADC36E89D59E8D1EC451F00A78337A4165,
	GPUInstanceDataBuffer_AsReadOnly_m7E7EAB66B500E1CAA7AEB2C2F7CAEBE40CCE729F,
	GPUInstanceDataBuffer__ctor_m62C97070C67C69A70905B44F586178FEFB54C95E,
	ReadOnly__ctor_mF532002BB9D890FF1F0582B28EE81FFF81C4843D,
	ReadOnly_CPUInstanceToGPUInstance_m01CDC3239F33A9D1A861D1816930C4A7E71440E5,
	ReadOnly_CPUInstanceArrayToGPUInstanceArray_m246175FBDE0C68B01CF4B8B0F73087368CA7ACF3,
	ConvertCPUInstancesToGPUInstancesJob_Execute_m16B5AACD9BEEFFA2046C2E1999F8C4AA7FF2B1D9,
	GPUInstanceDataBufferBuilder_CreateMetadataValue_m50A13411B4799C2C197B9C4EFD3EED2BB4C25036,
	NULL,
	GPUInstanceDataBufferBuilder_AddComponent_m26713FB7D74F3F47052241EEDE3D95D1502C35E8,
	GPUInstanceDataBufferBuilder_Build_mDEF5AC49115B5D3CD195C5802389B93AE1C25C8C,
	GPUInstanceDataBufferBuilder_Dispose_m1869839D0122EAE7FE7A7F9FE5356CCDE50D6636,
	GPUInstanceDataBufferUploader__ctor_mAD57A016FC2E0940D4A469F418E161A1DCA8CC8C,
	GPUInstanceDataBufferUploader_GetUploadBufferPtr_m0CB3210A88F79E17329FDE29AD57C33C975D55DD,
	GPUInstanceDataBufferUploader_GetUIntPerInstance_mE171D8F73FE5CDE5D3F83C730F31877B8649A59C,
	GPUInstanceDataBufferUploader_GetParamUIntOffset_mF231C740B0554E7A82228DAEA5FE5F6BEBD978FD,
	NULL,
	GPUInstanceDataBufferUploader_AllocateUploadHandles_mAFB40A36A5FDAF5C560EB04B822012E57219A607,
	NULL,
	NULL,
	GPUInstanceDataBufferUploader_SubmitToGpu_m352C6DB1B32BBB2D141C76ECCB19F8FF55940105,
	GPUInstanceDataBufferUploader_SubmitToGpu_m6037322A91258909D757C712390F08395A36CF9C,
	GPUInstanceDataBufferUploader_Dispose_m0D8CBA64EB57BF6554F97E74B238F0A159B574B5,
	UploadKernelIDs__cctor_m5DABBDCC0EDE576865CC927D7810EE469972169A,
	GPUResources_LoadShaders_m9ACB6FC1CAC3C07D223AB04073FEFEBCD55F5CDA,
	GPUResources_CreateResources_m07CBEC1195D765927E59B74DDFF7402B3608E22D,
	GPUResources_Dispose_m2D71F420E19EDC6E791D5FD0BDAA81501C6017E6,
	WriteInstanceDataParameterJob_Execute_mFBD8B3E368892E290A864D47C75EC3958286BC25,
	GPUInstanceDataBufferGrower__ctor_m2555453CA56595D3963837F7226E4F85D11379B1,
	GPUInstanceDataBufferGrower_SubmitToGpu_mF6B94F8512B09B0E57404C840A3C22D621C8EE9A,
	GPUInstanceDataBufferGrower_Dispose_m24A1DE3F1FE59498294C9845783B541018F54BE3,
	CopyInstancesKernelIDs__cctor_mE2A43876DE96902483CD0D2EFE3D31E698AA4715,
	GPUResources_LoadShaders_m4C5A24A42FDCD3D04EBB7C2342E23D14748CBB89,
	GPUResources_CreateResources_mD345D83E87BDDAF07F3A8628B9E1F1860BEC40C3,
	GPUResources_Dispose_mBB023C03E82397C5CF141105338158EEDA6841AF,
	InstanceHandle_get_index_mD1303DDB5E62977480B92E3DCECC534B1465CB94,
	InstanceHandle_set_index_mC56210BD1B26D7B86BCD9BF2CE5341188DC1A234,
	InstanceHandle_get_instanceIndex_mD7BAC819F382904784AC46D1F6A631B4A21CA8B5,
	InstanceHandle_get_type_mAC674106B77C6008F547FBAB2FE02F263D3FA40C,
	InstanceHandle_get_valid_m5629806E66255D75C3B1ED5826B0DA6D13B9E0CE,
	InstanceHandle_Create_mDF57F601CCEF1ED2DBFD880416FE0B5EB625DB2B,
	InstanceHandle_FromInt_m501BC299814E873C1040C63575F9391327992272,
	InstanceHandle_Equals_mB7F950B845D19368217960B6FE7DBEA51B013C9E,
	InstanceHandle_CompareTo_mC8763D64CE799F220739F8D3880F0725ADA9A93D,
	InstanceHandle_GetHashCode_m256554043D1CEC35A1670DA2BDD39B453A4985D9,
	InstanceHandle__cctor_m482B79BDF36DE1A1A2BDB9C9D97F597DE7ED7F77,
	SharedInstanceHandle_get_index_mA8EF25F39F6B747A2984740DC8A6A7F7F3B31606,
	SharedInstanceHandle_set_index_mBD1FDAC50BC4D743EF40A7195E2DF1E3B4BC1840,
	SharedInstanceHandle_get_valid_m723EEF4B52F015C0A6EC4BB621966C396311C865,
	SharedInstanceHandle_Equals_m8318CF0264558ADD64222B3A2593EACCED56BFE0,
	SharedInstanceHandle_CompareTo_m31419B958041797369105E6105847547F39F98C4,
	SharedInstanceHandle_GetHashCode_m5B97E179A78BD59969291F66E286E00873FC120D,
	SharedInstanceHandle__cctor_m1F42E7568683FEA4B78D0F18E1F5707420D6644E,
	GPUInstanceIndex_get_index_m0EDBD4FD5FC090990E2A24DCEBB5346B260C919D,
	GPUInstanceIndex_set_index_m24EF3293A5E4CA20F4186F53B459500CDAE40687,
	GPUInstanceIndex_get_valid_m5DBD00DEF2B390C4DB2E0B41686B6A09A064D27D,
	GPUInstanceIndex_Equals_mEF7254FCD24AAD9A15FD3D1555306A5F1C908D23,
	GPUInstanceIndex_CompareTo_m4CEFE1A49A0D625417B0123D3CA3AE385B785DCF,
	GPUInstanceIndex_GetHashCode_mD3CFF7872E06CD6F455F81B019F03672C213D970,
	GPUInstanceIndex__cctor_mBE79D3EFAD002DBED18492AEDE53BD38FCA142E7,
	InstanceAllocator_get_length_m762D1C624FF0B3D9F1DFED48EEA65465932C49DC,
	InstanceAllocator_set_length_m02BBC305E32676069A1D7855C5FF776FD7F24172,
	InstanceAllocator_get_valid_m86EC99BF39D782407913E9BAC1DB2D7CBA1FD797,
	InstanceAllocator_Initialize_m299908C0326706B4BF44DDEC7CDA3D1074347F01,
	InstanceAllocator_Dispose_mB710FC50CF870DBD3B226AE4B28057364D67237A,
	InstanceAllocator_AllocateInstance_m846D1D4EA89D654F8AD4272579E3A7591FE155AB,
	InstanceAllocator_FreeInstance_m4017808CB3B95F6FC5079EFDCC7CA13217C42E3C,
	InstanceAllocator_GetNumAllocated_mC907C85B73DDEAFAE3606B4099F5C7E808DA8C18,
	InstanceAllocators_Initialize_m1C63A61426D12441A3C4EABEC29B392FC93E90D9,
	InstanceAllocators_Dispose_m0B2675D6FA6E95D9B9CC4617A94F408D9E80F5F7,
	InstanceAllocators_GetInstanceAllocator_mF5B156CDAEDEEC2849590A544A5D99E691D0E37F,
	InstanceAllocators_GetInstanceHandlesLength_mBFEDAC598685E4B38B1E67DECA4E75B8E345ACE0,
	InstanceAllocators_GetInstancesLength_m7F121F6138F0713B01D5379DCA36255DF631D1C3,
	InstanceAllocators_AllocateInstance_m1BA734BD0D1B20F166DEBCCEE8AC5B2B0911D9A4,
	InstanceAllocators_FreeInstance_m0EBB63A12A593C580C54770B70A6032C55237AF6,
	InstanceAllocators_AllocateSharedInstance_mD61A96D871FD88F2826B307B6BBF976223E075CF,
	InstanceAllocators_FreeSharedInstance_m0B4E4FDCEA191C4829065D705C241F8667B658D5,
	CPUInstanceData_get_instancesLength_mEF0DF28231C26F0C2E7EDA6A7115A51F51E5109C,
	CPUInstanceData_set_instancesLength_mA37EF6CFF372799E7F182362873F19898021B186,
	CPUInstanceData_get_instancesCapacity_mB1E847621B15F7559FC491B24F54ABA253A73249,
	CPUInstanceData_set_instancesCapacity_m2BC970C6B7AA237310D79F3994737B40F962EE1D,
	CPUInstanceData_get_handlesLength_mA9DC002A27BBD94B09B5944B84C6F47C30692F48,
	CPUInstanceData_Initialize_mCB28B3C43BE61390B76A1B9C1C79BBFA33A33FD0,
	CPUInstanceData_Dispose_mA6CD2735982440ECA65880116C2D214E97ADFC20,
	CPUInstanceData_Grow_m54A454E32FB7C164E757F05A453DB71BBF6E1907,
	CPUInstanceData_AddUnsafe_m1F5C8C517A8258670937584F1DD66E9D47724BC2,
	CPUInstanceData_InstanceToIndex_m9D4701DC598D9046898239AF94F4E87F6A0F14EE,
	CPUInstanceData_IndexToInstance_m3C95DBA9646291FACE0234AC811183928C232321,
	CPUInstanceData_IsValidInstance_m5B3FF0F8456CF25EE209E68A230D594BB5DB36A3,
	CPUInstanceData_IsFreeInstanceHandle_mEEBB421BA3391592FEF206095FBBC6A4BF6D9589,
	CPUInstanceData_IsValidIndex_m4BE52A79DE124AA4872297A76A0FEA1D114CA010,
	CPUInstanceData_GetFreeInstancesCount_m1F0B5BDAFE8DC1DB434FC5682705494F3EDFE10C,
	CPUInstanceData_EnsureFreeInstances_mB7036C8793D1CB89C1C371A605581CBB4E177484,
	CPUInstanceData_AddNoGrow_mEB9B4B4BCDB7C718DA14898D903B5E0D45DE0455,
	CPUInstanceData_Add_m0736A5D9F85B1EC37E061411CC00335DFCC44D5A,
	CPUInstanceData_Remove_m599064EAE344F332CFE4CD00C9714E72623D6877,
	CPUInstanceData_Set_m47C0F837DC4AFF38FE4A50E426FFE05969B3FC19,
	CPUInstanceData_SetDefault_mDE2B13667675847C626111DE77EDE63FE25B5E42,
	CPUInstanceData_Get_SharedInstance_mE5ED4915B38984690F4AB5089634500FBCB6C3CB,
	CPUInstanceData_Get_LocalToWorldIsFlipped_m19881EEED025A0FC45721F63DFCCC1C462F93107,
	CPUInstanceData_Get_WorldAABB_mF9F14AE6B71FECD3BF51252AB06781687C9531EB,
	CPUInstanceData_Get_TetrahedronCacheIndex_m7D09D9EBD350591CCD9A948AF1F55DA98F85D1B6,
	CPUInstanceData_Get_WorldBounds_m59B12DD936A814F2E056E962A4889CD3FD473417,
	CPUInstanceData_Get_MovedInCurrentFrame_m58A543967F67149E003AD3FE6FE4C45F9E87C869,
	CPUInstanceData_Get_MovedInPreviousFrame_m9A0B9988AF8004828D13C96F89981F3C6AB15C29,
	CPUInstanceData_Get_VisibleInPreviousFrame_m3E67A47CD7703E4345BEE5A7068855FE6A589BB5,
	CPUInstanceData_Set_SharedInstance_mECEE018E02FA2F6502B59197A9AEF35FA2D35081,
	CPUInstanceData_Set_LocalToWorldIsFlipped_mB2B243184FD3C74C54A5D8EC74542BC0E019EF29,
	CPUInstanceData_Set_WorldAABB_m56D85CE12B7C589B053C471E803090DF9CA37073,
	CPUInstanceData_Set_TetrahedronCacheIndex_mAC10B658EB1AB6EF405BC32A6DE8E88F7AA45AD5,
	CPUInstanceData_Set_MovedInCurrentFrame_mB2081F967C2A8B6E6AC80F2E91DB3FD70851346F,
	CPUInstanceData_Set_MovedInPreviousFrame_m096AF4BED24D559F13122047DF89E56C3C431B4D,
	CPUInstanceData_Set_VisibleInPreviousFrame_m53D87829915906D1DD8E19F1F8D51631E9C02620,
	CPUInstanceData_AsReadOnly_m90E90427C88E3F72D123C44F0E7957BACDDA8306,
	ReadOnly_get_handlesLength_m50556F07B2F7144758202F76E202ED795E715DE9,
	ReadOnly_get_instancesLength_m0E85E521D7DB61518C6988B08646386F20A17307,
	ReadOnly__ctor_mFAEAA65432C0711F606AC98FD2320032B60A3309,
	ReadOnly_InstanceToIndex_mA64D7447F6466C660DE83765B1B42673D922CCE2,
	ReadOnly_IndexToInstance_m5BB2CA43E37DBA390AB7F29E6B5357A05226EE1B,
	ReadOnly_IsValidInstance_m560D63CCD78D3661349A6134A30A29EFEDD3C30A,
	ReadOnly_IsValidIndex_m2E036F94E4DFD41DF1CCA4148915594457CF0A9B,
	CPUSharedInstanceData_get_instancesLength_m6C9D2E7F861FF43E23759E8816653646F41038B6,
	CPUSharedInstanceData_set_instancesLength_m62AE1E8890A80D570FD3D11CD9E7C76B9D19124F,
	CPUSharedInstanceData_get_instancesCapacity_mE40EF07D597175079E8DB6639E444A8346BD5C67,
	CPUSharedInstanceData_set_instancesCapacity_mEBAB461BBCD3CD8127252B3F6156431A24943A69,
	CPUSharedInstanceData_get_handlesLength_mF238FD08383695F2D7F707F2FA87279FE939CE61,
	CPUSharedInstanceData_Initialize_m0D70CFA6688D2B1A165CDB20AB1760CBB4604A6E,
	CPUSharedInstanceData_Dispose_m37AA8B81C3E42B81EC8E948B3F3DF9197B75E48C,
	CPUSharedInstanceData_Grow_m0AED29A47C913CD92A9EA6281FABBBE76C4C26F3,
	CPUSharedInstanceData_AddUnsafe_mD66018426B29B8FC77CBA218E04D6470D3A08B69,
	CPUSharedInstanceData_SharedInstanceToIndex_m8D1697AC229D8D0D83FA702F6F440132FA123282,
	CPUSharedInstanceData_IndexToSharedInstance_m7E58052D57FC86E8F4C0571D1213F2D4774BD323,
	CPUSharedInstanceData_InstanceToIndex_mA58DA1C16D52FECF5ED6238B168203837D75F87C,
	CPUSharedInstanceData_IsValidInstance_m33E12C093A6F0194B4D388782B15C2DD64616384,
	CPUSharedInstanceData_IsFreeInstanceHandle_m972EDE9AF42DAADE66DAA3B1E1A8F8A85B594BFA,
	CPUSharedInstanceData_IsValidIndex_m24116C260518DD680C3CDBD5BB1797B7764DDBCD,
	CPUSharedInstanceData_GetFreeInstancesCount_mC18CC336EB3ED96DA46A39019C480485B58A26F5,
	CPUSharedInstanceData_EnsureFreeInstances_m9276B1E0AE6DE0DFAE5F6FA4AFFB7DD9E6939CB0,
	CPUSharedInstanceData_AddNoGrow_mAF3FBC643ABC12255E182EE8F8D26024AD8BBACD,
	CPUSharedInstanceData_Add_m3AFDBA31C2E77E106BD970496C5F0EFBDCB3FCD3,
	CPUSharedInstanceData_Remove_m1BA13D76461FC093E7667EA3746FC4A6C3E99EF9,
	CPUSharedInstanceData_Get_RendererGroupID_m09F08F1763DD649A37D88B169CA50B2BD4F71768,
	CPUSharedInstanceData_Get_MeshID_m70DD07FFBE12B43BA2546547FD26BC268E28EF43,
	CPUSharedInstanceData_Get_LocalAABB_mB7D5A9EA9CD43BBEE3E938D31500A518FD3C02D7,
	CPUSharedInstanceData_Get_Flags_m4F22F6B4DB27966ED5F633612B135EDCD274E341,
	CPUSharedInstanceData_Get_LODGroupAndMask_m4A750431421271D4D2960E84D4B3AD83F96A8A42,
	CPUSharedInstanceData_Get_GameObjectLayer_m75D2D7344A04236BFAE513142B29AB6CC8E7CB4D,
	CPUSharedInstanceData_Get_RefCount_mE216F6749BB1B06E795C52C2433EE85E6C57DC01,
	CPUSharedInstanceData_Get_MaterialIDs_mCE6EC8119990B7324D473B6DFB27B91A076404FC,
	CPUSharedInstanceData_Set_RendererGroupID_m06EFE6568E7FABED464A0A091DB7D8DE05FC8719,
	CPUSharedInstanceData_Set_MeshID_m97990780D2C7B92B6A146C6FB243D08D2674EAA9,
	CPUSharedInstanceData_Set_LocalAABB_mFA0C8491B2FCD9E1F5BB25FF598C7520D689D08C,
	CPUSharedInstanceData_Set_Flags_m9C1CE3AC11B09B3B2C9F65422B2FD31D23C49F4D,
	CPUSharedInstanceData_Set_LODGroupAndMask_mD27421C024B1779830BEFA1274063B72D7C182CA,
	CPUSharedInstanceData_Set_GameObjectLayer_m97AEE383F753133901141CCD0040B7D2C2E41EEC,
	CPUSharedInstanceData_Set_RefCount_m7C5AF143DDC85304B2840A688BB2DD095A03B7CC,
	CPUSharedInstanceData_Set_MaterialIDs_m3C8A0B397DBC9FEFF9661015CC32232A4910D812,
	CPUSharedInstanceData_Set_m5E3312BE2456604D50D30AA09F973F7534EF7197,
	CPUSharedInstanceData_SetDefault_m9241E530C50F996744FC4691DCD87C0E30602237,
	CPUSharedInstanceData_AsReadOnly_mC186E7142A7E53BFE6D90BB0AB1841600BE7CBF8,
	ReadOnly_get_handlesLength_mEAE03E557C81624E1F2CD280610A867E764EE456,
	ReadOnly_get_instancesLength_m82B5F22A5EE9DF8AB94546D3B739C1EB36AD38D6,
	ReadOnly__ctor_mDBC17F900B61DCF328F4BE2CDF0464E97F44614D,
	ReadOnly_SharedInstanceToIndex_m7CD21D39C3285388B428D1CDCAA9AFB10A0178B4,
	ReadOnly_IndexToSharedInstance_mC97CAB3F6DB3C92BCB528F85F5B9E42728DE3389,
	ReadOnly_IsValidSharedInstance_mC2056D855CF0ED6FA050810CE1035680DC453A1A,
	ReadOnly_IsValidIndex_mA6B2DAE40129D5F001698A34D413EE0CEB24E61D,
	ReadOnly_InstanceToIndex_mE76C9EBF3ACCFDD5D73B3A99925CAE88DE2DDBE8,
	SmallIntegerArray_get_Valid_m717ECD9B73FD5A373F40B0A623CB2C8E188012C0,
	SmallIntegerArray_set_Valid_mF9C378DA26AB93E1AB38CCD7371CA23ABB6779D3,
	SmallIntegerArray__ctor_mD7860879B9A093BE8CD0CFD815D125FDB746090E,
	SmallIntegerArray_get_Item_m81A85EC298981F31BB405CA783D90BB96697C2DE,
	SmallIntegerArray_set_Item_mFEF4007D7D6DA125990D22E32B34ADCBF7056EA2,
	SmallIntegerArray_Dispose_m11D070ED0582D69C7F631CAF8C44F93183D73579,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	EditorInstanceDataArrays_Initialize_m9893832043B3485AD59127B93D0872405CF77AFC,
	EditorInstanceDataArrays_Dispose_m16E99865063B2150E7BEFCE3BAE55831D9532B3B,
	EditorInstanceDataArrays_Grow_m57AFD6C22AA320DA1E98981C894EEB1F96C0B041,
	EditorInstanceDataArrays_Remove_mC3970E725DCDCEA7B636C2DCB4732C432787AE35,
	EditorInstanceDataArrays_SetDefault_mBB385CE94C9E46ADADB174CEFB393C2A19245724,
	ReadOnly__ctor_m2029FDC41CDEC78F717316642DB33534904EC6E2,
	PackedMatrix_FromMatrix4x4_mD8BF568A72FAFAA50614FD1F9DA6A7F257CB3E77,
	PackedMatrix_FromFloat4x4_m2EEC3F97BB2E382DD01F31AE83B11D73F2579A9D,
	InstanceDataSystem_get_hasBoundingSpheres_mE95EB4398294EC395CE2A5A16F5D86EF8D86AFBF,
	InstanceDataSystem_get_instanceData_mCB763544E2728F9E48CEF5CB5284044D1C61CDF1,
	InstanceDataSystem_get_sharedInstanceData_m917A6760CCBBEBB27FECC0035926431ED41D1BDF,
	InstanceDataSystem_get_aliveInstances_m24552E5DB0DD7022BEBC44E99BAD4E5B91C3FD89,
	InstanceDataSystem__ctor_m5F7B568C5D6BF6507682A782B497C5DF9AF288E7,
	InstanceDataSystem_Dispose_mD8F0ABE86EC7824BD24020C924702A073024A5FC,
	InstanceDataSystem_GetMaxInstancesOfType_mD0C2B5D78BAA3DF5116E66D663F0AB88A1267928,
	InstanceDataSystem_GetAliveInstancesOfType_mACA5AF484D118330CACC8C0D919BAFDDA30D43FA,
	InstanceDataSystem_EnsureIndexQueueBufferCapacity_mEC63AEE12228511E02036542B749925C591E4190,
	InstanceDataSystem_EnsureProbeBuffersCapacity_m5C8E2190B2C827606936372E985463BC746A65D2,
	InstanceDataSystem_EnsureTransformBuffersCapacity_m8101997E233AADA2DFCA0C139B74927BAD65C221,
	InstanceDataSystem_ScheduleInterpolateProbesAndUpdateTetrahedronCache_m138513395BC490C04056D11A8AE9A4017E69092D,
	InstanceDataSystem_DispatchProbeUpdateCommand_m89CB692F574017CDA489FFDC50B7D021F9BE624A,
	InstanceDataSystem_DispatchMotionUpdateCommand_m6CAB421EB2033825CFE9FF8C0A3F13FF6849BD53,
	InstanceDataSystem_DispatchTransformUpdateCommand_m408F702045F0792E5FAB3D30089984D2AC68492F,
	InstanceDataSystem_DispatchWindDataCopyHistoryCommand_m3CE9A16E6EDD1B23B2A1844EB0C8FE63297FCF44,
	InstanceDataSystem_UpdateInstanceMotionsData_m81BC58CE2698369C68C0E7AC1543A7AE4CD871FA,
	InstanceDataSystem_UpdateInstanceTransformsData_mA9273FAEEACA70AB121D953179312125AD328FCC,
	InstanceDataSystem_UpdateInstanceProbesData_m1CD19D71D15B03FC82F0D5434D43872B6482AEE2,
	InstanceDataSystem_UpdateInstanceWindDataHistory_m9E2E361D86A93AEC4256E9E45E6FF8C25DDEF97E,
	InstanceDataSystem_ReallocateAndGetInstances_mD8B36795100226FED3AFE497FC9DED84FF4A6476,
	InstanceDataSystem_FreeRendererGroupInstances_mDB237F9840CA6B5121A30D5238DEFCBBE2DC7B78,
	InstanceDataSystem_FreeInstances_m1FCCBE915D86469CC20E2C01AE6FB341734F2AF9,
	InstanceDataSystem_ScheduleUpdateInstanceDataJob_mEB4A7B9A770F619108268D0B11ABE99DCEFAC479,
	InstanceDataSystem_UpdateAllInstanceProbes_m2544131305465C5C6DE3956ACE326DC2B9DB05AF,
	InstanceDataSystem_InitializeInstanceTransforms_mF2F8A8EEDBFFA25647574740B190DD2899B5B0F8,
	InstanceDataSystem_UpdateInstanceTransforms_m7A0057B405E3D12CFF3EB78FCB3BE1D1593A0E43,
	InstanceDataSystem_UpdateInstanceMotions_mDCDA88917F5E5B6CC8D8FCFB50744E529C11CDFF,
	InstanceDataSystem_ScheduleQueryRendererGroupInstancesJob_m4C0025CA86226F2D5A23C721CA42E7E8DF4C30B4,
	InstanceDataSystem_ScheduleQueryRendererGroupInstancesJob_m04F92151A520DC0AF8F1FB4B7AFA040C0F625D0E,
	InstanceDataSystem_ScheduleQueryRendererGroupInstancesJob_mB490AA73553E991D855BFC67D1622FE3AC8C098E,
	InstanceDataSystem_ScheduleQuerySortedMeshInstancesJob_m04115ECA07C31067F98B727EE322A1786C70175C,
	InstanceDataSystem_ScheduleCollectInstancesLODGroupAndMasksJob_m9A29F99524770324E8E2896B54E5C08FF4A0979E,
	InstanceDataSystem_InternalSanityCheckStates_m972099150EFFB0CFB52E22F42C4E216C1B012A9B,
	InstanceDataSystem_GetVisibleTreeInstances_m215114432B8645A102573A589C21C9925471A451,
	InstanceDataSystem_UpdatePerFrameInstanceVisibility_m1C6A42FA01165B8F7D05C4179DD093BE19AA4512,
	NULL,
	InstanceTransformUpdateIDs__cctor_m22E50C74A91C8F98F112D7D4E8AD2D3CA77829C5,
	InstanceWindDataUpdateIDs__cctor_mDCE66DBD25DE0B17A7C41D329C4006A0AC407C09,
	QueryRendererGroupInstancesCountJob_Execute_m78EBD1A4D27FC820DB994D726466672AE6698114,
	ComputeInstancesOffsetAndResizeInstancesArrayJob_Execute_m7278A70AAF09ACD7DE06A58BBE53617ED59B3178,
	QueryRendererGroupInstancesJob_Execute_m689B8C96E8D3739314972C71A89F43C5DD8DD5EC,
	QueryRendererGroupInstancesMultiJob_Execute_m3ED5F46C8E2BBCFD2302EBF30F55D07CB4292593,
	QuerySortedMeshInstancesJob_Execute_m0B30D3DBCCB8FE5833D677F735387912A448CCDF,
	CalculateInterpolatedLightAndOcclusionProbesBatchJob_Execute_mB29926232C46662441F96B9405E9BE4D3A84109B,
	ScatterTetrahedronCacheIndicesJob_Execute_m157E3BAA3AA5106CD5629211DB4DF10C941B0942,
	TransformUpdateJob_Execute_m0C99BC8B89AD60C1B495E471F9F02FBEEF7F8342,
	ProbesUpdateJob_Execute_mFACBB95DE46DB9C879C2EE745CE37C4AEFB69AD6,
	MotionUpdateJob_Execute_m266055F93AABA39FD53E7EDBD6BAFE55F586891B,
	ReallocateInstancesJob_Execute_m8E05DC4EC3877696439237F2B9485F5218445828,
	FreeInstancesJob_Execute_mAED5669D78B3996A3298A55A6FF8625F90E6BADF,
	FreeRendererGroupInstancesJob_Execute_m475670DFB6FB0B8F6EFD0FA9ACD4CD17EFF326BD,
	UpdateRendererInstancesJob_Execute_m93597D2184F6460DD4610AD8E47868B1BA87A11C,
	CollectInstancesLODGroupsAndMasksJob_Execute_m2EFF9AB4F3476E491E51143F7F60F79020A0F780,
	GetVisibleNonProcessedTreeInstancesJob_Execute_mEFBAD1E57760EF2F4C2A4F4E6F6F40E6261C284A,
	UpdateCompactedInstanceVisibilityJob_Execute_mDFAB7C16E38743439A74CCA9AA310B383D2BA55B,
	InstanceTypeInfo__cctor_m3B458B3343FBC2FD6C40D4FB48ED7A275F513140,
	InstanceTypeInfo_InitParentTypes_m871A556C0C4609293ABEEBF110E20DD63FCD084C,
	InstanceTypeInfo_InitChildTypes_m172E3471BA713B1205C2B5BD08FC4E1D1518D41E,
	InstanceTypeInfo_GetMaxChildTypeRecursively_m145FABD376E475D616CC58209E97D94E586BC247,
	InstanceTypeInfo_FlattenChildInstanceTypes_m815D9E228DD3FF4C86C5A233A038D551FBC28AC8,
	InstanceTypeInfo_ValidateTypeRelationsAreCorrectlySorted_mD653B8D9D7F845BE0747448F358E441ADBB6D893,
	InstanceTypeInfo_GetParentType_m4EFCC55DA43E58978C6A983D91BCB6FAAF147529,
	InstanceTypeInfo_GetChildTypes_mA2B041904C54BC841991C707960DEF5842EA5093,
	InstanceNumInfo_InitDefault_m8CC8A9E8EBF0EF16311F391E0312C46A7219A920,
	InstanceNumInfo__ctor_m0C64766A7024C367CB84BA96F01861E951351650,
	InstanceNumInfo__ctor_mCFED34B4FC73F15366339611E22502A8366B016C,
	InstanceNumInfo_GetInstanceNum_m42CC8341EB7A73444DB20B9B64ACF5377CA7CE19,
	InstanceNumInfo_GetInstanceNumIncludingChildren_m45CB40CE2452143C10B4FFC0F05FA7751E416368,
	InstanceNumInfo_GetTotalInstanceNum_m55D6043571FB3440889F98A7D638F6A43D060982,
	OccluderDerivedData_FromParameters_mB285C6B3E3FBFB06A8E38D196D06D45FE722D88D,
	OccluderHandles_IsValid_m9FF256155930C1B40D4195C263AF38FCEB1B7904,
	OccluderHandles_UseForOcclusionTest_m8E9F3CDFD218EC3C5A70DA375F156DC75CB2CAAD,
	OccluderHandles_UseForOccluderUpdate_m4376DEB9151FDF5678FDFE2ED525A3B3AC31AF03,
	IndirectBufferAllocInfo_IsEmpty_m47785BE361D9B989BE0455AD1884831AB99E4009,
	IndirectBufferAllocInfo_IsWithinLimits_mE8807A3A2DDE4F3E3E9091E2441D847F4BFC4067,
	IndirectBufferAllocInfo_GetExtraDrawInfoSlotIndex_m2EAD4191867631265610C43212C34A56A2DAD969,
	IndirectBufferContext__ctor_mC7F33D8E6E9DECDB5A167C9CFD68F1CBB6E7D7F5,
	IndirectBufferContext_Matches_m40E2A7974D4B205D4BB770D4A68635D99FE4C721,
	OccluderContext_get_subviewCount_m49FDF73077D5C9F1789BA0C35A39A7F78FCBF414,
	OccluderContext_IsSubviewValid_m88BE2A076AC851E9D11AB02B30ECE80A7E4D6BE0,
	OccluderContext_get_depthBufferSizeInOccluderPixels_mF734AB99EBF484188554B86CB2E07048E6138C36,
	OccluderContext_Dispose_mF02789AFBB76CD0F4491CDD8A83BCD15938F22D8,
	OccluderContext_UpdateMipBounds_m71B416ED1B6D8827B30F8B1C1E69870328E6FCD9,
	OccluderContext_AllocateTexturesIfNecessary_m4F0340120C018852B2A697194F87A6C539D6C127,
	OccluderContext_SetKeyword_m57CB9C813FA45672B4E4EAD297757E2C427EE0EE,
	OccluderContext_SetupFarDepthPyramidConstants_m69DC95369344843C149515C9A2A9312DD0E99571,
	OccluderContext_CreateFarDepthPyramid_mAE9A7D75C802A5AB3F91ED35C2BF61DA52C0AB0F,
	OccluderContext_Import_m59CDBBE79F1A96B17BE009D5D561E35D69DA19D7,
	OccluderContext_PrepareOccluders_m558D900C293C248A3CEE8FE6640C98873039DF1D,
	OccluderContext_GetDebugOutput_m0B03B350C81D50A7DA8CEB969E70AF0F782675C5,
	ShaderIDs__cctor_mAF5448F8A3480811300797984917EC0136A2EEAE,
	InstanceOcclusionTestSubviewSettings_FromSpan_m23AA5216F285965B59FD98CCF986ABB9A0C527C5,
	IndirectBufferContextHandles_UseForOcclusionTest_m098E1C673230461EF27D4BC76A7186A5DADEEDAB,
	IndirectBufferContextStorage_get_instanceBuffer_mD965845788AD240262E1C46BB4BA405B73C8EA27,
	IndirectBufferContextStorage_get_instanceInfoBuffer_mC14C040CC88B2F11AFE1301EB1FB9C70E396E8E5,
	IndirectBufferContextStorage_get_argsBuffer_m81BE13A707FAF6B8F9361AEBCDD9CBA69611C334,
	IndirectBufferContextStorage_get_drawInfoBuffer_m0168780ED5CFCB3613F04F2C00561471352A5790,
	IndirectBufferContextStorage_get_visibleInstanceBufferHandle_mBD86573F23F6B0F8BF6C46C2996C449E571DC87F,
	IndirectBufferContextStorage_get_indirectArgsBufferHandle_m7DF465FC2B94D42E1ABE0EE29E0EE4A1C850E925,
	IndirectBufferContextStorage_ImportBuffers_mB024C5F1494D76DFEC07CDE6065AC7ACBB35E265,
	IndirectBufferContextStorage_get_instanceInfoGlobalArray_m6D87D347531D8E3C00FB0967CCAEE69FED54FC41,
	IndirectBufferContextStorage_get_drawInfoGlobalArray_mF90CEAECE8B83E043E462A4495421A574CC257D4,
	IndirectBufferContextStorage_get_allocationCounters_mF22684F6D0214AE0755E3DFA8BA047C9E63C70FC,
	IndirectBufferContextStorage_Init_mE1D9E857917AF5759F2E08D6B008C991CE5558AF,
	IndirectBufferContextStorage_AllocateInstanceBuffers_m83064D5ABC47473BE4547D8CDB301CC989362AD4,
	IndirectBufferContextStorage_FreeInstanceBuffers_mE035A24BD6179866735657D3D74C416AD39C597D,
	IndirectBufferContextStorage_AllocateDrawBuffers_mCB3267B00E8328A5031AAB859542061D665CE0A0,
	IndirectBufferContextStorage_FreeDrawBuffers_mAB74E77AD8B6348D492585D0ACB5AE42C2CEB171,
	IndirectBufferContextStorage_Dispose_mD7278C175C4C23BCB9190D68CE8343B543A24302,
	IndirectBufferContextStorage_SyncContexts_mB63C2465AF6D851CA68041FA1C12E24F24874A32,
	IndirectBufferContextStorage_ResetAllocators_m327B8163AEEAFE360011EAEBFBD80C96C6403467,
	IndirectBufferContextStorage_GrowBuffers_mAB6F1A326FB73F38678D592C809333FAE5A25D44,
	IndirectBufferContextStorage_ClearContextsAndGrowBuffers_m0B059924A2DB871C3C80B03DAF54A47C8A47E573,
	IndirectBufferContextStorage_TryAllocateContext_mC8D472C07C986BDCFC55CF8C4CE324230A38618C,
	IndirectBufferContextStorage_TryGetContextIndex_m848B7C96E09D5CE2C6D12221D0B78EA4DCE77EF1,
	IndirectBufferContextStorage_GetAllocInfoSubArray_m053D08700A1B5D4DB1AAF31B508E33E9FF418A4D,
	IndirectBufferContextStorage_GetAllocInfo_m9BF31C85DBB94B0D0F1B82AF66DA8493AB1B2595,
	IndirectBufferContextStorage_CopyFromStaging_mE99297FD5192A7F628FD7DC704E877280DB03891,
	IndirectBufferContextStorage_GetLimits_m2E8DDC54719944794D7342725107396F59D5CF71,
	IndirectBufferContextStorage_GetBufferContext_mD76FD0DD95CEE4D7D9B62F81573C4A05A5519941,
	IndirectBufferContextStorage_SetBufferContext_mEC8E692A99E595592E12F55F94B7E234A340DC2A,
	UpdateLODGroupTransformJob_Execute_m10AD64037A0D9FFAB351270A412B4B4342A6B8C5,
	AllocateOrGetLODGroupDataInstancesJob_Execute_mF977F85F444872F9F0E507A0DADE7F2550050CA7,
	UpdateLODGroupDataJob_Execute_m37868AFCE3AFCC80D46C8156CA2713096FE5EC3D,
	FreeLODGroupDataJob_Execute_mABCBCD7B1C65E4A56F7F608884D1F9FB4FA1AAF7,
	LODGroupDataPool_get_lodGroupDataHash_m62280E732F32C5C35B2DADCD304E46662939F045,
	LODGroupDataPool_get_lodGroupCullingData_m9D4FE39BAD4D72923936ABBBEBEEF7F2F3131865,
	LODGroupDataPool_get_crossfadedRendererCount_m1851897792114FF4241A4099060D707ECAD45334,
	LODGroupDataPool_get_activeLodGroupCount_m97904EE5C95159152B6C0A1ABC068C06B5079CD4,
	LODGroupDataPool__ctor_m41A2B2D9392893C14F8F1CC08EDE34AE43CDBC8C,
	LODGroupDataPool_Dispose_m018568FAFC3BCCE2F577FC92B6A3223CC585AA91,
	LODGroupDataPool_UpdateLODGroupTransformData_mA548FB2A357D0A1CF586FBD7B3D04B928BCE005A,
	LODGroupDataPool_UpdateLODGroupData_mF09A39F868F16124B4F2503B1F725FE54AE7A96B,
	LODGroupDataPool_FreeLODGroupData_m900936DC26BBC6F1ABF60871DAF69D93FB79C900,
	LodGroupShaderIDs__cctor_m317D81DF99F20606D1C93B871FD9CE2083C6C42A,
	LODGroupRenderingUtils_CalculateFOVHalfAngle_m419B49ED9977DF739E0E1E39573AC58D40C261BB,
	LODGroupRenderingUtils_CalculateScreenRelativeMetric_m3FBB837700CB21D0CE4207EAF4EC7727E9471BBF,
	LODGroupRenderingUtils_CalculatePerspectiveDistance_mCAF3705B907D10EF33B1FBC16B63C6E31E9EF722,
	LODGroupRenderingUtils_CalculateSqrPerspectiveDistance_m4D8787AC075B2D624D9594A40CF26541FF3261AD,
	LODGroupRenderingUtils_GetWorldReferencePoint_m52B6D8ACD8B14067173FA1CE5C9DB3BC69142F0A,
	LODGroupRenderingUtils_GetWorldSpaceScale_m8716B8D01BB1BA0E22EDEBADCD7ACE00F7C2F5EE,
	LODGroupRenderingUtils_GetWorldSpaceSize_m88BB3F93D7EB22A6D89863821D5B13ED0F9D9104,
	LODGroupRenderingUtils_CalculateLODDistance_m8E6D74E4D1F593158767E34CE33DC2379ED13B86,
	OcclusionTestComputeShader_Init_mC423CA21A41E4B44DB2715096F04D7498A80E960,
	SilhouettePlaneCache_Init_mF14F33F1C7D6CD3704478C92314526747ABDFF0C,
	SilhouettePlaneCache_Dispose_m52386469CD058770AAD5B4E19ADF603598BECEC5,
	SilhouettePlaneCache_Update_m08599EDAF7CC1D053E0E54A8DF15F55E1E0B6342,
	SilhouettePlaneCache_FreeUnusedSlots_m2249464604B48996B77945B9BBCDBDCECD2074C2,
	SilhouettePlaneCache_GetSubArray_m0527F754CEEB54300C083A4BDAE9B56D026DA63C,
	Slot__ctor_m0E99B79099FDB0098404A2FD223A9C029CFFF5D1,
	OcclusionCullingCommon_Init_mB12BBAEE22EA6EA4C93640CF113484C45AB21128,
	OcclusionCullingCommon_UseOcclusionDebug_mB3DD90044DC771F1A74BDEAC59C921AE66E9311D,
	OcclusionCullingCommon_PrepareCulling_mB0789630787C7E0CD26370E649348B1C2E368B0C,
	OcclusionCullingCommon_SetDepthPyramid_mD653D7921DC4590B1E5DDC848F3B3DDF10D15D07,
	OcclusionCullingCommon_SetDebugPyramid_m7DB573CC2B23E59F6E09FE953D2953447FB4D8BE,
	OcclusionCullingCommon_RenderDebugOcclusionTestOverlay_mFC06DC3F4302109DCBCE0016F77FDC7221C0F850,
	OcclusionCullingCommon_RenderDebugOccluderOverlay_mDCEE8545488D66BAFEEC82CA0A5B078EF76F1719,
	OcclusionCullingCommon_DispatchDebugClear_mD07E3E63ABEB291DB36385737735511B88AD3AC2,
	OcclusionCullingCommon_PrepareOccluders_mB04E538ADB8D350F2F77C2B0AEB3235B5537C78A,
	OcclusionCullingCommon_CreateFarDepthPyramid_mA599495FF407F8137E6B40745EFA5296FD390859,
	OcclusionCullingCommon_UpdateInstanceOccluders_m66590207897221E9FA80265BBEB4E9E40708646D,
	OcclusionCullingCommon_UpdateSilhouettePlanes_m4576EBD18929EC7B7AAA98EA599CEB053033161E,
	OcclusionCullingCommon_GetOcclusionTestDebugOutput_m3F8B14753A940E66F3378EE0A13B467CD5B54163,
	OcclusionCullingCommon_UpdateOccluderStats_mFCE4F68D13AD834D837ACC6CF5818BB454DEB374,
	OcclusionCullingCommon_HasOccluderContext_m24FD8FB63CF4F73E28369A7C5E4AB1A4B0C6EF90,
	OcclusionCullingCommon_GetOccluderContext_m5FA55C98ABA809491877468967428AEA6ED50AA9,
	OcclusionCullingCommon_UpdateFrame_m62E1615FE4BB0184C70EF0D5A1B5341A9E6B439E,
	OcclusionCullingCommon_NewContext_m192A0843FCB88873DB0DBC0D30E85E34D9CD3724,
	OcclusionCullingCommon_DeleteContext_mD0DD525EF7A79EDEC506F1FD27762960E7A9D773,
	OcclusionCullingCommon_Dispose_mA5C16ABDC8FFDCBDF1B0BBDAAF046EB707CAB0BE,
	OcclusionCullingCommon__ctor_m3B0C90E1EF8186EB97881C43D58E13303CACED1C,
	OcclusionCullingCommon__cctor_m65EF7B748745B32F17F979959B56ABA54B68E19D,
	OcclusionCullingCommon_U3CRenderDebugOcclusionTestOverlayU3Eb__29_1_m9B31475AE7F1F1FB5043C7E6AE2AB37D0D901037,
	ShaderIDs__cctor_mC4B7BFD4D1A496F04AC567A1D343648AF9932CDD,
	OcclusionTestOverlaySetupPassData__ctor_m319029C880BDA7B70BBB48CCC52A6DEEE84BC7AA,
	OcclusionTestOverlayPassData__ctor_m0D63CEF912BF6F987D0718384ED42945529D5FE0,
	OccluderOverlayPassData__ctor_m2BBEDE9EE87B99D51BD3A55ADE85B0FF7191D88E,
	UpdateOccludersPassData__ctor_m780741CED9AA7DEA6E7F15F1125830643B0940A5,
	U3CU3Ec__cctor_m69E4B9D0362E234583DB9D7CC8D28B7B958F008D,
	U3CU3Ec__ctor_m7AF3A5B26F1D35F52C4E1518DCB55AF32705CA12,
	U3CU3Ec_U3CRenderDebugOcclusionTestOverlayU3Eb__29_0_m6B98C8D250CCC733E809FCD7A6BEF46BE6416D27,
	U3CU3Ec_U3CRenderDebugOccluderOverlayU3Eb__32_0_m000074A9983218A19ECAA6BBF27D4DE6F0CEC6EC,
	U3CU3Ec_U3CUpdateInstanceOccludersU3Eb__37_0_m07755DD078337F25892B35E882F36CF2D77C600B,
	OcclusionCullingCommonShaderVariables__ctor_m6098CCD0E939B2F9DE8715FF129DAE892745C610,
	RenderersBatchersContextDesc_NewDefault_m60D7888149F2142AA66FECE97FDB3D098A3EC7DA,
	RenderersBatchersContext_get_renderersParameters_mFAD826F98D88258ACEB3BAAB3BCA506E9DA8C54E,
	RenderersBatchersContext_get_gpuInstanceDataBuffer_m58A374780F991800398A3A5C43B900F17E49CA78,
	RenderersBatchersContext_get_activeLodGroupCount_m67F17132BF666120D9AD4C4AF2B5332DA3C3B3E8,
	RenderersBatchersContext_get_defaultDescriptions_m9E1716E5F3F0528BCEF408D48F5114377A4449D5,
	RenderersBatchersContext_get_defaultMetadata_m2A1B42632AB9F8A3B0E162912B6E1E542AC52A78,
	RenderersBatchersContext_get_lodGroupCullingData_m71D5CF459C1ED069E69F643AEF402CE8684925ED,
	RenderersBatchersContext_get_instanceDataBufferVersion_m9F26A5C73EE9A8C8848F3163AAF3B50FBC96EFE3,
	RenderersBatchersContext_get_instanceDataBufferLayoutVersion_m633BE48CAD9AA78DD46E20B2208647B3A94D992D,
	RenderersBatchersContext_get_crossfadedRendererCount_m6D1B5BC5F0E565A3A4CFE7AB9BFF448DFE7BF58A,
	RenderersBatchersContext_get_cachedAmbientProbe_m122AB618901D9C67E31A3E1994C09FAE04AEAFE1,
	RenderersBatchersContext_get_hasBoundingSpheres_mA6745C1F53546E926C85BC0B69E1E176E5C07B54,
	RenderersBatchersContext_get_instanceData_mA110F9896EEF3B8277350408C9554A9CA4BBAA1F,
	RenderersBatchersContext_get_sharedInstanceData_m657B7F8E58C1857C9A941039A9C87EDEE14BE073,
	RenderersBatchersContext_get_instanceDataBuffer_m085CC45CC334F7C4AFFC82F08FE9041267BC3FC0,
	RenderersBatchersContext_get_aliveInstances_m464BB51D736CC6E53816E92B54FA52E20A6AB992,
	RenderersBatchersContext_get_smallMeshScreenPercentage_m20E6B516780C91E3EFFF054223A2AD8259D67CEA,
	RenderersBatchersContext_get_resources_m384802C47C8866FE84F3D19892ED70D03CAD5CF2,
	RenderersBatchersContext_get_occlusionCullingCommon_mB5106ABB84E6D34B14EBA467B292E39DDCB60C1D,
	RenderersBatchersContext_get_debugStats_m26AAE0C2CF41DBE02DD210D1FDDB808F8A88CB87,
	RenderersBatchersContext__ctor_m0284FF6010F6BE127276B918BCB7F8D488D82C33,
	RenderersBatchersContext_Dispose_mD6CFED69D7F9007FBA28516C2A6CCD9394D1FC3E,
	RenderersBatchersContext_GetMaxInstancesOfType_mEF99113F1507ABC8426119B2F16B92114F19E1B3,
	RenderersBatchersContext_GetAliveInstancesOfType_mAB16FC96B0BC9357E0DC9FA279AD4844AE0BBD60,
	RenderersBatchersContext_GrowInstanceBuffer_m72EEF32E7D68892D6B6C686290FB074274AF33AD,
	RenderersBatchersContext_EnsureInstanceBufferCapacity_mE609DC40C454449FDFCD61C0347BF4F4C7CFC395,
	RenderersBatchersContext_UpdateLODGroupData_mC3BBC143D600124BC3536CAFE8ADA3D80B9F4E1E,
	RenderersBatchersContext_TransformLODGroupData_m910C251DDACF06457FAB5E90FFE94CB76C84004E,
	RenderersBatchersContext_DestroyLODGroups_m2F2BB8BC930C966F0C1FD6392D669D26B2967675,
	RenderersBatchersContext_UpdateLODGroups_mCC2A5E08EF4A3A88B195D71F252997FAE8255490,
	RenderersBatchersContext_ReallocateAndGetInstances_m75003DE54327AFC9FC9226F543E2AA42ED4CA436,
	RenderersBatchersContext_ScheduleUpdateInstanceDataJob_m52A9965BBC3ACB0F00144C8D39E46478543B623B,
	RenderersBatchersContext_FreeRendererGroupInstances_m1BBD1A75AFD3CED5F347ED940D15EF20D303EA17,
	RenderersBatchersContext_FreeInstances_m8D8AFCF6F9AD2F684CBFCD5B9126C77B9BA856E0,
	RenderersBatchersContext_ScheduleQueryRendererGroupInstancesJob_mEE8968FD91E2F49D2AE33D4A3D0E8C745FF489E4,
	RenderersBatchersContext_ScheduleQueryRendererGroupInstancesJob_m41084D427885CB440E7ACDD227EF915E37B24FA3,
	RenderersBatchersContext_ScheduleQueryRendererGroupInstancesJob_m07B14A5915E64E55DB6DEE709DABD8446F320E13,
	RenderersBatchersContext_ScheduleQueryMeshInstancesJob_mEF8DDD764AFBFBFA23B708EF823511E8CD748966,
	RenderersBatchersContext_ChangeInstanceBufferVersion_m8BDA9E1B471724D930283E832CDC1C4D1172499C,
	RenderersBatchersContext_CreateDataBufferUploader_mE83CA3760B15FBD7BD9E8166D38C01ACA6DC4385,
	RenderersBatchersContext_SubmitToGpu_m7D51CAAFDF4D04FDB49B81F907ADA5C0023909BF,
	RenderersBatchersContext_SubmitToGpu_m522529681D96803ECD637E642083FD54D8FBAAB6,
	RenderersBatchersContext_InitializeInstanceTransforms_m3346F85D05A58656054559FF0D221E5F5D42C813,
	RenderersBatchersContext_UpdateInstanceTransforms_m83DE2D5F845C8D5C10B3E6B809BE32E00E1607AE,
	RenderersBatchersContext_UpdateAmbientProbeAndGpuBuffer_m9635A08E6A72E53938EA2C332B7F37BFD6925535,
	RenderersBatchersContext_UpdateInstanceWindDataHistory_m08DA4EE6C170DEA9C8A9B876071CEB4804438173,
	RenderersBatchersContext_UpdateInstanceMotions_m597C9A66CF49C8F6A010D5D7D0E866657DA207ED,
	RenderersBatchersContext_TransformLODGroups_mB0CB4CD84FB8FF1E35821FD3CB869166ED7D5B7D,
	RenderersBatchersContext_UpdatePerFrameInstanceVisibility_mBD8E7669A22B6C1D47BD0BF3BDC5E22BDD16FBB2,
	RenderersBatchersContext_ScheduleCollectInstancesLODGroupAndMasksJob_mD6FC667C7E0C513173E0720521FD54C3A385737A,
	RenderersBatchersContext_GetRendererInstanceHandle_mF6127D9881FD12DFF2E5AB4132343A50E46E3FE3,
	RenderersBatchersContext_GetVisibleTreeInstances_m5C91EC91858A7EA240EF72E870C8C6A14D1FCC7F,
	RenderersBatchersContext_GetInstanceDataBuffer_m7164DAD5855B34AA94DC599A67E3FCC547C6FC1E,
	RenderersBatchersContext_UpdateFrame_mCFA782A62647ADD043E3247EFF36079A2426DAD4,
	RenderersParameters_CreateInstanceDataBuffer_m945CE4EF304375414A46DDED06474BFC3132D971,
	RenderersParameters__ctor_mD3445734819B50610F768B8E6EF49822D5ABEE8A,
	RenderersParameters__cctor_m8D5D5734DCF7E98603C17A197EC062D2B1D88F05,
	RenderersParameters_U3C_ctorU3Eg__GetParamInfoU7C14_0_mD43A1760BB14DE3AF585F6E664A7641CA2E4560F,
	ParamNames__cctor_mAEF822BDB14694895783B71D8EACF1EEC9B15C91,
	ParamInfo_get_valid_m198E32DB1AAD1F43EAD6964E4EF79E79D078AF7D,
	NULL,
	NULL,
	ParallelBitArray_get_Length_m82FE0E2AC9FAB29DA67E28FFAEA04EB642955B08,
	ParallelBitArray_get_IsCreated_m31B6CFD3C95548F523C3D074463B827B7CD7A535,
	ParallelBitArray__ctor_m7BB9EA31D2DF48FD4BE5D8773C539A76C5D4E6E4,
	ParallelBitArray_Dispose_m24CBECA125F3D0090E9786E6AF56CB2E6DE452C6,
	ParallelBitArray_Dispose_m3806D56E9788D3C69BD6C6985C6E2F949A89F9B7,
	ParallelBitArray_Resize_mA268182EEF9B41198BFC3780A74CF12D9B232011,
	ParallelBitArray_Set_mEDB30931801E1F71F11F62CCB23F3537EEE0F1EA,
	ParallelBitArray_Get_m250C22A6191BAF4C5B314EFAF451E391D251F2B0,
	ParallelBitArray_GetChunk_m7303392F0138448DF74E7A709F38B500B9461ED2,
	ParallelBitArray_SetChunk_m48943193199714BCF0925DD8E14C8EB651885629,
	ParallelBitArray_InterlockedReadChunk_m19B068CDEB9686FE2DD8A42BF99D2011EEAFA84F,
	ParallelBitArray_InterlockedOrChunk_m51A85AD8A6A6FFD317303DEABA6B9797B79CE658,
	ParallelBitArray_ChunkCount_mE27E6F3D861AF09C5B31BD7F3964796B26A9C3B5,
	ParallelBitArray_GetSubArray_m45E14868BB90EC98D0467ABDA3DAD1BD4BFC49DD,
	ParallelBitArray_GetBitsArray_m31F100FDDB1EA0FE4E1592768ED14B843D0DD73D,
	ParallelBitArray_FillZeroes_mDAE3DE6ACB91DE00B5DC5D50415B7A0F938A08BB,
	ParallelSortExtensions_ParallelSort_m237D06D0D0DA504CE809A6FF2D2CEF9CE0221A08,
	ParallelSortExtensions_U3CParallelSortU3Eg__SwapU7C2_0_mDD868A15D4BFD33E6DFF6107497D4EB6EE040E16,
	RadixSortBucketCountJob_Execute_mF3ADEB0523C3DE92CB5CFEF01B65E72C9AB7C023,
	RadixSortBatchPrefixSumJob_AtomicIncrement_m89775B1090C6296097B6445BC76D2C6BE88F199E,
	RadixSortBatchPrefixSumJob_JobIndexPrefixSum_m9C47BE4B67FCFF29A8FA94D39589F9B9A2840EE3,
	RadixSortBatchPrefixSumJob_Execute_m434849692F7D93EF83545890B59FC96BF14AED93,
	RadixSortPrefixSumJob_Execute_mF1969BD6160F81BA429AF74A8944935FC83BC551,
	RadixSortBucketSortJob_Execute_mDB0AB3CD468DA898E41CBF3E9EF5BE26AD26E4D2,
	__JobReflectionRegistrationOutput__15867191014387474753_CreateJobReflectionData_m61B92F5EF70DF366B7640CA4487293699C2E2A18,
	__JobReflectionRegistrationOutput__15867191014387474753_EarlyInit_mCAFBD6F04F7737F01B0CA94B81910948BEB121CB,
};
extern void AABB_get_min_m209EF3ECD01E859258E1BDE8780CA4C25276DF70_AdjustorThunk (void);
extern void AABB_get_max_mCD29B3A2C2DEA60BBF50091D8E3C938511D82B08_AdjustorThunk (void);
extern void AABB_ToString_mB0C529C757F63FD0F78316C90D2B86206159B678_AdjustorThunk (void);
extern void ReceiverPlanes_LightFacingFrustumPlaneSubArray_m995D1E681E0ED63DFA8489C582A2FCBAF1C9356B_AdjustorThunk (void);
extern void ReceiverPlanes_SilhouettePlaneSubArray_m851F735918EE9297C64815C42AAD446BCBE55F4B_AdjustorThunk (void);
extern void ReceiverPlanes_Dispose_mE53DCBAD81DDB002760FC5661F3B26330B62ACF4_AdjustorThunk (void);
extern void FrustumPlaneCuller_Dispose_m802E9F4605F17D55D46C6C0D308830667DE0A08B_AdjustorThunk (void);
extern void PlanePacket4__ctor_m1B67A6C35FB1B6B6BE21D76B496DA3531F636CC7_AdjustorThunk (void);
extern void ReceiverSphereCuller_Dispose_mFCC83320D29032DBC7A3173ADFE8F764A472FFA5_AdjustorThunk (void);
extern void ReceiverSphereCuller_UseReceiverPlanes_mEA2EE8FE32563F3FBE40D6AADA9724719186674E_AdjustorThunk (void);
extern void ClassifyMaterialsJob_Execute_m94CBFED114435BCA576F6A9344BD213DCAC28DA6_AdjustorThunk (void);
extern void FindUnsupportedRenderersJob_Execute_m69B13CFCB49D64142693707BD2ABEE5BA954B26E_AdjustorThunk (void);
extern void FindRenderersFromMaterialJob_Execute_mBEE77639F82D94D447BBCD74866B3A1FC106CDC3_AdjustorThunk (void);
extern void GetMaterialsWithChangedPackedMaterialJob_Execute_mDCCF53C7F3BF79C9F789EEE83784BC5763F6413F_AdjustorThunk (void);
extern void OcclusionCullingSettings__ctor_mE814849AC60B1DC1AC17E02F1AF2128DF38FE95A_AdjustorThunk (void);
extern void OccluderSubviewUpdate__ctor_m482C60BE94ECE0B1F15E930936345D9F60E399A8_AdjustorThunk (void);
extern void OccluderParameters__ctor_mBBA1CD9856BD109C9F1D18715B28E2DA95CE83D9_AdjustorThunk (void);
extern void RangeKey_Equals_m05E612C122D91758588CEB7529751045E6F09493_AdjustorThunk (void);
extern void RangeKey_GetHashCode_mC38845B6A1CC657D6F6B1149E7448AA6A3EF3257_AdjustorThunk (void);
extern void DrawKey_Equals_mBA2838871085C8944B81322DC6E851F4273E8E91_AdjustorThunk (void);
extern void DrawKey_GetHashCode_m6E3C5F3D42D02D8035AA7E7B5501FDBC1551F4E5_AdjustorThunk (void);
extern void BinningConfig_get_visibilityConfigCount_m0B698DBD65F8147B56C14CA7CFB741EAB701B3B9_AdjustorThunk (void);
extern void CullingJob_CalculateLODVisibility_mEBC1FA7E67148AD8CCB4D54FBAB3CF92DDF72A0E_AdjustorThunk (void);
extern void CullingJob_CalculateVisibilityMask_m285DA7E28A8582C95BAE2C6794C34FC3F8F195ED_AdjustorThunk (void);
extern void CullingJob_Execute_m172CC218FA8F7198BD69C1C87294FD90D94DB884_AdjustorThunk (void);
extern void AllocateBinsPerBatch_IsInstanceFlipped_m5ED8B8D355142D10DB3983D15A256B5DBD4D6ABA_AdjustorThunk (void);
extern void AllocateBinsPerBatch_Execute_m8244D8F2860ED03B023A6D348D9471F2AF76E0F1_AdjustorThunk (void);
extern void PrefixSumDrawsAndInstances_Execute_m8EADFE8897EAE00866B67F8AE64CA774EE33B5F9_AdjustorThunk (void);
extern void DrawCommandOutputPerBatch_EncodeGPUInstanceIndexAndCrossFade_m6EFB0D6BB29BF2F65B66913BD2F82E4364ED8830_AdjustorThunk (void);
extern void DrawCommandOutputPerBatch_IsInstanceFlipped_m1AE2BD53A7D17783F262A94539417F423FD4CF79_AdjustorThunk (void);
extern void DrawCommandOutputPerBatch_Execute_mF995D35E2E640A3172F4BC62E37BF103CFBBBBA7_AdjustorThunk (void);
extern void CompactVisibilityMasksJob_Execute_m7D5B39DEFEC4944D35111B4B7D22267147B4AB1E_AdjustorThunk (void);
extern void InstanceCullerSplitDebugArray_get_Counters_mBDB8C933AEEB9F934132442CA20FC54A9E9A01AB_AdjustorThunk (void);
extern void InstanceCullerSplitDebugArray_Init_m0E461744D899802E8123D32B79AD2EEC7FF1ED66_AdjustorThunk (void);
extern void InstanceCullerSplitDebugArray_Dispose_m0FA405A9BA291E560088D998A71EE3FEF95100C1_AdjustorThunk (void);
extern void InstanceCullerSplitDebugArray_TryAddSplits_m8F0CAC17CB3DA747A187760D3961255C1E95BDF5_AdjustorThunk (void);
extern void InstanceCullerSplitDebugArray_AddSync_m05D0080857890E3C8E46730B1D4DC17E4FB71D90_AdjustorThunk (void);
extern void InstanceCullerSplitDebugArray_MoveToDebugStatsAndClear_mAA78838CCD17348C01F115022EF530C3F5B98AD4_AdjustorThunk (void);
extern void InstanceOcclusionEventDebugArray_get_CounterBuffer_mF25E7B744518F980443AFCF79E48EA3CCF852D04_AdjustorThunk (void);
extern void InstanceOcclusionEventDebugArray_Init_mF8AEF95440DA22ACC2DB5717CEBD9C8B8B3797D5_AdjustorThunk (void);
extern void InstanceOcclusionEventDebugArray_Dispose_m2D57744F19C60D3E9B6EFDF81BB08498853E37C1_AdjustorThunk (void);
extern void InstanceOcclusionEventDebugArray_TryAdd_m2FD080E0DDCA404C8C362A5933EC852A9CD5F109_AdjustorThunk (void);
extern void InstanceOcclusionEventDebugArray_MoveToDebugStatsAndClear_mFEF02F07D15F2F2942FABFD0C7C577FAB1859977_AdjustorThunk (void);
extern void Info_HasVersion_m9E077F93A4F89E728DF4FA88764ABF91C732D960_AdjustorThunk (void);
extern void InstanceCuller_Init_mDBBD40C7316EBE16746FACA43170B7E1001EC67F_AdjustorThunk (void);
extern void InstanceCuller_CreateFrustumCullingJob_m073380FE489347D6F8CFB772AE33D0599532F54A_AdjustorThunk (void);
extern void InstanceCuller_ComputeWorstCaseDrawCommandCount_mF1277DD5CAC20EEE023EA894E84D7C1ADD7B180D_AdjustorThunk (void);
extern void InstanceCuller_CreateCullJobTree_m2E52805D7C465D995C4A87D156710394A4BB0EDF_AdjustorThunk (void);
extern void InstanceCuller_CreateCompactedVisibilityMaskJob_m3A2F38959953B889E5E7456DE426ABA7134DFB9B_AdjustorThunk (void);
extern void InstanceCuller_InstanceOccludersUpdated_mCA7D8171B44CF76E6A48314D7F97F2B698DCCE05_AdjustorThunk (void);
extern void InstanceCuller_DisposeCompactVisibilityMasks_mE05A5A23578160F5FDE1F8F2E59B7B8E2819BC76_AdjustorThunk (void);
extern void InstanceCuller_DisposeSceneViewHiddenBits_mB8548C25259A3AE546942634429EF581779992BD_AdjustorThunk (void);
extern void InstanceCuller_GetCompactedVisibilityMasks_mED15105E7567F79042B542B56B4DE2ABEDE14C26_AdjustorThunk (void);
extern void InstanceCuller_InstanceOcclusionTest_m8E12352E27A10F62B9C737C3C7D0394DBC416F15_AdjustorThunk (void);
extern void InstanceCuller_EnsureValidOcclusionTestResults_m22DA66230772FAE8FA78B7E2B3839AB414BE8A22_AdjustorThunk (void);
extern void InstanceCuller_AddOcclusionCullingDispatch_mDA6F922B0734577B8F921E9AE4C02CEE4F8A32A5_AdjustorThunk (void);
extern void InstanceCuller_FlushDebugCounters_m1036A68627B296B0CE88254424A25EEA430BCCE6_AdjustorThunk (void);
extern void InstanceCuller_OnBeginSceneViewCameraRendering_m9F271E3A4A411E077FC767C85C1813F41C9ADA3D_AdjustorThunk (void);
extern void InstanceCuller_OnEndSceneViewCameraRendering_m01D33D11A4853EB0397A2996F10795662828C2F8_AdjustorThunk (void);
extern void InstanceCuller_UpdateFrame_m4273286746BAF0CADC1665DB994C64B47FB32F42_AdjustorThunk (void);
extern void InstanceCuller_OnBeginCameraRendering_mB7CDBC0AD43EEC324B9584B96C25DF3B911EC907_AdjustorThunk (void);
extern void InstanceCuller_OnEndCameraRendering_mF0DF0261BF1978BAFAC10F5F9DB533FC86B8089B_AdjustorThunk (void);
extern void InstanceCuller_Dispose_m7C8B413AE9FD5A5C4EAD81A22D2D4C1F2E6C368F_AdjustorThunk (void);
extern void SetupCullingJobInput_Execute_mDE838174B1F7960CBAD7010EB7FB70CCA168E977_AdjustorThunk (void);
extern void PrefixSumDrawInstancesJob_Execute_m6384DA1CB6A1E053F358640B9E2FE7F378677771_AdjustorThunk (void);
extern void BuildDrawListsJob_Execute_m731DC4DAB67702B447641FF630CD1C60A596636E_AdjustorThunk (void);
extern void FindDrawInstancesJob_Execute_m011CC04D8F9DA0E7E3F04783AA60964898BEDC1D_AdjustorThunk (void);
extern void FindMaterialDrawInstancesJob_Execute_m0A8C723C617595319217DB5AFD151AC28F7759A0_AdjustorThunk (void);
extern void RemoveDrawInstanceIndicesJob_RemoveDrawRange_m57FBE6346CD44B5B864E6CA89A0549D9C880E652_AdjustorThunk (void);
extern void RemoveDrawInstanceIndicesJob_RemoveDrawBatch_mF0A59FED062CDA68A971249F539ACDB5D90DB5A5_AdjustorThunk (void);
extern void RemoveDrawInstanceIndicesJob_Execute_m37A6D16D906E5C1A7C3340E64C3ADAB45607EFCC_AdjustorThunk (void);
extern void UpdatePackedMaterialDataCacheJob_ProcessMaterial_mCFF81645A1DB74D08DAAD17D679FA00A306E57E3_AdjustorThunk (void);
extern void UpdatePackedMaterialDataCacheJob_Execute_m117BE8C4EEAC8E86F3445464E84ECB6A954A3EBB_AdjustorThunk (void);
extern void CreateDrawBatchesJob_EditDrawRange_mC8D762596291034355C1B116E92CAF83C5051F96_AdjustorThunk (void);
extern void CreateDrawBatchesJob_EditDrawBatch_mAF28C16786B3B72E623304CCAD2B3DF57FDB15E5_AdjustorThunk (void);
extern void CreateDrawBatchesJob_ProcessRenderer_m520CC5A20C49B3A0A91F8A06F8D954DD8D860FE0_AdjustorThunk (void);
extern void CreateDrawBatchesJob_Execute_m28C7A7916BC78A53B2A02EAF503B94E85BD03F4A_AdjustorThunk (void);
extern void GPUInstanceComponentDesc__ctor_m3D6BFA5A8BBF78939717DABD22CDEDE234780EE0_AdjustorThunk (void);
extern void ReadOnly__ctor_mF532002BB9D890FF1F0582B28EE81FFF81C4843D_AdjustorThunk (void);
extern void ReadOnly_CPUInstanceToGPUInstance_m01CDC3239F33A9D1A861D1816930C4A7E71440E5_AdjustorThunk (void);
extern void ReadOnly_CPUInstanceArrayToGPUInstanceArray_m246175FBDE0C68B01CF4B8B0F73087368CA7ACF3_AdjustorThunk (void);
extern void ConvertCPUInstancesToGPUInstancesJob_Execute_m16B5AACD9BEEFFA2046C2E1999F8C4AA7FF2B1D9_AdjustorThunk (void);
extern void GPUInstanceDataBufferBuilder_CreateMetadataValue_m50A13411B4799C2C197B9C4EFD3EED2BB4C25036_AdjustorThunk (void);
extern void GPUInstanceDataBufferBuilder_AddComponent_m26713FB7D74F3F47052241EEDE3D95D1502C35E8_AdjustorThunk (void);
extern void GPUInstanceDataBufferBuilder_Build_mDEF5AC49115B5D3CD195C5802389B93AE1C25C8C_AdjustorThunk (void);
extern void GPUInstanceDataBufferBuilder_Dispose_m1869839D0122EAE7FE7A7F9FE5356CCDE50D6636_AdjustorThunk (void);
extern void GPUInstanceDataBufferUploader__ctor_mAD57A016FC2E0940D4A469F418E161A1DCA8CC8C_AdjustorThunk (void);
extern void GPUInstanceDataBufferUploader_GetUploadBufferPtr_m0CB3210A88F79E17329FDE29AD57C33C975D55DD_AdjustorThunk (void);
extern void GPUInstanceDataBufferUploader_GetUIntPerInstance_mE171D8F73FE5CDE5D3F83C730F31877B8649A59C_AdjustorThunk (void);
extern void GPUInstanceDataBufferUploader_GetParamUIntOffset_mF231C740B0554E7A82228DAEA5FE5F6BEBD978FD_AdjustorThunk (void);
extern void GPUInstanceDataBufferUploader_AllocateUploadHandles_mAFB40A36A5FDAF5C560EB04B822012E57219A607_AdjustorThunk (void);
extern void GPUInstanceDataBufferUploader_SubmitToGpu_m352C6DB1B32BBB2D141C76ECCB19F8FF55940105_AdjustorThunk (void);
extern void GPUInstanceDataBufferUploader_SubmitToGpu_m6037322A91258909D757C712390F08395A36CF9C_AdjustorThunk (void);
extern void GPUInstanceDataBufferUploader_Dispose_m0D8CBA64EB57BF6554F97E74B238F0A159B574B5_AdjustorThunk (void);
extern void GPUResources_LoadShaders_m9ACB6FC1CAC3C07D223AB04073FEFEBCD55F5CDA_AdjustorThunk (void);
extern void GPUResources_CreateResources_m07CBEC1195D765927E59B74DDFF7402B3608E22D_AdjustorThunk (void);
extern void GPUResources_Dispose_m2D71F420E19EDC6E791D5FD0BDAA81501C6017E6_AdjustorThunk (void);
extern void WriteInstanceDataParameterJob_Execute_mFBD8B3E368892E290A864D47C75EC3958286BC25_AdjustorThunk (void);
extern void GPUInstanceDataBufferGrower__ctor_m2555453CA56595D3963837F7226E4F85D11379B1_AdjustorThunk (void);
extern void GPUInstanceDataBufferGrower_SubmitToGpu_mF6B94F8512B09B0E57404C840A3C22D621C8EE9A_AdjustorThunk (void);
extern void GPUInstanceDataBufferGrower_Dispose_m24A1DE3F1FE59498294C9845783B541018F54BE3_AdjustorThunk (void);
extern void GPUResources_LoadShaders_m4C5A24A42FDCD3D04EBB7C2342E23D14748CBB89_AdjustorThunk (void);
extern void GPUResources_CreateResources_mD345D83E87BDDAF07F3A8628B9E1F1860BEC40C3_AdjustorThunk (void);
extern void GPUResources_Dispose_mBB023C03E82397C5CF141105338158EEDA6841AF_AdjustorThunk (void);
extern void InstanceHandle_get_index_mD1303DDB5E62977480B92E3DCECC534B1465CB94_AdjustorThunk (void);
extern void InstanceHandle_set_index_mC56210BD1B26D7B86BCD9BF2CE5341188DC1A234_AdjustorThunk (void);
extern void InstanceHandle_get_instanceIndex_mD7BAC819F382904784AC46D1F6A631B4A21CA8B5_AdjustorThunk (void);
extern void InstanceHandle_get_type_mAC674106B77C6008F547FBAB2FE02F263D3FA40C_AdjustorThunk (void);
extern void InstanceHandle_get_valid_m5629806E66255D75C3B1ED5826B0DA6D13B9E0CE_AdjustorThunk (void);
extern void InstanceHandle_Equals_mB7F950B845D19368217960B6FE7DBEA51B013C9E_AdjustorThunk (void);
extern void InstanceHandle_CompareTo_mC8763D64CE799F220739F8D3880F0725ADA9A93D_AdjustorThunk (void);
extern void InstanceHandle_GetHashCode_m256554043D1CEC35A1670DA2BDD39B453A4985D9_AdjustorThunk (void);
extern void SharedInstanceHandle_get_index_mA8EF25F39F6B747A2984740DC8A6A7F7F3B31606_AdjustorThunk (void);
extern void SharedInstanceHandle_set_index_mBD1FDAC50BC4D743EF40A7195E2DF1E3B4BC1840_AdjustorThunk (void);
extern void SharedInstanceHandle_get_valid_m723EEF4B52F015C0A6EC4BB621966C396311C865_AdjustorThunk (void);
extern void SharedInstanceHandle_Equals_m8318CF0264558ADD64222B3A2593EACCED56BFE0_AdjustorThunk (void);
extern void SharedInstanceHandle_CompareTo_m31419B958041797369105E6105847547F39F98C4_AdjustorThunk (void);
extern void SharedInstanceHandle_GetHashCode_m5B97E179A78BD59969291F66E286E00873FC120D_AdjustorThunk (void);
extern void GPUInstanceIndex_get_index_m0EDBD4FD5FC090990E2A24DCEBB5346B260C919D_AdjustorThunk (void);
extern void GPUInstanceIndex_set_index_m24EF3293A5E4CA20F4186F53B459500CDAE40687_AdjustorThunk (void);
extern void GPUInstanceIndex_get_valid_m5DBD00DEF2B390C4DB2E0B41686B6A09A064D27D_AdjustorThunk (void);
extern void GPUInstanceIndex_Equals_mEF7254FCD24AAD9A15FD3D1555306A5F1C908D23_AdjustorThunk (void);
extern void GPUInstanceIndex_CompareTo_m4CEFE1A49A0D625417B0123D3CA3AE385B785DCF_AdjustorThunk (void);
extern void GPUInstanceIndex_GetHashCode_mD3CFF7872E06CD6F455F81B019F03672C213D970_AdjustorThunk (void);
extern void InstanceAllocator_get_length_m762D1C624FF0B3D9F1DFED48EEA65465932C49DC_AdjustorThunk (void);
extern void InstanceAllocator_set_length_m02BBC305E32676069A1D7855C5FF776FD7F24172_AdjustorThunk (void);
extern void InstanceAllocator_get_valid_m86EC99BF39D782407913E9BAC1DB2D7CBA1FD797_AdjustorThunk (void);
extern void InstanceAllocator_Initialize_m299908C0326706B4BF44DDEC7CDA3D1074347F01_AdjustorThunk (void);
extern void InstanceAllocator_Dispose_mB710FC50CF870DBD3B226AE4B28057364D67237A_AdjustorThunk (void);
extern void InstanceAllocator_AllocateInstance_m846D1D4EA89D654F8AD4272579E3A7591FE155AB_AdjustorThunk (void);
extern void InstanceAllocator_FreeInstance_m4017808CB3B95F6FC5079EFDCC7CA13217C42E3C_AdjustorThunk (void);
extern void InstanceAllocator_GetNumAllocated_mC907C85B73DDEAFAE3606B4099F5C7E808DA8C18_AdjustorThunk (void);
extern void InstanceAllocators_Initialize_m1C63A61426D12441A3C4EABEC29B392FC93E90D9_AdjustorThunk (void);
extern void InstanceAllocators_Dispose_m0B2675D6FA6E95D9B9CC4617A94F408D9E80F5F7_AdjustorThunk (void);
extern void InstanceAllocators_GetInstanceAllocator_mF5B156CDAEDEEC2849590A544A5D99E691D0E37F_AdjustorThunk (void);
extern void InstanceAllocators_GetInstanceHandlesLength_mBFEDAC598685E4B38B1E67DECA4E75B8E345ACE0_AdjustorThunk (void);
extern void InstanceAllocators_GetInstancesLength_m7F121F6138F0713B01D5379DCA36255DF631D1C3_AdjustorThunk (void);
extern void InstanceAllocators_AllocateInstance_m1BA734BD0D1B20F166DEBCCEE8AC5B2B0911D9A4_AdjustorThunk (void);
extern void InstanceAllocators_FreeInstance_m0EBB63A12A593C580C54770B70A6032C55237AF6_AdjustorThunk (void);
extern void InstanceAllocators_AllocateSharedInstance_mD61A96D871FD88F2826B307B6BBF976223E075CF_AdjustorThunk (void);
extern void InstanceAllocators_FreeSharedInstance_m0B4E4FDCEA191C4829065D705C241F8667B658D5_AdjustorThunk (void);
extern void CPUInstanceData_get_instancesLength_mEF0DF28231C26F0C2E7EDA6A7115A51F51E5109C_AdjustorThunk (void);
extern void CPUInstanceData_set_instancesLength_mA37EF6CFF372799E7F182362873F19898021B186_AdjustorThunk (void);
extern void CPUInstanceData_get_instancesCapacity_mB1E847621B15F7559FC491B24F54ABA253A73249_AdjustorThunk (void);
extern void CPUInstanceData_set_instancesCapacity_m2BC970C6B7AA237310D79F3994737B40F962EE1D_AdjustorThunk (void);
extern void CPUInstanceData_get_handlesLength_mA9DC002A27BBD94B09B5944B84C6F47C30692F48_AdjustorThunk (void);
extern void CPUInstanceData_Initialize_mCB28B3C43BE61390B76A1B9C1C79BBFA33A33FD0_AdjustorThunk (void);
extern void CPUInstanceData_Dispose_mA6CD2735982440ECA65880116C2D214E97ADFC20_AdjustorThunk (void);
extern void CPUInstanceData_Grow_m54A454E32FB7C164E757F05A453DB71BBF6E1907_AdjustorThunk (void);
extern void CPUInstanceData_AddUnsafe_m1F5C8C517A8258670937584F1DD66E9D47724BC2_AdjustorThunk (void);
extern void CPUInstanceData_InstanceToIndex_m9D4701DC598D9046898239AF94F4E87F6A0F14EE_AdjustorThunk (void);
extern void CPUInstanceData_IndexToInstance_m3C95DBA9646291FACE0234AC811183928C232321_AdjustorThunk (void);
extern void CPUInstanceData_IsValidInstance_m5B3FF0F8456CF25EE209E68A230D594BB5DB36A3_AdjustorThunk (void);
extern void CPUInstanceData_IsFreeInstanceHandle_mEEBB421BA3391592FEF206095FBBC6A4BF6D9589_AdjustorThunk (void);
extern void CPUInstanceData_IsValidIndex_m4BE52A79DE124AA4872297A76A0FEA1D114CA010_AdjustorThunk (void);
extern void CPUInstanceData_GetFreeInstancesCount_m1F0B5BDAFE8DC1DB434FC5682705494F3EDFE10C_AdjustorThunk (void);
extern void CPUInstanceData_EnsureFreeInstances_mB7036C8793D1CB89C1C371A605581CBB4E177484_AdjustorThunk (void);
extern void CPUInstanceData_AddNoGrow_mEB9B4B4BCDB7C718DA14898D903B5E0D45DE0455_AdjustorThunk (void);
extern void CPUInstanceData_Add_m0736A5D9F85B1EC37E061411CC00335DFCC44D5A_AdjustorThunk (void);
extern void CPUInstanceData_Remove_m599064EAE344F332CFE4CD00C9714E72623D6877_AdjustorThunk (void);
extern void CPUInstanceData_Set_m47C0F837DC4AFF38FE4A50E426FFE05969B3FC19_AdjustorThunk (void);
extern void CPUInstanceData_SetDefault_mDE2B13667675847C626111DE77EDE63FE25B5E42_AdjustorThunk (void);
extern void CPUInstanceData_Get_SharedInstance_mE5ED4915B38984690F4AB5089634500FBCB6C3CB_AdjustorThunk (void);
extern void CPUInstanceData_Get_LocalToWorldIsFlipped_m19881EEED025A0FC45721F63DFCCC1C462F93107_AdjustorThunk (void);
extern void CPUInstanceData_Get_WorldAABB_mF9F14AE6B71FECD3BF51252AB06781687C9531EB_AdjustorThunk (void);
extern void CPUInstanceData_Get_TetrahedronCacheIndex_m7D09D9EBD350591CCD9A948AF1F55DA98F85D1B6_AdjustorThunk (void);
extern void CPUInstanceData_Get_WorldBounds_m59B12DD936A814F2E056E962A4889CD3FD473417_AdjustorThunk (void);
extern void CPUInstanceData_Get_MovedInCurrentFrame_m58A543967F67149E003AD3FE6FE4C45F9E87C869_AdjustorThunk (void);
extern void CPUInstanceData_Get_MovedInPreviousFrame_m9A0B9988AF8004828D13C96F89981F3C6AB15C29_AdjustorThunk (void);
extern void CPUInstanceData_Get_VisibleInPreviousFrame_m3E67A47CD7703E4345BEE5A7068855FE6A589BB5_AdjustorThunk (void);
extern void CPUInstanceData_Set_SharedInstance_mECEE018E02FA2F6502B59197A9AEF35FA2D35081_AdjustorThunk (void);
extern void CPUInstanceData_Set_LocalToWorldIsFlipped_mB2B243184FD3C74C54A5D8EC74542BC0E019EF29_AdjustorThunk (void);
extern void CPUInstanceData_Set_WorldAABB_m56D85CE12B7C589B053C471E803090DF9CA37073_AdjustorThunk (void);
extern void CPUInstanceData_Set_TetrahedronCacheIndex_mAC10B658EB1AB6EF405BC32A6DE8E88F7AA45AD5_AdjustorThunk (void);
extern void CPUInstanceData_Set_MovedInCurrentFrame_mB2081F967C2A8B6E6AC80F2E91DB3FD70851346F_AdjustorThunk (void);
extern void CPUInstanceData_Set_MovedInPreviousFrame_m096AF4BED24D559F13122047DF89E56C3C431B4D_AdjustorThunk (void);
extern void CPUInstanceData_Set_VisibleInPreviousFrame_m53D87829915906D1DD8E19F1F8D51631E9C02620_AdjustorThunk (void);
extern void CPUInstanceData_AsReadOnly_m90E90427C88E3F72D123C44F0E7957BACDDA8306_AdjustorThunk (void);
extern void ReadOnly_get_handlesLength_m50556F07B2F7144758202F76E202ED795E715DE9_AdjustorThunk (void);
extern void ReadOnly_get_instancesLength_m0E85E521D7DB61518C6988B08646386F20A17307_AdjustorThunk (void);
extern void ReadOnly__ctor_mFAEAA65432C0711F606AC98FD2320032B60A3309_AdjustorThunk (void);
extern void ReadOnly_InstanceToIndex_mA64D7447F6466C660DE83765B1B42673D922CCE2_AdjustorThunk (void);
extern void ReadOnly_IndexToInstance_m5BB2CA43E37DBA390AB7F29E6B5357A05226EE1B_AdjustorThunk (void);
extern void ReadOnly_IsValidInstance_m560D63CCD78D3661349A6134A30A29EFEDD3C30A_AdjustorThunk (void);
extern void ReadOnly_IsValidIndex_m2E036F94E4DFD41DF1CCA4148915594457CF0A9B_AdjustorThunk (void);
extern void CPUSharedInstanceData_get_instancesLength_m6C9D2E7F861FF43E23759E8816653646F41038B6_AdjustorThunk (void);
extern void CPUSharedInstanceData_set_instancesLength_m62AE1E8890A80D570FD3D11CD9E7C76B9D19124F_AdjustorThunk (void);
extern void CPUSharedInstanceData_get_instancesCapacity_mE40EF07D597175079E8DB6639E444A8346BD5C67_AdjustorThunk (void);
extern void CPUSharedInstanceData_set_instancesCapacity_mEBAB461BBCD3CD8127252B3F6156431A24943A69_AdjustorThunk (void);
extern void CPUSharedInstanceData_get_handlesLength_mF238FD08383695F2D7F707F2FA87279FE939CE61_AdjustorThunk (void);
extern void CPUSharedInstanceData_Initialize_m0D70CFA6688D2B1A165CDB20AB1760CBB4604A6E_AdjustorThunk (void);
extern void CPUSharedInstanceData_Dispose_m37AA8B81C3E42B81EC8E948B3F3DF9197B75E48C_AdjustorThunk (void);
extern void CPUSharedInstanceData_Grow_m0AED29A47C913CD92A9EA6281FABBBE76C4C26F3_AdjustorThunk (void);
extern void CPUSharedInstanceData_AddUnsafe_mD66018426B29B8FC77CBA218E04D6470D3A08B69_AdjustorThunk (void);
extern void CPUSharedInstanceData_SharedInstanceToIndex_m8D1697AC229D8D0D83FA702F6F440132FA123282_AdjustorThunk (void);
extern void CPUSharedInstanceData_IndexToSharedInstance_m7E58052D57FC86E8F4C0571D1213F2D4774BD323_AdjustorThunk (void);
extern void CPUSharedInstanceData_InstanceToIndex_mA58DA1C16D52FECF5ED6238B168203837D75F87C_AdjustorThunk (void);
extern void CPUSharedInstanceData_IsValidInstance_m33E12C093A6F0194B4D388782B15C2DD64616384_AdjustorThunk (void);
extern void CPUSharedInstanceData_IsFreeInstanceHandle_m972EDE9AF42DAADE66DAA3B1E1A8F8A85B594BFA_AdjustorThunk (void);
extern void CPUSharedInstanceData_IsValidIndex_m24116C260518DD680C3CDBD5BB1797B7764DDBCD_AdjustorThunk (void);
extern void CPUSharedInstanceData_GetFreeInstancesCount_mC18CC336EB3ED96DA46A39019C480485B58A26F5_AdjustorThunk (void);
extern void CPUSharedInstanceData_EnsureFreeInstances_m9276B1E0AE6DE0DFAE5F6FA4AFFB7DD9E6939CB0_AdjustorThunk (void);
extern void CPUSharedInstanceData_AddNoGrow_mAF3FBC643ABC12255E182EE8F8D26024AD8BBACD_AdjustorThunk (void);
extern void CPUSharedInstanceData_Add_m3AFDBA31C2E77E106BD970496C5F0EFBDCB3FCD3_AdjustorThunk (void);
extern void CPUSharedInstanceData_Remove_m1BA13D76461FC093E7667EA3746FC4A6C3E99EF9_AdjustorThunk (void);
extern void CPUSharedInstanceData_Get_RendererGroupID_m09F08F1763DD649A37D88B169CA50B2BD4F71768_AdjustorThunk (void);
extern void CPUSharedInstanceData_Get_MeshID_m70DD07FFBE12B43BA2546547FD26BC268E28EF43_AdjustorThunk (void);
extern void CPUSharedInstanceData_Get_LocalAABB_mB7D5A9EA9CD43BBEE3E938D31500A518FD3C02D7_AdjustorThunk (void);
extern void CPUSharedInstanceData_Get_Flags_m4F22F6B4DB27966ED5F633612B135EDCD274E341_AdjustorThunk (void);
extern void CPUSharedInstanceData_Get_LODGroupAndMask_m4A750431421271D4D2960E84D4B3AD83F96A8A42_AdjustorThunk (void);
extern void CPUSharedInstanceData_Get_GameObjectLayer_m75D2D7344A04236BFAE513142B29AB6CC8E7CB4D_AdjustorThunk (void);
extern void CPUSharedInstanceData_Get_RefCount_mE216F6749BB1B06E795C52C2433EE85E6C57DC01_AdjustorThunk (void);
extern void CPUSharedInstanceData_Get_MaterialIDs_mCE6EC8119990B7324D473B6DFB27B91A076404FC_AdjustorThunk (void);
extern void CPUSharedInstanceData_Set_RendererGroupID_m06EFE6568E7FABED464A0A091DB7D8DE05FC8719_AdjustorThunk (void);
extern void CPUSharedInstanceData_Set_MeshID_m97990780D2C7B92B6A146C6FB243D08D2674EAA9_AdjustorThunk (void);
extern void CPUSharedInstanceData_Set_LocalAABB_mFA0C8491B2FCD9E1F5BB25FF598C7520D689D08C_AdjustorThunk (void);
extern void CPUSharedInstanceData_Set_Flags_m9C1CE3AC11B09B3B2C9F65422B2FD31D23C49F4D_AdjustorThunk (void);
extern void CPUSharedInstanceData_Set_LODGroupAndMask_mD27421C024B1779830BEFA1274063B72D7C182CA_AdjustorThunk (void);
extern void CPUSharedInstanceData_Set_GameObjectLayer_m97AEE383F753133901141CCD0040B7D2C2E41EEC_AdjustorThunk (void);
extern void CPUSharedInstanceData_Set_RefCount_m7C5AF143DDC85304B2840A688BB2DD095A03B7CC_AdjustorThunk (void);
extern void CPUSharedInstanceData_Set_MaterialIDs_m3C8A0B397DBC9FEFF9661015CC32232A4910D812_AdjustorThunk (void);
extern void CPUSharedInstanceData_Set_m5E3312BE2456604D50D30AA09F973F7534EF7197_AdjustorThunk (void);
extern void CPUSharedInstanceData_SetDefault_m9241E530C50F996744FC4691DCD87C0E30602237_AdjustorThunk (void);
extern void CPUSharedInstanceData_AsReadOnly_mC186E7142A7E53BFE6D90BB0AB1841600BE7CBF8_AdjustorThunk (void);
extern void ReadOnly_get_handlesLength_mEAE03E557C81624E1F2CD280610A867E764EE456_AdjustorThunk (void);
extern void ReadOnly_get_instancesLength_m82B5F22A5EE9DF8AB94546D3B739C1EB36AD38D6_AdjustorThunk (void);
extern void ReadOnly__ctor_mDBC17F900B61DCF328F4BE2CDF0464E97F44614D_AdjustorThunk (void);
extern void ReadOnly_SharedInstanceToIndex_m7CD21D39C3285388B428D1CDCAA9AFB10A0178B4_AdjustorThunk (void);
extern void ReadOnly_IndexToSharedInstance_mC97CAB3F6DB3C92BCB528F85F5B9E42728DE3389_AdjustorThunk (void);
extern void ReadOnly_IsValidSharedInstance_mC2056D855CF0ED6FA050810CE1035680DC453A1A_AdjustorThunk (void);
extern void ReadOnly_IsValidIndex_mA6B2DAE40129D5F001698A34D413EE0CEB24E61D_AdjustorThunk (void);
extern void ReadOnly_InstanceToIndex_mE76C9EBF3ACCFDD5D73B3A99925CAE88DE2DDBE8_AdjustorThunk (void);
extern void SmallIntegerArray_get_Valid_m717ECD9B73FD5A373F40B0A623CB2C8E188012C0_AdjustorThunk (void);
extern void SmallIntegerArray_set_Valid_mF9C378DA26AB93E1AB38CCD7371CA23ABB6779D3_AdjustorThunk (void);
extern void SmallIntegerArray__ctor_mD7860879B9A093BE8CD0CFD815D125FDB746090E_AdjustorThunk (void);
extern void SmallIntegerArray_get_Item_m81A85EC298981F31BB405CA783D90BB96697C2DE_AdjustorThunk (void);
extern void SmallIntegerArray_set_Item_mFEF4007D7D6DA125990D22E32B34ADCBF7056EA2_AdjustorThunk (void);
extern void SmallIntegerArray_Dispose_m11D070ED0582D69C7F631CAF8C44F93183D73579_AdjustorThunk (void);
extern void EditorInstanceDataArrays_Initialize_m9893832043B3485AD59127B93D0872405CF77AFC_AdjustorThunk (void);
extern void EditorInstanceDataArrays_Dispose_m16E99865063B2150E7BEFCE3BAE55831D9532B3B_AdjustorThunk (void);
extern void EditorInstanceDataArrays_Grow_m57AFD6C22AA320DA1E98981C894EEB1F96C0B041_AdjustorThunk (void);
extern void EditorInstanceDataArrays_Remove_mC3970E725DCDCEA7B636C2DCB4732C432787AE35_AdjustorThunk (void);
extern void EditorInstanceDataArrays_SetDefault_mBB385CE94C9E46ADADB174CEFB393C2A19245724_AdjustorThunk (void);
extern void ReadOnly__ctor_m2029FDC41CDEC78F717316642DB33534904EC6E2_AdjustorThunk (void);
extern void QueryRendererGroupInstancesCountJob_Execute_m78EBD1A4D27FC820DB994D726466672AE6698114_AdjustorThunk (void);
extern void ComputeInstancesOffsetAndResizeInstancesArrayJob_Execute_m7278A70AAF09ACD7DE06A58BBE53617ED59B3178_AdjustorThunk (void);
extern void QueryRendererGroupInstancesJob_Execute_m689B8C96E8D3739314972C71A89F43C5DD8DD5EC_AdjustorThunk (void);
extern void QueryRendererGroupInstancesMultiJob_Execute_m3ED5F46C8E2BBCFD2302EBF30F55D07CB4292593_AdjustorThunk (void);
extern void QuerySortedMeshInstancesJob_Execute_m0B30D3DBCCB8FE5833D677F735387912A448CCDF_AdjustorThunk (void);
extern void CalculateInterpolatedLightAndOcclusionProbesBatchJob_Execute_mB29926232C46662441F96B9405E9BE4D3A84109B_AdjustorThunk (void);
extern void ScatterTetrahedronCacheIndicesJob_Execute_m157E3BAA3AA5106CD5629211DB4DF10C941B0942_AdjustorThunk (void);
extern void TransformUpdateJob_Execute_m0C99BC8B89AD60C1B495E471F9F02FBEEF7F8342_AdjustorThunk (void);
extern void ProbesUpdateJob_Execute_mFACBB95DE46DB9C879C2EE745CE37C4AEFB69AD6_AdjustorThunk (void);
extern void MotionUpdateJob_Execute_m266055F93AABA39FD53E7EDBD6BAFE55F586891B_AdjustorThunk (void);
extern void ReallocateInstancesJob_Execute_m8E05DC4EC3877696439237F2B9485F5218445828_AdjustorThunk (void);
extern void FreeInstancesJob_Execute_mAED5669D78B3996A3298A55A6FF8625F90E6BADF_AdjustorThunk (void);
extern void FreeRendererGroupInstancesJob_Execute_m475670DFB6FB0B8F6EFD0FA9ACD4CD17EFF326BD_AdjustorThunk (void);
extern void UpdateRendererInstancesJob_Execute_m93597D2184F6460DD4610AD8E47868B1BA87A11C_AdjustorThunk (void);
extern void CollectInstancesLODGroupsAndMasksJob_Execute_m2EFF9AB4F3476E491E51143F7F60F79020A0F780_AdjustorThunk (void);
extern void GetVisibleNonProcessedTreeInstancesJob_Execute_mEFBAD1E57760EF2F4C2A4F4E6F6F40E6261C284A_AdjustorThunk (void);
extern void UpdateCompactedInstanceVisibilityJob_Execute_mDFAB7C16E38743439A74CCA9AA310B383D2BA55B_AdjustorThunk (void);
extern void InstanceNumInfo_InitDefault_m8CC8A9E8EBF0EF16311F391E0312C46A7219A920_AdjustorThunk (void);
extern void InstanceNumInfo__ctor_m0C64766A7024C367CB84BA96F01861E951351650_AdjustorThunk (void);
extern void InstanceNumInfo__ctor_mCFED34B4FC73F15366339611E22502A8366B016C_AdjustorThunk (void);
extern void InstanceNumInfo_GetInstanceNum_m42CC8341EB7A73444DB20B9B64ACF5377CA7CE19_AdjustorThunk (void);
extern void InstanceNumInfo_GetInstanceNumIncludingChildren_m45CB40CE2452143C10B4FFC0F05FA7751E416368_AdjustorThunk (void);
extern void InstanceNumInfo_GetTotalInstanceNum_m55D6043571FB3440889F98A7D638F6A43D060982_AdjustorThunk (void);
extern void OccluderHandles_IsValid_m9FF256155930C1B40D4195C263AF38FCEB1B7904_AdjustorThunk (void);
extern void OccluderHandles_UseForOcclusionTest_m8E9F3CDFD218EC3C5A70DA375F156DC75CB2CAAD_AdjustorThunk (void);
extern void OccluderHandles_UseForOccluderUpdate_m4376DEB9151FDF5678FDFE2ED525A3B3AC31AF03_AdjustorThunk (void);
extern void IndirectBufferAllocInfo_IsEmpty_m47785BE361D9B989BE0455AD1884831AB99E4009_AdjustorThunk (void);
extern void IndirectBufferAllocInfo_IsWithinLimits_mE8807A3A2DDE4F3E3E9091E2441D847F4BFC4067_AdjustorThunk (void);
extern void IndirectBufferAllocInfo_GetExtraDrawInfoSlotIndex_m2EAD4191867631265610C43212C34A56A2DAD969_AdjustorThunk (void);
extern void IndirectBufferContext__ctor_mC7F33D8E6E9DECDB5A167C9CFD68F1CBB6E7D7F5_AdjustorThunk (void);
extern void IndirectBufferContext_Matches_m40E2A7974D4B205D4BB770D4A68635D99FE4C721_AdjustorThunk (void);
extern void OccluderContext_get_subviewCount_m49FDF73077D5C9F1789BA0C35A39A7F78FCBF414_AdjustorThunk (void);
extern void OccluderContext_IsSubviewValid_m88BE2A076AC851E9D11AB02B30ECE80A7E4D6BE0_AdjustorThunk (void);
extern void OccluderContext_get_depthBufferSizeInOccluderPixels_mF734AB99EBF484188554B86CB2E07048E6138C36_AdjustorThunk (void);
extern void OccluderContext_Dispose_mF02789AFBB76CD0F4491CDD8A83BCD15938F22D8_AdjustorThunk (void);
extern void OccluderContext_UpdateMipBounds_m71B416ED1B6D8827B30F8B1C1E69870328E6FCD9_AdjustorThunk (void);
extern void OccluderContext_AllocateTexturesIfNecessary_m4F0340120C018852B2A697194F87A6C539D6C127_AdjustorThunk (void);
extern void OccluderContext_SetupFarDepthPyramidConstants_m69DC95369344843C149515C9A2A9312DD0E99571_AdjustorThunk (void);
extern void OccluderContext_CreateFarDepthPyramid_mAE9A7D75C802A5AB3F91ED35C2BF61DA52C0AB0F_AdjustorThunk (void);
extern void OccluderContext_Import_m59CDBBE79F1A96B17BE009D5D561E35D69DA19D7_AdjustorThunk (void);
extern void OccluderContext_PrepareOccluders_m558D900C293C248A3CEE8FE6640C98873039DF1D_AdjustorThunk (void);
extern void OccluderContext_GetDebugOutput_m0B03B350C81D50A7DA8CEB969E70AF0F782675C5_AdjustorThunk (void);
extern void IndirectBufferContextHandles_UseForOcclusionTest_m098E1C673230461EF27D4BC76A7186A5DADEEDAB_AdjustorThunk (void);
extern void IndirectBufferContextStorage_get_instanceBuffer_mD965845788AD240262E1C46BB4BA405B73C8EA27_AdjustorThunk (void);
extern void IndirectBufferContextStorage_get_instanceInfoBuffer_mC14C040CC88B2F11AFE1301EB1FB9C70E396E8E5_AdjustorThunk (void);
extern void IndirectBufferContextStorage_get_argsBuffer_m81BE13A707FAF6B8F9361AEBCDD9CBA69611C334_AdjustorThunk (void);
extern void IndirectBufferContextStorage_get_drawInfoBuffer_m0168780ED5CFCB3613F04F2C00561471352A5790_AdjustorThunk (void);
extern void IndirectBufferContextStorage_get_visibleInstanceBufferHandle_mBD86573F23F6B0F8BF6C46C2996C449E571DC87F_AdjustorThunk (void);
extern void IndirectBufferContextStorage_get_indirectArgsBufferHandle_m7DF465FC2B94D42E1ABE0EE29E0EE4A1C850E925_AdjustorThunk (void);
extern void IndirectBufferContextStorage_ImportBuffers_mB024C5F1494D76DFEC07CDE6065AC7ACBB35E265_AdjustorThunk (void);
extern void IndirectBufferContextStorage_get_instanceInfoGlobalArray_m6D87D347531D8E3C00FB0967CCAEE69FED54FC41_AdjustorThunk (void);
extern void IndirectBufferContextStorage_get_drawInfoGlobalArray_mF90CEAECE8B83E043E462A4495421A574CC257D4_AdjustorThunk (void);
extern void IndirectBufferContextStorage_get_allocationCounters_mF22684F6D0214AE0755E3DFA8BA047C9E63C70FC_AdjustorThunk (void);
extern void IndirectBufferContextStorage_Init_mE1D9E857917AF5759F2E08D6B008C991CE5558AF_AdjustorThunk (void);
extern void IndirectBufferContextStorage_AllocateInstanceBuffers_m83064D5ABC47473BE4547D8CDB301CC989362AD4_AdjustorThunk (void);
extern void IndirectBufferContextStorage_FreeInstanceBuffers_mE035A24BD6179866735657D3D74C416AD39C597D_AdjustorThunk (void);
extern void IndirectBufferContextStorage_AllocateDrawBuffers_mCB3267B00E8328A5031AAB859542061D665CE0A0_AdjustorThunk (void);
extern void IndirectBufferContextStorage_FreeDrawBuffers_mAB74E77AD8B6348D492585D0ACB5AE42C2CEB171_AdjustorThunk (void);
extern void IndirectBufferContextStorage_Dispose_mD7278C175C4C23BCB9190D68CE8343B543A24302_AdjustorThunk (void);
extern void IndirectBufferContextStorage_SyncContexts_mB63C2465AF6D851CA68041FA1C12E24F24874A32_AdjustorThunk (void);
extern void IndirectBufferContextStorage_ResetAllocators_m327B8163AEEAFE360011EAEBFBD80C96C6403467_AdjustorThunk (void);
extern void IndirectBufferContextStorage_GrowBuffers_mAB6F1A326FB73F38678D592C809333FAE5A25D44_AdjustorThunk (void);
extern void IndirectBufferContextStorage_ClearContextsAndGrowBuffers_m0B059924A2DB871C3C80B03DAF54A47C8A47E573_AdjustorThunk (void);
extern void IndirectBufferContextStorage_TryAllocateContext_mC8D472C07C986BDCFC55CF8C4CE324230A38618C_AdjustorThunk (void);
extern void IndirectBufferContextStorage_TryGetContextIndex_m848B7C96E09D5CE2C6D12221D0B78EA4DCE77EF1_AdjustorThunk (void);
extern void IndirectBufferContextStorage_GetAllocInfoSubArray_m053D08700A1B5D4DB1AAF31B508E33E9FF418A4D_AdjustorThunk (void);
extern void IndirectBufferContextStorage_GetAllocInfo_m9BF31C85DBB94B0D0F1B82AF66DA8493AB1B2595_AdjustorThunk (void);
extern void IndirectBufferContextStorage_CopyFromStaging_mE99297FD5192A7F628FD7DC704E877280DB03891_AdjustorThunk (void);
extern void IndirectBufferContextStorage_GetLimits_m2E8DDC54719944794D7342725107396F59D5CF71_AdjustorThunk (void);
extern void IndirectBufferContextStorage_GetBufferContext_mD76FD0DD95CEE4D7D9B62F81573C4A05A5519941_AdjustorThunk (void);
extern void IndirectBufferContextStorage_SetBufferContext_mEC8E692A99E595592E12F55F94B7E234A340DC2A_AdjustorThunk (void);
extern void UpdateLODGroupTransformJob_Execute_m10AD64037A0D9FFAB351270A412B4B4342A6B8C5_AdjustorThunk (void);
extern void AllocateOrGetLODGroupDataInstancesJob_Execute_mF977F85F444872F9F0E507A0DADE7F2550050CA7_AdjustorThunk (void);
extern void UpdateLODGroupDataJob_Execute_m37868AFCE3AFCC80D46C8156CA2713096FE5EC3D_AdjustorThunk (void);
extern void FreeLODGroupDataJob_Execute_mABCBCD7B1C65E4A56F7F608884D1F9FB4FA1AAF7_AdjustorThunk (void);
extern void OcclusionTestComputeShader_Init_mC423CA21A41E4B44DB2715096F04D7498A80E960_AdjustorThunk (void);
extern void SilhouettePlaneCache_Init_mF14F33F1C7D6CD3704478C92314526747ABDFF0C_AdjustorThunk (void);
extern void SilhouettePlaneCache_Dispose_m52386469CD058770AAD5B4E19ADF603598BECEC5_AdjustorThunk (void);
extern void SilhouettePlaneCache_Update_m08599EDAF7CC1D053E0E54A8DF15F55E1E0B6342_AdjustorThunk (void);
extern void SilhouettePlaneCache_FreeUnusedSlots_m2249464604B48996B77945B9BBCDBDCECD2074C2_AdjustorThunk (void);
extern void SilhouettePlaneCache_GetSubArray_m0527F754CEEB54300C083A4BDAE9B56D026DA63C_AdjustorThunk (void);
extern void Slot__ctor_m0E99B79099FDB0098404A2FD223A9C029CFFF5D1_AdjustorThunk (void);
extern void OcclusionCullingCommonShaderVariables__ctor_m6098CCD0E939B2F9DE8715FF129DAE892745C610_AdjustorThunk (void);
extern void RenderersParameters__ctor_mD3445734819B50610F768B8E6EF49822D5ABEE8A_AdjustorThunk (void);
extern void ParamInfo_get_valid_m198E32DB1AAD1F43EAD6964E4EF79E79D078AF7D_AdjustorThunk (void);
extern void ParallelBitArray_get_Length_m82FE0E2AC9FAB29DA67E28FFAEA04EB642955B08_AdjustorThunk (void);
extern void ParallelBitArray_get_IsCreated_m31B6CFD3C95548F523C3D074463B827B7CD7A535_AdjustorThunk (void);
extern void ParallelBitArray__ctor_m7BB9EA31D2DF48FD4BE5D8773C539A76C5D4E6E4_AdjustorThunk (void);
extern void ParallelBitArray_Dispose_m24CBECA125F3D0090E9786E6AF56CB2E6DE452C6_AdjustorThunk (void);
extern void ParallelBitArray_Dispose_m3806D56E9788D3C69BD6C6985C6E2F949A89F9B7_AdjustorThunk (void);
extern void ParallelBitArray_Resize_mA268182EEF9B41198BFC3780A74CF12D9B232011_AdjustorThunk (void);
extern void ParallelBitArray_Set_mEDB30931801E1F71F11F62CCB23F3537EEE0F1EA_AdjustorThunk (void);
extern void ParallelBitArray_Get_m250C22A6191BAF4C5B314EFAF451E391D251F2B0_AdjustorThunk (void);
extern void ParallelBitArray_GetChunk_m7303392F0138448DF74E7A709F38B500B9461ED2_AdjustorThunk (void);
extern void ParallelBitArray_SetChunk_m48943193199714BCF0925DD8E14C8EB651885629_AdjustorThunk (void);
extern void ParallelBitArray_InterlockedReadChunk_m19B068CDEB9686FE2DD8A42BF99D2011EEAFA84F_AdjustorThunk (void);
extern void ParallelBitArray_InterlockedOrChunk_m51A85AD8A6A6FFD317303DEABA6B9797B79CE658_AdjustorThunk (void);
extern void ParallelBitArray_ChunkCount_mE27E6F3D861AF09C5B31BD7F3964796B26A9C3B5_AdjustorThunk (void);
extern void ParallelBitArray_GetSubArray_m45E14868BB90EC98D0467ABDA3DAD1BD4BFC49DD_AdjustorThunk (void);
extern void ParallelBitArray_GetBitsArray_m31F100FDDB1EA0FE4E1592768ED14B843D0DD73D_AdjustorThunk (void);
extern void ParallelBitArray_FillZeroes_mDAE3DE6ACB91DE00B5DC5D50415B7A0F938A08BB_AdjustorThunk (void);
extern void RadixSortBucketCountJob_Execute_mF3ADEB0523C3DE92CB5CFEF01B65E72C9AB7C023_AdjustorThunk (void);
extern void RadixSortBatchPrefixSumJob_JobIndexPrefixSum_m9C47BE4B67FCFF29A8FA94D39589F9B9A2840EE3_AdjustorThunk (void);
extern void RadixSortBatchPrefixSumJob_Execute_m434849692F7D93EF83545890B59FC96BF14AED93_AdjustorThunk (void);
extern void RadixSortPrefixSumJob_Execute_mF1969BD6160F81BA429AF74A8944935FC83BC551_AdjustorThunk (void);
extern void RadixSortBucketSortJob_Execute_mDB0AB3CD468DA898E41CBF3E9EF5BE26AD26E4D2_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[350] = 
{
	{ 0x06000005, AABB_get_min_m209EF3ECD01E859258E1BDE8780CA4C25276DF70_AdjustorThunk },
	{ 0x06000006, AABB_get_max_mCD29B3A2C2DEA60BBF50091D8E3C938511D82B08_AdjustorThunk },
	{ 0x06000007, AABB_ToString_mB0C529C757F63FD0F78316C90D2B86206159B678_AdjustorThunk },
	{ 0x0600007E, ReceiverPlanes_LightFacingFrustumPlaneSubArray_m995D1E681E0ED63DFA8489C582A2FCBAF1C9356B_AdjustorThunk },
	{ 0x0600007F, ReceiverPlanes_SilhouettePlaneSubArray_m851F735918EE9297C64815C42AAD446BCBE55F4B_AdjustorThunk },
	{ 0x06000081, ReceiverPlanes_Dispose_mE53DCBAD81DDB002760FC5661F3B26330B62ACF4_AdjustorThunk },
	{ 0x06000083, FrustumPlaneCuller_Dispose_m802E9F4605F17D55D46C6C0D308830667DE0A08B_AdjustorThunk },
	{ 0x06000086, PlanePacket4__ctor_m1B67A6C35FB1B6B6BE21D76B496DA3531F636CC7_AdjustorThunk },
	{ 0x06000088, ReceiverSphereCuller_Dispose_mFCC83320D29032DBC7A3173ADFE8F764A472FFA5_AdjustorThunk },
	{ 0x06000089, ReceiverSphereCuller_UseReceiverPlanes_mEA2EE8FE32563F3FBE40D6AADA9724719186674E_AdjustorThunk },
	{ 0x060000D9, ClassifyMaterialsJob_Execute_m94CBFED114435BCA576F6A9344BD213DCAC28DA6_AdjustorThunk },
	{ 0x060000DA, FindUnsupportedRenderersJob_Execute_m69B13CFCB49D64142693707BD2ABEE5BA954B26E_AdjustorThunk },
	{ 0x060000DB, FindRenderersFromMaterialJob_Execute_mBEE77639F82D94D447BBCD74866B3A1FC106CDC3_AdjustorThunk },
	{ 0x060000DC, GetMaterialsWithChangedPackedMaterialJob_Execute_mDCCF53C7F3BF79C9F789EEE83784BC5763F6413F_AdjustorThunk },
	{ 0x060000F5, OcclusionCullingSettings__ctor_mE814849AC60B1DC1AC17E02F1AF2128DF38FE95A_AdjustorThunk },
	{ 0x060000F6, OccluderSubviewUpdate__ctor_m482C60BE94ECE0B1F15E930936345D9F60E399A8_AdjustorThunk },
	{ 0x060000F7, OccluderParameters__ctor_mBBA1CD9856BD109C9F1D18715B28E2DA95CE83D9_AdjustorThunk },
	{ 0x06000100, RangeKey_Equals_m05E612C122D91758588CEB7529751045E6F09493_AdjustorThunk },
	{ 0x06000101, RangeKey_GetHashCode_mC38845B6A1CC657D6F6B1149E7448AA6A3EF3257_AdjustorThunk },
	{ 0x06000102, DrawKey_Equals_mBA2838871085C8944B81322DC6E851F4273E8E91_AdjustorThunk },
	{ 0x06000103, DrawKey_GetHashCode_m6E3C5F3D42D02D8035AA7E7B5501FDBC1551F4E5_AdjustorThunk },
	{ 0x06000104, BinningConfig_get_visibilityConfigCount_m0B698DBD65F8147B56C14CA7CFB741EAB701B3B9_AdjustorThunk },
	{ 0x06000106, CullingJob_CalculateLODVisibility_mEBC1FA7E67148AD8CCB4D54FBAB3CF92DDF72A0E_AdjustorThunk },
	{ 0x06000107, CullingJob_CalculateVisibilityMask_m285DA7E28A8582C95BAE2C6794C34FC3F8F195ED_AdjustorThunk },
	{ 0x06000108, CullingJob_Execute_m172CC218FA8F7198BD69C1C87294FD90D94DB884_AdjustorThunk },
	{ 0x06000109, AllocateBinsPerBatch_IsInstanceFlipped_m5ED8B8D355142D10DB3983D15A256B5DBD4D6ABA_AdjustorThunk },
	{ 0x0600010A, AllocateBinsPerBatch_Execute_m8244D8F2860ED03B023A6D348D9471F2AF76E0F1_AdjustorThunk },
	{ 0x0600010B, PrefixSumDrawsAndInstances_Execute_m8EADFE8897EAE00866B67F8AE64CA774EE33B5F9_AdjustorThunk },
	{ 0x0600010C, DrawCommandOutputPerBatch_EncodeGPUInstanceIndexAndCrossFade_m6EFB0D6BB29BF2F65B66913BD2F82E4364ED8830_AdjustorThunk },
	{ 0x0600010D, DrawCommandOutputPerBatch_IsInstanceFlipped_m1AE2BD53A7D17783F262A94539417F423FD4CF79_AdjustorThunk },
	{ 0x0600010E, DrawCommandOutputPerBatch_Execute_mF995D35E2E640A3172F4BC62E37BF103CFBBBBA7_AdjustorThunk },
	{ 0x0600010F, CompactVisibilityMasksJob_Execute_m7D5B39DEFEC4944D35111B4B7D22267147B4AB1E_AdjustorThunk },
	{ 0x06000110, InstanceCullerSplitDebugArray_get_Counters_mBDB8C933AEEB9F934132442CA20FC54A9E9A01AB_AdjustorThunk },
	{ 0x06000111, InstanceCullerSplitDebugArray_Init_m0E461744D899802E8123D32B79AD2EEC7FF1ED66_AdjustorThunk },
	{ 0x06000112, InstanceCullerSplitDebugArray_Dispose_m0FA405A9BA291E560088D998A71EE3FEF95100C1_AdjustorThunk },
	{ 0x06000113, InstanceCullerSplitDebugArray_TryAddSplits_m8F0CAC17CB3DA747A187760D3961255C1E95BDF5_AdjustorThunk },
	{ 0x06000114, InstanceCullerSplitDebugArray_AddSync_m05D0080857890E3C8E46730B1D4DC17E4FB71D90_AdjustorThunk },
	{ 0x06000115, InstanceCullerSplitDebugArray_MoveToDebugStatsAndClear_mAA78838CCD17348C01F115022EF530C3F5B98AD4_AdjustorThunk },
	{ 0x06000116, InstanceOcclusionEventDebugArray_get_CounterBuffer_mF25E7B744518F980443AFCF79E48EA3CCF852D04_AdjustorThunk },
	{ 0x06000117, InstanceOcclusionEventDebugArray_Init_mF8AEF95440DA22ACC2DB5717CEBD9C8B8B3797D5_AdjustorThunk },
	{ 0x06000118, InstanceOcclusionEventDebugArray_Dispose_m2D57744F19C60D3E9B6EFDF81BB08498853E37C1_AdjustorThunk },
	{ 0x06000119, InstanceOcclusionEventDebugArray_TryAdd_m2FD080E0DDCA404C8C362A5933EC852A9CD5F109_AdjustorThunk },
	{ 0x0600011A, InstanceOcclusionEventDebugArray_MoveToDebugStatsAndClear_mFEF02F07D15F2F2942FABFD0C7C577FAB1859977_AdjustorThunk },
	{ 0x0600011B, Info_HasVersion_m9E077F93A4F89E728DF4FA88764ABF91C732D960_AdjustorThunk },
	{ 0x0600011C, InstanceCuller_Init_mDBBD40C7316EBE16746FACA43170B7E1001EC67F_AdjustorThunk },
	{ 0x0600011D, InstanceCuller_CreateFrustumCullingJob_m073380FE489347D6F8CFB772AE33D0599532F54A_AdjustorThunk },
	{ 0x0600011E, InstanceCuller_ComputeWorstCaseDrawCommandCount_mF1277DD5CAC20EEE023EA894E84D7C1ADD7B180D_AdjustorThunk },
	{ 0x0600011F, InstanceCuller_CreateCullJobTree_m2E52805D7C465D995C4A87D156710394A4BB0EDF_AdjustorThunk },
	{ 0x06000120, InstanceCuller_CreateCompactedVisibilityMaskJob_m3A2F38959953B889E5E7456DE426ABA7134DFB9B_AdjustorThunk },
	{ 0x06000121, InstanceCuller_InstanceOccludersUpdated_mCA7D8171B44CF76E6A48314D7F97F2B698DCCE05_AdjustorThunk },
	{ 0x06000122, InstanceCuller_DisposeCompactVisibilityMasks_mE05A5A23578160F5FDE1F8F2E59B7B8E2819BC76_AdjustorThunk },
	{ 0x06000123, InstanceCuller_DisposeSceneViewHiddenBits_mB8548C25259A3AE546942634429EF581779992BD_AdjustorThunk },
	{ 0x06000124, InstanceCuller_GetCompactedVisibilityMasks_mED15105E7567F79042B542B56B4DE2ABEDE14C26_AdjustorThunk },
	{ 0x06000125, InstanceCuller_InstanceOcclusionTest_m8E12352E27A10F62B9C737C3C7D0394DBC416F15_AdjustorThunk },
	{ 0x06000126, InstanceCuller_EnsureValidOcclusionTestResults_m22DA66230772FAE8FA78B7E2B3839AB414BE8A22_AdjustorThunk },
	{ 0x06000127, InstanceCuller_AddOcclusionCullingDispatch_mDA6F922B0734577B8F921E9AE4C02CEE4F8A32A5_AdjustorThunk },
	{ 0x06000128, InstanceCuller_FlushDebugCounters_m1036A68627B296B0CE88254424A25EEA430BCCE6_AdjustorThunk },
	{ 0x06000129, InstanceCuller_OnBeginSceneViewCameraRendering_m9F271E3A4A411E077FC767C85C1813F41C9ADA3D_AdjustorThunk },
	{ 0x0600012A, InstanceCuller_OnEndSceneViewCameraRendering_m01D33D11A4853EB0397A2996F10795662828C2F8_AdjustorThunk },
	{ 0x0600012B, InstanceCuller_UpdateFrame_m4273286746BAF0CADC1665DB994C64B47FB32F42_AdjustorThunk },
	{ 0x0600012C, InstanceCuller_OnBeginCameraRendering_mB7CDBC0AD43EEC324B9584B96C25DF3B911EC907_AdjustorThunk },
	{ 0x0600012D, InstanceCuller_OnEndCameraRendering_mF0DF0261BF1978BAFAC10F5F9DB533FC86B8089B_AdjustorThunk },
	{ 0x0600012E, InstanceCuller_Dispose_m7C8B413AE9FD5A5C4EAD81A22D2D4C1F2E6C368F_AdjustorThunk },
	{ 0x06000130, SetupCullingJobInput_Execute_mDE838174B1F7960CBAD7010EB7FB70CCA168E977_AdjustorThunk },
	{ 0x0600013A, PrefixSumDrawInstancesJob_Execute_m6384DA1CB6A1E053F358640B9E2FE7F378677771_AdjustorThunk },
	{ 0x0600013C, BuildDrawListsJob_Execute_m731DC4DAB67702B447641FF630CD1C60A596636E_AdjustorThunk },
	{ 0x0600013D, FindDrawInstancesJob_Execute_m011CC04D8F9DA0E7E3F04783AA60964898BEDC1D_AdjustorThunk },
	{ 0x0600013E, FindMaterialDrawInstancesJob_Execute_m0A8C723C617595319217DB5AFD151AC28F7759A0_AdjustorThunk },
	{ 0x06000141, RemoveDrawInstanceIndicesJob_RemoveDrawRange_m57FBE6346CD44B5B864E6CA89A0549D9C880E652_AdjustorThunk },
	{ 0x06000142, RemoveDrawInstanceIndicesJob_RemoveDrawBatch_mF0A59FED062CDA68A971249F539ACDB5D90DB5A5_AdjustorThunk },
	{ 0x06000143, RemoveDrawInstanceIndicesJob_Execute_m37A6D16D906E5C1A7C3340E64C3ADAB45607EFCC_AdjustorThunk },
	{ 0x06000144, UpdatePackedMaterialDataCacheJob_ProcessMaterial_mCFF81645A1DB74D08DAAD17D679FA00A306E57E3_AdjustorThunk },
	{ 0x06000145, UpdatePackedMaterialDataCacheJob_Execute_m117BE8C4EEAC8E86F3445464E84ECB6A954A3EBB_AdjustorThunk },
	{ 0x06000146, CreateDrawBatchesJob_EditDrawRange_mC8D762596291034355C1B116E92CAF83C5051F96_AdjustorThunk },
	{ 0x06000147, CreateDrawBatchesJob_EditDrawBatch_mAF28C16786B3B72E623304CCAD2B3DF57FDB15E5_AdjustorThunk },
	{ 0x06000148, CreateDrawBatchesJob_ProcessRenderer_m520CC5A20C49B3A0A91F8A06F8D954DD8D860FE0_AdjustorThunk },
	{ 0x06000149, CreateDrawBatchesJob_Execute_m28C7A7916BC78A53B2A02EAF503B94E85BD03F4A_AdjustorThunk },
	{ 0x06000172, GPUInstanceComponentDesc__ctor_m3D6BFA5A8BBF78939717DABD22CDEDE234780EE0_AdjustorThunk },
	{ 0x0600017F, ReadOnly__ctor_mF532002BB9D890FF1F0582B28EE81FFF81C4843D_AdjustorThunk },
	{ 0x06000180, ReadOnly_CPUInstanceToGPUInstance_m01CDC3239F33A9D1A861D1816930C4A7E71440E5_AdjustorThunk },
	{ 0x06000181, ReadOnly_CPUInstanceArrayToGPUInstanceArray_m246175FBDE0C68B01CF4B8B0F73087368CA7ACF3_AdjustorThunk },
	{ 0x06000182, ConvertCPUInstancesToGPUInstancesJob_Execute_m16B5AACD9BEEFFA2046C2E1999F8C4AA7FF2B1D9_AdjustorThunk },
	{ 0x06000183, GPUInstanceDataBufferBuilder_CreateMetadataValue_m50A13411B4799C2C197B9C4EFD3EED2BB4C25036_AdjustorThunk },
	{ 0x06000185, GPUInstanceDataBufferBuilder_AddComponent_m26713FB7D74F3F47052241EEDE3D95D1502C35E8_AdjustorThunk },
	{ 0x06000186, GPUInstanceDataBufferBuilder_Build_mDEF5AC49115B5D3CD195C5802389B93AE1C25C8C_AdjustorThunk },
	{ 0x06000187, GPUInstanceDataBufferBuilder_Dispose_m1869839D0122EAE7FE7A7F9FE5356CCDE50D6636_AdjustorThunk },
	{ 0x06000188, GPUInstanceDataBufferUploader__ctor_mAD57A016FC2E0940D4A469F418E161A1DCA8CC8C_AdjustorThunk },
	{ 0x06000189, GPUInstanceDataBufferUploader_GetUploadBufferPtr_m0CB3210A88F79E17329FDE29AD57C33C975D55DD_AdjustorThunk },
	{ 0x0600018A, GPUInstanceDataBufferUploader_GetUIntPerInstance_mE171D8F73FE5CDE5D3F83C730F31877B8649A59C_AdjustorThunk },
	{ 0x0600018B, GPUInstanceDataBufferUploader_GetParamUIntOffset_mF231C740B0554E7A82228DAEA5FE5F6BEBD978FD_AdjustorThunk },
	{ 0x0600018D, GPUInstanceDataBufferUploader_AllocateUploadHandles_mAFB40A36A5FDAF5C560EB04B822012E57219A607_AdjustorThunk },
	{ 0x06000190, GPUInstanceDataBufferUploader_SubmitToGpu_m352C6DB1B32BBB2D141C76ECCB19F8FF55940105_AdjustorThunk },
	{ 0x06000191, GPUInstanceDataBufferUploader_SubmitToGpu_m6037322A91258909D757C712390F08395A36CF9C_AdjustorThunk },
	{ 0x06000192, GPUInstanceDataBufferUploader_Dispose_m0D8CBA64EB57BF6554F97E74B238F0A159B574B5_AdjustorThunk },
	{ 0x06000194, GPUResources_LoadShaders_m9ACB6FC1CAC3C07D223AB04073FEFEBCD55F5CDA_AdjustorThunk },
	{ 0x06000195, GPUResources_CreateResources_m07CBEC1195D765927E59B74DDFF7402B3608E22D_AdjustorThunk },
	{ 0x06000196, GPUResources_Dispose_m2D71F420E19EDC6E791D5FD0BDAA81501C6017E6_AdjustorThunk },
	{ 0x06000197, WriteInstanceDataParameterJob_Execute_mFBD8B3E368892E290A864D47C75EC3958286BC25_AdjustorThunk },
	{ 0x06000198, GPUInstanceDataBufferGrower__ctor_m2555453CA56595D3963837F7226E4F85D11379B1_AdjustorThunk },
	{ 0x06000199, GPUInstanceDataBufferGrower_SubmitToGpu_mF6B94F8512B09B0E57404C840A3C22D621C8EE9A_AdjustorThunk },
	{ 0x0600019A, GPUInstanceDataBufferGrower_Dispose_m24A1DE3F1FE59498294C9845783B541018F54BE3_AdjustorThunk },
	{ 0x0600019C, GPUResources_LoadShaders_m4C5A24A42FDCD3D04EBB7C2342E23D14748CBB89_AdjustorThunk },
	{ 0x0600019D, GPUResources_CreateResources_mD345D83E87BDDAF07F3A8628B9E1F1860BEC40C3_AdjustorThunk },
	{ 0x0600019E, GPUResources_Dispose_mBB023C03E82397C5CF141105338158EEDA6841AF_AdjustorThunk },
	{ 0x0600019F, InstanceHandle_get_index_mD1303DDB5E62977480B92E3DCECC534B1465CB94_AdjustorThunk },
	{ 0x060001A0, InstanceHandle_set_index_mC56210BD1B26D7B86BCD9BF2CE5341188DC1A234_AdjustorThunk },
	{ 0x060001A1, InstanceHandle_get_instanceIndex_mD7BAC819F382904784AC46D1F6A631B4A21CA8B5_AdjustorThunk },
	{ 0x060001A2, InstanceHandle_get_type_mAC674106B77C6008F547FBAB2FE02F263D3FA40C_AdjustorThunk },
	{ 0x060001A3, InstanceHandle_get_valid_m5629806E66255D75C3B1ED5826B0DA6D13B9E0CE_AdjustorThunk },
	{ 0x060001A6, InstanceHandle_Equals_mB7F950B845D19368217960B6FE7DBEA51B013C9E_AdjustorThunk },
	{ 0x060001A7, InstanceHandle_CompareTo_mC8763D64CE799F220739F8D3880F0725ADA9A93D_AdjustorThunk },
	{ 0x060001A8, InstanceHandle_GetHashCode_m256554043D1CEC35A1670DA2BDD39B453A4985D9_AdjustorThunk },
	{ 0x060001AA, SharedInstanceHandle_get_index_mA8EF25F39F6B747A2984740DC8A6A7F7F3B31606_AdjustorThunk },
	{ 0x060001AB, SharedInstanceHandle_set_index_mBD1FDAC50BC4D743EF40A7195E2DF1E3B4BC1840_AdjustorThunk },
	{ 0x060001AC, SharedInstanceHandle_get_valid_m723EEF4B52F015C0A6EC4BB621966C396311C865_AdjustorThunk },
	{ 0x060001AD, SharedInstanceHandle_Equals_m8318CF0264558ADD64222B3A2593EACCED56BFE0_AdjustorThunk },
	{ 0x060001AE, SharedInstanceHandle_CompareTo_m31419B958041797369105E6105847547F39F98C4_AdjustorThunk },
	{ 0x060001AF, SharedInstanceHandle_GetHashCode_m5B97E179A78BD59969291F66E286E00873FC120D_AdjustorThunk },
	{ 0x060001B1, GPUInstanceIndex_get_index_m0EDBD4FD5FC090990E2A24DCEBB5346B260C919D_AdjustorThunk },
	{ 0x060001B2, GPUInstanceIndex_set_index_m24EF3293A5E4CA20F4186F53B459500CDAE40687_AdjustorThunk },
	{ 0x060001B3, GPUInstanceIndex_get_valid_m5DBD00DEF2B390C4DB2E0B41686B6A09A064D27D_AdjustorThunk },
	{ 0x060001B4, GPUInstanceIndex_Equals_mEF7254FCD24AAD9A15FD3D1555306A5F1C908D23_AdjustorThunk },
	{ 0x060001B5, GPUInstanceIndex_CompareTo_m4CEFE1A49A0D625417B0123D3CA3AE385B785DCF_AdjustorThunk },
	{ 0x060001B6, GPUInstanceIndex_GetHashCode_mD3CFF7872E06CD6F455F81B019F03672C213D970_AdjustorThunk },
	{ 0x060001B8, InstanceAllocator_get_length_m762D1C624FF0B3D9F1DFED48EEA65465932C49DC_AdjustorThunk },
	{ 0x060001B9, InstanceAllocator_set_length_m02BBC305E32676069A1D7855C5FF776FD7F24172_AdjustorThunk },
	{ 0x060001BA, InstanceAllocator_get_valid_m86EC99BF39D782407913E9BAC1DB2D7CBA1FD797_AdjustorThunk },
	{ 0x060001BB, InstanceAllocator_Initialize_m299908C0326706B4BF44DDEC7CDA3D1074347F01_AdjustorThunk },
	{ 0x060001BC, InstanceAllocator_Dispose_mB710FC50CF870DBD3B226AE4B28057364D67237A_AdjustorThunk },
	{ 0x060001BD, InstanceAllocator_AllocateInstance_m846D1D4EA89D654F8AD4272579E3A7591FE155AB_AdjustorThunk },
	{ 0x060001BE, InstanceAllocator_FreeInstance_m4017808CB3B95F6FC5079EFDCC7CA13217C42E3C_AdjustorThunk },
	{ 0x060001BF, InstanceAllocator_GetNumAllocated_mC907C85B73DDEAFAE3606B4099F5C7E808DA8C18_AdjustorThunk },
	{ 0x060001C0, InstanceAllocators_Initialize_m1C63A61426D12441A3C4EABEC29B392FC93E90D9_AdjustorThunk },
	{ 0x060001C1, InstanceAllocators_Dispose_m0B2675D6FA6E95D9B9CC4617A94F408D9E80F5F7_AdjustorThunk },
	{ 0x060001C2, InstanceAllocators_GetInstanceAllocator_mF5B156CDAEDEEC2849590A544A5D99E691D0E37F_AdjustorThunk },
	{ 0x060001C3, InstanceAllocators_GetInstanceHandlesLength_mBFEDAC598685E4B38B1E67DECA4E75B8E345ACE0_AdjustorThunk },
	{ 0x060001C4, InstanceAllocators_GetInstancesLength_m7F121F6138F0713B01D5379DCA36255DF631D1C3_AdjustorThunk },
	{ 0x060001C5, InstanceAllocators_AllocateInstance_m1BA734BD0D1B20F166DEBCCEE8AC5B2B0911D9A4_AdjustorThunk },
	{ 0x060001C6, InstanceAllocators_FreeInstance_m0EBB63A12A593C580C54770B70A6032C55237AF6_AdjustorThunk },
	{ 0x060001C7, InstanceAllocators_AllocateSharedInstance_mD61A96D871FD88F2826B307B6BBF976223E075CF_AdjustorThunk },
	{ 0x060001C8, InstanceAllocators_FreeSharedInstance_m0B4E4FDCEA191C4829065D705C241F8667B658D5_AdjustorThunk },
	{ 0x060001C9, CPUInstanceData_get_instancesLength_mEF0DF28231C26F0C2E7EDA6A7115A51F51E5109C_AdjustorThunk },
	{ 0x060001CA, CPUInstanceData_set_instancesLength_mA37EF6CFF372799E7F182362873F19898021B186_AdjustorThunk },
	{ 0x060001CB, CPUInstanceData_get_instancesCapacity_mB1E847621B15F7559FC491B24F54ABA253A73249_AdjustorThunk },
	{ 0x060001CC, CPUInstanceData_set_instancesCapacity_m2BC970C6B7AA237310D79F3994737B40F962EE1D_AdjustorThunk },
	{ 0x060001CD, CPUInstanceData_get_handlesLength_mA9DC002A27BBD94B09B5944B84C6F47C30692F48_AdjustorThunk },
	{ 0x060001CE, CPUInstanceData_Initialize_mCB28B3C43BE61390B76A1B9C1C79BBFA33A33FD0_AdjustorThunk },
	{ 0x060001CF, CPUInstanceData_Dispose_mA6CD2735982440ECA65880116C2D214E97ADFC20_AdjustorThunk },
	{ 0x060001D0, CPUInstanceData_Grow_m54A454E32FB7C164E757F05A453DB71BBF6E1907_AdjustorThunk },
	{ 0x060001D1, CPUInstanceData_AddUnsafe_m1F5C8C517A8258670937584F1DD66E9D47724BC2_AdjustorThunk },
	{ 0x060001D2, CPUInstanceData_InstanceToIndex_m9D4701DC598D9046898239AF94F4E87F6A0F14EE_AdjustorThunk },
	{ 0x060001D3, CPUInstanceData_IndexToInstance_m3C95DBA9646291FACE0234AC811183928C232321_AdjustorThunk },
	{ 0x060001D4, CPUInstanceData_IsValidInstance_m5B3FF0F8456CF25EE209E68A230D594BB5DB36A3_AdjustorThunk },
	{ 0x060001D5, CPUInstanceData_IsFreeInstanceHandle_mEEBB421BA3391592FEF206095FBBC6A4BF6D9589_AdjustorThunk },
	{ 0x060001D6, CPUInstanceData_IsValidIndex_m4BE52A79DE124AA4872297A76A0FEA1D114CA010_AdjustorThunk },
	{ 0x060001D7, CPUInstanceData_GetFreeInstancesCount_m1F0B5BDAFE8DC1DB434FC5682705494F3EDFE10C_AdjustorThunk },
	{ 0x060001D8, CPUInstanceData_EnsureFreeInstances_mB7036C8793D1CB89C1C371A605581CBB4E177484_AdjustorThunk },
	{ 0x060001D9, CPUInstanceData_AddNoGrow_mEB9B4B4BCDB7C718DA14898D903B5E0D45DE0455_AdjustorThunk },
	{ 0x060001DA, CPUInstanceData_Add_m0736A5D9F85B1EC37E061411CC00335DFCC44D5A_AdjustorThunk },
	{ 0x060001DB, CPUInstanceData_Remove_m599064EAE344F332CFE4CD00C9714E72623D6877_AdjustorThunk },
	{ 0x060001DC, CPUInstanceData_Set_m47C0F837DC4AFF38FE4A50E426FFE05969B3FC19_AdjustorThunk },
	{ 0x060001DD, CPUInstanceData_SetDefault_mDE2B13667675847C626111DE77EDE63FE25B5E42_AdjustorThunk },
	{ 0x060001DE, CPUInstanceData_Get_SharedInstance_mE5ED4915B38984690F4AB5089634500FBCB6C3CB_AdjustorThunk },
	{ 0x060001DF, CPUInstanceData_Get_LocalToWorldIsFlipped_m19881EEED025A0FC45721F63DFCCC1C462F93107_AdjustorThunk },
	{ 0x060001E0, CPUInstanceData_Get_WorldAABB_mF9F14AE6B71FECD3BF51252AB06781687C9531EB_AdjustorThunk },
	{ 0x060001E1, CPUInstanceData_Get_TetrahedronCacheIndex_m7D09D9EBD350591CCD9A948AF1F55DA98F85D1B6_AdjustorThunk },
	{ 0x060001E2, CPUInstanceData_Get_WorldBounds_m59B12DD936A814F2E056E962A4889CD3FD473417_AdjustorThunk },
	{ 0x060001E3, CPUInstanceData_Get_MovedInCurrentFrame_m58A543967F67149E003AD3FE6FE4C45F9E87C869_AdjustorThunk },
	{ 0x060001E4, CPUInstanceData_Get_MovedInPreviousFrame_m9A0B9988AF8004828D13C96F89981F3C6AB15C29_AdjustorThunk },
	{ 0x060001E5, CPUInstanceData_Get_VisibleInPreviousFrame_m3E67A47CD7703E4345BEE5A7068855FE6A589BB5_AdjustorThunk },
	{ 0x060001E6, CPUInstanceData_Set_SharedInstance_mECEE018E02FA2F6502B59197A9AEF35FA2D35081_AdjustorThunk },
	{ 0x060001E7, CPUInstanceData_Set_LocalToWorldIsFlipped_mB2B243184FD3C74C54A5D8EC74542BC0E019EF29_AdjustorThunk },
	{ 0x060001E8, CPUInstanceData_Set_WorldAABB_m56D85CE12B7C589B053C471E803090DF9CA37073_AdjustorThunk },
	{ 0x060001E9, CPUInstanceData_Set_TetrahedronCacheIndex_mAC10B658EB1AB6EF405BC32A6DE8E88F7AA45AD5_AdjustorThunk },
	{ 0x060001EA, CPUInstanceData_Set_MovedInCurrentFrame_mB2081F967C2A8B6E6AC80F2E91DB3FD70851346F_AdjustorThunk },
	{ 0x060001EB, CPUInstanceData_Set_MovedInPreviousFrame_m096AF4BED24D559F13122047DF89E56C3C431B4D_AdjustorThunk },
	{ 0x060001EC, CPUInstanceData_Set_VisibleInPreviousFrame_m53D87829915906D1DD8E19F1F8D51631E9C02620_AdjustorThunk },
	{ 0x060001ED, CPUInstanceData_AsReadOnly_m90E90427C88E3F72D123C44F0E7957BACDDA8306_AdjustorThunk },
	{ 0x060001EE, ReadOnly_get_handlesLength_m50556F07B2F7144758202F76E202ED795E715DE9_AdjustorThunk },
	{ 0x060001EF, ReadOnly_get_instancesLength_m0E85E521D7DB61518C6988B08646386F20A17307_AdjustorThunk },
	{ 0x060001F0, ReadOnly__ctor_mFAEAA65432C0711F606AC98FD2320032B60A3309_AdjustorThunk },
	{ 0x060001F1, ReadOnly_InstanceToIndex_mA64D7447F6466C660DE83765B1B42673D922CCE2_AdjustorThunk },
	{ 0x060001F2, ReadOnly_IndexToInstance_m5BB2CA43E37DBA390AB7F29E6B5357A05226EE1B_AdjustorThunk },
	{ 0x060001F3, ReadOnly_IsValidInstance_m560D63CCD78D3661349A6134A30A29EFEDD3C30A_AdjustorThunk },
	{ 0x060001F4, ReadOnly_IsValidIndex_m2E036F94E4DFD41DF1CCA4148915594457CF0A9B_AdjustorThunk },
	{ 0x060001F5, CPUSharedInstanceData_get_instancesLength_m6C9D2E7F861FF43E23759E8816653646F41038B6_AdjustorThunk },
	{ 0x060001F6, CPUSharedInstanceData_set_instancesLength_m62AE1E8890A80D570FD3D11CD9E7C76B9D19124F_AdjustorThunk },
	{ 0x060001F7, CPUSharedInstanceData_get_instancesCapacity_mE40EF07D597175079E8DB6639E444A8346BD5C67_AdjustorThunk },
	{ 0x060001F8, CPUSharedInstanceData_set_instancesCapacity_mEBAB461BBCD3CD8127252B3F6156431A24943A69_AdjustorThunk },
	{ 0x060001F9, CPUSharedInstanceData_get_handlesLength_mF238FD08383695F2D7F707F2FA87279FE939CE61_AdjustorThunk },
	{ 0x060001FA, CPUSharedInstanceData_Initialize_m0D70CFA6688D2B1A165CDB20AB1760CBB4604A6E_AdjustorThunk },
	{ 0x060001FB, CPUSharedInstanceData_Dispose_m37AA8B81C3E42B81EC8E948B3F3DF9197B75E48C_AdjustorThunk },
	{ 0x060001FC, CPUSharedInstanceData_Grow_m0AED29A47C913CD92A9EA6281FABBBE76C4C26F3_AdjustorThunk },
	{ 0x060001FD, CPUSharedInstanceData_AddUnsafe_mD66018426B29B8FC77CBA218E04D6470D3A08B69_AdjustorThunk },
	{ 0x060001FE, CPUSharedInstanceData_SharedInstanceToIndex_m8D1697AC229D8D0D83FA702F6F440132FA123282_AdjustorThunk },
	{ 0x060001FF, CPUSharedInstanceData_IndexToSharedInstance_m7E58052D57FC86E8F4C0571D1213F2D4774BD323_AdjustorThunk },
	{ 0x06000200, CPUSharedInstanceData_InstanceToIndex_mA58DA1C16D52FECF5ED6238B168203837D75F87C_AdjustorThunk },
	{ 0x06000201, CPUSharedInstanceData_IsValidInstance_m33E12C093A6F0194B4D388782B15C2DD64616384_AdjustorThunk },
	{ 0x06000202, CPUSharedInstanceData_IsFreeInstanceHandle_m972EDE9AF42DAADE66DAA3B1E1A8F8A85B594BFA_AdjustorThunk },
	{ 0x06000203, CPUSharedInstanceData_IsValidIndex_m24116C260518DD680C3CDBD5BB1797B7764DDBCD_AdjustorThunk },
	{ 0x06000204, CPUSharedInstanceData_GetFreeInstancesCount_mC18CC336EB3ED96DA46A39019C480485B58A26F5_AdjustorThunk },
	{ 0x06000205, CPUSharedInstanceData_EnsureFreeInstances_m9276B1E0AE6DE0DFAE5F6FA4AFFB7DD9E6939CB0_AdjustorThunk },
	{ 0x06000206, CPUSharedInstanceData_AddNoGrow_mAF3FBC643ABC12255E182EE8F8D26024AD8BBACD_AdjustorThunk },
	{ 0x06000207, CPUSharedInstanceData_Add_m3AFDBA31C2E77E106BD970496C5F0EFBDCB3FCD3_AdjustorThunk },
	{ 0x06000208, CPUSharedInstanceData_Remove_m1BA13D76461FC093E7667EA3746FC4A6C3E99EF9_AdjustorThunk },
	{ 0x06000209, CPUSharedInstanceData_Get_RendererGroupID_m09F08F1763DD649A37D88B169CA50B2BD4F71768_AdjustorThunk },
	{ 0x0600020A, CPUSharedInstanceData_Get_MeshID_m70DD07FFBE12B43BA2546547FD26BC268E28EF43_AdjustorThunk },
	{ 0x0600020B, CPUSharedInstanceData_Get_LocalAABB_mB7D5A9EA9CD43BBEE3E938D31500A518FD3C02D7_AdjustorThunk },
	{ 0x0600020C, CPUSharedInstanceData_Get_Flags_m4F22F6B4DB27966ED5F633612B135EDCD274E341_AdjustorThunk },
	{ 0x0600020D, CPUSharedInstanceData_Get_LODGroupAndMask_m4A750431421271D4D2960E84D4B3AD83F96A8A42_AdjustorThunk },
	{ 0x0600020E, CPUSharedInstanceData_Get_GameObjectLayer_m75D2D7344A04236BFAE513142B29AB6CC8E7CB4D_AdjustorThunk },
	{ 0x0600020F, CPUSharedInstanceData_Get_RefCount_mE216F6749BB1B06E795C52C2433EE85E6C57DC01_AdjustorThunk },
	{ 0x06000210, CPUSharedInstanceData_Get_MaterialIDs_mCE6EC8119990B7324D473B6DFB27B91A076404FC_AdjustorThunk },
	{ 0x06000211, CPUSharedInstanceData_Set_RendererGroupID_m06EFE6568E7FABED464A0A091DB7D8DE05FC8719_AdjustorThunk },
	{ 0x06000212, CPUSharedInstanceData_Set_MeshID_m97990780D2C7B92B6A146C6FB243D08D2674EAA9_AdjustorThunk },
	{ 0x06000213, CPUSharedInstanceData_Set_LocalAABB_mFA0C8491B2FCD9E1F5BB25FF598C7520D689D08C_AdjustorThunk },
	{ 0x06000214, CPUSharedInstanceData_Set_Flags_m9C1CE3AC11B09B3B2C9F65422B2FD31D23C49F4D_AdjustorThunk },
	{ 0x06000215, CPUSharedInstanceData_Set_LODGroupAndMask_mD27421C024B1779830BEFA1274063B72D7C182CA_AdjustorThunk },
	{ 0x06000216, CPUSharedInstanceData_Set_GameObjectLayer_m97AEE383F753133901141CCD0040B7D2C2E41EEC_AdjustorThunk },
	{ 0x06000217, CPUSharedInstanceData_Set_RefCount_m7C5AF143DDC85304B2840A688BB2DD095A03B7CC_AdjustorThunk },
	{ 0x06000218, CPUSharedInstanceData_Set_MaterialIDs_m3C8A0B397DBC9FEFF9661015CC32232A4910D812_AdjustorThunk },
	{ 0x06000219, CPUSharedInstanceData_Set_m5E3312BE2456604D50D30AA09F973F7534EF7197_AdjustorThunk },
	{ 0x0600021A, CPUSharedInstanceData_SetDefault_m9241E530C50F996744FC4691DCD87C0E30602237_AdjustorThunk },
	{ 0x0600021B, CPUSharedInstanceData_AsReadOnly_mC186E7142A7E53BFE6D90BB0AB1841600BE7CBF8_AdjustorThunk },
	{ 0x0600021C, ReadOnly_get_handlesLength_mEAE03E557C81624E1F2CD280610A867E764EE456_AdjustorThunk },
	{ 0x0600021D, ReadOnly_get_instancesLength_m82B5F22A5EE9DF8AB94546D3B739C1EB36AD38D6_AdjustorThunk },
	{ 0x0600021E, ReadOnly__ctor_mDBC17F900B61DCF328F4BE2CDF0464E97F44614D_AdjustorThunk },
	{ 0x0600021F, ReadOnly_SharedInstanceToIndex_m7CD21D39C3285388B428D1CDCAA9AFB10A0178B4_AdjustorThunk },
	{ 0x06000220, ReadOnly_IndexToSharedInstance_mC97CAB3F6DB3C92BCB528F85F5B9E42728DE3389_AdjustorThunk },
	{ 0x06000221, ReadOnly_IsValidSharedInstance_mC2056D855CF0ED6FA050810CE1035680DC453A1A_AdjustorThunk },
	{ 0x06000222, ReadOnly_IsValidIndex_mA6B2DAE40129D5F001698A34D413EE0CEB24E61D_AdjustorThunk },
	{ 0x06000223, ReadOnly_InstanceToIndex_mE76C9EBF3ACCFDD5D73B3A99925CAE88DE2DDBE8_AdjustorThunk },
	{ 0x06000224, SmallIntegerArray_get_Valid_m717ECD9B73FD5A373F40B0A623CB2C8E188012C0_AdjustorThunk },
	{ 0x06000225, SmallIntegerArray_set_Valid_mF9C378DA26AB93E1AB38CCD7371CA23ABB6779D3_AdjustorThunk },
	{ 0x06000226, SmallIntegerArray__ctor_mD7860879B9A093BE8CD0CFD815D125FDB746090E_AdjustorThunk },
	{ 0x06000227, SmallIntegerArray_get_Item_m81A85EC298981F31BB405CA783D90BB96697C2DE_AdjustorThunk },
	{ 0x06000228, SmallIntegerArray_set_Item_mFEF4007D7D6DA125990D22E32B34ADCBF7056EA2_AdjustorThunk },
	{ 0x06000229, SmallIntegerArray_Dispose_m11D070ED0582D69C7F631CAF8C44F93183D73579_AdjustorThunk },
	{ 0x0600022F, EditorInstanceDataArrays_Initialize_m9893832043B3485AD59127B93D0872405CF77AFC_AdjustorThunk },
	{ 0x06000230, EditorInstanceDataArrays_Dispose_m16E99865063B2150E7BEFCE3BAE55831D9532B3B_AdjustorThunk },
	{ 0x06000231, EditorInstanceDataArrays_Grow_m57AFD6C22AA320DA1E98981C894EEB1F96C0B041_AdjustorThunk },
	{ 0x06000232, EditorInstanceDataArrays_Remove_mC3970E725DCDCEA7B636C2DCB4732C432787AE35_AdjustorThunk },
	{ 0x06000233, EditorInstanceDataArrays_SetDefault_mBB385CE94C9E46ADADB174CEFB393C2A19245724_AdjustorThunk },
	{ 0x06000234, ReadOnly__ctor_m2029FDC41CDEC78F717316642DB33534904EC6E2_AdjustorThunk },
	{ 0x0600025E, QueryRendererGroupInstancesCountJob_Execute_m78EBD1A4D27FC820DB994D726466672AE6698114_AdjustorThunk },
	{ 0x0600025F, ComputeInstancesOffsetAndResizeInstancesArrayJob_Execute_m7278A70AAF09ACD7DE06A58BBE53617ED59B3178_AdjustorThunk },
	{ 0x06000260, QueryRendererGroupInstancesJob_Execute_m689B8C96E8D3739314972C71A89F43C5DD8DD5EC_AdjustorThunk },
	{ 0x06000261, QueryRendererGroupInstancesMultiJob_Execute_m3ED5F46C8E2BBCFD2302EBF30F55D07CB4292593_AdjustorThunk },
	{ 0x06000262, QuerySortedMeshInstancesJob_Execute_m0B30D3DBCCB8FE5833D677F735387912A448CCDF_AdjustorThunk },
	{ 0x06000263, CalculateInterpolatedLightAndOcclusionProbesBatchJob_Execute_mB29926232C46662441F96B9405E9BE4D3A84109B_AdjustorThunk },
	{ 0x06000264, ScatterTetrahedronCacheIndicesJob_Execute_m157E3BAA3AA5106CD5629211DB4DF10C941B0942_AdjustorThunk },
	{ 0x06000265, TransformUpdateJob_Execute_m0C99BC8B89AD60C1B495E471F9F02FBEEF7F8342_AdjustorThunk },
	{ 0x06000266, ProbesUpdateJob_Execute_mFACBB95DE46DB9C879C2EE745CE37C4AEFB69AD6_AdjustorThunk },
	{ 0x06000267, MotionUpdateJob_Execute_m266055F93AABA39FD53E7EDBD6BAFE55F586891B_AdjustorThunk },
	{ 0x06000268, ReallocateInstancesJob_Execute_m8E05DC4EC3877696439237F2B9485F5218445828_AdjustorThunk },
	{ 0x06000269, FreeInstancesJob_Execute_mAED5669D78B3996A3298A55A6FF8625F90E6BADF_AdjustorThunk },
	{ 0x0600026A, FreeRendererGroupInstancesJob_Execute_m475670DFB6FB0B8F6EFD0FA9ACD4CD17EFF326BD_AdjustorThunk },
	{ 0x0600026B, UpdateRendererInstancesJob_Execute_m93597D2184F6460DD4610AD8E47868B1BA87A11C_AdjustorThunk },
	{ 0x0600026C, CollectInstancesLODGroupsAndMasksJob_Execute_m2EFF9AB4F3476E491E51143F7F60F79020A0F780_AdjustorThunk },
	{ 0x0600026D, GetVisibleNonProcessedTreeInstancesJob_Execute_mEFBAD1E57760EF2F4C2A4F4E6F6F40E6261C284A_AdjustorThunk },
	{ 0x0600026E, UpdateCompactedInstanceVisibilityJob_Execute_mDFAB7C16E38743439A74CCA9AA310B383D2BA55B_AdjustorThunk },
	{ 0x06000277, InstanceNumInfo_InitDefault_m8CC8A9E8EBF0EF16311F391E0312C46A7219A920_AdjustorThunk },
	{ 0x06000278, InstanceNumInfo__ctor_m0C64766A7024C367CB84BA96F01861E951351650_AdjustorThunk },
	{ 0x06000279, InstanceNumInfo__ctor_mCFED34B4FC73F15366339611E22502A8366B016C_AdjustorThunk },
	{ 0x0600027A, InstanceNumInfo_GetInstanceNum_m42CC8341EB7A73444DB20B9B64ACF5377CA7CE19_AdjustorThunk },
	{ 0x0600027B, InstanceNumInfo_GetInstanceNumIncludingChildren_m45CB40CE2452143C10B4FFC0F05FA7751E416368_AdjustorThunk },
	{ 0x0600027C, InstanceNumInfo_GetTotalInstanceNum_m55D6043571FB3440889F98A7D638F6A43D060982_AdjustorThunk },
	{ 0x0600027E, OccluderHandles_IsValid_m9FF256155930C1B40D4195C263AF38FCEB1B7904_AdjustorThunk },
	{ 0x0600027F, OccluderHandles_UseForOcclusionTest_m8E9F3CDFD218EC3C5A70DA375F156DC75CB2CAAD_AdjustorThunk },
	{ 0x06000280, OccluderHandles_UseForOccluderUpdate_m4376DEB9151FDF5678FDFE2ED525A3B3AC31AF03_AdjustorThunk },
	{ 0x06000281, IndirectBufferAllocInfo_IsEmpty_m47785BE361D9B989BE0455AD1884831AB99E4009_AdjustorThunk },
	{ 0x06000282, IndirectBufferAllocInfo_IsWithinLimits_mE8807A3A2DDE4F3E3E9091E2441D847F4BFC4067_AdjustorThunk },
	{ 0x06000283, IndirectBufferAllocInfo_GetExtraDrawInfoSlotIndex_m2EAD4191867631265610C43212C34A56A2DAD969_AdjustorThunk },
	{ 0x06000284, IndirectBufferContext__ctor_mC7F33D8E6E9DECDB5A167C9CFD68F1CBB6E7D7F5_AdjustorThunk },
	{ 0x06000285, IndirectBufferContext_Matches_m40E2A7974D4B205D4BB770D4A68635D99FE4C721_AdjustorThunk },
	{ 0x06000286, OccluderContext_get_subviewCount_m49FDF73077D5C9F1789BA0C35A39A7F78FCBF414_AdjustorThunk },
	{ 0x06000287, OccluderContext_IsSubviewValid_m88BE2A076AC851E9D11AB02B30ECE80A7E4D6BE0_AdjustorThunk },
	{ 0x06000288, OccluderContext_get_depthBufferSizeInOccluderPixels_mF734AB99EBF484188554B86CB2E07048E6138C36_AdjustorThunk },
	{ 0x06000289, OccluderContext_Dispose_mF02789AFBB76CD0F4491CDD8A83BCD15938F22D8_AdjustorThunk },
	{ 0x0600028A, OccluderContext_UpdateMipBounds_m71B416ED1B6D8827B30F8B1C1E69870328E6FCD9_AdjustorThunk },
	{ 0x0600028B, OccluderContext_AllocateTexturesIfNecessary_m4F0340120C018852B2A697194F87A6C539D6C127_AdjustorThunk },
	{ 0x0600028D, OccluderContext_SetupFarDepthPyramidConstants_m69DC95369344843C149515C9A2A9312DD0E99571_AdjustorThunk },
	{ 0x0600028E, OccluderContext_CreateFarDepthPyramid_mAE9A7D75C802A5AB3F91ED35C2BF61DA52C0AB0F_AdjustorThunk },
	{ 0x0600028F, OccluderContext_Import_m59CDBBE79F1A96B17BE009D5D561E35D69DA19D7_AdjustorThunk },
	{ 0x06000290, OccluderContext_PrepareOccluders_m558D900C293C248A3CEE8FE6640C98873039DF1D_AdjustorThunk },
	{ 0x06000291, OccluderContext_GetDebugOutput_m0B03B350C81D50A7DA8CEB969E70AF0F782675C5_AdjustorThunk },
	{ 0x06000294, IndirectBufferContextHandles_UseForOcclusionTest_m098E1C673230461EF27D4BC76A7186A5DADEEDAB_AdjustorThunk },
	{ 0x06000295, IndirectBufferContextStorage_get_instanceBuffer_mD965845788AD240262E1C46BB4BA405B73C8EA27_AdjustorThunk },
	{ 0x06000296, IndirectBufferContextStorage_get_instanceInfoBuffer_mC14C040CC88B2F11AFE1301EB1FB9C70E396E8E5_AdjustorThunk },
	{ 0x06000297, IndirectBufferContextStorage_get_argsBuffer_m81BE13A707FAF6B8F9361AEBCDD9CBA69611C334_AdjustorThunk },
	{ 0x06000298, IndirectBufferContextStorage_get_drawInfoBuffer_m0168780ED5CFCB3613F04F2C00561471352A5790_AdjustorThunk },
	{ 0x06000299, IndirectBufferContextStorage_get_visibleInstanceBufferHandle_mBD86573F23F6B0F8BF6C46C2996C449E571DC87F_AdjustorThunk },
	{ 0x0600029A, IndirectBufferContextStorage_get_indirectArgsBufferHandle_m7DF465FC2B94D42E1ABE0EE29E0EE4A1C850E925_AdjustorThunk },
	{ 0x0600029B, IndirectBufferContextStorage_ImportBuffers_mB024C5F1494D76DFEC07CDE6065AC7ACBB35E265_AdjustorThunk },
	{ 0x0600029C, IndirectBufferContextStorage_get_instanceInfoGlobalArray_m6D87D347531D8E3C00FB0967CCAEE69FED54FC41_AdjustorThunk },
	{ 0x0600029D, IndirectBufferContextStorage_get_drawInfoGlobalArray_mF90CEAECE8B83E043E462A4495421A574CC257D4_AdjustorThunk },
	{ 0x0600029E, IndirectBufferContextStorage_get_allocationCounters_mF22684F6D0214AE0755E3DFA8BA047C9E63C70FC_AdjustorThunk },
	{ 0x0600029F, IndirectBufferContextStorage_Init_mE1D9E857917AF5759F2E08D6B008C991CE5558AF_AdjustorThunk },
	{ 0x060002A0, IndirectBufferContextStorage_AllocateInstanceBuffers_m83064D5ABC47473BE4547D8CDB301CC989362AD4_AdjustorThunk },
	{ 0x060002A1, IndirectBufferContextStorage_FreeInstanceBuffers_mE035A24BD6179866735657D3D74C416AD39C597D_AdjustorThunk },
	{ 0x060002A2, IndirectBufferContextStorage_AllocateDrawBuffers_mCB3267B00E8328A5031AAB859542061D665CE0A0_AdjustorThunk },
	{ 0x060002A3, IndirectBufferContextStorage_FreeDrawBuffers_mAB74E77AD8B6348D492585D0ACB5AE42C2CEB171_AdjustorThunk },
	{ 0x060002A4, IndirectBufferContextStorage_Dispose_mD7278C175C4C23BCB9190D68CE8343B543A24302_AdjustorThunk },
	{ 0x060002A5, IndirectBufferContextStorage_SyncContexts_mB63C2465AF6D851CA68041FA1C12E24F24874A32_AdjustorThunk },
	{ 0x060002A6, IndirectBufferContextStorage_ResetAllocators_m327B8163AEEAFE360011EAEBFBD80C96C6403467_AdjustorThunk },
	{ 0x060002A7, IndirectBufferContextStorage_GrowBuffers_mAB6F1A326FB73F38678D592C809333FAE5A25D44_AdjustorThunk },
	{ 0x060002A8, IndirectBufferContextStorage_ClearContextsAndGrowBuffers_m0B059924A2DB871C3C80B03DAF54A47C8A47E573_AdjustorThunk },
	{ 0x060002A9, IndirectBufferContextStorage_TryAllocateContext_mC8D472C07C986BDCFC55CF8C4CE324230A38618C_AdjustorThunk },
	{ 0x060002AA, IndirectBufferContextStorage_TryGetContextIndex_m848B7C96E09D5CE2C6D12221D0B78EA4DCE77EF1_AdjustorThunk },
	{ 0x060002AB, IndirectBufferContextStorage_GetAllocInfoSubArray_m053D08700A1B5D4DB1AAF31B508E33E9FF418A4D_AdjustorThunk },
	{ 0x060002AC, IndirectBufferContextStorage_GetAllocInfo_m9BF31C85DBB94B0D0F1B82AF66DA8493AB1B2595_AdjustorThunk },
	{ 0x060002AD, IndirectBufferContextStorage_CopyFromStaging_mE99297FD5192A7F628FD7DC704E877280DB03891_AdjustorThunk },
	{ 0x060002AE, IndirectBufferContextStorage_GetLimits_m2E8DDC54719944794D7342725107396F59D5CF71_AdjustorThunk },
	{ 0x060002AF, IndirectBufferContextStorage_GetBufferContext_mD76FD0DD95CEE4D7D9B62F81573C4A05A5519941_AdjustorThunk },
	{ 0x060002B0, IndirectBufferContextStorage_SetBufferContext_mEC8E692A99E595592E12F55F94B7E234A340DC2A_AdjustorThunk },
	{ 0x060002B1, UpdateLODGroupTransformJob_Execute_m10AD64037A0D9FFAB351270A412B4B4342A6B8C5_AdjustorThunk },
	{ 0x060002B2, AllocateOrGetLODGroupDataInstancesJob_Execute_mF977F85F444872F9F0E507A0DADE7F2550050CA7_AdjustorThunk },
	{ 0x060002B3, UpdateLODGroupDataJob_Execute_m37868AFCE3AFCC80D46C8156CA2713096FE5EC3D_AdjustorThunk },
	{ 0x060002B4, FreeLODGroupDataJob_Execute_mABCBCD7B1C65E4A56F7F608884D1F9FB4FA1AAF7_AdjustorThunk },
	{ 0x060002C7, OcclusionTestComputeShader_Init_mC423CA21A41E4B44DB2715096F04D7498A80E960_AdjustorThunk },
	{ 0x060002C8, SilhouettePlaneCache_Init_mF14F33F1C7D6CD3704478C92314526747ABDFF0C_AdjustorThunk },
	{ 0x060002C9, SilhouettePlaneCache_Dispose_m52386469CD058770AAD5B4E19ADF603598BECEC5_AdjustorThunk },
	{ 0x060002CA, SilhouettePlaneCache_Update_m08599EDAF7CC1D053E0E54A8DF15F55E1E0B6342_AdjustorThunk },
	{ 0x060002CB, SilhouettePlaneCache_FreeUnusedSlots_m2249464604B48996B77945B9BBCDBDCECD2074C2_AdjustorThunk },
	{ 0x060002CC, SilhouettePlaneCache_GetSubArray_m0527F754CEEB54300C083A4BDAE9B56D026DA63C_AdjustorThunk },
	{ 0x060002CD, Slot__ctor_m0E99B79099FDB0098404A2FD223A9C029CFFF5D1_AdjustorThunk },
	{ 0x060002EF, OcclusionCullingCommonShaderVariables__ctor_m6098CCD0E939B2F9DE8715FF129DAE892745C610_AdjustorThunk },
	{ 0x06000327, RenderersParameters__ctor_mD3445734819B50610F768B8E6EF49822D5ABEE8A_AdjustorThunk },
	{ 0x0600032B, ParamInfo_get_valid_m198E32DB1AAD1F43EAD6964E4EF79E79D078AF7D_AdjustorThunk },
	{ 0x0600032E, ParallelBitArray_get_Length_m82FE0E2AC9FAB29DA67E28FFAEA04EB642955B08_AdjustorThunk },
	{ 0x0600032F, ParallelBitArray_get_IsCreated_m31B6CFD3C95548F523C3D074463B827B7CD7A535_AdjustorThunk },
	{ 0x06000330, ParallelBitArray__ctor_m7BB9EA31D2DF48FD4BE5D8773C539A76C5D4E6E4_AdjustorThunk },
	{ 0x06000331, ParallelBitArray_Dispose_m24CBECA125F3D0090E9786E6AF56CB2E6DE452C6_AdjustorThunk },
	{ 0x06000332, ParallelBitArray_Dispose_m3806D56E9788D3C69BD6C6985C6E2F949A89F9B7_AdjustorThunk },
	{ 0x06000333, ParallelBitArray_Resize_mA268182EEF9B41198BFC3780A74CF12D9B232011_AdjustorThunk },
	{ 0x06000334, ParallelBitArray_Set_mEDB30931801E1F71F11F62CCB23F3537EEE0F1EA_AdjustorThunk },
	{ 0x06000335, ParallelBitArray_Get_m250C22A6191BAF4C5B314EFAF451E391D251F2B0_AdjustorThunk },
	{ 0x06000336, ParallelBitArray_GetChunk_m7303392F0138448DF74E7A709F38B500B9461ED2_AdjustorThunk },
	{ 0x06000337, ParallelBitArray_SetChunk_m48943193199714BCF0925DD8E14C8EB651885629_AdjustorThunk },
	{ 0x06000338, ParallelBitArray_InterlockedReadChunk_m19B068CDEB9686FE2DD8A42BF99D2011EEAFA84F_AdjustorThunk },
	{ 0x06000339, ParallelBitArray_InterlockedOrChunk_m51A85AD8A6A6FFD317303DEABA6B9797B79CE658_AdjustorThunk },
	{ 0x0600033A, ParallelBitArray_ChunkCount_mE27E6F3D861AF09C5B31BD7F3964796B26A9C3B5_AdjustorThunk },
	{ 0x0600033B, ParallelBitArray_GetSubArray_m45E14868BB90EC98D0467ABDA3DAD1BD4BFC49DD_AdjustorThunk },
	{ 0x0600033C, ParallelBitArray_GetBitsArray_m31F100FDDB1EA0FE4E1592768ED14B843D0DD73D_AdjustorThunk },
	{ 0x0600033D, ParallelBitArray_FillZeroes_mDAE3DE6ACB91DE00B5DC5D50415B7A0F938A08BB_AdjustorThunk },
	{ 0x06000340, RadixSortBucketCountJob_Execute_mF3ADEB0523C3DE92CB5CFEF01B65E72C9AB7C023_AdjustorThunk },
	{ 0x06000342, RadixSortBatchPrefixSumJob_JobIndexPrefixSum_m9C47BE4B67FCFF29A8FA94D39589F9B9A2840EE3_AdjustorThunk },
	{ 0x06000343, RadixSortBatchPrefixSumJob_Execute_m434849692F7D93EF83545890B59FC96BF14AED93_AdjustorThunk },
	{ 0x06000344, RadixSortPrefixSumJob_Execute_mF1969BD6160F81BA429AF74A8944935FC83BC551_AdjustorThunk },
	{ 0x06000345, RadixSortBucketSortJob_Execute_mDB0AB3CD468DA898E41CBF3E9EF5BE26AD26E4D2_AdjustorThunk },
};
static const int32_t s_InvokerIndices[839] = 
{
	12563,
	12563,
	21889,
	12563,
	12579,
	12579,
	12380,
	15123,
	16711,
	20000,
	20024,
	12563,
	12205,
	9335,
	12563,
	12563,
	18290,
	18290,
	12563,
	12563,
	12205,
	9335,
	12563,
	12563,
	18290,
	18290,
	12563,
	12563,
	12205,
	9335,
	6467,
	12205,
	9335,
	12205,
	9335,
	12205,
	9335,
	20159,
	20161,
	20086,
	21757,
	21757,
	21757,
	20337,
	20326,
	20326,
	20326,
	20326,
	20337,
	20337,
	12205,
	12205,
	12205,
	6467,
	12380,
	12563,
	21856,
	12380,
	12323,
	9510,
	9510,
	9510,
	21856,
	12563,
	12380,
	12205,
	12205,
	12323,
	12323,
	12205,
	12380,
	12380,
	12563,
	12205,
	9335,
	12205,
	9335,
	12205,
	9335,
	12205,
	9335,
	12205,
	9335,
	12323,
	9453,
	12205,
	12471,
	9591,
	12205,
	12471,
	9591,
	12205,
	12205,
	9335,
	12563,
	12205,
	12205,
	12205,
	12563,
	12205,
	12563,
	12205,
	12380,
	12380,
	12380,
	12380,
	12380,
	12563,
	12205,
	12380,
	12380,
	12380,
	12380,
	12380,
	12380,
	12380,
	12563,
	12205,
	12380,
	12380,
	12380,
	17504,
	18725,
	18725,
	20043,
	11344,
	11344,
	20396,
	9460,
	17637,
	9460,
	14520,
	15921,
	1878,
	20397,
	9460,
	12205,
	17638,
	14897,
	14902,
	12380,
	12380,
	12380,
	2051,
	12563,
	12563,
	12563,
	9510,
	9510,
	12563,
	8846,
	8842,
	8846,
	8846,
	2038,
	2037,
	3712,
	3536,
	9555,
	12563,
	1914,
	1914,
	9455,
	12563,
	1872,
	21775,
	21725,
	20684,
	21856,
	16410,
	16409,
	21856,
	15060,
	14303,
	21775,
	12563,
	12563,
	21725,
	21743,
	21725,
	21725,
	21856,
	21856,
	20669,
	12380,
	12273,
	1947,
	12563,
	4844,
	21856,
	4847,
	4847,
	4847,
	4847,
	12563,
	3711,
	8846,
	1874,
	1873,
	4876,
	3706,
	8842,
	3711,
	3155,
	3537,
	3538,
	1231,
	3538,
	621,
	5294,
	1562,
	1563,
	21725,
	17041,
	15523,
	18298,
	12563,
	12563,
	4318,
	12563,
	21856,
	12563,
	12563,
	12323,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12563,
	20529,
	4318,
	9453,
	9453,
	-1,
	-1,
	-1,
	21856,
	6503,
	2748,
	20029,
	21725,
	6701,
	12323,
	6544,
	12323,
	12323,
	20536,
	1824,
	1850,
	9453,
	6613,
	9453,
	12563,
	3256,
	6613,
	9453,
	4318,
	11330,
	12563,
	12563,
	1733,
	4325,
	9510,
	12380,
	12563,
	12563,
	425,
	9510,
	12205,
	4756,
	55,
	816,
	37,
	1760,
	1995,
	12563,
	12563,
	8166,
	1386,
	9453,
	334,
	12563,
	12563,
	12563,
	12563,
	9510,
	9510,
	12563,
	21856,
	12563,
	12563,
	21856,
	12563,
	4756,
	4749,
	2031,
	593,
	1904,
	21755,
	12563,
	20187,
	9453,
	4318,
	4318,
	-1,
	-1,
	9307,
	9307,
	12563,
	9453,
	12563,
	5798,
	2673,
	9453,
	12563,
	11369,
	11375,
	11368,
	11379,
	11370,
	11330,
	11330,
	12205,
	12563,
	12563,
	12563,
	8846,
	8842,
	8895,
	12563,
	12563,
	11376,
	11377,
	2051,
	12181,
	12563,
	5831,
	12563,
	12380,
	1232,
	9455,
	8842,
	8846,
	8846,
	9555,
	8846,
	9307,
	3536,
	619,
	4318,
	12563,
	8166,
	12563,
	9510,
	9510,
	311,
	21757,
	12205,
	17300,
	3256,
	3298,
	3256,
	7162,
	7214,
	3705,
	12563,
	12635,
	12563,
	9510,
	7162,
	3705,
	9453,
	1763,
	-1,
	310,
	8106,
	12563,
	1907,
	12325,
	12323,
	7646,
	-1,
	9453,
	-1,
	-1,
	1381,
	1382,
	12563,
	21856,
	9510,
	1361,
	12563,
	9453,
	4722,
	8106,
	12563,
	21856,
	9510,
	12563,
	12563,
	12323,
	9453,
	12323,
	12323,
	12205,
	17317,
	20160,
	6609,
	7642,
	12323,
	21856,
	12323,
	9453,
	12205,
	6749,
	7754,
	12323,
	21856,
	12323,
	9453,
	12205,
	6569,
	7615,
	12323,
	21856,
	12323,
	9453,
	12205,
	4318,
	12563,
	12323,
	9453,
	12323,
	12563,
	12563,
	7212,
	7646,
	7646,
	7215,
	9449,
	12470,
	9590,
	12323,
	9453,
	12323,
	9453,
	12323,
	9453,
	12563,
	9453,
	9449,
	7642,
	7215,
	6609,
	6609,
	6613,
	12323,
	9453,
	9449,
	9449,
	9449,
	119,
	9449,
	8263,
	6609,
	5806,
	7642,
	5800,
	6609,
	6609,
	6609,
	3940,
	3938,
	3937,
	3939,
	3938,
	3938,
	3938,
	12600,
	12323,
	12323,
	9307,
	7642,
	7215,
	6609,
	6613,
	12323,
	9453,
	12323,
	9453,
	12323,
	9453,
	12563,
	9453,
	9590,
	7754,
	8264,
	3212,
	6749,
	6749,
	6613,
	12323,
	9453,
	9590,
	9590,
	9590,
	7754,
	7754,
	5805,
	7075,
	8406,
	7754,
	7754,
	5805,
	4851,
	4851,
	4849,
	4850,
	4852,
	4851,
	4851,
	4849,
	51,
	9590,
	12601,
	12323,
	12323,
	9307,
	7754,
	8264,
	6749,
	6613,
	3212,
	12205,
	9335,
	4318,
	7646,
	4318,
	12563,
	-1,
	-1,
	-1,
	-1,
	-1,
	9453,
	12563,
	9453,
	4318,
	9453,
	9307,
	20370,
	20370,
	12205,
	12600,
	12601,
	11328,
	1987,
	12563,
	7646,
	7646,
	9453,
	9453,
	9453,
	278,
	308,
	1351,
	185,
	1867,
	3847,
	303,
	1871,
	1867,
	3811,
	8846,
	8842,
	1759,
	3847,
	620,
	1292,
	3847,
	3537,
	3538,
	1231,
	3538,
	3534,
	12205,
	293,
	9307,
	-1,
	21856,
	21856,
	4318,
	12563,
	4318,
	4318,
	4318,
	9453,
	9453,
	4318,
	4318,
	9453,
	12563,
	12563,
	12563,
	9453,
	9453,
	4318,
	4318,
	21856,
	21856,
	21856,
	20196,
	18139,
	21856,
	20196,
	20337,
	12563,
	4318,
	4318,
	7646,
	7646,
	12323,
	20369,
	12205,
	9510,
	9510,
	12205,
	6467,
	12323,
	9460,
	1616,
	12323,
	6613,
	12550,
	12563,
	12563,
	9335,
	15051,
	3582,
	203,
	8158,
	9307,
	12387,
	21856,
	20162,
	9510,
	12380,
	12380,
	12380,
	12380,
	12288,
	12288,
	7194,
	11326,
	11325,
	11330,
	12563,
	9453,
	12563,
	9453,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	7646,
	7646,
	5232,
	7192,
	4722,
	7195,
	7193,
	4305,
	9453,
	12563,
	9453,
	12563,
	11378,
	11371,
	12323,
	12323,
	2058,
	12563,
	9307,
	9307,
	8846,
	21856,
	20452,
	17680,
	15897,
	15897,
	20640,
	20450,
	20450,
	17689,
	9510,
	12563,
	12563,
	1965,
	4318,
	5253,
	1993,
	9510,
	20027,
	333,
	15030,
	15030,
	1469,
	728,
	4747,
	3583,
	1385,
	1644,
	4135,
	8163,
	9510,
	6613,
	2819,
	12563,
	7646,
	9453,
	12563,
	12563,
	21856,
	4759,
	21856,
	12563,
	12563,
	12563,
	12563,
	21856,
	12563,
	4756,
	4759,
	4756,
	1309,
	21807,
	12439,
	12380,
	12323,
	11641,
	11336,
	11371,
	12323,
	12323,
	12323,
	12473,
	12205,
	12600,
	12601,
	12635,
	11328,
	12471,
	12380,
	12380,
	12380,
	1914,
	12563,
	7646,
	7646,
	9307,
	12563,
	9307,
	9307,
	8846,
	8846,
	3811,
	3535,
	8846,
	8842,
	3537,
	3538,
	1231,
	3538,
	12563,
	3144,
	1870,
	1866,
	1869,
	3706,
	9335,
	8834,
	12563,
	8846,
	9307,
	3534,
	7215,
	293,
	12380,
	12563,
	17542,
	9307,
	21856,
	16652,
	21856,
	12205,
	-1,
	-1,
	12323,
	12205,
	1993,
	12563,
	9460,
	9453,
	4244,
	6613,
	8410,
	4491,
	8410,
	4491,
	12323,
	8167,
	11332,
	9453,
	20264,
	18116,
	9453,
	20180,
	3257,
	9453,
	9453,
	9453,
	21856,
	21856,
};
static const Il2CppTokenRangePair s_rgctxIndices[9] = 
{
	{ 0x02000053, { 0, 4 } },
	{ 0x02000054, { 4, 8 } },
	{ 0x06000184, { 12, 1 } },
	{ 0x0600018C, { 13, 1 } },
	{ 0x0600018E, { 14, 2 } },
	{ 0x0600018F, { 16, 4 } },
	{ 0x0600025B, { 20, 6 } },
	{ 0x0600032C, { 26, 3 } },
	{ 0x0600032D, { 29, 1 } },
};
extern const uint32_t g_rgctx_FindNonRegisteredInstancesJob_1_tDAF2B034C0E609E2703AA44DE4160DF2E1DCFB93;
extern const uint32_t g_rgctx_NativeParallelHashMap_2_t75F6446DE935DC0DDD1C2A717B531E7A68ABCE93;
extern const uint32_t g_rgctx_NativeParallelHashMap_2_ContainsKey_m23A3AF0C4477862B146218C1FCBA4117FE7BED3C;
extern const uint32_t g_rgctx_NativeParallelHashMap_2_t75F6446DE935DC0DDD1C2A717B531E7A68ABCE93;
extern const uint32_t g_rgctx_RegisterNewInstancesJob_1_t82FA13850A0E6F3D9F08BC990DBC5C6B976EA9CD;
extern const uint32_t g_rgctx_ParallelWriter_tA8CDA67DA69EFD2597BA1E7A471330792DBD026F;
extern const uint32_t g_rgctx_NativeArray_1_tE943096AC0D0C96EB24760E1AE16B8B7CE73E0E1;
extern const uint32_t g_rgctx_NativeArray_1_get_Item_mA4DBE85D5A0274DE0F3E7CEE0F8FB6989A949513;
extern const uint32_t g_rgctx_NativeArray_1_tE943096AC0D0C96EB24760E1AE16B8B7CE73E0E1;
extern const uint32_t g_rgctx_T_tEE913C7D9B7553485F0BC832451DAB52A03D3A2B;
extern const uint32_t g_rgctx_ParallelWriter_TryAdd_mFD4D3377AB5F54FB0480E1852404CCDE2389BE85;
extern const uint32_t g_rgctx_ParallelWriter_tA8CDA67DA69EFD2597BA1E7A471330792DBD026F;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_tB93F845DBDA86B86E875C4B8733BA04EF2079EF4_mC1117E52BBA0452311F896F34EC5F717A8C11EC9;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t9E2BC6384D3C627F4120CA7164476B41F6EF7AA4_m914262E127293EC1BCE79DEEB893EF0EB63C363A;
extern const uint32_t g_rgctx_NativeArray_1_t3CD1365D537A2522B7C4D7465DFD26F5A6648A37;
extern const uint32_t g_rgctx_GPUInstanceDataBufferUploader_WriteInstanceDataJob_TisT_t996EE38E80212E224255DA4B2B4FBEA40E749C74_m5B9DCE67AD22F60678E7A9BE1B97C03D8243A4C1;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_tC0CB470D64AD7A60EAA5044E2C0D29708268A1E8_m53B5E2AC41CE70357DB9319D78311386CA0A6EB8;
extern const uint32_t g_rgctx_NativeArray_1_tFC41F54943C6ABF8383700B191400D9ACCC7F8A6;
extern const uint32_t g_rgctx_NativeArray_1_Reinterpret_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_mB2BE21A398F87A262169C466AA756EE105CB1143;
extern const uint32_t g_rgctx_NativeArray_1_tFC41F54943C6ABF8383700B191400D9ACCC7F8A6;
extern const uint32_t g_rgctx_NativeList_1U26_tA81C87FFF6BBD659600A85358F015CC2BE156C60;
extern const uint32_t g_rgctx_NativeList_1_tDD7B29B449688208F82E01BCB2FC739CC145640B;
extern const uint32_t g_rgctx_NativeList_1_GetUnsafeList_mC14052B83905500663287FDCDC2179ED7A31DA72;
extern const uint32_t g_rgctx_NativeList_1_tDD7B29B449688208F82E01BCB2FC739CC145640B;
extern const uint32_t g_rgctx_UnsafeList_1U2A_tD48451A85C691AF76336014E1209BDFAE40DFC06;
extern const uint32_t g_rgctx_UnsafeList_1_t0BE57485CACA9167C3BECBB0C3CADCB8B703D4D8;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t786800A697A5649BD68C130E20B971A79AB25767_mC00A89FBAFC0107F479321BF6CA5C7928FF5A7F6;
extern const uint32_t g_rgctx_UnsafeUtility_AlignOf_TisT_t786800A697A5649BD68C130E20B971A79AB25767_m2E66B0CFB74EED0070E50CBC8F976A2A7D423B43;
extern const uint32_t g_rgctx_TU2A_t3DE8E903FF8FC7FF2141A921D9B72BEC4B18F6C2;
extern const uint32_t g_rgctx_TU2A_t5A4FC207791C4374F473C71836DCEB68BFDE4D31;
static const Il2CppRGCTXDefinition s_rgctxValues[30] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FindNonRegisteredInstancesJob_1_tDAF2B034C0E609E2703AA44DE4160DF2E1DCFB93 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeParallelHashMap_2_t75F6446DE935DC0DDD1C2A717B531E7A68ABCE93 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeParallelHashMap_2_ContainsKey_m23A3AF0C4477862B146218C1FCBA4117FE7BED3C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeParallelHashMap_2_t75F6446DE935DC0DDD1C2A717B531E7A68ABCE93 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_RegisterNewInstancesJob_1_t82FA13850A0E6F3D9F08BC990DBC5C6B976EA9CD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ParallelWriter_tA8CDA67DA69EFD2597BA1E7A471330792DBD026F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tE943096AC0D0C96EB24760E1AE16B8B7CE73E0E1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Item_mA4DBE85D5A0274DE0F3E7CEE0F8FB6989A949513 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tE943096AC0D0C96EB24760E1AE16B8B7CE73E0E1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tEE913C7D9B7553485F0BC832451DAB52A03D3A2B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ParallelWriter_TryAdd_mFD4D3377AB5F54FB0480E1852404CCDE2389BE85 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ParallelWriter_tA8CDA67DA69EFD2597BA1E7A471330792DBD026F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_tB93F845DBDA86B86E875C4B8733BA04EF2079EF4_mC1117E52BBA0452311F896F34EC5F717A8C11EC9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t9E2BC6384D3C627F4120CA7164476B41F6EF7AA4_m914262E127293EC1BCE79DEEB893EF0EB63C363A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t3CD1365D537A2522B7C4D7465DFD26F5A6648A37 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GPUInstanceDataBufferUploader_WriteInstanceDataJob_TisT_t996EE38E80212E224255DA4B2B4FBEA40E749C74_m5B9DCE67AD22F60678E7A9BE1B97C03D8243A4C1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_tC0CB470D64AD7A60EAA5044E2C0D29708268A1E8_m53B5E2AC41CE70357DB9319D78311386CA0A6EB8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tFC41F54943C6ABF8383700B191400D9ACCC7F8A6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Reinterpret_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_mB2BE21A398F87A262169C466AA756EE105CB1143 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tFC41F54943C6ABF8383700B191400D9ACCC7F8A6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeList_1U26_tA81C87FFF6BBD659600A85358F015CC2BE156C60 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeList_1_tDD7B29B449688208F82E01BCB2FC739CC145640B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1_GetUnsafeList_mC14052B83905500663287FDCDC2179ED7A31DA72 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeList_1_tDD7B29B449688208F82E01BCB2FC739CC145640B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1U2A_tD48451A85C691AF76336014E1209BDFAE40DFC06 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t0BE57485CACA9167C3BECBB0C3CADCB8B703D4D8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t786800A697A5649BD68C130E20B971A79AB25767_mC00A89FBAFC0107F479321BF6CA5C7928FF5A7F6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AlignOf_TisT_t786800A697A5649BD68C130E20B971A79AB25767_m2E66B0CFB74EED0070E50CBC8F976A2A7D423B43 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t3DE8E903FF8FC7FF2141A921D9B72BEC4B18F6C2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t5A4FC207791C4374F473C71836DCEB68BFDE4D31 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_RenderPipelines_GPUDriven_Runtime_CodeGenModule;
const Il2CppCodeGenModule g_Unity_RenderPipelines_GPUDriven_Runtime_CodeGenModule = 
{
	"Unity.RenderPipelines.GPUDriven.Runtime.dll",
	839,
	s_methodPointers,
	350,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	9,
	s_rgctxIndices,
	30,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
