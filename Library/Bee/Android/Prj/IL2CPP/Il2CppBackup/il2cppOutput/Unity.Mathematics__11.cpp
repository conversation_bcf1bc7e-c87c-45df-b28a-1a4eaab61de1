﻿#include "pch-cpp.hpp"






struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct IFormatProvider_tC202922D43BFF3525109ABF3FB79625F5646AB52;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct String_t;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;

IL2CPP_EXTERN_C RuntimeClass* ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteralBA12CD7184B95EC551FFCA240321EB14D0272CE2;
IL2CPP_EXTERN_C String_t* _stringLiteralFB182D98F776AC1C061FA5C163FE7F6E7C08B5BD;
IL2CPP_EXTERN_C String_t* _stringLiteralFEB995DBF7004A48F3EDB181C599FB99B4A380FB;
IL2CPP_EXTERN_C const RuntimeMethod* Plane_CheckPlaneIsNormalized_m6408EC4BE1D7A0ADB6832288E286D9C70404B888_RuntimeMethod_var;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CPrivateImplementationDetailsU3E_t9B4B9DAF21D9563D4BA9D73CA7897B99567AAEF9  : public RuntimeObject
{
};
struct Math_t02C7364C06982BA8F186B38E3C811582A40AB897  : public RuntimeObject
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F 
{
	double ___m_value;
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B 
{
	uint32_t ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 
{
	bool ___x;
	bool ___y;
	bool ___z;
};
struct bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 
{
	bool ___x;
	bool ___y;
	bool ___z;
	bool ___w;
};
struct double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 
{
	double ___x;
	double ___y;
	double ___z;
	double ___w;
};
struct float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E 
{
	float ___x;
	float ___y;
	float ___z;
};
struct float4_t89D9A294E7A79BD81BFBDD18654508532958555E 
{
	float ___x;
	float ___y;
	float ___z;
	float ___w;
};
struct int4_tBA77D4945786DE82C3A487B33955EA1004996052 
{
	int32_t ___x;
	int32_t ___y;
	int32_t ___z;
	int32_t ___w;
};
struct uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B 
{
	uint32_t ___x;
	uint32_t ___y;
	uint32_t ___z;
};
struct uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 
{
	uint32_t ___x;
	uint32_t ___y;
	uint32_t ___z;
	uint32_t ___w;
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D5559_tE7FBCADDA517313CAB8A3CC17B00BAC533A03F2D 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D5559_tE7FBCADDA517313CAB8A3CC17B00BAC533A03F2D__padding[5559];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D8823_tF0E95C5ADA206757E04D85FFE80F26DF555EC27D 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D8823_tF0E95C5ADA206757E04D85FFE80F26DF555EC27D__padding[8823];
	};
};
#pragma pack(pop, tp)
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___Min;
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___Max;
};
struct Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 
{
	float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___NormalAndDistance;
};
struct bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 
{
	bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 ___c0;
	bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 ___c1;
	bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 ___c2;
	bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 ___c3;
};
struct double4x4_tB452F9489714C6B8D74D46CA2CF1F0CA8F185D3C 
{
	double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 ___c0;
	double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 ___c1;
	double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 ___c2;
	double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 ___c3;
};
struct float3x3_tB318DB8C7E54B6CA9E14EB9AC7F5964C1189FC79 
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___c0;
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___c1;
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___c2;
};
struct float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 
{
	float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___c0;
	float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___c1;
	float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___c2;
	float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___c3;
};
struct int4x4_tEF359B46039347312A6AC932AD04CA9CE675AB30 
{
	int4_tBA77D4945786DE82C3A487B33955EA1004996052 ___c0;
	int4_tBA77D4945786DE82C3A487B33955EA1004996052 ___c1;
	int4_tBA77D4945786DE82C3A487B33955EA1004996052 ___c2;
	int4_tBA77D4945786DE82C3A487B33955EA1004996052 ___c3;
};
struct quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 
{
	float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___value;
};
struct uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A 
{
	uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___c0;
	uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___c1;
	uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___c2;
	uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___c3;
};
struct RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD 
{
	quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 ___rot;
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___pos;
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
	String_t* ____paramName;
};
struct U3CPrivateImplementationDetailsU3E_t9B4B9DAF21D9563D4BA9D73CA7897B99567AAEF9_StaticFields
{
	__StaticArrayInitTypeSizeU3D5559_tE7FBCADDA517313CAB8A3CC17B00BAC533A03F2D ___F06F2BC33791D95CCB2DFD42D18B50237CB6DE6F027A49E4FA389EE209D657BC;
	__StaticArrayInitTypeSizeU3D8823_tF0E95C5ADA206757E04D85FFE80F26DF555EC27D ___FAB1CDCCDA9509F995678B2EA3B40FAA97E30B4CEA871F8FBD535C3C22EFA761;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5_StaticFields
{
	double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 ___zero;
};
struct float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E_StaticFields
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___zero;
};
struct float4_t89D9A294E7A79BD81BFBDD18654508532958555E_StaticFields
{
	float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___zero;
};
struct int4_tBA77D4945786DE82C3A487B33955EA1004996052_StaticFields
{
	int4_tBA77D4945786DE82C3A487B33955EA1004996052 ___zero;
};
struct uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B_StaticFields
{
	uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B ___zero;
};
struct uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9_StaticFields
{
	uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___zero;
};
struct double4x4_tB452F9489714C6B8D74D46CA2CF1F0CA8F185D3C_StaticFields
{
	double4x4_tB452F9489714C6B8D74D46CA2CF1F0CA8F185D3C ___identity;
	double4x4_tB452F9489714C6B8D74D46CA2CF1F0CA8F185D3C ___zero;
};
struct float3x3_tB318DB8C7E54B6CA9E14EB9AC7F5964C1189FC79_StaticFields
{
	float3x3_tB318DB8C7E54B6CA9E14EB9AC7F5964C1189FC79 ___identity;
	float3x3_tB318DB8C7E54B6CA9E14EB9AC7F5964C1189FC79 ___zero;
};
struct float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2_StaticFields
{
	float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 ___identity;
	float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 ___zero;
};
struct int4x4_tEF359B46039347312A6AC932AD04CA9CE675AB30_StaticFields
{
	int4x4_tEF359B46039347312A6AC932AD04CA9CE675AB30 ___identity;
	int4x4_tEF359B46039347312A6AC932AD04CA9CE675AB30 ___zero;
};
struct quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4_StaticFields
{
	quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 ___identity;
};
struct uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A_StaticFields
{
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___identity;
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___zero;
};
struct RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD_StaticFields
{
	RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD ___identity;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918  : public RuntimeArray
{
	ALIGN_FIELD (8) RuntimeObject* m_Items[1];

	inline RuntimeObject* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, RuntimeObject* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline RuntimeObject* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, RuntimeObject* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};



IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_c0, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_c1, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___2_c2, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___3_c3, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* __this, uint32_t ___0_x, uint32_t ___1_y, uint32_t ___2_z, uint32_t ___3_w, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_mDF3E33FF44DA38AB4B132456C1BD34AC1546027F_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, uint32_t ___0_m00, uint32_t ___1_m01, uint32_t ___2_m02, uint32_t ___3_m03, uint32_t ___4_m10, uint32_t ___5_m11, uint32_t ___6_m12, uint32_t ___7_m13, uint32_t ___8_m20, uint32_t ___9_m21, uint32_t ___10_m22, uint32_t ___11_m23, uint32_t ___12_m30, uint32_t ___13_m31, uint32_t ___14_m32, uint32_t ___15_m33, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Implicit_m194DE3172FAB8E0625B0F5826454FE3C91DA68DF_inline (uint32_t ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_mB044E6CC91777237724520F26351840B0932A1E8_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, uint32_t ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* __this, uint32_t ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 math_select_m59B92233FC64029995EE77D0D626FE89FF714D19_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_falseValue, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_trueValue, bool ___2_test, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_m00363B3E264E5A1B4E4013E66F1EE47858161688_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, bool ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 math_select_m94A91F3756E494BE8FDC90304F980A0BB366550E_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_falseValue, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_trueValue, bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 ___2_test, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_mCE45F10896048B5121C7A9C4B4DD4FFFD893BCD8_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Explicit_m52EA594D5CFD520009B14F44F586D6A36EE1F0F2_inline (int32_t ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_m6962CB8B6F4B51CE18577B571A8DE0D3367C9213_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, int32_t ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Explicit_m4BA2A6F8AC721A602D73382462C962382691DD70_inline (int4_tBA77D4945786DE82C3A487B33955EA1004996052 ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_mC40D16C3F1B689A4EDAA2EC86DA52D0E371C752B_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, int4x4_tEF359B46039347312A6AC932AD04CA9CE675AB30 ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Explicit_mF62DF180DDD889D04C4B894C1CC11C4FFB757C56_inline (float ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_m94AD3FDC6F57005991FE28510E0516643C9250CE_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, float ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Explicit_mCEEF0983FAE417D4A4DAC4BAA4FD415E6770C20D_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_mFA79D1F3F53003B4BC1960DACFAE87DA465EDD2D_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Explicit_m642DF9B9E499124B9322085581DCE979B7CCA961_inline (double ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_m8B2B846C47859C1A0F6BBDD6804B0F20AA4CA415_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, double ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Explicit_m461E06F58E3962E8ECF5E15E0119B2FC2D391FF5_inline (double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_m94C7CE06BDF397CA5348A1EA85358374782BA6DA_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, double4x4_tB452F9489714C6B8D74D46CA2CF1F0CA8F185D3C ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Multiply_mDD93D0730642A1089848321B9B0E5E923EE575ED_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Multiply_m40354E397B1D99D73B5333378C8698C087A1A2D6_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Multiply_mF7AFA2D5F25EC1F6F7AEC21564D18A46C7B37B48_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Addition_m391200EB43B6C4CDD219079A0D3CDFFA35B0B652_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Addition_m3968FBC6698B10C2DC1D0222C72BB0F67CA1E6D6_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Addition_m6F5942C7C169158FD66ECCE72E54D0494886F669_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Subtraction_mF815D62CBBE8EAE82A64035CA71DA415A54DC64F_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Subtraction_mA5D1B1DE817EFBC8685C2EB3CAB6EF0DFFDC3F92_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Subtraction_m4DA7085E5BF60C4AD13FC74177EEC6AC6FA65340_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Division_m767BDF5F276A7AE45164F10D04573BE5DA17DDB5_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Division_m9612CEBFC3CB81D66C58ABB1E67077FC69B657BF_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Division_mED3DE29C6531D09E8CB0E75B7DFA0380CE7A8218_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Modulus_m76B34ADA3D74231D5BE5A809380D075D3B5EF921_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Modulus_m8C76FB3CB8FA2D498525823CEC6DDE42B5B4FDDF_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Modulus_m6319F62EDDC062DCF736D348ABA648B560259204_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Increment_m64BB67C319046CB11232CE66183D3A2B46917DF0_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_val, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Decrement_m0F6103CB4C76260923D9B080C4D41DFCD63B28B1_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_val, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_LessThan_m3628E2E315AC8DA6CB3C6B655173D481C19FDD19_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline (bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936* __this, bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 ___0_c0, bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 ___1_c1, bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 ___2_c2, bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 ___3_c3, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_LessThan_mB35B79849BA45A0BB50B6C58EA63AEBC816D06A2_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_LessThan_mD054F90ECC9EEC5FBBA6A5B185C12C3FA51D717E_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_LessThanOrEqual_m8A52F50B097C2A9BDFA919675AB046B390F9B324_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_LessThanOrEqual_m8B0420CC432FFED5FB94D3772815893A6114B180_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_LessThanOrEqual_m3C3804A4D76E0D681B39390EF7A17E37E88F7540_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_GreaterThan_m77A71F392617EFC97C92684F40A3C05B99C8EB6B_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_GreaterThan_mF8C8A88E77644D1B6F50F98D4DF718ED9DD37514_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_GreaterThan_mDD1A94F81F32D1B5BEDD7794CEBD9A1CE8FD2C46_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_GreaterThanOrEqual_m35B238651EBAFA2F8241FF4A0E012CEA20F53EAE_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_GreaterThanOrEqual_m06C3ADD093FCFD022D46878D3BC19CD3049521B0_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_GreaterThanOrEqual_mD6829718DDCC243DA19306FEEA05BF4AD83AFB66_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_UnaryNegation_m4E2B5BF3588706130CB50ACA4CDA4617BD9C42D3_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_val, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_UnaryPlus_m6DD02E0B287D5C0A9C8BD14709A01A113FF7F888_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_val, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_LeftShift_m5E109F47429DAA6E55073B8BBD9988FF9B18E4A2_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_x, int32_t ___1_n, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_RightShift_m277ED9AFF0C41C957E79413C0B4A19EB28503EED_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_x, int32_t ___1_n, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_Equality_m5E8D2F6C5A385AACC9A966226C00DD40E44046E3_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_Equality_mA7515DAB8E3AAD2B5BDA4ED9653F0BF393419A91_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_Equality_m9B94A5CD832272DCFD0B9C99D480556656579A6D_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_Inequality_m58973FE7AABF39D4BAB65975F3A537F31DFE279E_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_Inequality_mEED47D66CB338F4A7E98F9C4B5372FF9E9452A79_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_Inequality_mAEA94A9E6197044FE2EA3FFF6F4EA234E74F2F2F_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_OnesComplement_m694E43D937D79E275E9633CF8BA49DE0D11E929F_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_val, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_BitwiseAnd_m1A9CB1A2976212FE13C84F970D4C59C30A54D875_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_BitwiseAnd_mEF924757B2BE1F41834950F7880B93C25B812044_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_BitwiseAnd_mD7D621F58D732913D0B45E67C22F12A34E249C28_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_BitwiseOr_mDA88C9E25D0910D512ABABDC200D6E3A2E68B573_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_BitwiseOr_mA3F6C811A2EF145F2D7240A9B07801089AC380BD_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_BitwiseOr_mA1E41EFAAF04FDF3A9B4253C9BD99E55B447255C_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_ExclusiveOr_m5B57FB4F864B88CB06B4949AA275A70D02BF7889_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_ExclusiveOr_m4A4BB6F529F97A0027A7F9F989D21BC84F474391_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_ExclusiveOr_m18704377E7E81E1741DAAB2CBCB23D7BB6A00CC6_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* uint4x4_get_Item_m4F543805E3B92E11399E5829D2D3984CE8657C4A (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* __this, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool uint4x4_Equals_m1DADC37353246BE77BB8D6E000FB2E79CD33D8CF_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_rhs, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool uint4x4_Equals_mC9C251DF1B71FA38A4CFAD07CC3B50E4C0132A9A (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, RuntimeObject* ___0_o, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint32_t math_hash_m5DD32EF0CE916EDF69ED64D20E961217FB867527_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t uint4x4_GetHashCode_m812B7AA9E1BCBCCDEB7E00AE247C960964B3EB65_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Format_m918500C1EFB475181349A79989BB79BB36102894 (String_t* ___0_format, ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___1_args, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* uint4x4_ToString_m636D074F6F88F38EB2590202338118CE9AA9468B_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE (uint32_t* __this, String_t* ___0_format, RuntimeObject* ___1_provider, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* uint4x4_ToString_m97543E4A4C7E05B6B1C86437E56F4246AA5FC456_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, String_t* ___0_format, RuntimeObject* ___1_formatProvider, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_min, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_max, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Multiply_m6E5DC552C8B0F9A180298BD9197FF47B14E0EA81_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 MinMaxAABB_CreateFromCenterAndHalfExtents_mF18074A621916107BF592378F72B6F113294F508_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_center, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_halfExtents, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E MinMaxAABB_get_Extents_m7E246B61BA832B3FB5EB8DCA4A35EC93688F105C (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E MinMaxAABB_get_HalfExtents_m3656E833DBE99FE9D023F0E4B1497CF35F6BC948 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E MinMaxAABB_get_Center_mC27A51CF13A95A053DEA07566A1F1900CFD98F4C (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 float3_op_LessThanOrEqual_m18273875F0537224587D1622DD53562971D9FF48_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool math_all_mB8957D1E684773F171F74448AD9591F3619890A4_inline (bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MinMaxAABB_get_IsValid_m84D96167E3AE9516D86FEC54B1CD138BD53FC5B8 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_y, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float MinMaxAABB_get_SurfaceArea_m542F0EA64B3A4C7975BAB0C3EBD354D79F34B1CE (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 float3_op_GreaterThanOrEqual_m01767B59951623AD803736AB63E12D9BC6FC1AAE_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 bool3_op_BitwiseAnd_mE44CC5838094A40F1F605A7798994197165A63E1_inline (bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___0_lhs, bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MinMaxAABB_Contains_m507D89756D76CA0E2CBD8CAD23C6CE27A5F39089_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MinMaxAABB_Contains_m8AB762A87FD22983A2A5ED2C96D3C559876D8823_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MinMaxAABB_Overlaps_mEEC801434D524543F9E6D5D77A642A5184F8C4D9_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Subtraction_m111BEEA770E140739DDC6A3410736DFF7EE32045_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Addition_mABF24BC9A16C272B9F5AB21A601B9D9A831F8C43_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MinMaxAABB_Expand_m8574B6375684AC91E413410881D03B71C400DC75_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float ___0_signedDistance, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_min_m13CC8D5B7844D954C3125DD72831C693AB8A7FF5_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_max_m247D41258606F80861E72309300DF6A3F8B50AE4_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MinMaxAABB_Encapsulate_m7043627B976BE96D6D734288EEA09563352F753B_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MinMaxAABB_Encapsulate_mE01CED4767A6B50D846AC30AFCA49A91BA820CD5_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MinMaxAABB_Equals_m6DC492AB1804679250EC6C97CC6DF32299EA8E11_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_other, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Format_mFB7DA489BD99F4670881FF50EC017BFB0A5C0987 (String_t* ___0_format, RuntimeObject* ___1_arg0, RuntimeObject* ___2_arg1, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* MinMaxAABB_ToString_mD299FEC6092F6072F7BC91773DA766E0E61DEC3F_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_rotate_m68CD27B1D0643EA356D0AB41ECB004CE094FDA3F_inline (quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 ___0_q, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_abs_mC7F2BBD861835C82A0A47A47A44B73E704D7F63B_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_transform_m5F6B69A9C0E6E1AF63D8112D8753394891972E44_inline (RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD ___0_a, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_pos, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void float3x3__ctor_m3EA9552B4922CACBAC36054687E8EF9C1ED99951 (float3x3_tB318DB8C7E54B6CA9E14EB9AC7F5964C1189FC79* __this, float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 ___0_f4x4, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 Math_Transform_m762480F0988F685C5039CBFBFA540C85385A53CC_inline (float3x3_tB318DB8C7E54B6CA9E14EB9AC7F5964C1189FC79 ___0_transform, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___1_aabb, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_xxx_mFD7DFB9FF23BB0B3437F12CC35DB3D1E0ADF7B20_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 float3_op_LessThan_m540182EDEF57B5A865B1C22972CF5C3E862B9C51_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_falseValue, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_trueValue, bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___2_test, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 bool3_op_LogicalNot_m85C703CC4098B3731505A162957F91C0373548BD_inline (bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___0_val, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_yyy_m6FCA12991237EDC77F7C4B6A7F73710338330CCD_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_zzz_m1C7C995F170030A7EF534E2C99E6AFE6928AE9D4_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E* __this, float ___0_x, float ___1_y, float ___2_z, float ___3_w, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E Plane_Normalize_mFF53F95372AE2A79C71B546A70B0F152EC308544_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_planeCoefficients, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Plane__ctor_mE41939B3E3E2AE7802AA9571AB429BAB47C56A65_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float ___0_coefficientA, float ___1_coefficientB, float ___2_coefficientC, float ___3_coefficientD, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_xyz, float ___1_w, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Plane__ctor_mAEEAADCE34CB243E12A9FE0240D4E508913C1153_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_normal, float ___1_distance, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Plane__ctor_m645C0F13FB29D9E443284F1BC42C02CE3B5C27D4_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_normal, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_pointInPlane, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Plane__ctor_m77B64CCE37D396DD70CD0A841F4E6E4F72D1B20A_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_vector1InPlane, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_vector2InPlane, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___2_pointInPlane, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E Plane_get_Normal_mAA5C1BEAEFB0848A4CD29E254CC9EF010DD6FE4B (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float4_set_xyz_m331D16059D51A5C6CA8AE8FD1E13A68C0570A9C7_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane_set_Normal_m88265A5E767B48CF718AC03AB03BBF15DC82A837 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Plane_get_Distance_m66B8C8674B20E3B19B0CFD363D33AA8A67CA75FE (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane_set_Distance_m6DDC9F56E9FEE8D4DC5A61AB2A8322695F855181 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_lengthsq_mC699F3F214F05B26BEBAF1B46E3AA3C00407A532_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_rsqrt_mC67B3430EAADA7C5347E87B23859C569BC010E72_inline (float ___0_x, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E float4_op_Multiply_m712573F441DA8AF0843DE2167927FB76E642B1EB_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_lhs, float ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E Plane_op_Implicit_mD02477CC24787906751D3F5E401D2E11BF99AC98_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 ___0_plane, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_dot_m20F2285F7227DC308D9CF2DCB8EAAD3E774501D4_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_x, float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Plane_SignedDistanceToPoint_mE52778BC70A3A0FF9DDB0FE52D71C587D837F993_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E Plane_Projection_mFF8C23401C366A3B4EB017B4DAAAF4E8A9132CFE_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E float4_op_UnaryNegation_m5A491FC1978650D62EBEDC51992CF4B2113C8C5B_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_val, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 Plane_get_Flipped_m6D004985368EE6234BA9A5D2800557FFB3A351FA (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentException__ctor_m026938A67AF9D36BB7ED27F80425D7194B514465 (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane_CheckPlaneIsNormalized_m6408EC4BE1D7A0ADB6832288E286D9C70404B888 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4__ctor_m00DD9230DF75F4825012D055BBF5FCC3A08D78B3_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* __this, int32_t ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4__ctor_mE3E045BA2F8A6BADAF84CD437BC4123BAD640226_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* __this, int4_tBA77D4945786DE82C3A487B33955EA1004996052 ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4__ctor_mC40F8DC4FDACF816F7989C9D2693B4FDE0116AFB_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* __this, float ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4__ctor_m0FDC55AFC114988A17F4E6248D13C331E0F4C544_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* __this, float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4__ctor_m2B3C71B8C523717F9E57A2EC936A9DB6C147B73D_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* __this, double ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4__ctor_mD155EFD6C699622167DC52223E62E3C023D86CF8_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* __this, double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline (bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619* __this, bool ___0_x, bool ___1_y, bool ___2_z, bool ___3_w, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 math_uint4_m7F6A5341327B9EF6B80C82FD1B93E4F32287A336_inline (uint32_t ___0_x, uint32_t ___1_y, uint32_t ___2_z, uint32_t ___3_w, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint32_t math_csum_m6A99E69A84442A729781A97F78B260223DD01D8F_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_x, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void bool3__ctor_m3683F21E6C110670CDDA02E4C1F6E063E936FEE2_inline (bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861* __this, bool ___0_x, bool ___1_y, bool ___2_z, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline (float ___0_x, float ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline (float ___0_x, float ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Multiply_m38F52B61F8E5636955A1A6DF3A75BD0724148350_inline (float ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B math_asuint_m4AEE8C17FEDA05D4C77C427818D1C9EF5E31521E_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B uint3_op_BitwiseAnd_m772BFC3A60526C264937ABCA92F1CAAFC2B0D634_inline (uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_asfloat_m7A90E1FAABD250FCEC00839D01B098BB046F7933_inline (uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B ___0_x, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_mul_mE9E04B2868E4D4BA5BD873E4F876D550D36C2E99_inline (quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 ___0_q, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_sqrt_mEF31DE7BD0179009683C5D7B0C58E6571B30CF4A_inline (float ___0_x, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Single_IsNaN_mFE637F6ECA9F7697CE8EFF56427858F4C5EDF75D_inline (float ___0_f, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint3__ctor_mEFEA14BBA36F53111474B0C3C3B729061F1ACCAF_inline (uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B* __this, uint32_t ___0_x, uint32_t ___1_y, uint32_t ___2_z, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t BitConverter_SingleToInt32Bits_mC760C7CFC89725E3CF68DC45BE3A9A42A7E7DA73_inline (float ___0_value, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_c0, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_c1, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___2_c2, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___3_c3, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_c0;
		__this->___c0 = L_0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = ___1_c1;
		__this->___c1 = L_1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___2_c2;
		__this->___c2 = L_2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = ___3_c3;
		__this->___c3 = L_3;
		return;
	}
}
IL2CPP_EXTERN_C  void uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_AdjustorThunk (RuntimeObject* __this, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_c0, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_c1, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___2_c2, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___3_c3, const RuntimeMethod* method)
{
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*>(__this + _offset);
	uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline(_thisAdjusted, ___0_c0, ___1_c1, ___2_c2, ___3_c3, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void uint4x4__ctor_mDF3E33FF44DA38AB4B132456C1BD34AC1546027F (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, uint32_t ___0_m00, uint32_t ___1_m01, uint32_t ___2_m02, uint32_t ___3_m03, uint32_t ___4_m10, uint32_t ___5_m11, uint32_t ___6_m12, uint32_t ___7_m13, uint32_t ___8_m20, uint32_t ___9_m21, uint32_t ___10_m22, uint32_t ___11_m23, uint32_t ___12_m30, uint32_t ___13_m31, uint32_t ___14_m32, uint32_t ___15_m33, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_m00;
		uint32_t L_1 = ___4_m10;
		uint32_t L_2 = ___8_m20;
		uint32_t L_3 = ___12_m30;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4;
		memset((&L_4), 0, sizeof(L_4));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_4), L_0, L_1, L_2, L_3, NULL);
		__this->___c0 = L_4;
		uint32_t L_5 = ___1_m01;
		uint32_t L_6 = ___5_m11;
		uint32_t L_7 = ___9_m21;
		uint32_t L_8 = ___13_m31;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9;
		memset((&L_9), 0, sizeof(L_9));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_9), L_5, L_6, L_7, L_8, NULL);
		__this->___c1 = L_9;
		uint32_t L_10 = ___2_m02;
		uint32_t L_11 = ___6_m12;
		uint32_t L_12 = ___10_m22;
		uint32_t L_13 = ___14_m32;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14;
		memset((&L_14), 0, sizeof(L_14));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_14), L_10, L_11, L_12, L_13, NULL);
		__this->___c2 = L_14;
		uint32_t L_15 = ___3_m03;
		uint32_t L_16 = ___7_m13;
		uint32_t L_17 = ___11_m23;
		uint32_t L_18 = ___15_m33;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_19;
		memset((&L_19), 0, sizeof(L_19));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_19), L_15, L_16, L_17, L_18, NULL);
		__this->___c3 = L_19;
		return;
	}
}
IL2CPP_EXTERN_C  void uint4x4__ctor_mDF3E33FF44DA38AB4B132456C1BD34AC1546027F_AdjustorThunk (RuntimeObject* __this, uint32_t ___0_m00, uint32_t ___1_m01, uint32_t ___2_m02, uint32_t ___3_m03, uint32_t ___4_m10, uint32_t ___5_m11, uint32_t ___6_m12, uint32_t ___7_m13, uint32_t ___8_m20, uint32_t ___9_m21, uint32_t ___10_m22, uint32_t ___11_m23, uint32_t ___12_m30, uint32_t ___13_m31, uint32_t ___14_m32, uint32_t ___15_m33, const RuntimeMethod* method)
{
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*>(__this + _offset);
	uint4x4__ctor_mDF3E33FF44DA38AB4B132456C1BD34AC1546027F_inline(_thisAdjusted, ___0_m00, ___1_m01, ___2_m02, ___3_m03, ___4_m10, ___5_m11, ___6_m12, ___7_m13, ___8_m20, ___9_m21, ___10_m22, ___11_m23, ___12_m30, ___13_m31, ___14_m32, ___15_m33, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void uint4x4__ctor_mB044E6CC91777237724520F26351840B0932A1E8 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, uint32_t ___0_v, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1;
		L_1 = uint4_op_Implicit_m194DE3172FAB8E0625B0F5826454FE3C91DA68DF_inline(L_0, NULL);
		__this->___c0 = L_1;
		uint32_t L_2 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Implicit_m194DE3172FAB8E0625B0F5826454FE3C91DA68DF_inline(L_2, NULL);
		__this->___c1 = L_3;
		uint32_t L_4 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		L_5 = uint4_op_Implicit_m194DE3172FAB8E0625B0F5826454FE3C91DA68DF_inline(L_4, NULL);
		__this->___c2 = L_5;
		uint32_t L_6 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_Implicit_m194DE3172FAB8E0625B0F5826454FE3C91DA68DF_inline(L_6, NULL);
		__this->___c3 = L_7;
		return;
	}
}
IL2CPP_EXTERN_C  void uint4x4__ctor_mB044E6CC91777237724520F26351840B0932A1E8_AdjustorThunk (RuntimeObject* __this, uint32_t ___0_v, const RuntimeMethod* method)
{
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*>(__this + _offset);
	uint4x4__ctor_mB044E6CC91777237724520F26351840B0932A1E8_inline(_thisAdjusted, ___0_v, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void uint4x4__ctor_m00363B3E264E5A1B4E4013E66F1EE47858161688 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, bool ___0_v, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0;
		memset((&L_0), 0, sizeof(L_0));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_0), 0, NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_1), 1, NULL);
		bool L_2 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = math_select_m59B92233FC64029995EE77D0D626FE89FF714D19_inline(L_0, L_1, L_2, NULL);
		__this->___c0 = L_3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4;
		memset((&L_4), 0, sizeof(L_4));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_4), 0, NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		memset((&L_5), 0, sizeof(L_5));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_5), 1, NULL);
		bool L_6 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = math_select_m59B92233FC64029995EE77D0D626FE89FF714D19_inline(L_4, L_5, L_6, NULL);
		__this->___c1 = L_7;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8;
		memset((&L_8), 0, sizeof(L_8));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_8), 0, NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9;
		memset((&L_9), 0, sizeof(L_9));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_9), 1, NULL);
		bool L_10 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = math_select_m59B92233FC64029995EE77D0D626FE89FF714D19_inline(L_8, L_9, L_10, NULL);
		__this->___c2 = L_11;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_12), 0, NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13;
		memset((&L_13), 0, sizeof(L_13));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_13), 1, NULL);
		bool L_14 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = math_select_m59B92233FC64029995EE77D0D626FE89FF714D19_inline(L_12, L_13, L_14, NULL);
		__this->___c3 = L_15;
		return;
	}
}
IL2CPP_EXTERN_C  void uint4x4__ctor_m00363B3E264E5A1B4E4013E66F1EE47858161688_AdjustorThunk (RuntimeObject* __this, bool ___0_v, const RuntimeMethod* method)
{
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*>(__this + _offset);
	uint4x4__ctor_m00363B3E264E5A1B4E4013E66F1EE47858161688_inline(_thisAdjusted, ___0_v, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void uint4x4__ctor_mCE45F10896048B5121C7A9C4B4DD4FFFD893BCD8 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 ___0_v, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0;
		memset((&L_0), 0, sizeof(L_0));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_0), 0, NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_1), 1, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_2 = ___0_v;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_3 = L_2.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4;
		L_4 = math_select_m94A91F3756E494BE8FDC90304F980A0BB366550E_inline(L_0, L_1, L_3, NULL);
		__this->___c0 = L_4;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		memset((&L_5), 0, sizeof(L_5));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_5), 0, NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6;
		memset((&L_6), 0, sizeof(L_6));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_6), 1, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_7 = ___0_v;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_8 = L_7.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9;
		L_9 = math_select_m94A91F3756E494BE8FDC90304F980A0BB366550E_inline(L_5, L_6, L_8, NULL);
		__this->___c1 = L_9;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10;
		memset((&L_10), 0, sizeof(L_10));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_10), 0, NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		memset((&L_11), 0, sizeof(L_11));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_11), 1, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_12 = ___0_v;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_13 = L_12.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14;
		L_14 = math_select_m94A91F3756E494BE8FDC90304F980A0BB366550E_inline(L_10, L_11, L_13, NULL);
		__this->___c2 = L_14;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		memset((&L_15), 0, sizeof(L_15));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_15), 0, NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_16), 1, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_17 = ___0_v;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_18 = L_17.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_19;
		L_19 = math_select_m94A91F3756E494BE8FDC90304F980A0BB366550E_inline(L_15, L_16, L_18, NULL);
		__this->___c3 = L_19;
		return;
	}
}
IL2CPP_EXTERN_C  void uint4x4__ctor_mCE45F10896048B5121C7A9C4B4DD4FFFD893BCD8_AdjustorThunk (RuntimeObject* __this, bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 ___0_v, const RuntimeMethod* method)
{
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*>(__this + _offset);
	uint4x4__ctor_mCE45F10896048B5121C7A9C4B4DD4FFFD893BCD8_inline(_thisAdjusted, ___0_v, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void uint4x4__ctor_m6962CB8B6F4B51CE18577B571A8DE0D3367C9213 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, int32_t ___0_v, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1;
		L_1 = uint4_op_Explicit_m52EA594D5CFD520009B14F44F586D6A36EE1F0F2_inline(L_0, NULL);
		__this->___c0 = L_1;
		int32_t L_2 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Explicit_m52EA594D5CFD520009B14F44F586D6A36EE1F0F2_inline(L_2, NULL);
		__this->___c1 = L_3;
		int32_t L_4 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		L_5 = uint4_op_Explicit_m52EA594D5CFD520009B14F44F586D6A36EE1F0F2_inline(L_4, NULL);
		__this->___c2 = L_5;
		int32_t L_6 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_Explicit_m52EA594D5CFD520009B14F44F586D6A36EE1F0F2_inline(L_6, NULL);
		__this->___c3 = L_7;
		return;
	}
}
IL2CPP_EXTERN_C  void uint4x4__ctor_m6962CB8B6F4B51CE18577B571A8DE0D3367C9213_AdjustorThunk (RuntimeObject* __this, int32_t ___0_v, const RuntimeMethod* method)
{
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*>(__this + _offset);
	uint4x4__ctor_m6962CB8B6F4B51CE18577B571A8DE0D3367C9213_inline(_thisAdjusted, ___0_v, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void uint4x4__ctor_mC40D16C3F1B689A4EDAA2EC86DA52D0E371C752B (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, int4x4_tEF359B46039347312A6AC932AD04CA9CE675AB30 ___0_v, const RuntimeMethod* method) 
{
	{
		int4x4_tEF359B46039347312A6AC932AD04CA9CE675AB30 L_0 = ___0_v;
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_1 = L_0.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2;
		L_2 = uint4_op_Explicit_m4BA2A6F8AC721A602D73382462C962382691DD70_inline(L_1, NULL);
		__this->___c0 = L_2;
		int4x4_tEF359B46039347312A6AC932AD04CA9CE675AB30 L_3 = ___0_v;
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_4 = L_3.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		L_5 = uint4_op_Explicit_m4BA2A6F8AC721A602D73382462C962382691DD70_inline(L_4, NULL);
		__this->___c1 = L_5;
		int4x4_tEF359B46039347312A6AC932AD04CA9CE675AB30 L_6 = ___0_v;
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_7 = L_6.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8;
		L_8 = uint4_op_Explicit_m4BA2A6F8AC721A602D73382462C962382691DD70_inline(L_7, NULL);
		__this->___c2 = L_8;
		int4x4_tEF359B46039347312A6AC932AD04CA9CE675AB30 L_9 = ___0_v;
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_10 = L_9.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_Explicit_m4BA2A6F8AC721A602D73382462C962382691DD70_inline(L_10, NULL);
		__this->___c3 = L_11;
		return;
	}
}
IL2CPP_EXTERN_C  void uint4x4__ctor_mC40D16C3F1B689A4EDAA2EC86DA52D0E371C752B_AdjustorThunk (RuntimeObject* __this, int4x4_tEF359B46039347312A6AC932AD04CA9CE675AB30 ___0_v, const RuntimeMethod* method)
{
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*>(__this + _offset);
	uint4x4__ctor_mC40D16C3F1B689A4EDAA2EC86DA52D0E371C752B_inline(_thisAdjusted, ___0_v, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void uint4x4__ctor_m94AD3FDC6F57005991FE28510E0516643C9250CE (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, float ___0_v, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1;
		L_1 = uint4_op_Explicit_mF62DF180DDD889D04C4B894C1CC11C4FFB757C56_inline(L_0, NULL);
		__this->___c0 = L_1;
		float L_2 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Explicit_mF62DF180DDD889D04C4B894C1CC11C4FFB757C56_inline(L_2, NULL);
		__this->___c1 = L_3;
		float L_4 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		L_5 = uint4_op_Explicit_mF62DF180DDD889D04C4B894C1CC11C4FFB757C56_inline(L_4, NULL);
		__this->___c2 = L_5;
		float L_6 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_Explicit_mF62DF180DDD889D04C4B894C1CC11C4FFB757C56_inline(L_6, NULL);
		__this->___c3 = L_7;
		return;
	}
}
IL2CPP_EXTERN_C  void uint4x4__ctor_m94AD3FDC6F57005991FE28510E0516643C9250CE_AdjustorThunk (RuntimeObject* __this, float ___0_v, const RuntimeMethod* method)
{
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*>(__this + _offset);
	uint4x4__ctor_m94AD3FDC6F57005991FE28510E0516643C9250CE_inline(_thisAdjusted, ___0_v, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void uint4x4__ctor_mFA79D1F3F53003B4BC1960DACFAE87DA465EDD2D (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 ___0_v, const RuntimeMethod* method) 
{
	{
		float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 L_0 = ___0_v;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_1 = L_0.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2;
		L_2 = uint4_op_Explicit_mCEEF0983FAE417D4A4DAC4BAA4FD415E6770C20D_inline(L_1, NULL);
		__this->___c0 = L_2;
		float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 L_3 = ___0_v;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_4 = L_3.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		L_5 = uint4_op_Explicit_mCEEF0983FAE417D4A4DAC4BAA4FD415E6770C20D_inline(L_4, NULL);
		__this->___c1 = L_5;
		float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 L_6 = ___0_v;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_7 = L_6.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8;
		L_8 = uint4_op_Explicit_mCEEF0983FAE417D4A4DAC4BAA4FD415E6770C20D_inline(L_7, NULL);
		__this->___c2 = L_8;
		float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 L_9 = ___0_v;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_10 = L_9.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_Explicit_mCEEF0983FAE417D4A4DAC4BAA4FD415E6770C20D_inline(L_10, NULL);
		__this->___c3 = L_11;
		return;
	}
}
IL2CPP_EXTERN_C  void uint4x4__ctor_mFA79D1F3F53003B4BC1960DACFAE87DA465EDD2D_AdjustorThunk (RuntimeObject* __this, float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 ___0_v, const RuntimeMethod* method)
{
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*>(__this + _offset);
	uint4x4__ctor_mFA79D1F3F53003B4BC1960DACFAE87DA465EDD2D_inline(_thisAdjusted, ___0_v, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void uint4x4__ctor_m8B2B846C47859C1A0F6BBDD6804B0F20AA4CA415 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, double ___0_v, const RuntimeMethod* method) 
{
	{
		double L_0 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1;
		L_1 = uint4_op_Explicit_m642DF9B9E499124B9322085581DCE979B7CCA961_inline(L_0, NULL);
		__this->___c0 = L_1;
		double L_2 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Explicit_m642DF9B9E499124B9322085581DCE979B7CCA961_inline(L_2, NULL);
		__this->___c1 = L_3;
		double L_4 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		L_5 = uint4_op_Explicit_m642DF9B9E499124B9322085581DCE979B7CCA961_inline(L_4, NULL);
		__this->___c2 = L_5;
		double L_6 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_Explicit_m642DF9B9E499124B9322085581DCE979B7CCA961_inline(L_6, NULL);
		__this->___c3 = L_7;
		return;
	}
}
IL2CPP_EXTERN_C  void uint4x4__ctor_m8B2B846C47859C1A0F6BBDD6804B0F20AA4CA415_AdjustorThunk (RuntimeObject* __this, double ___0_v, const RuntimeMethod* method)
{
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*>(__this + _offset);
	uint4x4__ctor_m8B2B846C47859C1A0F6BBDD6804B0F20AA4CA415_inline(_thisAdjusted, ___0_v, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void uint4x4__ctor_m94C7CE06BDF397CA5348A1EA85358374782BA6DA (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, double4x4_tB452F9489714C6B8D74D46CA2CF1F0CA8F185D3C ___0_v, const RuntimeMethod* method) 
{
	{
		double4x4_tB452F9489714C6B8D74D46CA2CF1F0CA8F185D3C L_0 = ___0_v;
		double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 L_1 = L_0.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2;
		L_2 = uint4_op_Explicit_m461E06F58E3962E8ECF5E15E0119B2FC2D391FF5_inline(L_1, NULL);
		__this->___c0 = L_2;
		double4x4_tB452F9489714C6B8D74D46CA2CF1F0CA8F185D3C L_3 = ___0_v;
		double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 L_4 = L_3.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		L_5 = uint4_op_Explicit_m461E06F58E3962E8ECF5E15E0119B2FC2D391FF5_inline(L_4, NULL);
		__this->___c1 = L_5;
		double4x4_tB452F9489714C6B8D74D46CA2CF1F0CA8F185D3C L_6 = ___0_v;
		double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 L_7 = L_6.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8;
		L_8 = uint4_op_Explicit_m461E06F58E3962E8ECF5E15E0119B2FC2D391FF5_inline(L_7, NULL);
		__this->___c2 = L_8;
		double4x4_tB452F9489714C6B8D74D46CA2CF1F0CA8F185D3C L_9 = ___0_v;
		double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 L_10 = L_9.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_Explicit_m461E06F58E3962E8ECF5E15E0119B2FC2D391FF5_inline(L_10, NULL);
		__this->___c3 = L_11;
		return;
	}
}
IL2CPP_EXTERN_C  void uint4x4__ctor_m94C7CE06BDF397CA5348A1EA85358374782BA6DA_AdjustorThunk (RuntimeObject* __this, double4x4_tB452F9489714C6B8D74D46CA2CF1F0CA8F185D3C ___0_v, const RuntimeMethod* method)
{
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*>(__this + _offset);
	uint4x4__ctor_m94C7CE06BDF397CA5348A1EA85358374782BA6DA_inline(_thisAdjusted, ___0_v, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Implicit_m469E05D9740C434DDE6FAA155B2D308DB83677D9 (uint32_t ___0_v, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_v;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4x4__ctor_mB044E6CC91777237724520F26351840B0932A1E8_inline((&L_1), L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Explicit_mA2D596225A37743660C95447C48E36F74283E721 (bool ___0_v, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_v;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4x4__ctor_m00363B3E264E5A1B4E4013E66F1EE47858161688_inline((&L_1), L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Explicit_mC3ABF8C25676E0AAB8FFD75441C5FC65508BA6E2 (bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 ___0_v, const RuntimeMethod* method) 
{
	{
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_0 = ___0_v;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4x4__ctor_mCE45F10896048B5121C7A9C4B4DD4FFFD893BCD8_inline((&L_1), L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Explicit_m92E65782DA0CB89125D8DC051A1B725BEBFC4314 (int32_t ___0_v, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_v;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4x4__ctor_m6962CB8B6F4B51CE18577B571A8DE0D3367C9213_inline((&L_1), L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Explicit_mC080336AC465DE82B2E25C7A56213E774D45C807 (int4x4_tEF359B46039347312A6AC932AD04CA9CE675AB30 ___0_v, const RuntimeMethod* method) 
{
	{
		int4x4_tEF359B46039347312A6AC932AD04CA9CE675AB30 L_0 = ___0_v;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4x4__ctor_mC40D16C3F1B689A4EDAA2EC86DA52D0E371C752B_inline((&L_1), L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Explicit_m1D14AB6D6D5DAD170908ADEC5293D0F29EA52F1E (float ___0_v, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_v;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4x4__ctor_m94AD3FDC6F57005991FE28510E0516643C9250CE_inline((&L_1), L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Explicit_m099744ED72310DF3D41438C94F22FB9EF7B2C083 (float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 ___0_v, const RuntimeMethod* method) 
{
	{
		float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 L_0 = ___0_v;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4x4__ctor_mFA79D1F3F53003B4BC1960DACFAE87DA465EDD2D_inline((&L_1), L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Explicit_m6713ADB494D371190951EBC825C872C797863559 (double ___0_v, const RuntimeMethod* method) 
{
	{
		double L_0 = ___0_v;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4x4__ctor_m8B2B846C47859C1A0F6BBDD6804B0F20AA4CA415_inline((&L_1), L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Explicit_m70A41363AC242588347BEBF7FC5D969C76BDBE8B (double4x4_tB452F9489714C6B8D74D46CA2CF1F0CA8F185D3C ___0_v, const RuntimeMethod* method) 
{
	{
		double4x4_tB452F9489714C6B8D74D46CA2CF1F0CA8F185D3C L_0 = ___0_v;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4x4__ctor_m94C7CE06BDF397CA5348A1EA85358374782BA6DA_inline((&L_1), L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Multiply_m3C15A7CE8EEFD802FBBFB634597DF57C2FAE3886 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = L_2.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4;
		L_4 = uint4_op_Multiply_mDD93D0730642A1089848321B9B0E5E923EE575ED_inline(L_1, L_3, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_7 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = L_7.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9;
		L_9 = uint4_op_Multiply_mDD93D0730642A1089848321B9B0E5E923EE575ED_inline(L_6, L_8, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_10 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11 = L_10.___c2;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14;
		L_14 = uint4_op_Multiply_mDD93D0730642A1089848321B9B0E5E923EE575ED_inline(L_11, L_13, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_15 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16 = L_15.___c3;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_17 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_18 = L_17.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_19;
		L_19 = uint4_op_Multiply_mDD93D0730642A1089848321B9B0E5E923EE575ED_inline(L_16, L_18, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_20;
		memset((&L_20), 0, sizeof(L_20));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_20), L_4, L_9, L_14, L_19, NULL);
		return L_20;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Multiply_m180752B467C4795B8A16FDC2A27EFCC1666891BB (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Multiply_m40354E397B1D99D73B5333378C8698C087A1A2D6_inline(L_1, L_2, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_4 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5 = L_4.___c1;
		uint32_t L_6 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_Multiply_m40354E397B1D99D73B5333378C8698C087A1A2D6_inline(L_5, L_6, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_8 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = L_8.___c2;
		uint32_t L_10 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_Multiply_m40354E397B1D99D73B5333378C8698C087A1A2D6_inline(L_9, L_10, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c3;
		uint32_t L_14 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_Multiply_m40354E397B1D99D73B5333378C8698C087A1A2D6_inline(L_13, L_14, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Multiply_m62A518F24D57B78BEFE138E6A61C19F309B7E750 (uint32_t ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = L_1.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Multiply_mF7AFA2D5F25EC1F6F7AEC21564D18A46C7B37B48_inline(L_0, L_2, NULL);
		uint32_t L_4 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_Multiply_mF7AFA2D5F25EC1F6F7AEC21564D18A46C7B37B48_inline(L_4, L_6, NULL);
		uint32_t L_8 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_Multiply_mF7AFA2D5F25EC1F6F7AEC21564D18A46C7B37B48_inline(L_8, L_10, NULL);
		uint32_t L_12 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_13 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = L_13.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_Multiply_mF7AFA2D5F25EC1F6F7AEC21564D18A46C7B37B48_inline(L_12, L_14, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Addition_m5B86B9B7B0F13D37EDDD4E05B9BAA4113D2ED53F (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = L_2.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4;
		L_4 = uint4_op_Addition_m391200EB43B6C4CDD219079A0D3CDFFA35B0B652_inline(L_1, L_3, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_7 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = L_7.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9;
		L_9 = uint4_op_Addition_m391200EB43B6C4CDD219079A0D3CDFFA35B0B652_inline(L_6, L_8, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_10 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11 = L_10.___c2;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14;
		L_14 = uint4_op_Addition_m391200EB43B6C4CDD219079A0D3CDFFA35B0B652_inline(L_11, L_13, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_15 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16 = L_15.___c3;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_17 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_18 = L_17.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_19;
		L_19 = uint4_op_Addition_m391200EB43B6C4CDD219079A0D3CDFFA35B0B652_inline(L_16, L_18, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_20;
		memset((&L_20), 0, sizeof(L_20));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_20), L_4, L_9, L_14, L_19, NULL);
		return L_20;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Addition_m180F4205AC055AC54372271DF0A35C9D45F95CA6 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Addition_m3968FBC6698B10C2DC1D0222C72BB0F67CA1E6D6_inline(L_1, L_2, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_4 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5 = L_4.___c1;
		uint32_t L_6 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_Addition_m3968FBC6698B10C2DC1D0222C72BB0F67CA1E6D6_inline(L_5, L_6, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_8 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = L_8.___c2;
		uint32_t L_10 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_Addition_m3968FBC6698B10C2DC1D0222C72BB0F67CA1E6D6_inline(L_9, L_10, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c3;
		uint32_t L_14 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_Addition_m3968FBC6698B10C2DC1D0222C72BB0F67CA1E6D6_inline(L_13, L_14, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Addition_m1025CE0941B6F46899CACA0FADF579E0953C5FDE (uint32_t ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = L_1.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Addition_m6F5942C7C169158FD66ECCE72E54D0494886F669_inline(L_0, L_2, NULL);
		uint32_t L_4 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_Addition_m6F5942C7C169158FD66ECCE72E54D0494886F669_inline(L_4, L_6, NULL);
		uint32_t L_8 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_Addition_m6F5942C7C169158FD66ECCE72E54D0494886F669_inline(L_8, L_10, NULL);
		uint32_t L_12 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_13 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = L_13.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_Addition_m6F5942C7C169158FD66ECCE72E54D0494886F669_inline(L_12, L_14, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Subtraction_mE71749456690B7446240E75D468BB22ED3DBB958 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = L_2.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4;
		L_4 = uint4_op_Subtraction_mF815D62CBBE8EAE82A64035CA71DA415A54DC64F_inline(L_1, L_3, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_7 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = L_7.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9;
		L_9 = uint4_op_Subtraction_mF815D62CBBE8EAE82A64035CA71DA415A54DC64F_inline(L_6, L_8, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_10 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11 = L_10.___c2;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14;
		L_14 = uint4_op_Subtraction_mF815D62CBBE8EAE82A64035CA71DA415A54DC64F_inline(L_11, L_13, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_15 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16 = L_15.___c3;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_17 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_18 = L_17.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_19;
		L_19 = uint4_op_Subtraction_mF815D62CBBE8EAE82A64035CA71DA415A54DC64F_inline(L_16, L_18, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_20;
		memset((&L_20), 0, sizeof(L_20));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_20), L_4, L_9, L_14, L_19, NULL);
		return L_20;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Subtraction_m65DDE11179617B010DAB383D3617E6894FF95159 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Subtraction_mA5D1B1DE817EFBC8685C2EB3CAB6EF0DFFDC3F92_inline(L_1, L_2, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_4 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5 = L_4.___c1;
		uint32_t L_6 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_Subtraction_mA5D1B1DE817EFBC8685C2EB3CAB6EF0DFFDC3F92_inline(L_5, L_6, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_8 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = L_8.___c2;
		uint32_t L_10 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_Subtraction_mA5D1B1DE817EFBC8685C2EB3CAB6EF0DFFDC3F92_inline(L_9, L_10, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c3;
		uint32_t L_14 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_Subtraction_mA5D1B1DE817EFBC8685C2EB3CAB6EF0DFFDC3F92_inline(L_13, L_14, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Subtraction_mF76228E97D751A529431922BE17CCEAD0CA564A4 (uint32_t ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = L_1.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Subtraction_m4DA7085E5BF60C4AD13FC74177EEC6AC6FA65340_inline(L_0, L_2, NULL);
		uint32_t L_4 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_Subtraction_m4DA7085E5BF60C4AD13FC74177EEC6AC6FA65340_inline(L_4, L_6, NULL);
		uint32_t L_8 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_Subtraction_m4DA7085E5BF60C4AD13FC74177EEC6AC6FA65340_inline(L_8, L_10, NULL);
		uint32_t L_12 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_13 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = L_13.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_Subtraction_m4DA7085E5BF60C4AD13FC74177EEC6AC6FA65340_inline(L_12, L_14, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Division_m257861B6E88F994139032B98B773362A24427538 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = L_2.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4;
		L_4 = uint4_op_Division_m767BDF5F276A7AE45164F10D04573BE5DA17DDB5_inline(L_1, L_3, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_7 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = L_7.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9;
		L_9 = uint4_op_Division_m767BDF5F276A7AE45164F10D04573BE5DA17DDB5_inline(L_6, L_8, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_10 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11 = L_10.___c2;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14;
		L_14 = uint4_op_Division_m767BDF5F276A7AE45164F10D04573BE5DA17DDB5_inline(L_11, L_13, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_15 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16 = L_15.___c3;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_17 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_18 = L_17.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_19;
		L_19 = uint4_op_Division_m767BDF5F276A7AE45164F10D04573BE5DA17DDB5_inline(L_16, L_18, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_20;
		memset((&L_20), 0, sizeof(L_20));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_20), L_4, L_9, L_14, L_19, NULL);
		return L_20;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Division_m3C4AB4680FC72BBD9C47987F6B30AAB369A2D8EB (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Division_m9612CEBFC3CB81D66C58ABB1E67077FC69B657BF_inline(L_1, L_2, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_4 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5 = L_4.___c1;
		uint32_t L_6 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_Division_m9612CEBFC3CB81D66C58ABB1E67077FC69B657BF_inline(L_5, L_6, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_8 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = L_8.___c2;
		uint32_t L_10 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_Division_m9612CEBFC3CB81D66C58ABB1E67077FC69B657BF_inline(L_9, L_10, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c3;
		uint32_t L_14 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_Division_m9612CEBFC3CB81D66C58ABB1E67077FC69B657BF_inline(L_13, L_14, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Division_mDDEF6325A60677F563462C380BC3FE4D4F6B2A65 (uint32_t ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = L_1.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Division_mED3DE29C6531D09E8CB0E75B7DFA0380CE7A8218_inline(L_0, L_2, NULL);
		uint32_t L_4 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_Division_mED3DE29C6531D09E8CB0E75B7DFA0380CE7A8218_inline(L_4, L_6, NULL);
		uint32_t L_8 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_Division_mED3DE29C6531D09E8CB0E75B7DFA0380CE7A8218_inline(L_8, L_10, NULL);
		uint32_t L_12 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_13 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = L_13.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_Division_mED3DE29C6531D09E8CB0E75B7DFA0380CE7A8218_inline(L_12, L_14, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Modulus_m4725612FC954B4EF96907375174B9B8483EEB243 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = L_2.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4;
		L_4 = uint4_op_Modulus_m76B34ADA3D74231D5BE5A809380D075D3B5EF921_inline(L_1, L_3, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_7 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = L_7.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9;
		L_9 = uint4_op_Modulus_m76B34ADA3D74231D5BE5A809380D075D3B5EF921_inline(L_6, L_8, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_10 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11 = L_10.___c2;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14;
		L_14 = uint4_op_Modulus_m76B34ADA3D74231D5BE5A809380D075D3B5EF921_inline(L_11, L_13, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_15 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16 = L_15.___c3;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_17 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_18 = L_17.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_19;
		L_19 = uint4_op_Modulus_m76B34ADA3D74231D5BE5A809380D075D3B5EF921_inline(L_16, L_18, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_20;
		memset((&L_20), 0, sizeof(L_20));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_20), L_4, L_9, L_14, L_19, NULL);
		return L_20;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Modulus_m7F3FB1383608DD5EA027F271F4A551B8C9D95161 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Modulus_m8C76FB3CB8FA2D498525823CEC6DDE42B5B4FDDF_inline(L_1, L_2, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_4 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5 = L_4.___c1;
		uint32_t L_6 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_Modulus_m8C76FB3CB8FA2D498525823CEC6DDE42B5B4FDDF_inline(L_5, L_6, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_8 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = L_8.___c2;
		uint32_t L_10 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_Modulus_m8C76FB3CB8FA2D498525823CEC6DDE42B5B4FDDF_inline(L_9, L_10, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c3;
		uint32_t L_14 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_Modulus_m8C76FB3CB8FA2D498525823CEC6DDE42B5B4FDDF_inline(L_13, L_14, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Modulus_m6D231B9DDC9A631E3B5B688352A1873A766230DF (uint32_t ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = L_1.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Modulus_m6319F62EDDC062DCF736D348ABA648B560259204_inline(L_0, L_2, NULL);
		uint32_t L_4 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_Modulus_m6319F62EDDC062DCF736D348ABA648B560259204_inline(L_4, L_6, NULL);
		uint32_t L_8 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_Modulus_m6319F62EDDC062DCF736D348ABA648B560259204_inline(L_8, L_10, NULL);
		uint32_t L_12 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_13 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = L_13.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_Modulus_m6319F62EDDC062DCF736D348ABA648B560259204_inline(L_12, L_14, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Increment_m66C201E7C71EE9E6EAEDB062E6E1368B00FBA149 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_val, const RuntimeMethod* method) 
{
	uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_0 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&(&___0_val)->___c0);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_1 = L_0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = (*(uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)L_1);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Increment_m64BB67C319046CB11232CE66183D3A2B46917DF0_inline(L_2, NULL);
		V_0 = L_3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = V_0;
		*(uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)L_1 = L_4;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5 = V_0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_6 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&(&___0_val)->___c1);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_7 = L_6;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = (*(uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)L_7);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9;
		L_9 = uint4_op_Increment_m64BB67C319046CB11232CE66183D3A2B46917DF0_inline(L_8, NULL);
		V_0 = L_9;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = V_0;
		*(uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)L_7 = L_10;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11 = V_0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_12 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&(&___0_val)->___c2);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_13 = L_12;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = (*(uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)L_13);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_Increment_m64BB67C319046CB11232CE66183D3A2B46917DF0_inline(L_14, NULL);
		V_0 = L_15;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16 = V_0;
		*(uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)L_13 = L_16;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_17 = V_0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_18 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&(&___0_val)->___c3);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_19 = L_18;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_20 = (*(uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)L_19);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_21;
		L_21 = uint4_op_Increment_m64BB67C319046CB11232CE66183D3A2B46917DF0_inline(L_20, NULL);
		V_0 = L_21;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_22 = V_0;
		*(uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)L_19 = L_22;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_23 = V_0;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_24;
		memset((&L_24), 0, sizeof(L_24));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_24), L_5, L_11, L_17, L_23, NULL);
		return L_24;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_Decrement_mF63BC8EEA28F46FA8B1E750B7F234442D1E9479E (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_val, const RuntimeMethod* method) 
{
	uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_0 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&(&___0_val)->___c0);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_1 = L_0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = (*(uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)L_1);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Decrement_m0F6103CB4C76260923D9B080C4D41DFCD63B28B1_inline(L_2, NULL);
		V_0 = L_3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = V_0;
		*(uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)L_1 = L_4;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5 = V_0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_6 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&(&___0_val)->___c1);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_7 = L_6;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = (*(uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)L_7);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9;
		L_9 = uint4_op_Decrement_m0F6103CB4C76260923D9B080C4D41DFCD63B28B1_inline(L_8, NULL);
		V_0 = L_9;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = V_0;
		*(uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)L_7 = L_10;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11 = V_0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_12 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&(&___0_val)->___c2);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_13 = L_12;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = (*(uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)L_13);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_Decrement_m0F6103CB4C76260923D9B080C4D41DFCD63B28B1_inline(L_14, NULL);
		V_0 = L_15;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16 = V_0;
		*(uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)L_13 = L_16;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_17 = V_0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_18 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&(&___0_val)->___c3);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_19 = L_18;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_20 = (*(uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)L_19);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_21;
		L_21 = uint4_op_Decrement_m0F6103CB4C76260923D9B080C4D41DFCD63B28B1_inline(L_20, NULL);
		V_0 = L_21;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_22 = V_0;
		*(uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)L_19 = L_22;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_23 = V_0;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_24;
		memset((&L_24), 0, sizeof(L_24));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_24), L_5, L_11, L_17, L_23, NULL);
		return L_24;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 uint4x4_op_LessThan_m4BE8BEC4CC6213D0878F326AC5DFDD37E0C5367C (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = L_2.___c0;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_4;
		L_4 = uint4_op_LessThan_m3628E2E315AC8DA6CB3C6B655173D481C19FDD19_inline(L_1, L_3, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_7 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = L_7.___c1;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_9;
		L_9 = uint4_op_LessThan_m3628E2E315AC8DA6CB3C6B655173D481C19FDD19_inline(L_6, L_8, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_10 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11 = L_10.___c2;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c2;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_14;
		L_14 = uint4_op_LessThan_m3628E2E315AC8DA6CB3C6B655173D481C19FDD19_inline(L_11, L_13, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_15 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16 = L_15.___c3;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_17 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_18 = L_17.___c3;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_19;
		L_19 = uint4_op_LessThan_m3628E2E315AC8DA6CB3C6B655173D481C19FDD19_inline(L_16, L_18, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_20;
		memset((&L_20), 0, sizeof(L_20));
		bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline((&L_20), L_4, L_9, L_14, L_19, NULL);
		return L_20;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 uint4x4_op_LessThan_mD9A7CA1CE6EECA29725D910B35D68A09C5BF6E90 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint32_t L_2 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_3;
		L_3 = uint4_op_LessThan_mB35B79849BA45A0BB50B6C58EA63AEBC816D06A2_inline(L_1, L_2, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_4 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5 = L_4.___c1;
		uint32_t L_6 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_7;
		L_7 = uint4_op_LessThan_mB35B79849BA45A0BB50B6C58EA63AEBC816D06A2_inline(L_5, L_6, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_8 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = L_8.___c2;
		uint32_t L_10 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_11;
		L_11 = uint4_op_LessThan_mB35B79849BA45A0BB50B6C58EA63AEBC816D06A2_inline(L_9, L_10, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c3;
		uint32_t L_14 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_15;
		L_15 = uint4_op_LessThan_mB35B79849BA45A0BB50B6C58EA63AEBC816D06A2_inline(L_13, L_14, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_16;
		memset((&L_16), 0, sizeof(L_16));
		bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 uint4x4_op_LessThan_m22EBA0D37E4813D2CC1C25021A4DC0CC849ECC7F (uint32_t ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = L_1.___c0;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_3;
		L_3 = uint4_op_LessThan_mD054F90ECC9EEC5FBBA6A5B185C12C3FA51D717E_inline(L_0, L_2, NULL);
		uint32_t L_4 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_7;
		L_7 = uint4_op_LessThan_mD054F90ECC9EEC5FBBA6A5B185C12C3FA51D717E_inline(L_4, L_6, NULL);
		uint32_t L_8 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c2;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_11;
		L_11 = uint4_op_LessThan_mD054F90ECC9EEC5FBBA6A5B185C12C3FA51D717E_inline(L_8, L_10, NULL);
		uint32_t L_12 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_13 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = L_13.___c3;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_15;
		L_15 = uint4_op_LessThan_mD054F90ECC9EEC5FBBA6A5B185C12C3FA51D717E_inline(L_12, L_14, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_16;
		memset((&L_16), 0, sizeof(L_16));
		bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 uint4x4_op_LessThanOrEqual_m413ED458265871B3252805D691B3466F39A73A46 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = L_2.___c0;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_4;
		L_4 = uint4_op_LessThanOrEqual_m8A52F50B097C2A9BDFA919675AB046B390F9B324_inline(L_1, L_3, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_7 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = L_7.___c1;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_9;
		L_9 = uint4_op_LessThanOrEqual_m8A52F50B097C2A9BDFA919675AB046B390F9B324_inline(L_6, L_8, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_10 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11 = L_10.___c2;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c2;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_14;
		L_14 = uint4_op_LessThanOrEqual_m8A52F50B097C2A9BDFA919675AB046B390F9B324_inline(L_11, L_13, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_15 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16 = L_15.___c3;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_17 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_18 = L_17.___c3;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_19;
		L_19 = uint4_op_LessThanOrEqual_m8A52F50B097C2A9BDFA919675AB046B390F9B324_inline(L_16, L_18, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_20;
		memset((&L_20), 0, sizeof(L_20));
		bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline((&L_20), L_4, L_9, L_14, L_19, NULL);
		return L_20;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 uint4x4_op_LessThanOrEqual_mD5BEA5734FC7BAC42BD446556451976F17CFE391 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint32_t L_2 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_3;
		L_3 = uint4_op_LessThanOrEqual_m8B0420CC432FFED5FB94D3772815893A6114B180_inline(L_1, L_2, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_4 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5 = L_4.___c1;
		uint32_t L_6 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_7;
		L_7 = uint4_op_LessThanOrEqual_m8B0420CC432FFED5FB94D3772815893A6114B180_inline(L_5, L_6, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_8 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = L_8.___c2;
		uint32_t L_10 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_11;
		L_11 = uint4_op_LessThanOrEqual_m8B0420CC432FFED5FB94D3772815893A6114B180_inline(L_9, L_10, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c3;
		uint32_t L_14 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_15;
		L_15 = uint4_op_LessThanOrEqual_m8B0420CC432FFED5FB94D3772815893A6114B180_inline(L_13, L_14, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_16;
		memset((&L_16), 0, sizeof(L_16));
		bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 uint4x4_op_LessThanOrEqual_m24D6E35798B3E795F0A9C98355530648DD168DF9 (uint32_t ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = L_1.___c0;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_3;
		L_3 = uint4_op_LessThanOrEqual_m3C3804A4D76E0D681B39390EF7A17E37E88F7540_inline(L_0, L_2, NULL);
		uint32_t L_4 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_7;
		L_7 = uint4_op_LessThanOrEqual_m3C3804A4D76E0D681B39390EF7A17E37E88F7540_inline(L_4, L_6, NULL);
		uint32_t L_8 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c2;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_11;
		L_11 = uint4_op_LessThanOrEqual_m3C3804A4D76E0D681B39390EF7A17E37E88F7540_inline(L_8, L_10, NULL);
		uint32_t L_12 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_13 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = L_13.___c3;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_15;
		L_15 = uint4_op_LessThanOrEqual_m3C3804A4D76E0D681B39390EF7A17E37E88F7540_inline(L_12, L_14, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_16;
		memset((&L_16), 0, sizeof(L_16));
		bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 uint4x4_op_GreaterThan_m92004FFE5F952CDCF3A282887AD98AAABB2549EB (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = L_2.___c0;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_4;
		L_4 = uint4_op_GreaterThan_m77A71F392617EFC97C92684F40A3C05B99C8EB6B_inline(L_1, L_3, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_7 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = L_7.___c1;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_9;
		L_9 = uint4_op_GreaterThan_m77A71F392617EFC97C92684F40A3C05B99C8EB6B_inline(L_6, L_8, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_10 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11 = L_10.___c2;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c2;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_14;
		L_14 = uint4_op_GreaterThan_m77A71F392617EFC97C92684F40A3C05B99C8EB6B_inline(L_11, L_13, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_15 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16 = L_15.___c3;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_17 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_18 = L_17.___c3;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_19;
		L_19 = uint4_op_GreaterThan_m77A71F392617EFC97C92684F40A3C05B99C8EB6B_inline(L_16, L_18, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_20;
		memset((&L_20), 0, sizeof(L_20));
		bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline((&L_20), L_4, L_9, L_14, L_19, NULL);
		return L_20;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 uint4x4_op_GreaterThan_m565CE49ACB94787B6EA0EC30A8900E92D075C1E7 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint32_t L_2 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_3;
		L_3 = uint4_op_GreaterThan_mF8C8A88E77644D1B6F50F98D4DF718ED9DD37514_inline(L_1, L_2, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_4 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5 = L_4.___c1;
		uint32_t L_6 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_7;
		L_7 = uint4_op_GreaterThan_mF8C8A88E77644D1B6F50F98D4DF718ED9DD37514_inline(L_5, L_6, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_8 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = L_8.___c2;
		uint32_t L_10 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_11;
		L_11 = uint4_op_GreaterThan_mF8C8A88E77644D1B6F50F98D4DF718ED9DD37514_inline(L_9, L_10, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c3;
		uint32_t L_14 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_15;
		L_15 = uint4_op_GreaterThan_mF8C8A88E77644D1B6F50F98D4DF718ED9DD37514_inline(L_13, L_14, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_16;
		memset((&L_16), 0, sizeof(L_16));
		bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 uint4x4_op_GreaterThan_m291ED844F8DE17B42CB0F7E4C764812576BD26D4 (uint32_t ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = L_1.___c0;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_3;
		L_3 = uint4_op_GreaterThan_mDD1A94F81F32D1B5BEDD7794CEBD9A1CE8FD2C46_inline(L_0, L_2, NULL);
		uint32_t L_4 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_7;
		L_7 = uint4_op_GreaterThan_mDD1A94F81F32D1B5BEDD7794CEBD9A1CE8FD2C46_inline(L_4, L_6, NULL);
		uint32_t L_8 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c2;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_11;
		L_11 = uint4_op_GreaterThan_mDD1A94F81F32D1B5BEDD7794CEBD9A1CE8FD2C46_inline(L_8, L_10, NULL);
		uint32_t L_12 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_13 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = L_13.___c3;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_15;
		L_15 = uint4_op_GreaterThan_mDD1A94F81F32D1B5BEDD7794CEBD9A1CE8FD2C46_inline(L_12, L_14, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_16;
		memset((&L_16), 0, sizeof(L_16));
		bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 uint4x4_op_GreaterThanOrEqual_mC5ACA224EC834113997ACFD33EE6A02A098B7396 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = L_2.___c0;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_4;
		L_4 = uint4_op_GreaterThanOrEqual_m35B238651EBAFA2F8241FF4A0E012CEA20F53EAE_inline(L_1, L_3, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_7 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = L_7.___c1;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_9;
		L_9 = uint4_op_GreaterThanOrEqual_m35B238651EBAFA2F8241FF4A0E012CEA20F53EAE_inline(L_6, L_8, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_10 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11 = L_10.___c2;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c2;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_14;
		L_14 = uint4_op_GreaterThanOrEqual_m35B238651EBAFA2F8241FF4A0E012CEA20F53EAE_inline(L_11, L_13, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_15 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16 = L_15.___c3;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_17 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_18 = L_17.___c3;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_19;
		L_19 = uint4_op_GreaterThanOrEqual_m35B238651EBAFA2F8241FF4A0E012CEA20F53EAE_inline(L_16, L_18, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_20;
		memset((&L_20), 0, sizeof(L_20));
		bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline((&L_20), L_4, L_9, L_14, L_19, NULL);
		return L_20;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 uint4x4_op_GreaterThanOrEqual_mA70D2B6A40598DFDF24F3807AD01EBCDAAC11D9E (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint32_t L_2 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_3;
		L_3 = uint4_op_GreaterThanOrEqual_m06C3ADD093FCFD022D46878D3BC19CD3049521B0_inline(L_1, L_2, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_4 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5 = L_4.___c1;
		uint32_t L_6 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_7;
		L_7 = uint4_op_GreaterThanOrEqual_m06C3ADD093FCFD022D46878D3BC19CD3049521B0_inline(L_5, L_6, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_8 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = L_8.___c2;
		uint32_t L_10 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_11;
		L_11 = uint4_op_GreaterThanOrEqual_m06C3ADD093FCFD022D46878D3BC19CD3049521B0_inline(L_9, L_10, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c3;
		uint32_t L_14 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_15;
		L_15 = uint4_op_GreaterThanOrEqual_m06C3ADD093FCFD022D46878D3BC19CD3049521B0_inline(L_13, L_14, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_16;
		memset((&L_16), 0, sizeof(L_16));
		bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 uint4x4_op_GreaterThanOrEqual_mD51A6B61204AA9C4D11C37BEC8B62594DCAD90AA (uint32_t ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = L_1.___c0;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_3;
		L_3 = uint4_op_GreaterThanOrEqual_mD6829718DDCC243DA19306FEEA05BF4AD83AFB66_inline(L_0, L_2, NULL);
		uint32_t L_4 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_7;
		L_7 = uint4_op_GreaterThanOrEqual_mD6829718DDCC243DA19306FEEA05BF4AD83AFB66_inline(L_4, L_6, NULL);
		uint32_t L_8 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c2;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_11;
		L_11 = uint4_op_GreaterThanOrEqual_mD6829718DDCC243DA19306FEEA05BF4AD83AFB66_inline(L_8, L_10, NULL);
		uint32_t L_12 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_13 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = L_13.___c3;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_15;
		L_15 = uint4_op_GreaterThanOrEqual_mD6829718DDCC243DA19306FEEA05BF4AD83AFB66_inline(L_12, L_14, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_16;
		memset((&L_16), 0, sizeof(L_16));
		bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_UnaryNegation_m2669C036A6D95B688E8644EE82E060F765B650D2 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_val, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_val;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2;
		L_2 = uint4_op_UnaryNegation_m4E2B5BF3588706130CB50ACA4CDA4617BD9C42D3_inline(L_1, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_3 = ___0_val;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = L_3.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		L_5 = uint4_op_UnaryNegation_m4E2B5BF3588706130CB50ACA4CDA4617BD9C42D3_inline(L_4, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_6 = ___0_val;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7 = L_6.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8;
		L_8 = uint4_op_UnaryNegation_m4E2B5BF3588706130CB50ACA4CDA4617BD9C42D3_inline(L_7, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___0_val;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_UnaryNegation_m4E2B5BF3588706130CB50ACA4CDA4617BD9C42D3_inline(L_10, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_12), L_2, L_5, L_8, L_11, NULL);
		return L_12;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_UnaryPlus_m2DB96D490385899EB24E3DF25A16373A290520F1 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_val, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_val;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2;
		L_2 = uint4_op_UnaryPlus_m6DD02E0B287D5C0A9C8BD14709A01A113FF7F888_inline(L_1, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_3 = ___0_val;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = L_3.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		L_5 = uint4_op_UnaryPlus_m6DD02E0B287D5C0A9C8BD14709A01A113FF7F888_inline(L_4, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_6 = ___0_val;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7 = L_6.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8;
		L_8 = uint4_op_UnaryPlus_m6DD02E0B287D5C0A9C8BD14709A01A113FF7F888_inline(L_7, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___0_val;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_UnaryPlus_m6DD02E0B287D5C0A9C8BD14709A01A113FF7F888_inline(L_10, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_12), L_2, L_5, L_8, L_11, NULL);
		return L_12;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_LeftShift_mE670E2D278889D35469E4EB41FA8B95741A8D621 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_x, int32_t ___1_n, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		int32_t L_2 = ___1_n;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_LeftShift_m5E109F47429DAA6E55073B8BBD9988FF9B18E4A2_inline(L_1, L_2, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_4 = ___0_x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5 = L_4.___c1;
		int32_t L_6 = ___1_n;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_LeftShift_m5E109F47429DAA6E55073B8BBD9988FF9B18E4A2_inline(L_5, L_6, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_8 = ___0_x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = L_8.___c2;
		int32_t L_10 = ___1_n;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_LeftShift_m5E109F47429DAA6E55073B8BBD9988FF9B18E4A2_inline(L_9, L_10, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___0_x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c3;
		int32_t L_14 = ___1_n;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_LeftShift_m5E109F47429DAA6E55073B8BBD9988FF9B18E4A2_inline(L_13, L_14, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_RightShift_mE3D52147ACD645C9651325DCF219A7C0527ADBC5 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_x, int32_t ___1_n, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		int32_t L_2 = ___1_n;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_RightShift_m277ED9AFF0C41C957E79413C0B4A19EB28503EED_inline(L_1, L_2, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_4 = ___0_x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5 = L_4.___c1;
		int32_t L_6 = ___1_n;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_RightShift_m277ED9AFF0C41C957E79413C0B4A19EB28503EED_inline(L_5, L_6, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_8 = ___0_x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = L_8.___c2;
		int32_t L_10 = ___1_n;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_RightShift_m277ED9AFF0C41C957E79413C0B4A19EB28503EED_inline(L_9, L_10, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___0_x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c3;
		int32_t L_14 = ___1_n;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_RightShift_m277ED9AFF0C41C957E79413C0B4A19EB28503EED_inline(L_13, L_14, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 uint4x4_op_Equality_m8B0457B80F79F11A1BD2A105427F10E92AB78466 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = L_2.___c0;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_4;
		L_4 = uint4_op_Equality_m5E8D2F6C5A385AACC9A966226C00DD40E44046E3_inline(L_1, L_3, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_7 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = L_7.___c1;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_9;
		L_9 = uint4_op_Equality_m5E8D2F6C5A385AACC9A966226C00DD40E44046E3_inline(L_6, L_8, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_10 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11 = L_10.___c2;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c2;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_14;
		L_14 = uint4_op_Equality_m5E8D2F6C5A385AACC9A966226C00DD40E44046E3_inline(L_11, L_13, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_15 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16 = L_15.___c3;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_17 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_18 = L_17.___c3;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_19;
		L_19 = uint4_op_Equality_m5E8D2F6C5A385AACC9A966226C00DD40E44046E3_inline(L_16, L_18, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_20;
		memset((&L_20), 0, sizeof(L_20));
		bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline((&L_20), L_4, L_9, L_14, L_19, NULL);
		return L_20;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 uint4x4_op_Equality_mF230B50319D25AE117D8AC6EF816508DA32D2A74 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint32_t L_2 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_3;
		L_3 = uint4_op_Equality_mA7515DAB8E3AAD2B5BDA4ED9653F0BF393419A91_inline(L_1, L_2, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_4 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5 = L_4.___c1;
		uint32_t L_6 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_7;
		L_7 = uint4_op_Equality_mA7515DAB8E3AAD2B5BDA4ED9653F0BF393419A91_inline(L_5, L_6, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_8 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = L_8.___c2;
		uint32_t L_10 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_11;
		L_11 = uint4_op_Equality_mA7515DAB8E3AAD2B5BDA4ED9653F0BF393419A91_inline(L_9, L_10, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c3;
		uint32_t L_14 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_15;
		L_15 = uint4_op_Equality_mA7515DAB8E3AAD2B5BDA4ED9653F0BF393419A91_inline(L_13, L_14, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_16;
		memset((&L_16), 0, sizeof(L_16));
		bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 uint4x4_op_Equality_mE940916D90B6BBB2BEC3C0B7986E1DBA0943573E (uint32_t ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = L_1.___c0;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_3;
		L_3 = uint4_op_Equality_m9B94A5CD832272DCFD0B9C99D480556656579A6D_inline(L_0, L_2, NULL);
		uint32_t L_4 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_7;
		L_7 = uint4_op_Equality_m9B94A5CD832272DCFD0B9C99D480556656579A6D_inline(L_4, L_6, NULL);
		uint32_t L_8 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c2;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_11;
		L_11 = uint4_op_Equality_m9B94A5CD832272DCFD0B9C99D480556656579A6D_inline(L_8, L_10, NULL);
		uint32_t L_12 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_13 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = L_13.___c3;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_15;
		L_15 = uint4_op_Equality_m9B94A5CD832272DCFD0B9C99D480556656579A6D_inline(L_12, L_14, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_16;
		memset((&L_16), 0, sizeof(L_16));
		bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 uint4x4_op_Inequality_mA4524A238A25A07019828E0739BDF0DEC55A453E (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = L_2.___c0;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_4;
		L_4 = uint4_op_Inequality_m58973FE7AABF39D4BAB65975F3A537F31DFE279E_inline(L_1, L_3, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_7 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = L_7.___c1;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_9;
		L_9 = uint4_op_Inequality_m58973FE7AABF39D4BAB65975F3A537F31DFE279E_inline(L_6, L_8, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_10 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11 = L_10.___c2;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c2;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_14;
		L_14 = uint4_op_Inequality_m58973FE7AABF39D4BAB65975F3A537F31DFE279E_inline(L_11, L_13, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_15 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16 = L_15.___c3;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_17 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_18 = L_17.___c3;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_19;
		L_19 = uint4_op_Inequality_m58973FE7AABF39D4BAB65975F3A537F31DFE279E_inline(L_16, L_18, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_20;
		memset((&L_20), 0, sizeof(L_20));
		bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline((&L_20), L_4, L_9, L_14, L_19, NULL);
		return L_20;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 uint4x4_op_Inequality_m79DEA00429273EC685B6493092FD9BF4BBA5D433 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint32_t L_2 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_3;
		L_3 = uint4_op_Inequality_mEED47D66CB338F4A7E98F9C4B5372FF9E9452A79_inline(L_1, L_2, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_4 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5 = L_4.___c1;
		uint32_t L_6 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_7;
		L_7 = uint4_op_Inequality_mEED47D66CB338F4A7E98F9C4B5372FF9E9452A79_inline(L_5, L_6, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_8 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = L_8.___c2;
		uint32_t L_10 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_11;
		L_11 = uint4_op_Inequality_mEED47D66CB338F4A7E98F9C4B5372FF9E9452A79_inline(L_9, L_10, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c3;
		uint32_t L_14 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_15;
		L_15 = uint4_op_Inequality_mEED47D66CB338F4A7E98F9C4B5372FF9E9452A79_inline(L_13, L_14, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_16;
		memset((&L_16), 0, sizeof(L_16));
		bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 uint4x4_op_Inequality_m2DE575C4DE4D291B585C79ED0826F2FDD5619C6A (uint32_t ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = L_1.___c0;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_3;
		L_3 = uint4_op_Inequality_mAEA94A9E6197044FE2EA3FFF6F4EA234E74F2F2F_inline(L_0, L_2, NULL);
		uint32_t L_4 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_7;
		L_7 = uint4_op_Inequality_mAEA94A9E6197044FE2EA3FFF6F4EA234E74F2F2F_inline(L_4, L_6, NULL);
		uint32_t L_8 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c2;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_11;
		L_11 = uint4_op_Inequality_mAEA94A9E6197044FE2EA3FFF6F4EA234E74F2F2F_inline(L_8, L_10, NULL);
		uint32_t L_12 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_13 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = L_13.___c3;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_15;
		L_15 = uint4_op_Inequality_mAEA94A9E6197044FE2EA3FFF6F4EA234E74F2F2F_inline(L_12, L_14, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_16;
		memset((&L_16), 0, sizeof(L_16));
		bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_OnesComplement_m75110725B186374697D0FB1FCA0465C6491B36BF (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_val, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_val;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2;
		L_2 = uint4_op_OnesComplement_m694E43D937D79E275E9633CF8BA49DE0D11E929F_inline(L_1, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_3 = ___0_val;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = L_3.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		L_5 = uint4_op_OnesComplement_m694E43D937D79E275E9633CF8BA49DE0D11E929F_inline(L_4, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_6 = ___0_val;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7 = L_6.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8;
		L_8 = uint4_op_OnesComplement_m694E43D937D79E275E9633CF8BA49DE0D11E929F_inline(L_7, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___0_val;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_OnesComplement_m694E43D937D79E275E9633CF8BA49DE0D11E929F_inline(L_10, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_12), L_2, L_5, L_8, L_11, NULL);
		return L_12;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_BitwiseAnd_m7F67F9FFC729764F2064C2137034FD316B23A847 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = L_2.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4;
		L_4 = uint4_op_BitwiseAnd_m1A9CB1A2976212FE13C84F970D4C59C30A54D875_inline(L_1, L_3, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_7 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = L_7.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9;
		L_9 = uint4_op_BitwiseAnd_m1A9CB1A2976212FE13C84F970D4C59C30A54D875_inline(L_6, L_8, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_10 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11 = L_10.___c2;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14;
		L_14 = uint4_op_BitwiseAnd_m1A9CB1A2976212FE13C84F970D4C59C30A54D875_inline(L_11, L_13, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_15 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16 = L_15.___c3;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_17 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_18 = L_17.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_19;
		L_19 = uint4_op_BitwiseAnd_m1A9CB1A2976212FE13C84F970D4C59C30A54D875_inline(L_16, L_18, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_20;
		memset((&L_20), 0, sizeof(L_20));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_20), L_4, L_9, L_14, L_19, NULL);
		return L_20;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_BitwiseAnd_mD8FE96691235FA37E51DEACD552640F980B3CEFB (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_BitwiseAnd_mEF924757B2BE1F41834950F7880B93C25B812044_inline(L_1, L_2, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_4 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5 = L_4.___c1;
		uint32_t L_6 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_BitwiseAnd_mEF924757B2BE1F41834950F7880B93C25B812044_inline(L_5, L_6, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_8 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = L_8.___c2;
		uint32_t L_10 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_BitwiseAnd_mEF924757B2BE1F41834950F7880B93C25B812044_inline(L_9, L_10, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c3;
		uint32_t L_14 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_BitwiseAnd_mEF924757B2BE1F41834950F7880B93C25B812044_inline(L_13, L_14, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_BitwiseAnd_m87190444D65587B2610A481BE89804C775A339E4 (uint32_t ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = L_1.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_BitwiseAnd_mD7D621F58D732913D0B45E67C22F12A34E249C28_inline(L_0, L_2, NULL);
		uint32_t L_4 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_BitwiseAnd_mD7D621F58D732913D0B45E67C22F12A34E249C28_inline(L_4, L_6, NULL);
		uint32_t L_8 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_BitwiseAnd_mD7D621F58D732913D0B45E67C22F12A34E249C28_inline(L_8, L_10, NULL);
		uint32_t L_12 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_13 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = L_13.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_BitwiseAnd_mD7D621F58D732913D0B45E67C22F12A34E249C28_inline(L_12, L_14, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_BitwiseOr_m13A48CE3C0A7F30F3FE20A53EDE4EBA9BE7AFBAF (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = L_2.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4;
		L_4 = uint4_op_BitwiseOr_mDA88C9E25D0910D512ABABDC200D6E3A2E68B573_inline(L_1, L_3, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_7 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = L_7.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9;
		L_9 = uint4_op_BitwiseOr_mDA88C9E25D0910D512ABABDC200D6E3A2E68B573_inline(L_6, L_8, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_10 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11 = L_10.___c2;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14;
		L_14 = uint4_op_BitwiseOr_mDA88C9E25D0910D512ABABDC200D6E3A2E68B573_inline(L_11, L_13, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_15 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16 = L_15.___c3;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_17 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_18 = L_17.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_19;
		L_19 = uint4_op_BitwiseOr_mDA88C9E25D0910D512ABABDC200D6E3A2E68B573_inline(L_16, L_18, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_20;
		memset((&L_20), 0, sizeof(L_20));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_20), L_4, L_9, L_14, L_19, NULL);
		return L_20;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_BitwiseOr_m3D235FC00F7CA630FF4809C6C5A5733749915E66 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_BitwiseOr_mA3F6C811A2EF145F2D7240A9B07801089AC380BD_inline(L_1, L_2, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_4 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5 = L_4.___c1;
		uint32_t L_6 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_BitwiseOr_mA3F6C811A2EF145F2D7240A9B07801089AC380BD_inline(L_5, L_6, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_8 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = L_8.___c2;
		uint32_t L_10 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_BitwiseOr_mA3F6C811A2EF145F2D7240A9B07801089AC380BD_inline(L_9, L_10, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c3;
		uint32_t L_14 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_BitwiseOr_mA3F6C811A2EF145F2D7240A9B07801089AC380BD_inline(L_13, L_14, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_BitwiseOr_m4BCB787078298257FF3E82172044399FBBF0E8D1 (uint32_t ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = L_1.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_BitwiseOr_mA1E41EFAAF04FDF3A9B4253C9BD99E55B447255C_inline(L_0, L_2, NULL);
		uint32_t L_4 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_BitwiseOr_mA1E41EFAAF04FDF3A9B4253C9BD99E55B447255C_inline(L_4, L_6, NULL);
		uint32_t L_8 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_BitwiseOr_mA1E41EFAAF04FDF3A9B4253C9BD99E55B447255C_inline(L_8, L_10, NULL);
		uint32_t L_12 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_13 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = L_13.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_BitwiseOr_mA1E41EFAAF04FDF3A9B4253C9BD99E55B447255C_inline(L_12, L_14, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_ExclusiveOr_mB82EB01F558828445AA2C3BA81681BE51530A106 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = L_2.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4;
		L_4 = uint4_op_ExclusiveOr_m5B57FB4F864B88CB06B4949AA275A70D02BF7889_inline(L_1, L_3, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_7 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = L_7.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9;
		L_9 = uint4_op_ExclusiveOr_m5B57FB4F864B88CB06B4949AA275A70D02BF7889_inline(L_6, L_8, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_10 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11 = L_10.___c2;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14;
		L_14 = uint4_op_ExclusiveOr_m5B57FB4F864B88CB06B4949AA275A70D02BF7889_inline(L_11, L_13, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_15 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16 = L_15.___c3;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_17 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_18 = L_17.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_19;
		L_19 = uint4_op_ExclusiveOr_m5B57FB4F864B88CB06B4949AA275A70D02BF7889_inline(L_16, L_18, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_20;
		memset((&L_20), 0, sizeof(L_20));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_20), L_4, L_9, L_14, L_19, NULL);
		return L_20;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_ExclusiveOr_m5CBC6B7B700BE025BCED9994078F774B904A5F2F (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_ExclusiveOr_m4A4BB6F529F97A0027A7F9F989D21BC84F474391_inline(L_1, L_2, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_4 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5 = L_4.___c1;
		uint32_t L_6 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_ExclusiveOr_m4A4BB6F529F97A0027A7F9F989D21BC84F474391_inline(L_5, L_6, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_8 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = L_8.___c2;
		uint32_t L_10 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_ExclusiveOr_m4A4BB6F529F97A0027A7F9F989D21BC84F474391_inline(L_9, L_10, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_12 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13 = L_12.___c3;
		uint32_t L_14 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_ExclusiveOr_m4A4BB6F529F97A0027A7F9F989D21BC84F474391_inline(L_13, L_14, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A uint4x4_op_ExclusiveOr_m874F42555833D1FB638C20B178BD45CCCD77FF24 (uint32_t ___0_lhs, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = L_1.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_ExclusiveOr_m18704377E7E81E1741DAAB2CBCB23D7BB6A00CC6_inline(L_0, L_2, NULL);
		uint32_t L_4 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_ExclusiveOr_m18704377E7E81E1741DAAB2CBCB23D7BB6A00CC6_inline(L_4, L_6, NULL);
		uint32_t L_8 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_ExclusiveOr_m18704377E7E81E1741DAAB2CBCB23D7BB6A00CC6_inline(L_8, L_10, NULL);
		uint32_t L_12 = ___0_lhs;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_13 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = L_13.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = uint4_op_ExclusiveOr_m18704377E7E81E1741DAAB2CBCB23D7BB6A00CC6_inline(L_12, L_14, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline((&L_16), L_3, L_7, L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* uint4x4_get_Item_m4F543805E3B92E11399E5829D2D3984CE8657C4A (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, int32_t ___0_index, const RuntimeMethod* method) 
{
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* V_0 = NULL;
	{
		V_0 = __this;
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* L_0 = V_0;
		int32_t L_1 = ___0_index;
		uint32_t L_2 = sizeof(uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9);
		return (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(((intptr_t)il2cpp_codegen_add((intptr_t)((uintptr_t)L_0), ((intptr_t)il2cpp_codegen_multiply(((intptr_t)L_1), (int32_t)L_2)))));
	}
}
IL2CPP_EXTERN_C  uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* uint4x4_get_Item_m4F543805E3B92E11399E5829D2D3984CE8657C4A_AdjustorThunk (RuntimeObject* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*>(__this + _offset);
	uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* _returnValue;
	_returnValue = uint4x4_get_Item_m4F543805E3B92E11399E5829D2D3984CE8657C4A(_thisAdjusted, ___0_index, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool uint4x4_Equals_m1DADC37353246BE77BB8D6E000FB2E79CD33D8CF (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_0 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c0);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1 = ___0_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = L_1.___c0;
		bool L_3;
		L_3 = uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB_inline(L_0, L_2, NULL);
		if (!L_3)
		{
			goto IL_004b;
		}
	}
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_4 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c1);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___0_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		bool L_7;
		L_7 = uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB_inline(L_4, L_6, NULL);
		if (!L_7)
		{
			goto IL_004b;
		}
	}
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_8 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c2);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___0_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c2;
		bool L_11;
		L_11 = uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB_inline(L_8, L_10, NULL);
		if (!L_11)
		{
			goto IL_004b;
		}
	}
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_12 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c3);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_13 = ___0_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = L_13.___c3;
		bool L_15;
		L_15 = uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB_inline(L_12, L_14, NULL);
		return L_15;
	}

IL_004b:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C  bool uint4x4_Equals_m1DADC37353246BE77BB8D6E000FB2E79CD33D8CF_AdjustorThunk (RuntimeObject* __this, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_rhs, const RuntimeMethod* method)
{
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*>(__this + _offset);
	bool _returnValue;
	_returnValue = uint4x4_Equals_m1DADC37353246BE77BB8D6E000FB2E79CD33D8CF_inline(_thisAdjusted, ___0_rhs, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool uint4x4_Equals_mC9C251DF1B71FA38A4CFAD07CC3B50E4C0132A9A (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, RuntimeObject* ___0_o, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		RuntimeObject* L_0 = ___0_o;
		if (!((RuntimeObject*)IsInstSealed((RuntimeObject*)L_0, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A_il2cpp_TypeInfo_var)))
		{
			goto IL_0017;
		}
	}
	{
		RuntimeObject* L_1 = ___0_o;
		V_0 = ((*(uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*)UnBox(L_1, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A_il2cpp_TypeInfo_var)));
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_2 = V_0;
		bool L_3;
		L_3 = uint4x4_Equals_m1DADC37353246BE77BB8D6E000FB2E79CD33D8CF_inline(__this, L_2, NULL);
		return L_3;
	}

IL_0017:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C  bool uint4x4_Equals_mC9C251DF1B71FA38A4CFAD07CC3B50E4C0132A9A_AdjustorThunk (RuntimeObject* __this, RuntimeObject* ___0_o, const RuntimeMethod* method)
{
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*>(__this + _offset);
	bool _returnValue;
	_returnValue = uint4x4_Equals_mC9C251DF1B71FA38A4CFAD07CC3B50E4C0132A9A(_thisAdjusted, ___0_o, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t uint4x4_GetHashCode_m812B7AA9E1BCBCCDEB7E00AE247C960964B3EB65 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = (*(uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*)__this);
		uint32_t L_1;
		L_1 = math_hash_m5DD32EF0CE916EDF69ED64D20E961217FB867527_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C  int32_t uint4x4_GetHashCode_m812B7AA9E1BCBCCDEB7E00AE247C960964B3EB65_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = uint4x4_GetHashCode_m812B7AA9E1BCBCCDEB7E00AE247C960964B3EB65_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* uint4x4_ToString_m636D074F6F88F38EB2590202338118CE9AA9468B (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBA12CD7184B95EC551FFCA240321EB14D0272CE2);
		s_Il2CppMethodInitialized = true;
	}
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_0 = (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)SZArrayNew(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var, (uint32_t)((int32_t)16));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_1 = L_0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_2 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c0);
		uint32_t L_3 = L_2->___x;
		uint32_t L_4 = L_3;
		RuntimeObject* L_5 = Box(il2cpp_defaults.uint32_class, &L_4);
		NullCheck(L_1);
		ArrayElementTypeCheck (L_1, L_5);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject*)L_5);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_6 = L_1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_7 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c1);
		uint32_t L_8 = L_7->___x;
		uint32_t L_9 = L_8;
		RuntimeObject* L_10 = Box(il2cpp_defaults.uint32_class, &L_9);
		NullCheck(L_6);
		ArrayElementTypeCheck (L_6, L_10);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(1), (RuntimeObject*)L_10);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_11 = L_6;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_12 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c2);
		uint32_t L_13 = L_12->___x;
		uint32_t L_14 = L_13;
		RuntimeObject* L_15 = Box(il2cpp_defaults.uint32_class, &L_14);
		NullCheck(L_11);
		ArrayElementTypeCheck (L_11, L_15);
		(L_11)->SetAt(static_cast<il2cpp_array_size_t>(2), (RuntimeObject*)L_15);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_16 = L_11;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_17 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c3);
		uint32_t L_18 = L_17->___x;
		uint32_t L_19 = L_18;
		RuntimeObject* L_20 = Box(il2cpp_defaults.uint32_class, &L_19);
		NullCheck(L_16);
		ArrayElementTypeCheck (L_16, L_20);
		(L_16)->SetAt(static_cast<il2cpp_array_size_t>(3), (RuntimeObject*)L_20);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_21 = L_16;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_22 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c0);
		uint32_t L_23 = L_22->___y;
		uint32_t L_24 = L_23;
		RuntimeObject* L_25 = Box(il2cpp_defaults.uint32_class, &L_24);
		NullCheck(L_21);
		ArrayElementTypeCheck (L_21, L_25);
		(L_21)->SetAt(static_cast<il2cpp_array_size_t>(4), (RuntimeObject*)L_25);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_26 = L_21;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_27 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c1);
		uint32_t L_28 = L_27->___y;
		uint32_t L_29 = L_28;
		RuntimeObject* L_30 = Box(il2cpp_defaults.uint32_class, &L_29);
		NullCheck(L_26);
		ArrayElementTypeCheck (L_26, L_30);
		(L_26)->SetAt(static_cast<il2cpp_array_size_t>(5), (RuntimeObject*)L_30);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_31 = L_26;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_32 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c2);
		uint32_t L_33 = L_32->___y;
		uint32_t L_34 = L_33;
		RuntimeObject* L_35 = Box(il2cpp_defaults.uint32_class, &L_34);
		NullCheck(L_31);
		ArrayElementTypeCheck (L_31, L_35);
		(L_31)->SetAt(static_cast<il2cpp_array_size_t>(6), (RuntimeObject*)L_35);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_36 = L_31;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_37 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c3);
		uint32_t L_38 = L_37->___y;
		uint32_t L_39 = L_38;
		RuntimeObject* L_40 = Box(il2cpp_defaults.uint32_class, &L_39);
		NullCheck(L_36);
		ArrayElementTypeCheck (L_36, L_40);
		(L_36)->SetAt(static_cast<il2cpp_array_size_t>(7), (RuntimeObject*)L_40);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_41 = L_36;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_42 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c0);
		uint32_t L_43 = L_42->___z;
		uint32_t L_44 = L_43;
		RuntimeObject* L_45 = Box(il2cpp_defaults.uint32_class, &L_44);
		NullCheck(L_41);
		ArrayElementTypeCheck (L_41, L_45);
		(L_41)->SetAt(static_cast<il2cpp_array_size_t>(8), (RuntimeObject*)L_45);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_46 = L_41;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_47 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c1);
		uint32_t L_48 = L_47->___z;
		uint32_t L_49 = L_48;
		RuntimeObject* L_50 = Box(il2cpp_defaults.uint32_class, &L_49);
		NullCheck(L_46);
		ArrayElementTypeCheck (L_46, L_50);
		(L_46)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)9)), (RuntimeObject*)L_50);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_51 = L_46;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_52 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c2);
		uint32_t L_53 = L_52->___z;
		uint32_t L_54 = L_53;
		RuntimeObject* L_55 = Box(il2cpp_defaults.uint32_class, &L_54);
		NullCheck(L_51);
		ArrayElementTypeCheck (L_51, L_55);
		(L_51)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)10)), (RuntimeObject*)L_55);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_56 = L_51;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_57 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c3);
		uint32_t L_58 = L_57->___z;
		uint32_t L_59 = L_58;
		RuntimeObject* L_60 = Box(il2cpp_defaults.uint32_class, &L_59);
		NullCheck(L_56);
		ArrayElementTypeCheck (L_56, L_60);
		(L_56)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)11)), (RuntimeObject*)L_60);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_61 = L_56;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_62 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c0);
		uint32_t L_63 = L_62->___w;
		uint32_t L_64 = L_63;
		RuntimeObject* L_65 = Box(il2cpp_defaults.uint32_class, &L_64);
		NullCheck(L_61);
		ArrayElementTypeCheck (L_61, L_65);
		(L_61)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)12)), (RuntimeObject*)L_65);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_66 = L_61;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_67 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c1);
		uint32_t L_68 = L_67->___w;
		uint32_t L_69 = L_68;
		RuntimeObject* L_70 = Box(il2cpp_defaults.uint32_class, &L_69);
		NullCheck(L_66);
		ArrayElementTypeCheck (L_66, L_70);
		(L_66)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)13)), (RuntimeObject*)L_70);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_71 = L_66;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_72 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c2);
		uint32_t L_73 = L_72->___w;
		uint32_t L_74 = L_73;
		RuntimeObject* L_75 = Box(il2cpp_defaults.uint32_class, &L_74);
		NullCheck(L_71);
		ArrayElementTypeCheck (L_71, L_75);
		(L_71)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)14)), (RuntimeObject*)L_75);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_76 = L_71;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_77 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c3);
		uint32_t L_78 = L_77->___w;
		uint32_t L_79 = L_78;
		RuntimeObject* L_80 = Box(il2cpp_defaults.uint32_class, &L_79);
		NullCheck(L_76);
		ArrayElementTypeCheck (L_76, L_80);
		(L_76)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)15)), (RuntimeObject*)L_80);
		String_t* L_81;
		L_81 = String_Format_m918500C1EFB475181349A79989BB79BB36102894(_stringLiteralBA12CD7184B95EC551FFCA240321EB14D0272CE2, L_76, NULL);
		return L_81;
	}
}
IL2CPP_EXTERN_C  String_t* uint4x4_ToString_m636D074F6F88F38EB2590202338118CE9AA9468B_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*>(__this + _offset);
	String_t* _returnValue;
	_returnValue = uint4x4_ToString_m636D074F6F88F38EB2590202338118CE9AA9468B_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* uint4x4_ToString_m97543E4A4C7E05B6B1C86437E56F4246AA5FC456 (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, String_t* ___0_format, RuntimeObject* ___1_formatProvider, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBA12CD7184B95EC551FFCA240321EB14D0272CE2);
		s_Il2CppMethodInitialized = true;
	}
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_0 = (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)SZArrayNew(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var, (uint32_t)((int32_t)16));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_1 = L_0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_2 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c0);
		uint32_t* L_3 = (uint32_t*)(&L_2->___x);
		String_t* L_4 = ___0_format;
		RuntimeObject* L_5 = ___1_formatProvider;
		String_t* L_6;
		L_6 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_3, L_4, L_5, NULL);
		NullCheck(L_1);
		ArrayElementTypeCheck (L_1, L_6);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject*)L_6);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_7 = L_1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_8 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c1);
		uint32_t* L_9 = (uint32_t*)(&L_8->___x);
		String_t* L_10 = ___0_format;
		RuntimeObject* L_11 = ___1_formatProvider;
		String_t* L_12;
		L_12 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_9, L_10, L_11, NULL);
		NullCheck(L_7);
		ArrayElementTypeCheck (L_7, L_12);
		(L_7)->SetAt(static_cast<il2cpp_array_size_t>(1), (RuntimeObject*)L_12);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_13 = L_7;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_14 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c2);
		uint32_t* L_15 = (uint32_t*)(&L_14->___x);
		String_t* L_16 = ___0_format;
		RuntimeObject* L_17 = ___1_formatProvider;
		String_t* L_18;
		L_18 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_15, L_16, L_17, NULL);
		NullCheck(L_13);
		ArrayElementTypeCheck (L_13, L_18);
		(L_13)->SetAt(static_cast<il2cpp_array_size_t>(2), (RuntimeObject*)L_18);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_19 = L_13;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_20 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c3);
		uint32_t* L_21 = (uint32_t*)(&L_20->___x);
		String_t* L_22 = ___0_format;
		RuntimeObject* L_23 = ___1_formatProvider;
		String_t* L_24;
		L_24 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_21, L_22, L_23, NULL);
		NullCheck(L_19);
		ArrayElementTypeCheck (L_19, L_24);
		(L_19)->SetAt(static_cast<il2cpp_array_size_t>(3), (RuntimeObject*)L_24);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_25 = L_19;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_26 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c0);
		uint32_t* L_27 = (uint32_t*)(&L_26->___y);
		String_t* L_28 = ___0_format;
		RuntimeObject* L_29 = ___1_formatProvider;
		String_t* L_30;
		L_30 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_27, L_28, L_29, NULL);
		NullCheck(L_25);
		ArrayElementTypeCheck (L_25, L_30);
		(L_25)->SetAt(static_cast<il2cpp_array_size_t>(4), (RuntimeObject*)L_30);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_31 = L_25;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_32 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c1);
		uint32_t* L_33 = (uint32_t*)(&L_32->___y);
		String_t* L_34 = ___0_format;
		RuntimeObject* L_35 = ___1_formatProvider;
		String_t* L_36;
		L_36 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_33, L_34, L_35, NULL);
		NullCheck(L_31);
		ArrayElementTypeCheck (L_31, L_36);
		(L_31)->SetAt(static_cast<il2cpp_array_size_t>(5), (RuntimeObject*)L_36);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_37 = L_31;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_38 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c2);
		uint32_t* L_39 = (uint32_t*)(&L_38->___y);
		String_t* L_40 = ___0_format;
		RuntimeObject* L_41 = ___1_formatProvider;
		String_t* L_42;
		L_42 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_39, L_40, L_41, NULL);
		NullCheck(L_37);
		ArrayElementTypeCheck (L_37, L_42);
		(L_37)->SetAt(static_cast<il2cpp_array_size_t>(6), (RuntimeObject*)L_42);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_43 = L_37;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_44 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c3);
		uint32_t* L_45 = (uint32_t*)(&L_44->___y);
		String_t* L_46 = ___0_format;
		RuntimeObject* L_47 = ___1_formatProvider;
		String_t* L_48;
		L_48 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_45, L_46, L_47, NULL);
		NullCheck(L_43);
		ArrayElementTypeCheck (L_43, L_48);
		(L_43)->SetAt(static_cast<il2cpp_array_size_t>(7), (RuntimeObject*)L_48);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_49 = L_43;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_50 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c0);
		uint32_t* L_51 = (uint32_t*)(&L_50->___z);
		String_t* L_52 = ___0_format;
		RuntimeObject* L_53 = ___1_formatProvider;
		String_t* L_54;
		L_54 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_51, L_52, L_53, NULL);
		NullCheck(L_49);
		ArrayElementTypeCheck (L_49, L_54);
		(L_49)->SetAt(static_cast<il2cpp_array_size_t>(8), (RuntimeObject*)L_54);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_55 = L_49;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_56 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c1);
		uint32_t* L_57 = (uint32_t*)(&L_56->___z);
		String_t* L_58 = ___0_format;
		RuntimeObject* L_59 = ___1_formatProvider;
		String_t* L_60;
		L_60 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_57, L_58, L_59, NULL);
		NullCheck(L_55);
		ArrayElementTypeCheck (L_55, L_60);
		(L_55)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)9)), (RuntimeObject*)L_60);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_61 = L_55;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_62 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c2);
		uint32_t* L_63 = (uint32_t*)(&L_62->___z);
		String_t* L_64 = ___0_format;
		RuntimeObject* L_65 = ___1_formatProvider;
		String_t* L_66;
		L_66 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_63, L_64, L_65, NULL);
		NullCheck(L_61);
		ArrayElementTypeCheck (L_61, L_66);
		(L_61)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)10)), (RuntimeObject*)L_66);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_67 = L_61;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_68 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c3);
		uint32_t* L_69 = (uint32_t*)(&L_68->___z);
		String_t* L_70 = ___0_format;
		RuntimeObject* L_71 = ___1_formatProvider;
		String_t* L_72;
		L_72 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_69, L_70, L_71, NULL);
		NullCheck(L_67);
		ArrayElementTypeCheck (L_67, L_72);
		(L_67)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)11)), (RuntimeObject*)L_72);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_73 = L_67;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_74 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c0);
		uint32_t* L_75 = (uint32_t*)(&L_74->___w);
		String_t* L_76 = ___0_format;
		RuntimeObject* L_77 = ___1_formatProvider;
		String_t* L_78;
		L_78 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_75, L_76, L_77, NULL);
		NullCheck(L_73);
		ArrayElementTypeCheck (L_73, L_78);
		(L_73)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)12)), (RuntimeObject*)L_78);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_79 = L_73;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_80 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c1);
		uint32_t* L_81 = (uint32_t*)(&L_80->___w);
		String_t* L_82 = ___0_format;
		RuntimeObject* L_83 = ___1_formatProvider;
		String_t* L_84;
		L_84 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_81, L_82, L_83, NULL);
		NullCheck(L_79);
		ArrayElementTypeCheck (L_79, L_84);
		(L_79)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)13)), (RuntimeObject*)L_84);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_85 = L_79;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_86 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c2);
		uint32_t* L_87 = (uint32_t*)(&L_86->___w);
		String_t* L_88 = ___0_format;
		RuntimeObject* L_89 = ___1_formatProvider;
		String_t* L_90;
		L_90 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_87, L_88, L_89, NULL);
		NullCheck(L_85);
		ArrayElementTypeCheck (L_85, L_90);
		(L_85)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)14)), (RuntimeObject*)L_90);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_91 = L_85;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_92 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c3);
		uint32_t* L_93 = (uint32_t*)(&L_92->___w);
		String_t* L_94 = ___0_format;
		RuntimeObject* L_95 = ___1_formatProvider;
		String_t* L_96;
		L_96 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_93, L_94, L_95, NULL);
		NullCheck(L_91);
		ArrayElementTypeCheck (L_91, L_96);
		(L_91)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)15)), (RuntimeObject*)L_96);
		String_t* L_97;
		L_97 = String_Format_m918500C1EFB475181349A79989BB79BB36102894(_stringLiteralBA12CD7184B95EC551FFCA240321EB14D0272CE2, L_91, NULL);
		return L_97;
	}
}
IL2CPP_EXTERN_C  String_t* uint4x4_ToString_m97543E4A4C7E05B6B1C86437E56F4246AA5FC456_AdjustorThunk (RuntimeObject* __this, String_t* ___0_format, RuntimeObject* ___1_formatProvider, const RuntimeMethod* method)
{
	uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*>(__this + _offset);
	String_t* _returnValue;
	_returnValue = uint4x4_ToString_m97543E4A4C7E05B6B1C86437E56F4246AA5FC456_inline(_thisAdjusted, ___0_format, ___1_formatProvider, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void uint4x4__cctor_m12C70FD8608F4EA8312B9D471F53A31A9AFD4F9D (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0;
		memset((&L_0), 0, sizeof(L_0));
		uint4x4__ctor_mDF3E33FF44DA38AB4B132456C1BD34AC1546027F_inline((&L_0), 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, NULL);
		((uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A_StaticFields*)il2cpp_codegen_static_fields_for(uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A_il2cpp_TypeInfo_var))->___identity = L_0;
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_min, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_max, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_min;
		__this->___Min = L_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___1_max;
		__this->___Max = L_1;
		return;
	}
}
IL2CPP_EXTERN_C  void MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C_AdjustorThunk (RuntimeObject* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_min, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_max, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C_inline(_thisAdjusted, ___0_min, ___1_max, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 MinMaxAABB_CreateFromCenterAndExtents_mD8638596E84DB7E4E76C346D75B707EBE96AD411 (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_center, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_extents, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_center;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___1_extents;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Multiply_m6E5DC552C8B0F9A180298BD9197FF47B14E0EA81_inline(L_1, (0.5f), NULL);
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_3;
		L_3 = MinMaxAABB_CreateFromCenterAndHalfExtents_mF18074A621916107BF592378F72B6F113294F508_inline(L_0, L_2, NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 MinMaxAABB_CreateFromCenterAndHalfExtents_mF18074A621916107BF592378F72B6F113294F508 (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_center, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_halfExtents, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_center;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___1_halfExtents;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_0, L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = ___0_center;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___1_halfExtents;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		L_5 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_3, L_4, NULL);
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_6;
		memset((&L_6), 0, sizeof(L_6));
		MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C_inline((&L_6), L_2, L_5, NULL);
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E MinMaxAABB_get_Extents_m7E246B61BA832B3FB5EB8DCA4A35EC93688F105C (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Max;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = __this->___Min;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C  float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E MinMaxAABB_get_Extents_m7E246B61BA832B3FB5EB8DCA4A35EC93688F105C_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E _returnValue;
	_returnValue = MinMaxAABB_get_Extents_m7E246B61BA832B3FB5EB8DCA4A35EC93688F105C(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E MinMaxAABB_get_HalfExtents_m3656E833DBE99FE9D023F0E4B1497CF35F6BC948 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Max;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = __this->___Min;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_0, L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = float3_op_Multiply_m6E5DC552C8B0F9A180298BD9197FF47B14E0EA81_inline(L_2, (0.5f), NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C  float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E MinMaxAABB_get_HalfExtents_m3656E833DBE99FE9D023F0E4B1497CF35F6BC948_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E _returnValue;
	_returnValue = MinMaxAABB_get_HalfExtents_m3656E833DBE99FE9D023F0E4B1497CF35F6BC948(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E MinMaxAABB_get_Center_mC27A51CF13A95A053DEA07566A1F1900CFD98F4C (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Max;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = __this->___Min;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_0, L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = float3_op_Multiply_m6E5DC552C8B0F9A180298BD9197FF47B14E0EA81_inline(L_2, (0.5f), NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C  float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E MinMaxAABB_get_Center_mC27A51CF13A95A053DEA07566A1F1900CFD98F4C_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E _returnValue;
	_returnValue = MinMaxAABB_get_Center_mC27A51CF13A95A053DEA07566A1F1900CFD98F4C(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MinMaxAABB_get_IsValid_m84D96167E3AE9516D86FEC54B1CD138BD53FC5B8 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = __this->___Max;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_2;
		L_2 = float3_op_LessThanOrEqual_m18273875F0537224587D1622DD53562971D9FF48_inline(L_0, L_1, NULL);
		bool L_3;
		L_3 = math_all_mB8957D1E684773F171F74448AD9591F3619890A4_inline(L_2, NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C  bool MinMaxAABB_get_IsValid_m84D96167E3AE9516D86FEC54B1CD138BD53FC5B8_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	bool _returnValue;
	_returnValue = MinMaxAABB_get_IsValid_m84D96167E3AE9516D86FEC54B1CD138BD53FC5B8(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float MinMaxAABB_get_SurfaceArea_m542F0EA64B3A4C7975BAB0C3EBD354D79F34B1CE (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) 
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Max;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = __this->___Min;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_0, L_1, NULL);
		V_0 = L_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4;
		L_4 = float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_inline((&V_0), NULL);
		float L_5;
		L_5 = math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD_inline(L_3, L_4, NULL);
		return ((float)il2cpp_codegen_multiply((2.0f), L_5));
	}
}
IL2CPP_EXTERN_C  float MinMaxAABB_get_SurfaceArea_m542F0EA64B3A4C7975BAB0C3EBD354D79F34B1CE_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	float _returnValue;
	_returnValue = MinMaxAABB_get_SurfaceArea_m542F0EA64B3A4C7975BAB0C3EBD354D79F34B1CE(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MinMaxAABB_Contains_m507D89756D76CA0E2CBD8CAD23C6CE27A5F39089 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = __this->___Min;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_2;
		L_2 = float3_op_GreaterThanOrEqual_m01767B59951623AD803736AB63E12D9BC6FC1AAE_inline(L_0, L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = __this->___Max;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_5;
		L_5 = float3_op_LessThanOrEqual_m18273875F0537224587D1622DD53562971D9FF48_inline(L_3, L_4, NULL);
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_6;
		L_6 = bool3_op_BitwiseAnd_mE44CC5838094A40F1F605A7798994197165A63E1_inline(L_2, L_5, NULL);
		bool L_7;
		L_7 = math_all_mB8957D1E684773F171F74448AD9591F3619890A4_inline(L_6, NULL);
		return L_7;
	}
}
IL2CPP_EXTERN_C  bool MinMaxAABB_Contains_m507D89756D76CA0E2CBD8CAD23C6CE27A5F39089_AdjustorThunk (RuntimeObject* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	bool _returnValue;
	_returnValue = MinMaxAABB_Contains_m507D89756D76CA0E2CBD8CAD23C6CE27A5F39089_inline(_thisAdjusted, ___0_point, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MinMaxAABB_Contains_m8AB762A87FD22983A2A5ED2C96D3C559876D8823 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_1 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = L_1.___Min;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_3;
		L_3 = float3_op_LessThanOrEqual_m18273875F0537224587D1622DD53562971D9FF48_inline(L_0, L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = __this->___Max;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_5 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = L_5.___Max;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_7;
		L_7 = float3_op_GreaterThanOrEqual_m01767B59951623AD803736AB63E12D9BC6FC1AAE_inline(L_4, L_6, NULL);
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_8;
		L_8 = bool3_op_BitwiseAnd_mE44CC5838094A40F1F605A7798994197165A63E1_inline(L_3, L_7, NULL);
		bool L_9;
		L_9 = math_all_mB8957D1E684773F171F74448AD9591F3619890A4_inline(L_8, NULL);
		return L_9;
	}
}
IL2CPP_EXTERN_C  bool MinMaxAABB_Contains_m8AB762A87FD22983A2A5ED2C96D3C559876D8823_AdjustorThunk (RuntimeObject* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	bool _returnValue;
	_returnValue = MinMaxAABB_Contains_m8AB762A87FD22983A2A5ED2C96D3C559876D8823_inline(_thisAdjusted, ___0_aabb, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MinMaxAABB_Overlaps_mEEC801434D524543F9E6D5D77A642A5184F8C4D9 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Max;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_1 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = L_1.___Min;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_3;
		L_3 = float3_op_GreaterThanOrEqual_m01767B59951623AD803736AB63E12D9BC6FC1AAE_inline(L_0, L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = __this->___Min;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_5 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = L_5.___Max;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_7;
		L_7 = float3_op_LessThanOrEqual_m18273875F0537224587D1622DD53562971D9FF48_inline(L_4, L_6, NULL);
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_8;
		L_8 = bool3_op_BitwiseAnd_mE44CC5838094A40F1F605A7798994197165A63E1_inline(L_3, L_7, NULL);
		bool L_9;
		L_9 = math_all_mB8957D1E684773F171F74448AD9591F3619890A4_inline(L_8, NULL);
		return L_9;
	}
}
IL2CPP_EXTERN_C  bool MinMaxAABB_Overlaps_mEEC801434D524543F9E6D5D77A642A5184F8C4D9_AdjustorThunk (RuntimeObject* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	bool _returnValue;
	_returnValue = MinMaxAABB_Overlaps_mEEC801434D524543F9E6D5D77A642A5184F8C4D9_inline(_thisAdjusted, ___0_aabb, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MinMaxAABB_Expand_m8574B6375684AC91E413410881D03B71C400DC75 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float ___0_signedDistance, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min;
		float L_1 = ___0_signedDistance;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Subtraction_m111BEEA770E140739DDC6A3410736DFF7EE32045_inline(L_0, L_1, NULL);
		__this->___Min = L_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = __this->___Max;
		float L_4 = ___0_signedDistance;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		L_5 = float3_op_Addition_mABF24BC9A16C272B9F5AB21A601B9D9A831F8C43_inline(L_3, L_4, NULL);
		__this->___Max = L_5;
		return;
	}
}
IL2CPP_EXTERN_C  void MinMaxAABB_Expand_m8574B6375684AC91E413410881D03B71C400DC75_AdjustorThunk (RuntimeObject* __this, float ___0_signedDistance, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	MinMaxAABB_Expand_m8574B6375684AC91E413410881D03B71C400DC75_inline(_thisAdjusted, ___0_signedDistance, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MinMaxAABB_Encapsulate_m7043627B976BE96D6D734288EEA09563352F753B (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_1 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = L_1.___Min;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = math_min_m13CC8D5B7844D954C3125DD72831C693AB8A7FF5_inline(L_0, L_2, NULL);
		__this->___Min = L_3;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = __this->___Max;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_5 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = L_5.___Max;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_7;
		L_7 = math_max_m247D41258606F80861E72309300DF6A3F8B50AE4_inline(L_4, L_6, NULL);
		__this->___Max = L_7;
		return;
	}
}
IL2CPP_EXTERN_C  void MinMaxAABB_Encapsulate_m7043627B976BE96D6D734288EEA09563352F753B_AdjustorThunk (RuntimeObject* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	MinMaxAABB_Encapsulate_m7043627B976BE96D6D734288EEA09563352F753B_inline(_thisAdjusted, ___0_aabb, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MinMaxAABB_Encapsulate_mE01CED4767A6B50D846AC30AFCA49A91BA820CD5 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = math_min_m13CC8D5B7844D954C3125DD72831C693AB8A7FF5_inline(L_0, L_1, NULL);
		__this->___Min = L_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = __this->___Max;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		L_5 = math_max_m247D41258606F80861E72309300DF6A3F8B50AE4_inline(L_3, L_4, NULL);
		__this->___Max = L_5;
		return;
	}
}
IL2CPP_EXTERN_C  void MinMaxAABB_Encapsulate_mE01CED4767A6B50D846AC30AFCA49A91BA820CD5_AdjustorThunk (RuntimeObject* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	MinMaxAABB_Encapsulate_mE01CED4767A6B50D846AC30AFCA49A91BA820CD5_inline(_thisAdjusted, ___0_point, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MinMaxAABB_Equals_m6DC492AB1804679250EC6C97CC6DF32299EA8E11 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_other, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_0 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&__this->___Min);
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_1 = ___0_other;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = L_1.___Min;
		bool L_3;
		L_3 = float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A_inline(L_0, L_2, NULL);
		if (!L_3)
		{
			goto IL_0025;
		}
	}
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_4 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&__this->___Max);
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_5 = ___0_other;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = L_5.___Max;
		bool L_7;
		L_7 = float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A_inline(L_4, L_6, NULL);
		return L_7;
	}

IL_0025:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C  bool MinMaxAABB_Equals_m6DC492AB1804679250EC6C97CC6DF32299EA8E11_AdjustorThunk (RuntimeObject* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_other, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	bool _returnValue;
	_returnValue = MinMaxAABB_Equals_m6DC492AB1804679250EC6C97CC6DF32299EA8E11_inline(_thisAdjusted, ___0_other, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* MinMaxAABB_ToString_mD299FEC6092F6072F7BC91773DA766E0E61DEC3F (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralFEB995DBF7004A48F3EDB181C599FB99B4A380FB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = L_0;
		RuntimeObject* L_2 = Box(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E_il2cpp_TypeInfo_var, &L_1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = __this->___Max;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = L_3;
		RuntimeObject* L_5 = Box(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E_il2cpp_TypeInfo_var, &L_4);
		String_t* L_6;
		L_6 = String_Format_mFB7DA489BD99F4670881FF50EC017BFB0A5C0987(_stringLiteralFEB995DBF7004A48F3EDB181C599FB99B4A380FB, L_2, L_5, NULL);
		return L_6;
	}
}
IL2CPP_EXTERN_C  String_t* MinMaxAABB_ToString_mD299FEC6092F6072F7BC91773DA766E0E61DEC3F_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	String_t* _returnValue;
	_returnValue = MinMaxAABB_ToString_mD299FEC6092F6072F7BC91773DA766E0E61DEC3F_inline(_thisAdjusted, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 Math_Transform_m412573C4965FDBD6C72DB99EA7B13301A5C7F068 (RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD ___0_transform, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___1_aabb, const RuntimeMethod* method) 
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_0;
	memset((&V_0), 0, sizeof(V_0));
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_1;
	memset((&V_1), 0, sizeof(V_1));
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_2;
	memset((&V_2), 0, sizeof(V_2));
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_3;
	memset((&V_3), 0, sizeof(V_3));
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_4;
	memset((&V_4), 0, sizeof(V_4));
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0;
		L_0 = MinMaxAABB_get_HalfExtents_m3656E833DBE99FE9D023F0E4B1497CF35F6BC948((&___1_aabb), NULL);
		V_0 = L_0;
		RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD L_1 = ___0_transform;
		quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 L_2 = L_1.___rot;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = V_0;
		float L_4 = L_3.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		memset((&L_5), 0, sizeof(L_5));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_5), L_4, (0.0f), (0.0f), NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6;
		L_6 = math_rotate_m68CD27B1D0643EA356D0AB41ECB004CE094FDA3F_inline(L_2, L_5, NULL);
		RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD L_7 = ___0_transform;
		quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 L_8 = L_7.___rot;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9 = V_0;
		float L_10 = L_9.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_11;
		memset((&L_11), 0, sizeof(L_11));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_11), (0.0f), L_10, (0.0f), NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_12;
		L_12 = math_rotate_m68CD27B1D0643EA356D0AB41ECB004CE094FDA3F_inline(L_8, L_11, NULL);
		V_1 = L_12;
		RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD L_13 = ___0_transform;
		quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 L_14 = L_13.___rot;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_15 = V_0;
		float L_16 = L_15.___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_17;
		memset((&L_17), 0, sizeof(L_17));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_17), (0.0f), (0.0f), L_16, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_18;
		L_18 = math_rotate_m68CD27B1D0643EA356D0AB41ECB004CE094FDA3F_inline(L_14, L_17, NULL);
		V_2 = L_18;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_19;
		L_19 = math_abs_mC7F2BBD861835C82A0A47A47A44B73E704D7F63B_inline(L_6, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_20 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_21;
		L_21 = math_abs_mC7F2BBD861835C82A0A47A47A44B73E704D7F63B_inline(L_20, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_22;
		L_22 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_19, L_21, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_23 = V_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_24;
		L_24 = math_abs_mC7F2BBD861835C82A0A47A47A44B73E704D7F63B_inline(L_23, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_25;
		L_25 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_22, L_24, NULL);
		V_3 = L_25;
		RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD L_26 = ___0_transform;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_27;
		L_27 = MinMaxAABB_get_Center_mC27A51CF13A95A053DEA07566A1F1900CFD98F4C((&___1_aabb), NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_28;
		L_28 = math_transform_m5F6B69A9C0E6E1AF63D8112D8753394891972E44_inline(L_26, L_27, NULL);
		V_4 = L_28;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_29 = V_4;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_30 = V_3;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_31;
		L_31 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_29, L_30, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_32 = V_4;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_33 = V_3;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_34;
		L_34 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_32, L_33, NULL);
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_35;
		memset((&L_35), 0, sizeof(L_35));
		MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C_inline((&L_35), L_31, L_34, NULL);
		return L_35;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 Math_Transform_m3E33DC460F20ED83A2964AF47CCDD5C6866DA7E8 (float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 ___0_transform, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___1_aabb, const RuntimeMethod* method) 
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 L_0 = ___0_transform;
		float3x3_tB318DB8C7E54B6CA9E14EB9AC7F5964C1189FC79 L_1;
		memset((&L_1), 0, sizeof(L_1));
		float3x3__ctor_m3EA9552B4922CACBAC36054687E8EF9C1ED99951((&L_1), L_0, NULL);
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_2 = ___1_aabb;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_3;
		L_3 = Math_Transform_m762480F0988F685C5039CBFBFA540C85385A53CC_inline(L_1, L_2, NULL);
		V_0 = L_3;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_4 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_0)->___Min);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_5 = L_4;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_5);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_7 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&(&___0_transform)->___c3);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8;
		L_8 = float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline(L_7, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9;
		L_9 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_6, L_8, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_5 = L_9;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_10 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_0)->___Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_11 = L_10;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_12 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_11);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_13 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&(&___0_transform)->___c3);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_14;
		L_14 = float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline(L_13, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_15;
		L_15 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_12, L_14, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_11 = L_15;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_16 = V_0;
		return L_16;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 Math_Transform_m762480F0988F685C5039CBFBFA540C85385A53CC (float3x3_tB318DB8C7E54B6CA9E14EB9AC7F5964C1189FC79 ___0_transform, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___1_aabb, const RuntimeMethod* method) 
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_0;
	memset((&V_0), 0, sizeof(V_0));
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_1;
	memset((&V_1), 0, sizeof(V_1));
	bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 V_2;
	memset((&V_2), 0, sizeof(V_2));
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 V_3;
	memset((&V_3), 0, sizeof(V_3));
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_0 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1;
		L_1 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_0, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_2 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Min);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = float3_get_xxx_mFD7DFB9FF23BB0B3437F12CC35DB3D1E0ADF7B20_inline(L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4;
		L_4 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_1, L_3, NULL);
		V_0 = L_4;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_5 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6;
		L_6 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_5, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_7 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8;
		L_8 = float3_get_xxx_mFD7DFB9FF23BB0B3437F12CC35DB3D1E0ADF7B20_inline(L_7, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9;
		L_9 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_6, L_8, NULL);
		V_1 = L_9;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_11 = V_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_12;
		L_12 = float3_op_LessThan_m540182EDEF57B5A865B1C22972CF5C3E862B9C51_inline(L_10, L_11, NULL);
		V_2 = L_12;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_13 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_14 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_15 = V_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_16;
		L_16 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_13, L_14, L_15, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_17 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_18 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_19 = V_2;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_20;
		L_20 = bool3_op_LogicalNot_m85C703CC4098B3731505A162957F91C0373548BD_inline(L_19, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_21;
		L_21 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_17, L_18, L_20, NULL);
		MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C_inline((&V_3), L_16, L_21, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_22 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_23;
		L_23 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_22, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_24 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Min);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_25;
		L_25 = float3_get_yyy_m6FCA12991237EDC77F7C4B6A7F73710338330CCD_inline(L_24, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_26;
		L_26 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_23, L_25, NULL);
		V_0 = L_26;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_27 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_28;
		L_28 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_27, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_29 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_30;
		L_30 = float3_get_yyy_m6FCA12991237EDC77F7C4B6A7F73710338330CCD_inline(L_29, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_31;
		L_31 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_28, L_30, NULL);
		V_1 = L_31;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_32 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_33 = V_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_34;
		L_34 = float3_op_LessThan_m540182EDEF57B5A865B1C22972CF5C3E862B9C51_inline(L_32, L_33, NULL);
		V_2 = L_34;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_35 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_3)->___Min);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_36 = L_35;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_37 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_36);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_38 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_39 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_40 = V_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_41;
		L_41 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_38, L_39, L_40, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_42;
		L_42 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_37, L_41, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_36 = L_42;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_43 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_3)->___Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_44 = L_43;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_45 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_44);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_46 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_47 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_48 = V_2;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_49;
		L_49 = bool3_op_LogicalNot_m85C703CC4098B3731505A162957F91C0373548BD_inline(L_48, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_50;
		L_50 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_46, L_47, L_49, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_51;
		L_51 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_45, L_50, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_44 = L_51;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_52 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c2);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_53;
		L_53 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_52, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_54 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Min);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_55;
		L_55 = float3_get_zzz_m1C7C995F170030A7EF534E2C99E6AFE6928AE9D4_inline(L_54, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_56;
		L_56 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_53, L_55, NULL);
		V_0 = L_56;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_57 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c2);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_58;
		L_58 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_57, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_59 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_60;
		L_60 = float3_get_zzz_m1C7C995F170030A7EF534E2C99E6AFE6928AE9D4_inline(L_59, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_61;
		L_61 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_58, L_60, NULL);
		V_1 = L_61;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_62 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_63 = V_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_64;
		L_64 = float3_op_LessThan_m540182EDEF57B5A865B1C22972CF5C3E862B9C51_inline(L_62, L_63, NULL);
		V_2 = L_64;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_65 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_3)->___Min);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_66 = L_65;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_67 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_66);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_68 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_69 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_70 = V_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_71;
		L_71 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_68, L_69, L_70, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_72;
		L_72 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_67, L_71, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_66 = L_72;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_73 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_3)->___Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_74 = L_73;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_75 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_74);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_76 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_77 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_78 = V_2;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_79;
		L_79 = bool3_op_LogicalNot_m85C703CC4098B3731505A162957F91C0373548BD_inline(L_78, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_80;
		L_80 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_76, L_77, L_79, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_81;
		L_81 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_75, L_80, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_74 = L_81;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_82 = V_3;
		return L_82;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane__ctor_mE41939B3E3E2AE7802AA9571AB429BAB47C56A65 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float ___0_coefficientA, float ___1_coefficientB, float ___2_coefficientC, float ___3_coefficientD, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_coefficientA;
		float L_1 = ___1_coefficientB;
		float L_2 = ___2_coefficientC;
		float L_3 = ___3_coefficientD;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_4;
		memset((&L_4), 0, sizeof(L_4));
		float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D_inline((&L_4), L_0, L_1, L_2, L_3, NULL);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_5;
		L_5 = Plane_Normalize_mFF53F95372AE2A79C71B546A70B0F152EC308544_inline(L_4, NULL);
		__this->___NormalAndDistance = L_5;
		return;
	}
}
IL2CPP_EXTERN_C  void Plane__ctor_mE41939B3E3E2AE7802AA9571AB429BAB47C56A65_AdjustorThunk (RuntimeObject* __this, float ___0_coefficientA, float ___1_coefficientB, float ___2_coefficientC, float ___3_coefficientD, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	Plane__ctor_mE41939B3E3E2AE7802AA9571AB429BAB47C56A65_inline(_thisAdjusted, ___0_coefficientA, ___1_coefficientB, ___2_coefficientC, ___3_coefficientD, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane__ctor_mAEEAADCE34CB243E12A9FE0240D4E508913C1153 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_normal, float ___1_distance, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_normal;
		float L_1 = ___1_distance;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_2;
		memset((&L_2), 0, sizeof(L_2));
		float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_inline((&L_2), L_0, L_1, NULL);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_3;
		L_3 = Plane_Normalize_mFF53F95372AE2A79C71B546A70B0F152EC308544_inline(L_2, NULL);
		__this->___NormalAndDistance = L_3;
		return;
	}
}
IL2CPP_EXTERN_C  void Plane__ctor_mAEEAADCE34CB243E12A9FE0240D4E508913C1153_AdjustorThunk (RuntimeObject* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_normal, float ___1_distance, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	Plane__ctor_mAEEAADCE34CB243E12A9FE0240D4E508913C1153_inline(_thisAdjusted, ___0_normal, ___1_distance, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane__ctor_m645C0F13FB29D9E443284F1BC42C02CE3B5C27D4 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_normal, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_pointInPlane, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_normal;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_normal;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_pointInPlane;
		float L_3;
		L_3 = math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD_inline(L_1, L_2, NULL);
		Plane__ctor_mAEEAADCE34CB243E12A9FE0240D4E508913C1153_inline(__this, L_0, ((-L_3)), NULL);
		return;
	}
}
IL2CPP_EXTERN_C  void Plane__ctor_m645C0F13FB29D9E443284F1BC42C02CE3B5C27D4_AdjustorThunk (RuntimeObject* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_normal, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_pointInPlane, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	Plane__ctor_m645C0F13FB29D9E443284F1BC42C02CE3B5C27D4_inline(_thisAdjusted, ___0_normal, ___1_pointInPlane, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane__ctor_m77B64CCE37D396DD70CD0A841F4E6E4F72D1B20A (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_vector1InPlane, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_vector2InPlane, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___2_pointInPlane, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_vector1InPlane;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___1_vector2InPlane;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180_inline(L_0, L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = ___2_pointInPlane;
		Plane__ctor_m645C0F13FB29D9E443284F1BC42C02CE3B5C27D4_inline(__this, L_2, L_3, NULL);
		return;
	}
}
IL2CPP_EXTERN_C  void Plane__ctor_m77B64CCE37D396DD70CD0A841F4E6E4F72D1B20A_AdjustorThunk (RuntimeObject* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_vector1InPlane, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_vector2InPlane, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___2_pointInPlane, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	Plane__ctor_m77B64CCE37D396DD70CD0A841F4E6E4F72D1B20A_inline(_thisAdjusted, ___0_vector1InPlane, ___1_vector2InPlane, ___2_pointInPlane, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 Plane_CreateFromUnitNormalAndDistance_mBE602081B26029F3D51C338D9D94BBFBC27539A5 (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_unitNormal, float ___1_distance, const RuntimeMethod* method) 
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82));
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_unitNormal;
		float L_1 = ___1_distance;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_2;
		memset((&L_2), 0, sizeof(L_2));
		float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_inline((&L_2), L_0, L_1, NULL);
		(&V_0)->___NormalAndDistance = L_2;
		Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 L_3 = V_0;
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 Plane_CreateFromUnitNormalAndPointInPlane_m41976CA41AE866D620744C534DF0CEE994039D95 (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_unitNormal, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_pointInPlane, const RuntimeMethod* method) 
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82));
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_unitNormal;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_unitNormal;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_pointInPlane;
		float L_3;
		L_3 = math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD_inline(L_1, L_2, NULL);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_4;
		memset((&L_4), 0, sizeof(L_4));
		float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_inline((&L_4), L_0, ((-L_3)), NULL);
		(&V_0)->___NormalAndDistance = L_4;
		Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 L_5 = V_0;
		return L_5;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E Plane_get_Normal_mAA5C1BEAEFB0848A4CD29E254CC9EF010DD6FE4B (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, const RuntimeMethod* method) 
{
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_0 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&__this->___NormalAndDistance);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1;
		L_1 = float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C  float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E Plane_get_Normal_mAA5C1BEAEFB0848A4CD29E254CC9EF010DD6FE4B_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E _returnValue;
	_returnValue = Plane_get_Normal_mAA5C1BEAEFB0848A4CD29E254CC9EF010DD6FE4B(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane_set_Normal_m88265A5E767B48CF718AC03AB03BBF15DC82A837 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_value, const RuntimeMethod* method) 
{
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_0 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&__this->___NormalAndDistance);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_value;
		float4_set_xyz_m331D16059D51A5C6CA8AE8FD1E13A68C0570A9C7_inline(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C  void Plane_set_Normal_m88265A5E767B48CF718AC03AB03BBF15DC82A837_AdjustorThunk (RuntimeObject* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_value, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	Plane_set_Normal_m88265A5E767B48CF718AC03AB03BBF15DC82A837(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Plane_get_Distance_m66B8C8674B20E3B19B0CFD363D33AA8A67CA75FE (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, const RuntimeMethod* method) 
{
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_0 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&__this->___NormalAndDistance);
		float L_1 = L_0->___w;
		return L_1;
	}
}
IL2CPP_EXTERN_C  float Plane_get_Distance_m66B8C8674B20E3B19B0CFD363D33AA8A67CA75FE_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	float _returnValue;
	_returnValue = Plane_get_Distance_m66B8C8674B20E3B19B0CFD363D33AA8A67CA75FE(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane_set_Distance_m6DDC9F56E9FEE8D4DC5A61AB2A8322695F855181 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float ___0_value, const RuntimeMethod* method) 
{
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_0 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&__this->___NormalAndDistance);
		float L_1 = ___0_value;
		L_0->___w = L_1;
		return;
	}
}
IL2CPP_EXTERN_C  void Plane_set_Distance_m6DDC9F56E9FEE8D4DC5A61AB2A8322695F855181_AdjustorThunk (RuntimeObject* __this, float ___0_value, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	Plane_set_Distance_m6DDC9F56E9FEE8D4DC5A61AB2A8322695F855181(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 Plane_Normalize_m620F20BC9EEECB986839A98C2166EC4B8D05E3E6 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 ___0_plane, const RuntimeMethod* method) 
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82));
		Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 L_0 = ___0_plane;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_1 = L_0.___NormalAndDistance;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_2;
		L_2 = Plane_Normalize_mFF53F95372AE2A79C71B546A70B0F152EC308544_inline(L_1, NULL);
		(&V_0)->___NormalAndDistance = L_2;
		Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 L_3 = V_0;
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E Plane_Normalize_mFF53F95372AE2A79C71B546A70B0F152EC308544 (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_planeCoefficients, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 V_1;
	memset((&V_1), 0, sizeof(V_1));
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0;
		L_0 = float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline((&___0_planeCoefficients), NULL);
		float L_1;
		L_1 = math_lengthsq_mC699F3F214F05B26BEBAF1B46E3AA3C00407A532_inline(L_0, NULL);
		float L_2;
		L_2 = math_rsqrt_mC67B3430EAADA7C5347E87B23859C569BC010E72_inline(L_1, NULL);
		V_0 = L_2;
		il2cpp_codegen_initobj((&V_1), sizeof(Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82));
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_3 = ___0_planeCoefficients;
		float L_4 = V_0;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_5;
		L_5 = float4_op_Multiply_m712573F441DA8AF0843DE2167927FB76E642B1EB_inline(L_3, L_4, NULL);
		(&V_1)->___NormalAndDistance = L_5;
		Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 L_6 = V_1;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_7;
		L_7 = Plane_op_Implicit_mD02477CC24787906751D3F5E401D2E11BF99AC98_inline(L_6, NULL);
		return L_7;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Plane_SignedDistanceToPoint_mE52778BC70A3A0FF9DDB0FE52D71C587D837F993 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) 
{
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_0 = __this->___NormalAndDistance;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_point;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_2;
		memset((&L_2), 0, sizeof(L_2));
		float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_inline((&L_2), L_1, (1.0f), NULL);
		float L_3;
		L_3 = math_dot_m20F2285F7227DC308D9CF2DCB8EAAD3E774501D4_inline(L_0, L_2, NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C  float Plane_SignedDistanceToPoint_mE52778BC70A3A0FF9DDB0FE52D71C587D837F993_AdjustorThunk (RuntimeObject* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	float _returnValue;
	_returnValue = Plane_SignedDistanceToPoint_mE52778BC70A3A0FF9DDB0FE52D71C587D837F993_inline(_thisAdjusted, ___0_point, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E Plane_Projection_mFF8C23401C366A3B4EB017B4DAAAF4E8A9132CFE (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1;
		L_1 = Plane_get_Normal_mAA5C1BEAEFB0848A4CD29E254CC9EF010DD6FE4B(__this, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___0_point;
		float L_3;
		L_3 = Plane_SignedDistanceToPoint_mE52778BC70A3A0FF9DDB0FE52D71C587D837F993_inline(__this, L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4;
		L_4 = float3_op_Multiply_m6E5DC552C8B0F9A180298BD9197FF47B14E0EA81_inline(L_1, L_3, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		L_5 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_0, L_4, NULL);
		return L_5;
	}
}
IL2CPP_EXTERN_C  float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E Plane_Projection_mFF8C23401C366A3B4EB017B4DAAAF4E8A9132CFE_AdjustorThunk (RuntimeObject* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E _returnValue;
	_returnValue = Plane_Projection_mFF8C23401C366A3B4EB017B4DAAAF4E8A9132CFE_inline(_thisAdjusted, ___0_point, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 Plane_get_Flipped_m6D004985368EE6234BA9A5D2800557FFB3A351FA (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, const RuntimeMethod* method) 
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82));
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_0 = __this->___NormalAndDistance;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_1;
		L_1 = float4_op_UnaryNegation_m5A491FC1978650D62EBEDC51992CF4B2113C8C5B_inline(L_0, NULL);
		(&V_0)->___NormalAndDistance = L_1;
		Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 L_2 = V_0;
		return L_2;
	}
}
IL2CPP_EXTERN_C  Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 Plane_get_Flipped_m6D004985368EE6234BA9A5D2800557FFB3A351FA_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 _returnValue;
	_returnValue = Plane_get_Flipped_m6D004985368EE6234BA9A5D2800557FFB3A351FA(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E Plane_op_Implicit_mD02477CC24787906751D3F5E401D2E11BF99AC98 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 ___0_plane, const RuntimeMethod* method) 
{
	{
		Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 L_0 = ___0_plane;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_1 = L_0.___NormalAndDistance;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane_CheckPlaneIsNormalized_m6408EC4BE1D7A0ADB6832288E286D9C70404B888 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_1;
	memset((&V_1), 0, sizeof(V_1));
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0;
		L_0 = Plane_get_Normal_mAA5C1BEAEFB0848A4CD29E254CC9EF010DD6FE4B(__this, NULL);
		V_1 = L_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1;
		L_1 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline((&V_1), NULL);
		float L_2;
		L_2 = math_lengthsq_mC699F3F214F05B26BEBAF1B46E3AA3C00407A532_inline(L_1, NULL);
		V_0 = L_2;
		float L_3 = V_0;
		if ((((float)L_3) < ((float)(0.998001039f))))
		{
			goto IL_0024;
		}
	}
	{
		float L_4 = V_0;
		if ((!(((float)L_4) > ((float)(1.00200105f)))))
		{
			goto IL_002f;
		}
	}

IL_0024:
	{
		ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* L_5 = (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263_il2cpp_TypeInfo_var)));
		ArgumentException__ctor_m026938A67AF9D36BB7ED27F80425D7194B514465(L_5, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralFB182D98F776AC1C061FA5C163FE7F6E7C08B5BD)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_5, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Plane_CheckPlaneIsNormalized_m6408EC4BE1D7A0ADB6832288E286D9C70404B888_RuntimeMethod_var)));
	}

IL_002f:
	{
		return;
	}
}
IL2CPP_EXTERN_C  void Plane_CheckPlaneIsNormalized_m6408EC4BE1D7A0ADB6832288E286D9C70404B888_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	Plane_CheckPlaneIsNormalized_m6408EC4BE1D7A0ADB6832288E286D9C70404B888(_thisAdjusted, method);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_mAD4482928730E83CAB8325FD5785BF567E737281_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_c0, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_c1, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___2_c2, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___3_c3, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_c0;
		__this->___c0 = L_0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = ___1_c1;
		__this->___c1 = L_1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___2_c2;
		__this->___c2 = L_2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = ___3_c3;
		__this->___c3 = L_3;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* __this, uint32_t ___0_x, uint32_t ___1_y, uint32_t ___2_z, uint32_t ___3_w, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_x;
		__this->___x = L_0;
		uint32_t L_1 = ___1_y;
		__this->___y = L_1;
		uint32_t L_2 = ___2_z;
		__this->___z = L_2;
		uint32_t L_3 = ___3_w;
		__this->___w = L_3;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_mDF3E33FF44DA38AB4B132456C1BD34AC1546027F_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, uint32_t ___0_m00, uint32_t ___1_m01, uint32_t ___2_m02, uint32_t ___3_m03, uint32_t ___4_m10, uint32_t ___5_m11, uint32_t ___6_m12, uint32_t ___7_m13, uint32_t ___8_m20, uint32_t ___9_m21, uint32_t ___10_m22, uint32_t ___11_m23, uint32_t ___12_m30, uint32_t ___13_m31, uint32_t ___14_m32, uint32_t ___15_m33, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_m00;
		uint32_t L_1 = ___4_m10;
		uint32_t L_2 = ___8_m20;
		uint32_t L_3 = ___12_m30;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4;
		memset((&L_4), 0, sizeof(L_4));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_4), L_0, L_1, L_2, L_3, NULL);
		__this->___c0 = L_4;
		uint32_t L_5 = ___1_m01;
		uint32_t L_6 = ___5_m11;
		uint32_t L_7 = ___9_m21;
		uint32_t L_8 = ___13_m31;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9;
		memset((&L_9), 0, sizeof(L_9));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_9), L_5, L_6, L_7, L_8, NULL);
		__this->___c1 = L_9;
		uint32_t L_10 = ___2_m02;
		uint32_t L_11 = ___6_m12;
		uint32_t L_12 = ___10_m22;
		uint32_t L_13 = ___14_m32;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14;
		memset((&L_14), 0, sizeof(L_14));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_14), L_10, L_11, L_12, L_13, NULL);
		__this->___c2 = L_14;
		uint32_t L_15 = ___3_m03;
		uint32_t L_16 = ___7_m13;
		uint32_t L_17 = ___11_m23;
		uint32_t L_18 = ___15_m33;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_19;
		memset((&L_19), 0, sizeof(L_19));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_19), L_15, L_16, L_17, L_18, NULL);
		__this->___c3 = L_19;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Implicit_m194DE3172FAB8E0625B0F5826454FE3C91DA68DF_inline (uint32_t ___0_v, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_1), L_0, NULL);
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_mB044E6CC91777237724520F26351840B0932A1E8_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, uint32_t ___0_v, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1;
		L_1 = uint4_op_Implicit_m194DE3172FAB8E0625B0F5826454FE3C91DA68DF_inline(L_0, NULL);
		__this->___c0 = L_1;
		uint32_t L_2 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Implicit_m194DE3172FAB8E0625B0F5826454FE3C91DA68DF_inline(L_2, NULL);
		__this->___c1 = L_3;
		uint32_t L_4 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		L_5 = uint4_op_Implicit_m194DE3172FAB8E0625B0F5826454FE3C91DA68DF_inline(L_4, NULL);
		__this->___c2 = L_5;
		uint32_t L_6 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_Implicit_m194DE3172FAB8E0625B0F5826454FE3C91DA68DF_inline(L_6, NULL);
		__this->___c3 = L_7;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* __this, uint32_t ___0_v, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_v;
		__this->___x = L_0;
		uint32_t L_1 = ___0_v;
		__this->___y = L_1;
		uint32_t L_2 = ___0_v;
		__this->___z = L_2;
		uint32_t L_3 = ___0_v;
		__this->___w = L_3;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 math_select_m59B92233FC64029995EE77D0D626FE89FF714D19_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_falseValue, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_trueValue, bool ___2_test, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___2_test;
		if (L_0)
		{
			goto IL_0005;
		}
	}
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = ___0_falseValue;
		return L_1;
	}

IL_0005:
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___1_trueValue;
		return L_2;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_m00363B3E264E5A1B4E4013E66F1EE47858161688_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, bool ___0_v, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0;
		memset((&L_0), 0, sizeof(L_0));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_0), 0, NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_1), 1, NULL);
		bool L_2 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = math_select_m59B92233FC64029995EE77D0D626FE89FF714D19_inline(L_0, L_1, L_2, NULL);
		__this->___c0 = L_3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4;
		memset((&L_4), 0, sizeof(L_4));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_4), 0, NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		memset((&L_5), 0, sizeof(L_5));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_5), 1, NULL);
		bool L_6 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = math_select_m59B92233FC64029995EE77D0D626FE89FF714D19_inline(L_4, L_5, L_6, NULL);
		__this->___c1 = L_7;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8;
		memset((&L_8), 0, sizeof(L_8));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_8), 0, NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9;
		memset((&L_9), 0, sizeof(L_9));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_9), 1, NULL);
		bool L_10 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = math_select_m59B92233FC64029995EE77D0D626FE89FF714D19_inline(L_8, L_9, L_10, NULL);
		__this->___c2 = L_11;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_12), 0, NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13;
		memset((&L_13), 0, sizeof(L_13));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_13), 1, NULL);
		bool L_14 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		L_15 = math_select_m59B92233FC64029995EE77D0D626FE89FF714D19_inline(L_12, L_13, L_14, NULL);
		__this->___c3 = L_15;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 math_select_m94A91F3756E494BE8FDC90304F980A0BB366550E_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_falseValue, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_trueValue, bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 ___2_test, const RuntimeMethod* method) 
{
	uint32_t G_B3_0 = 0;
	uint32_t G_B5_0 = 0;
	uint32_t G_B4_0 = 0;
	uint32_t G_B6_0 = 0;
	uint32_t G_B6_1 = 0;
	uint32_t G_B8_0 = 0;
	uint32_t G_B8_1 = 0;
	uint32_t G_B7_0 = 0;
	uint32_t G_B7_1 = 0;
	uint32_t G_B9_0 = 0;
	uint32_t G_B9_1 = 0;
	uint32_t G_B9_2 = 0;
	uint32_t G_B11_0 = 0;
	uint32_t G_B11_1 = 0;
	uint32_t G_B11_2 = 0;
	uint32_t G_B10_0 = 0;
	uint32_t G_B10_1 = 0;
	uint32_t G_B10_2 = 0;
	uint32_t G_B12_0 = 0;
	uint32_t G_B12_1 = 0;
	uint32_t G_B12_2 = 0;
	uint32_t G_B12_3 = 0;
	{
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_0 = ___2_test;
		bool L_1 = L_0.___x;
		if (L_1)
		{
			goto IL_0010;
		}
	}
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___0_falseValue;
		uint32_t L_3 = L_2.___x;
		G_B3_0 = L_3;
		goto IL_0016;
	}

IL_0010:
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___1_trueValue;
		uint32_t L_5 = L_4.___x;
		G_B3_0 = L_5;
	}

IL_0016:
	{
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_6 = ___2_test;
		bool L_7 = L_6.___y;
		if (L_7)
		{
			G_B5_0 = G_B3_0;
			goto IL_0026;
		}
		G_B4_0 = G_B3_0;
	}
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = ___0_falseValue;
		uint32_t L_9 = L_8.___y;
		G_B6_0 = L_9;
		G_B6_1 = G_B4_0;
		goto IL_002c;
	}

IL_0026:
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_trueValue;
		uint32_t L_11 = L_10.___y;
		G_B6_0 = L_11;
		G_B6_1 = G_B5_0;
	}

IL_002c:
	{
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_12 = ___2_test;
		bool L_13 = L_12.___z;
		if (L_13)
		{
			G_B8_0 = G_B6_0;
			G_B8_1 = G_B6_1;
			goto IL_003c;
		}
		G_B7_0 = G_B6_0;
		G_B7_1 = G_B6_1;
	}
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = ___0_falseValue;
		uint32_t L_15 = L_14.___z;
		G_B9_0 = L_15;
		G_B9_1 = G_B7_0;
		G_B9_2 = G_B7_1;
		goto IL_0042;
	}

IL_003c:
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16 = ___1_trueValue;
		uint32_t L_17 = L_16.___z;
		G_B9_0 = L_17;
		G_B9_1 = G_B8_0;
		G_B9_2 = G_B8_1;
	}

IL_0042:
	{
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_18 = ___2_test;
		bool L_19 = L_18.___w;
		if (L_19)
		{
			G_B11_0 = G_B9_0;
			G_B11_1 = G_B9_1;
			G_B11_2 = G_B9_2;
			goto IL_0052;
		}
		G_B10_0 = G_B9_0;
		G_B10_1 = G_B9_1;
		G_B10_2 = G_B9_2;
	}
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_20 = ___0_falseValue;
		uint32_t L_21 = L_20.___w;
		G_B12_0 = L_21;
		G_B12_1 = G_B10_0;
		G_B12_2 = G_B10_1;
		G_B12_3 = G_B10_2;
		goto IL_0058;
	}

IL_0052:
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_22 = ___1_trueValue;
		uint32_t L_23 = L_22.___w;
		G_B12_0 = L_23;
		G_B12_1 = G_B11_0;
		G_B12_2 = G_B11_1;
		G_B12_3 = G_B11_2;
	}

IL_0058:
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_24;
		memset((&L_24), 0, sizeof(L_24));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_24), G_B12_3, G_B12_2, G_B12_1, G_B12_0, NULL);
		return L_24;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_mCE45F10896048B5121C7A9C4B4DD4FFFD893BCD8_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 ___0_v, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0;
		memset((&L_0), 0, sizeof(L_0));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_0), 0, NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_1), 1, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_2 = ___0_v;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_3 = L_2.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4;
		L_4 = math_select_m94A91F3756E494BE8FDC90304F980A0BB366550E_inline(L_0, L_1, L_3, NULL);
		__this->___c0 = L_4;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		memset((&L_5), 0, sizeof(L_5));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_5), 0, NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6;
		memset((&L_6), 0, sizeof(L_6));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_6), 1, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_7 = ___0_v;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_8 = L_7.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9;
		L_9 = math_select_m94A91F3756E494BE8FDC90304F980A0BB366550E_inline(L_5, L_6, L_8, NULL);
		__this->___c1 = L_9;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10;
		memset((&L_10), 0, sizeof(L_10));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_10), 0, NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		memset((&L_11), 0, sizeof(L_11));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_11), 1, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_12 = ___0_v;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_13 = L_12.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14;
		L_14 = math_select_m94A91F3756E494BE8FDC90304F980A0BB366550E_inline(L_10, L_11, L_13, NULL);
		__this->___c2 = L_14;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15;
		memset((&L_15), 0, sizeof(L_15));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_15), 0, NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4__ctor_m0927237EB810FB562DE9A12B3A3942BCE672656F_inline((&L_16), 1, NULL);
		bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936 L_17 = ___0_v;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_18 = L_17.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_19;
		L_19 = math_select_m94A91F3756E494BE8FDC90304F980A0BB366550E_inline(L_15, L_16, L_18, NULL);
		__this->___c3 = L_19;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Explicit_m52EA594D5CFD520009B14F44F586D6A36EE1F0F2_inline (int32_t ___0_v, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4__ctor_m00DD9230DF75F4825012D055BBF5FCC3A08D78B3_inline((&L_1), L_0, NULL);
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_m6962CB8B6F4B51CE18577B571A8DE0D3367C9213_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, int32_t ___0_v, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1;
		L_1 = uint4_op_Explicit_m52EA594D5CFD520009B14F44F586D6A36EE1F0F2_inline(L_0, NULL);
		__this->___c0 = L_1;
		int32_t L_2 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Explicit_m52EA594D5CFD520009B14F44F586D6A36EE1F0F2_inline(L_2, NULL);
		__this->___c1 = L_3;
		int32_t L_4 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		L_5 = uint4_op_Explicit_m52EA594D5CFD520009B14F44F586D6A36EE1F0F2_inline(L_4, NULL);
		__this->___c2 = L_5;
		int32_t L_6 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_Explicit_m52EA594D5CFD520009B14F44F586D6A36EE1F0F2_inline(L_6, NULL);
		__this->___c3 = L_7;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Explicit_m4BA2A6F8AC721A602D73382462C962382691DD70_inline (int4_tBA77D4945786DE82C3A487B33955EA1004996052 ___0_v, const RuntimeMethod* method) 
{
	{
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_0 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4__ctor_mE3E045BA2F8A6BADAF84CD437BC4123BAD640226_inline((&L_1), L_0, NULL);
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_mC40D16C3F1B689A4EDAA2EC86DA52D0E371C752B_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, int4x4_tEF359B46039347312A6AC932AD04CA9CE675AB30 ___0_v, const RuntimeMethod* method) 
{
	{
		int4x4_tEF359B46039347312A6AC932AD04CA9CE675AB30 L_0 = ___0_v;
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_1 = L_0.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2;
		L_2 = uint4_op_Explicit_m4BA2A6F8AC721A602D73382462C962382691DD70_inline(L_1, NULL);
		__this->___c0 = L_2;
		int4x4_tEF359B46039347312A6AC932AD04CA9CE675AB30 L_3 = ___0_v;
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_4 = L_3.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		L_5 = uint4_op_Explicit_m4BA2A6F8AC721A602D73382462C962382691DD70_inline(L_4, NULL);
		__this->___c1 = L_5;
		int4x4_tEF359B46039347312A6AC932AD04CA9CE675AB30 L_6 = ___0_v;
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_7 = L_6.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8;
		L_8 = uint4_op_Explicit_m4BA2A6F8AC721A602D73382462C962382691DD70_inline(L_7, NULL);
		__this->___c2 = L_8;
		int4x4_tEF359B46039347312A6AC932AD04CA9CE675AB30 L_9 = ___0_v;
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_10 = L_9.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_Explicit_m4BA2A6F8AC721A602D73382462C962382691DD70_inline(L_10, NULL);
		__this->___c3 = L_11;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Explicit_mF62DF180DDD889D04C4B894C1CC11C4FFB757C56_inline (float ___0_v, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4__ctor_mC40F8DC4FDACF816F7989C9D2693B4FDE0116AFB_inline((&L_1), L_0, NULL);
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_m94AD3FDC6F57005991FE28510E0516643C9250CE_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, float ___0_v, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1;
		L_1 = uint4_op_Explicit_mF62DF180DDD889D04C4B894C1CC11C4FFB757C56_inline(L_0, NULL);
		__this->___c0 = L_1;
		float L_2 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Explicit_mF62DF180DDD889D04C4B894C1CC11C4FFB757C56_inline(L_2, NULL);
		__this->___c1 = L_3;
		float L_4 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		L_5 = uint4_op_Explicit_mF62DF180DDD889D04C4B894C1CC11C4FFB757C56_inline(L_4, NULL);
		__this->___c2 = L_5;
		float L_6 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_Explicit_mF62DF180DDD889D04C4B894C1CC11C4FFB757C56_inline(L_6, NULL);
		__this->___c3 = L_7;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Explicit_mCEEF0983FAE417D4A4DAC4BAA4FD415E6770C20D_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_v, const RuntimeMethod* method) 
{
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_0 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4__ctor_m0FDC55AFC114988A17F4E6248D13C331E0F4C544_inline((&L_1), L_0, NULL);
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_mFA79D1F3F53003B4BC1960DACFAE87DA465EDD2D_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 ___0_v, const RuntimeMethod* method) 
{
	{
		float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 L_0 = ___0_v;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_1 = L_0.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2;
		L_2 = uint4_op_Explicit_mCEEF0983FAE417D4A4DAC4BAA4FD415E6770C20D_inline(L_1, NULL);
		__this->___c0 = L_2;
		float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 L_3 = ___0_v;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_4 = L_3.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		L_5 = uint4_op_Explicit_mCEEF0983FAE417D4A4DAC4BAA4FD415E6770C20D_inline(L_4, NULL);
		__this->___c1 = L_5;
		float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 L_6 = ___0_v;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_7 = L_6.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8;
		L_8 = uint4_op_Explicit_mCEEF0983FAE417D4A4DAC4BAA4FD415E6770C20D_inline(L_7, NULL);
		__this->___c2 = L_8;
		float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 L_9 = ___0_v;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_10 = L_9.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_Explicit_mCEEF0983FAE417D4A4DAC4BAA4FD415E6770C20D_inline(L_10, NULL);
		__this->___c3 = L_11;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Explicit_m642DF9B9E499124B9322085581DCE979B7CCA961_inline (double ___0_v, const RuntimeMethod* method) 
{
	{
		double L_0 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4__ctor_m2B3C71B8C523717F9E57A2EC936A9DB6C147B73D_inline((&L_1), L_0, NULL);
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_m8B2B846C47859C1A0F6BBDD6804B0F20AA4CA415_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, double ___0_v, const RuntimeMethod* method) 
{
	{
		double L_0 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1;
		L_1 = uint4_op_Explicit_m642DF9B9E499124B9322085581DCE979B7CCA961_inline(L_0, NULL);
		__this->___c0 = L_1;
		double L_2 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Explicit_m642DF9B9E499124B9322085581DCE979B7CCA961_inline(L_2, NULL);
		__this->___c1 = L_3;
		double L_4 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		L_5 = uint4_op_Explicit_m642DF9B9E499124B9322085581DCE979B7CCA961_inline(L_4, NULL);
		__this->___c2 = L_5;
		double L_6 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_Explicit_m642DF9B9E499124B9322085581DCE979B7CCA961_inline(L_6, NULL);
		__this->___c3 = L_7;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Explicit_m461E06F58E3962E8ECF5E15E0119B2FC2D391FF5_inline (double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 ___0_v, const RuntimeMethod* method) 
{
	{
		double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 L_0 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1;
		memset((&L_1), 0, sizeof(L_1));
		uint4__ctor_mD155EFD6C699622167DC52223E62E3C023D86CF8_inline((&L_1), L_0, NULL);
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4x4__ctor_m94C7CE06BDF397CA5348A1EA85358374782BA6DA_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, double4x4_tB452F9489714C6B8D74D46CA2CF1F0CA8F185D3C ___0_v, const RuntimeMethod* method) 
{
	{
		double4x4_tB452F9489714C6B8D74D46CA2CF1F0CA8F185D3C L_0 = ___0_v;
		double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 L_1 = L_0.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2;
		L_2 = uint4_op_Explicit_m461E06F58E3962E8ECF5E15E0119B2FC2D391FF5_inline(L_1, NULL);
		__this->___c0 = L_2;
		double4x4_tB452F9489714C6B8D74D46CA2CF1F0CA8F185D3C L_3 = ___0_v;
		double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 L_4 = L_3.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5;
		L_5 = uint4_op_Explicit_m461E06F58E3962E8ECF5E15E0119B2FC2D391FF5_inline(L_4, NULL);
		__this->___c1 = L_5;
		double4x4_tB452F9489714C6B8D74D46CA2CF1F0CA8F185D3C L_6 = ___0_v;
		double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 L_7 = L_6.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8;
		L_8 = uint4_op_Explicit_m461E06F58E3962E8ECF5E15E0119B2FC2D391FF5_inline(L_7, NULL);
		__this->___c2 = L_8;
		double4x4_tB452F9489714C6B8D74D46CA2CF1F0CA8F185D3C L_9 = ___0_v;
		double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 L_10 = L_9.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = uint4_op_Explicit_m461E06F58E3962E8ECF5E15E0119B2FC2D391FF5_inline(L_10, NULL);
		__this->___c3 = L_11;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Multiply_mDD93D0730642A1089848321B9B0E5E923EE575ED_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___1_rhs;
		uint32_t L_3 = L_2.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___0_lhs;
		uint32_t L_5 = L_4.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___1_rhs;
		uint32_t L_7 = L_6.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = ___0_lhs;
		uint32_t L_9 = L_8.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12 = ___0_lhs;
		uint32_t L_13 = L_12.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = ___1_rhs;
		uint32_t L_15 = L_14.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_16), ((int32_t)il2cpp_codegen_multiply((int32_t)L_1, (int32_t)L_3)), ((int32_t)il2cpp_codegen_multiply((int32_t)L_5, (int32_t)L_7)), ((int32_t)il2cpp_codegen_multiply((int32_t)L_9, (int32_t)L_11)), ((int32_t)il2cpp_codegen_multiply((int32_t)L_13, (int32_t)L_15)), NULL);
		return L_16;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Multiply_m40354E397B1D99D73B5333378C8698C087A1A2D6_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = ___0_lhs;
		uint32_t L_4 = L_3.___y;
		uint32_t L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_lhs;
		uint32_t L_7 = L_6.___z;
		uint32_t L_8 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = ___0_lhs;
		uint32_t L_10 = L_9.___w;
		uint32_t L_11 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_12), ((int32_t)il2cpp_codegen_multiply((int32_t)L_1, (int32_t)L_2)), ((int32_t)il2cpp_codegen_multiply((int32_t)L_4, (int32_t)L_5)), ((int32_t)il2cpp_codegen_multiply((int32_t)L_7, (int32_t)L_8)), ((int32_t)il2cpp_codegen_multiply((int32_t)L_10, (int32_t)L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Multiply_mF7AFA2D5F25EC1F6F7AEC21564D18A46C7B37B48_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = ___1_rhs;
		uint32_t L_2 = L_1.___x;
		uint32_t L_3 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___1_rhs;
		uint32_t L_5 = L_4.___y;
		uint32_t L_6 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7 = ___1_rhs;
		uint32_t L_8 = L_7.___z;
		uint32_t L_9 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_12), ((int32_t)il2cpp_codegen_multiply((int32_t)L_0, (int32_t)L_2)), ((int32_t)il2cpp_codegen_multiply((int32_t)L_3, (int32_t)L_5)), ((int32_t)il2cpp_codegen_multiply((int32_t)L_6, (int32_t)L_8)), ((int32_t)il2cpp_codegen_multiply((int32_t)L_9, (int32_t)L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Addition_m391200EB43B6C4CDD219079A0D3CDFFA35B0B652_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___1_rhs;
		uint32_t L_3 = L_2.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___0_lhs;
		uint32_t L_5 = L_4.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___1_rhs;
		uint32_t L_7 = L_6.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = ___0_lhs;
		uint32_t L_9 = L_8.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12 = ___0_lhs;
		uint32_t L_13 = L_12.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = ___1_rhs;
		uint32_t L_15 = L_14.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_16), ((int32_t)il2cpp_codegen_add((int32_t)L_1, (int32_t)L_3)), ((int32_t)il2cpp_codegen_add((int32_t)L_5, (int32_t)L_7)), ((int32_t)il2cpp_codegen_add((int32_t)L_9, (int32_t)L_11)), ((int32_t)il2cpp_codegen_add((int32_t)L_13, (int32_t)L_15)), NULL);
		return L_16;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Addition_m3968FBC6698B10C2DC1D0222C72BB0F67CA1E6D6_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = ___0_lhs;
		uint32_t L_4 = L_3.___y;
		uint32_t L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_lhs;
		uint32_t L_7 = L_6.___z;
		uint32_t L_8 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = ___0_lhs;
		uint32_t L_10 = L_9.___w;
		uint32_t L_11 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_12), ((int32_t)il2cpp_codegen_add((int32_t)L_1, (int32_t)L_2)), ((int32_t)il2cpp_codegen_add((int32_t)L_4, (int32_t)L_5)), ((int32_t)il2cpp_codegen_add((int32_t)L_7, (int32_t)L_8)), ((int32_t)il2cpp_codegen_add((int32_t)L_10, (int32_t)L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Addition_m6F5942C7C169158FD66ECCE72E54D0494886F669_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = ___1_rhs;
		uint32_t L_2 = L_1.___x;
		uint32_t L_3 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___1_rhs;
		uint32_t L_5 = L_4.___y;
		uint32_t L_6 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7 = ___1_rhs;
		uint32_t L_8 = L_7.___z;
		uint32_t L_9 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_12), ((int32_t)il2cpp_codegen_add((int32_t)L_0, (int32_t)L_2)), ((int32_t)il2cpp_codegen_add((int32_t)L_3, (int32_t)L_5)), ((int32_t)il2cpp_codegen_add((int32_t)L_6, (int32_t)L_8)), ((int32_t)il2cpp_codegen_add((int32_t)L_9, (int32_t)L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Subtraction_mF815D62CBBE8EAE82A64035CA71DA415A54DC64F_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___1_rhs;
		uint32_t L_3 = L_2.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___0_lhs;
		uint32_t L_5 = L_4.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___1_rhs;
		uint32_t L_7 = L_6.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = ___0_lhs;
		uint32_t L_9 = L_8.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12 = ___0_lhs;
		uint32_t L_13 = L_12.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = ___1_rhs;
		uint32_t L_15 = L_14.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_16), ((int32_t)il2cpp_codegen_subtract((int32_t)L_1, (int32_t)L_3)), ((int32_t)il2cpp_codegen_subtract((int32_t)L_5, (int32_t)L_7)), ((int32_t)il2cpp_codegen_subtract((int32_t)L_9, (int32_t)L_11)), ((int32_t)il2cpp_codegen_subtract((int32_t)L_13, (int32_t)L_15)), NULL);
		return L_16;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Subtraction_mA5D1B1DE817EFBC8685C2EB3CAB6EF0DFFDC3F92_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = ___0_lhs;
		uint32_t L_4 = L_3.___y;
		uint32_t L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_lhs;
		uint32_t L_7 = L_6.___z;
		uint32_t L_8 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = ___0_lhs;
		uint32_t L_10 = L_9.___w;
		uint32_t L_11 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_12), ((int32_t)il2cpp_codegen_subtract((int32_t)L_1, (int32_t)L_2)), ((int32_t)il2cpp_codegen_subtract((int32_t)L_4, (int32_t)L_5)), ((int32_t)il2cpp_codegen_subtract((int32_t)L_7, (int32_t)L_8)), ((int32_t)il2cpp_codegen_subtract((int32_t)L_10, (int32_t)L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Subtraction_m4DA7085E5BF60C4AD13FC74177EEC6AC6FA65340_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = ___1_rhs;
		uint32_t L_2 = L_1.___x;
		uint32_t L_3 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___1_rhs;
		uint32_t L_5 = L_4.___y;
		uint32_t L_6 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7 = ___1_rhs;
		uint32_t L_8 = L_7.___z;
		uint32_t L_9 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_12), ((int32_t)il2cpp_codegen_subtract((int32_t)L_0, (int32_t)L_2)), ((int32_t)il2cpp_codegen_subtract((int32_t)L_3, (int32_t)L_5)), ((int32_t)il2cpp_codegen_subtract((int32_t)L_6, (int32_t)L_8)), ((int32_t)il2cpp_codegen_subtract((int32_t)L_9, (int32_t)L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Division_m767BDF5F276A7AE45164F10D04573BE5DA17DDB5_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___1_rhs;
		uint32_t L_3 = L_2.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___0_lhs;
		uint32_t L_5 = L_4.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___1_rhs;
		uint32_t L_7 = L_6.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = ___0_lhs;
		uint32_t L_9 = L_8.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12 = ___0_lhs;
		uint32_t L_13 = L_12.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = ___1_rhs;
		uint32_t L_15 = L_14.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_16), ((int32_t)((uint32_t)(int32_t)L_1/(uint32_t)(int32_t)L_3)), ((int32_t)((uint32_t)(int32_t)L_5/(uint32_t)(int32_t)L_7)), ((int32_t)((uint32_t)(int32_t)L_9/(uint32_t)(int32_t)L_11)), ((int32_t)((uint32_t)(int32_t)L_13/(uint32_t)(int32_t)L_15)), NULL);
		return L_16;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Division_m9612CEBFC3CB81D66C58ABB1E67077FC69B657BF_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = ___0_lhs;
		uint32_t L_4 = L_3.___y;
		uint32_t L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_lhs;
		uint32_t L_7 = L_6.___z;
		uint32_t L_8 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = ___0_lhs;
		uint32_t L_10 = L_9.___w;
		uint32_t L_11 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_12), ((int32_t)((uint32_t)(int32_t)L_1/(uint32_t)(int32_t)L_2)), ((int32_t)((uint32_t)(int32_t)L_4/(uint32_t)(int32_t)L_5)), ((int32_t)((uint32_t)(int32_t)L_7/(uint32_t)(int32_t)L_8)), ((int32_t)((uint32_t)(int32_t)L_10/(uint32_t)(int32_t)L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Division_mED3DE29C6531D09E8CB0E75B7DFA0380CE7A8218_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = ___1_rhs;
		uint32_t L_2 = L_1.___x;
		uint32_t L_3 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___1_rhs;
		uint32_t L_5 = L_4.___y;
		uint32_t L_6 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7 = ___1_rhs;
		uint32_t L_8 = L_7.___z;
		uint32_t L_9 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_12), ((int32_t)((uint32_t)(int32_t)L_0/(uint32_t)(int32_t)L_2)), ((int32_t)((uint32_t)(int32_t)L_3/(uint32_t)(int32_t)L_5)), ((int32_t)((uint32_t)(int32_t)L_6/(uint32_t)(int32_t)L_8)), ((int32_t)((uint32_t)(int32_t)L_9/(uint32_t)(int32_t)L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Modulus_m76B34ADA3D74231D5BE5A809380D075D3B5EF921_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___1_rhs;
		uint32_t L_3 = L_2.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___0_lhs;
		uint32_t L_5 = L_4.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___1_rhs;
		uint32_t L_7 = L_6.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = ___0_lhs;
		uint32_t L_9 = L_8.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12 = ___0_lhs;
		uint32_t L_13 = L_12.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = ___1_rhs;
		uint32_t L_15 = L_14.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_16), ((int32_t)((uint32_t)(int32_t)L_1%(uint32_t)(int32_t)L_3)), ((int32_t)((uint32_t)(int32_t)L_5%(uint32_t)(int32_t)L_7)), ((int32_t)((uint32_t)(int32_t)L_9%(uint32_t)(int32_t)L_11)), ((int32_t)((uint32_t)(int32_t)L_13%(uint32_t)(int32_t)L_15)), NULL);
		return L_16;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Modulus_m8C76FB3CB8FA2D498525823CEC6DDE42B5B4FDDF_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = ___0_lhs;
		uint32_t L_4 = L_3.___y;
		uint32_t L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_lhs;
		uint32_t L_7 = L_6.___z;
		uint32_t L_8 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = ___0_lhs;
		uint32_t L_10 = L_9.___w;
		uint32_t L_11 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_12), ((int32_t)((uint32_t)(int32_t)L_1%(uint32_t)(int32_t)L_2)), ((int32_t)((uint32_t)(int32_t)L_4%(uint32_t)(int32_t)L_5)), ((int32_t)((uint32_t)(int32_t)L_7%(uint32_t)(int32_t)L_8)), ((int32_t)((uint32_t)(int32_t)L_10%(uint32_t)(int32_t)L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Modulus_m6319F62EDDC062DCF736D348ABA648B560259204_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = ___1_rhs;
		uint32_t L_2 = L_1.___x;
		uint32_t L_3 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___1_rhs;
		uint32_t L_5 = L_4.___y;
		uint32_t L_6 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7 = ___1_rhs;
		uint32_t L_8 = L_7.___z;
		uint32_t L_9 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_12), ((int32_t)((uint32_t)(int32_t)L_0%(uint32_t)(int32_t)L_2)), ((int32_t)((uint32_t)(int32_t)L_3%(uint32_t)(int32_t)L_5)), ((int32_t)((uint32_t)(int32_t)L_6%(uint32_t)(int32_t)L_8)), ((int32_t)((uint32_t)(int32_t)L_9%(uint32_t)(int32_t)L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Increment_m64BB67C319046CB11232CE66183D3A2B46917DF0_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_val, const RuntimeMethod* method) 
{
	uint32_t V_0 = 0;
	{
		uint32_t* L_0 = (uint32_t*)(&(&___0_val)->___x);
		uint32_t* L_1 = L_0;
		int32_t L_2 = *((uint32_t*)L_1);
		V_0 = ((int32_t)il2cpp_codegen_add(L_2, 1));
		uint32_t L_3 = V_0;
		*((int32_t*)L_1) = (int32_t)L_3;
		uint32_t L_4 = V_0;
		uint32_t* L_5 = (uint32_t*)(&(&___0_val)->___y);
		uint32_t* L_6 = L_5;
		int32_t L_7 = *((uint32_t*)L_6);
		V_0 = ((int32_t)il2cpp_codegen_add(L_7, 1));
		uint32_t L_8 = V_0;
		*((int32_t*)L_6) = (int32_t)L_8;
		uint32_t L_9 = V_0;
		uint32_t* L_10 = (uint32_t*)(&(&___0_val)->___z);
		uint32_t* L_11 = L_10;
		int32_t L_12 = *((uint32_t*)L_11);
		V_0 = ((int32_t)il2cpp_codegen_add(L_12, 1));
		uint32_t L_13 = V_0;
		*((int32_t*)L_11) = (int32_t)L_13;
		uint32_t L_14 = V_0;
		uint32_t* L_15 = (uint32_t*)(&(&___0_val)->___w);
		uint32_t* L_16 = L_15;
		int32_t L_17 = *((uint32_t*)L_16);
		V_0 = ((int32_t)il2cpp_codegen_add(L_17, 1));
		uint32_t L_18 = V_0;
		*((int32_t*)L_16) = (int32_t)L_18;
		uint32_t L_19 = V_0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_20;
		memset((&L_20), 0, sizeof(L_20));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_20), L_4, L_9, L_14, L_19, NULL);
		return L_20;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_Decrement_m0F6103CB4C76260923D9B080C4D41DFCD63B28B1_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_val, const RuntimeMethod* method) 
{
	uint32_t V_0 = 0;
	{
		uint32_t* L_0 = (uint32_t*)(&(&___0_val)->___x);
		uint32_t* L_1 = L_0;
		int32_t L_2 = *((uint32_t*)L_1);
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_2, 1));
		uint32_t L_3 = V_0;
		*((int32_t*)L_1) = (int32_t)L_3;
		uint32_t L_4 = V_0;
		uint32_t* L_5 = (uint32_t*)(&(&___0_val)->___y);
		uint32_t* L_6 = L_5;
		int32_t L_7 = *((uint32_t*)L_6);
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_7, 1));
		uint32_t L_8 = V_0;
		*((int32_t*)L_6) = (int32_t)L_8;
		uint32_t L_9 = V_0;
		uint32_t* L_10 = (uint32_t*)(&(&___0_val)->___z);
		uint32_t* L_11 = L_10;
		int32_t L_12 = *((uint32_t*)L_11);
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_12, 1));
		uint32_t L_13 = V_0;
		*((int32_t*)L_11) = (int32_t)L_13;
		uint32_t L_14 = V_0;
		uint32_t* L_15 = (uint32_t*)(&(&___0_val)->___w);
		uint32_t* L_16 = L_15;
		int32_t L_17 = *((uint32_t*)L_16);
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_17, 1));
		uint32_t L_18 = V_0;
		*((int32_t*)L_16) = (int32_t)L_18;
		uint32_t L_19 = V_0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_20;
		memset((&L_20), 0, sizeof(L_20));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_20), L_4, L_9, L_14, L_19, NULL);
		return L_20;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_LessThan_m3628E2E315AC8DA6CB3C6B655173D481C19FDD19_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___1_rhs;
		uint32_t L_3 = L_2.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___0_lhs;
		uint32_t L_5 = L_4.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___1_rhs;
		uint32_t L_7 = L_6.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = ___0_lhs;
		uint32_t L_9 = L_8.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12 = ___0_lhs;
		uint32_t L_13 = L_12.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = ___1_rhs;
		uint32_t L_15 = L_14.___w;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_16;
		memset((&L_16), 0, sizeof(L_16));
		bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline((&L_16), (bool)((!(((uint32_t)L_1) >= ((uint32_t)L_3)))? 1 : 0), (bool)((!(((uint32_t)L_5) >= ((uint32_t)L_7)))? 1 : 0), (bool)((!(((uint32_t)L_9) >= ((uint32_t)L_11)))? 1 : 0), (bool)((!(((uint32_t)L_13) >= ((uint32_t)L_15)))? 1 : 0), NULL);
		return L_16;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void bool4x4__ctor_m3A3AFC7B534067434119A70AEAECFAC98FF9AE26_inline (bool4x4_t4D8C1B679F35EA8DA2C8F8E4D536596582BAD936* __this, bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 ___0_c0, bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 ___1_c1, bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 ___2_c2, bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 ___3_c3, const RuntimeMethod* method) 
{
	{
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_0 = ___0_c0;
		__this->___c0 = L_0;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_1 = ___1_c1;
		__this->___c1 = L_1;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_2 = ___2_c2;
		__this->___c2 = L_2;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_3 = ___3_c3;
		__this->___c3 = L_3;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_LessThan_mB35B79849BA45A0BB50B6C58EA63AEBC816D06A2_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = ___0_lhs;
		uint32_t L_4 = L_3.___y;
		uint32_t L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_lhs;
		uint32_t L_7 = L_6.___z;
		uint32_t L_8 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = ___0_lhs;
		uint32_t L_10 = L_9.___w;
		uint32_t L_11 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline((&L_12), (bool)((!(((uint32_t)L_1) >= ((uint32_t)L_2)))? 1 : 0), (bool)((!(((uint32_t)L_4) >= ((uint32_t)L_5)))? 1 : 0), (bool)((!(((uint32_t)L_7) >= ((uint32_t)L_8)))? 1 : 0), (bool)((!(((uint32_t)L_10) >= ((uint32_t)L_11)))? 1 : 0), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_LessThan_mD054F90ECC9EEC5FBBA6A5B185C12C3FA51D717E_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = ___1_rhs;
		uint32_t L_2 = L_1.___x;
		uint32_t L_3 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___1_rhs;
		uint32_t L_5 = L_4.___y;
		uint32_t L_6 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7 = ___1_rhs;
		uint32_t L_8 = L_7.___z;
		uint32_t L_9 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___w;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline((&L_12), (bool)((!(((uint32_t)L_0) >= ((uint32_t)L_2)))? 1 : 0), (bool)((!(((uint32_t)L_3) >= ((uint32_t)L_5)))? 1 : 0), (bool)((!(((uint32_t)L_6) >= ((uint32_t)L_8)))? 1 : 0), (bool)((!(((uint32_t)L_9) >= ((uint32_t)L_11)))? 1 : 0), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_LessThanOrEqual_m8A52F50B097C2A9BDFA919675AB046B390F9B324_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___1_rhs;
		uint32_t L_3 = L_2.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___0_lhs;
		uint32_t L_5 = L_4.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___1_rhs;
		uint32_t L_7 = L_6.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = ___0_lhs;
		uint32_t L_9 = L_8.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12 = ___0_lhs;
		uint32_t L_13 = L_12.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = ___1_rhs;
		uint32_t L_15 = L_14.___w;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_16;
		memset((&L_16), 0, sizeof(L_16));
		bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline((&L_16), (bool)((((int32_t)((!(((uint32_t)L_1) <= ((uint32_t)L_3)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((uint32_t)L_5) <= ((uint32_t)L_7)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((uint32_t)L_9) <= ((uint32_t)L_11)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((uint32_t)L_13) <= ((uint32_t)L_15)))? 1 : 0)) == ((int32_t)0))? 1 : 0), NULL);
		return L_16;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_LessThanOrEqual_m8B0420CC432FFED5FB94D3772815893A6114B180_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = ___0_lhs;
		uint32_t L_4 = L_3.___y;
		uint32_t L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_lhs;
		uint32_t L_7 = L_6.___z;
		uint32_t L_8 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = ___0_lhs;
		uint32_t L_10 = L_9.___w;
		uint32_t L_11 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline((&L_12), (bool)((((int32_t)((!(((uint32_t)L_1) <= ((uint32_t)L_2)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((uint32_t)L_4) <= ((uint32_t)L_5)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((uint32_t)L_7) <= ((uint32_t)L_8)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((uint32_t)L_10) <= ((uint32_t)L_11)))? 1 : 0)) == ((int32_t)0))? 1 : 0), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_LessThanOrEqual_m3C3804A4D76E0D681B39390EF7A17E37E88F7540_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = ___1_rhs;
		uint32_t L_2 = L_1.___x;
		uint32_t L_3 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___1_rhs;
		uint32_t L_5 = L_4.___y;
		uint32_t L_6 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7 = ___1_rhs;
		uint32_t L_8 = L_7.___z;
		uint32_t L_9 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___w;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline((&L_12), (bool)((((int32_t)((!(((uint32_t)L_0) <= ((uint32_t)L_2)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((uint32_t)L_3) <= ((uint32_t)L_5)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((uint32_t)L_6) <= ((uint32_t)L_8)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((uint32_t)L_9) <= ((uint32_t)L_11)))? 1 : 0)) == ((int32_t)0))? 1 : 0), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_GreaterThan_m77A71F392617EFC97C92684F40A3C05B99C8EB6B_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___1_rhs;
		uint32_t L_3 = L_2.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___0_lhs;
		uint32_t L_5 = L_4.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___1_rhs;
		uint32_t L_7 = L_6.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = ___0_lhs;
		uint32_t L_9 = L_8.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12 = ___0_lhs;
		uint32_t L_13 = L_12.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = ___1_rhs;
		uint32_t L_15 = L_14.___w;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_16;
		memset((&L_16), 0, sizeof(L_16));
		bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline((&L_16), (bool)((!(((uint32_t)L_1) <= ((uint32_t)L_3)))? 1 : 0), (bool)((!(((uint32_t)L_5) <= ((uint32_t)L_7)))? 1 : 0), (bool)((!(((uint32_t)L_9) <= ((uint32_t)L_11)))? 1 : 0), (bool)((!(((uint32_t)L_13) <= ((uint32_t)L_15)))? 1 : 0), NULL);
		return L_16;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_GreaterThan_mF8C8A88E77644D1B6F50F98D4DF718ED9DD37514_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = ___0_lhs;
		uint32_t L_4 = L_3.___y;
		uint32_t L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_lhs;
		uint32_t L_7 = L_6.___z;
		uint32_t L_8 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = ___0_lhs;
		uint32_t L_10 = L_9.___w;
		uint32_t L_11 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline((&L_12), (bool)((!(((uint32_t)L_1) <= ((uint32_t)L_2)))? 1 : 0), (bool)((!(((uint32_t)L_4) <= ((uint32_t)L_5)))? 1 : 0), (bool)((!(((uint32_t)L_7) <= ((uint32_t)L_8)))? 1 : 0), (bool)((!(((uint32_t)L_10) <= ((uint32_t)L_11)))? 1 : 0), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_GreaterThan_mDD1A94F81F32D1B5BEDD7794CEBD9A1CE8FD2C46_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = ___1_rhs;
		uint32_t L_2 = L_1.___x;
		uint32_t L_3 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___1_rhs;
		uint32_t L_5 = L_4.___y;
		uint32_t L_6 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7 = ___1_rhs;
		uint32_t L_8 = L_7.___z;
		uint32_t L_9 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___w;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline((&L_12), (bool)((!(((uint32_t)L_0) <= ((uint32_t)L_2)))? 1 : 0), (bool)((!(((uint32_t)L_3) <= ((uint32_t)L_5)))? 1 : 0), (bool)((!(((uint32_t)L_6) <= ((uint32_t)L_8)))? 1 : 0), (bool)((!(((uint32_t)L_9) <= ((uint32_t)L_11)))? 1 : 0), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_GreaterThanOrEqual_m35B238651EBAFA2F8241FF4A0E012CEA20F53EAE_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___1_rhs;
		uint32_t L_3 = L_2.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___0_lhs;
		uint32_t L_5 = L_4.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___1_rhs;
		uint32_t L_7 = L_6.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = ___0_lhs;
		uint32_t L_9 = L_8.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12 = ___0_lhs;
		uint32_t L_13 = L_12.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = ___1_rhs;
		uint32_t L_15 = L_14.___w;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_16;
		memset((&L_16), 0, sizeof(L_16));
		bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline((&L_16), (bool)((((int32_t)((!(((uint32_t)L_1) >= ((uint32_t)L_3)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((uint32_t)L_5) >= ((uint32_t)L_7)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((uint32_t)L_9) >= ((uint32_t)L_11)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((uint32_t)L_13) >= ((uint32_t)L_15)))? 1 : 0)) == ((int32_t)0))? 1 : 0), NULL);
		return L_16;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_GreaterThanOrEqual_m06C3ADD093FCFD022D46878D3BC19CD3049521B0_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = ___0_lhs;
		uint32_t L_4 = L_3.___y;
		uint32_t L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_lhs;
		uint32_t L_7 = L_6.___z;
		uint32_t L_8 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = ___0_lhs;
		uint32_t L_10 = L_9.___w;
		uint32_t L_11 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline((&L_12), (bool)((((int32_t)((!(((uint32_t)L_1) >= ((uint32_t)L_2)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((uint32_t)L_4) >= ((uint32_t)L_5)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((uint32_t)L_7) >= ((uint32_t)L_8)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((uint32_t)L_10) >= ((uint32_t)L_11)))? 1 : 0)) == ((int32_t)0))? 1 : 0), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_GreaterThanOrEqual_mD6829718DDCC243DA19306FEEA05BF4AD83AFB66_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = ___1_rhs;
		uint32_t L_2 = L_1.___x;
		uint32_t L_3 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___1_rhs;
		uint32_t L_5 = L_4.___y;
		uint32_t L_6 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7 = ___1_rhs;
		uint32_t L_8 = L_7.___z;
		uint32_t L_9 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___w;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline((&L_12), (bool)((((int32_t)((!(((uint32_t)L_0) >= ((uint32_t)L_2)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((uint32_t)L_3) >= ((uint32_t)L_5)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((uint32_t)L_6) >= ((uint32_t)L_8)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((uint32_t)L_9) >= ((uint32_t)L_11)))? 1 : 0)) == ((int32_t)0))? 1 : 0), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_UnaryNegation_m4E2B5BF3588706130CB50ACA4CDA4617BD9C42D3_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_val, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_val;
		uint32_t L_1 = L_0.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___0_val;
		uint32_t L_3 = L_2.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___0_val;
		uint32_t L_5 = L_4.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_val;
		uint32_t L_7 = L_6.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8;
		memset((&L_8), 0, sizeof(L_8));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_8), ((int32_t)(uint32_t)((-((int64_t)(uint64_t)((uint32_t)L_1))))), ((int32_t)(uint32_t)((-((int64_t)(uint64_t)((uint32_t)L_3))))), ((int32_t)(uint32_t)((-((int64_t)(uint64_t)((uint32_t)L_5))))), ((int32_t)(uint32_t)((-((int64_t)(uint64_t)((uint32_t)L_7))))), NULL);
		return L_8;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_UnaryPlus_m6DD02E0B287D5C0A9C8BD14709A01A113FF7F888_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_val, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_val;
		uint32_t L_1 = L_0.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___0_val;
		uint32_t L_3 = L_2.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___0_val;
		uint32_t L_5 = L_4.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_val;
		uint32_t L_7 = L_6.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8;
		memset((&L_8), 0, sizeof(L_8));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_8), L_1, L_3, L_5, L_7, NULL);
		return L_8;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_LeftShift_m5E109F47429DAA6E55073B8BBD9988FF9B18E4A2_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_x, int32_t ___1_n, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_x;
		uint32_t L_1 = L_0.___x;
		int32_t L_2 = ___1_n;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = ___0_x;
		uint32_t L_4 = L_3.___y;
		int32_t L_5 = ___1_n;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_x;
		uint32_t L_7 = L_6.___z;
		int32_t L_8 = ___1_n;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = ___0_x;
		uint32_t L_10 = L_9.___w;
		int32_t L_11 = ___1_n;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_12), ((int32_t)((int32_t)L_1<<((int32_t)(L_2&((int32_t)31))))), ((int32_t)((int32_t)L_4<<((int32_t)(L_5&((int32_t)31))))), ((int32_t)((int32_t)L_7<<((int32_t)(L_8&((int32_t)31))))), ((int32_t)((int32_t)L_10<<((int32_t)(L_11&((int32_t)31))))), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_RightShift_m277ED9AFF0C41C957E79413C0B4A19EB28503EED_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_x, int32_t ___1_n, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_x;
		uint32_t L_1 = L_0.___x;
		int32_t L_2 = ___1_n;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = ___0_x;
		uint32_t L_4 = L_3.___y;
		int32_t L_5 = ___1_n;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_x;
		uint32_t L_7 = L_6.___z;
		int32_t L_8 = ___1_n;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = ___0_x;
		uint32_t L_10 = L_9.___w;
		int32_t L_11 = ___1_n;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_12), ((int32_t)((uint32_t)L_1>>((int32_t)(L_2&((int32_t)31))))), ((int32_t)((uint32_t)L_4>>((int32_t)(L_5&((int32_t)31))))), ((int32_t)((uint32_t)L_7>>((int32_t)(L_8&((int32_t)31))))), ((int32_t)((uint32_t)L_10>>((int32_t)(L_11&((int32_t)31))))), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_Equality_m5E8D2F6C5A385AACC9A966226C00DD40E44046E3_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___1_rhs;
		uint32_t L_3 = L_2.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___0_lhs;
		uint32_t L_5 = L_4.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___1_rhs;
		uint32_t L_7 = L_6.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = ___0_lhs;
		uint32_t L_9 = L_8.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12 = ___0_lhs;
		uint32_t L_13 = L_12.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = ___1_rhs;
		uint32_t L_15 = L_14.___w;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_16;
		memset((&L_16), 0, sizeof(L_16));
		bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline((&L_16), (bool)((((int32_t)L_1) == ((int32_t)L_3))? 1 : 0), (bool)((((int32_t)L_5) == ((int32_t)L_7))? 1 : 0), (bool)((((int32_t)L_9) == ((int32_t)L_11))? 1 : 0), (bool)((((int32_t)L_13) == ((int32_t)L_15))? 1 : 0), NULL);
		return L_16;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_Equality_mA7515DAB8E3AAD2B5BDA4ED9653F0BF393419A91_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = ___0_lhs;
		uint32_t L_4 = L_3.___y;
		uint32_t L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_lhs;
		uint32_t L_7 = L_6.___z;
		uint32_t L_8 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = ___0_lhs;
		uint32_t L_10 = L_9.___w;
		uint32_t L_11 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline((&L_12), (bool)((((int32_t)L_1) == ((int32_t)L_2))? 1 : 0), (bool)((((int32_t)L_4) == ((int32_t)L_5))? 1 : 0), (bool)((((int32_t)L_7) == ((int32_t)L_8))? 1 : 0), (bool)((((int32_t)L_10) == ((int32_t)L_11))? 1 : 0), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_Equality_m9B94A5CD832272DCFD0B9C99D480556656579A6D_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = ___1_rhs;
		uint32_t L_2 = L_1.___x;
		uint32_t L_3 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___1_rhs;
		uint32_t L_5 = L_4.___y;
		uint32_t L_6 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7 = ___1_rhs;
		uint32_t L_8 = L_7.___z;
		uint32_t L_9 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___w;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline((&L_12), (bool)((((int32_t)L_0) == ((int32_t)L_2))? 1 : 0), (bool)((((int32_t)L_3) == ((int32_t)L_5))? 1 : 0), (bool)((((int32_t)L_6) == ((int32_t)L_8))? 1 : 0), (bool)((((int32_t)L_9) == ((int32_t)L_11))? 1 : 0), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_Inequality_m58973FE7AABF39D4BAB65975F3A537F31DFE279E_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___1_rhs;
		uint32_t L_3 = L_2.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___0_lhs;
		uint32_t L_5 = L_4.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___1_rhs;
		uint32_t L_7 = L_6.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = ___0_lhs;
		uint32_t L_9 = L_8.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12 = ___0_lhs;
		uint32_t L_13 = L_12.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = ___1_rhs;
		uint32_t L_15 = L_14.___w;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_16;
		memset((&L_16), 0, sizeof(L_16));
		bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline((&L_16), (bool)((((int32_t)((((int32_t)L_1) == ((int32_t)L_3))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((((int32_t)L_5) == ((int32_t)L_7))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((((int32_t)L_9) == ((int32_t)L_11))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((((int32_t)L_13) == ((int32_t)L_15))? 1 : 0)) == ((int32_t)0))? 1 : 0), NULL);
		return L_16;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_Inequality_mEED47D66CB338F4A7E98F9C4B5372FF9E9452A79_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = ___0_lhs;
		uint32_t L_4 = L_3.___y;
		uint32_t L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_lhs;
		uint32_t L_7 = L_6.___z;
		uint32_t L_8 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = ___0_lhs;
		uint32_t L_10 = L_9.___w;
		uint32_t L_11 = ___1_rhs;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline((&L_12), (bool)((((int32_t)((((int32_t)L_1) == ((int32_t)L_2))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((((int32_t)L_4) == ((int32_t)L_5))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((((int32_t)L_7) == ((int32_t)L_8))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((((int32_t)L_10) == ((int32_t)L_11))? 1 : 0)) == ((int32_t)0))? 1 : 0), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 uint4_op_Inequality_mAEA94A9E6197044FE2EA3FFF6F4EA234E74F2F2F_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = ___1_rhs;
		uint32_t L_2 = L_1.___x;
		uint32_t L_3 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___1_rhs;
		uint32_t L_5 = L_4.___y;
		uint32_t L_6 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7 = ___1_rhs;
		uint32_t L_8 = L_7.___z;
		uint32_t L_9 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___w;
		bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline((&L_12), (bool)((((int32_t)((((int32_t)L_0) == ((int32_t)L_2))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((((int32_t)L_3) == ((int32_t)L_5))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((((int32_t)L_6) == ((int32_t)L_8))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((((int32_t)L_9) == ((int32_t)L_11))? 1 : 0)) == ((int32_t)0))? 1 : 0), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_OnesComplement_m694E43D937D79E275E9633CF8BA49DE0D11E929F_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_val, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_val;
		uint32_t L_1 = L_0.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___0_val;
		uint32_t L_3 = L_2.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___0_val;
		uint32_t L_5 = L_4.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_val;
		uint32_t L_7 = L_6.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8;
		memset((&L_8), 0, sizeof(L_8));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_8), ((~((int32_t)L_1))), ((~((int32_t)L_3))), ((~((int32_t)L_5))), ((~((int32_t)L_7))), NULL);
		return L_8;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_BitwiseAnd_m1A9CB1A2976212FE13C84F970D4C59C30A54D875_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___1_rhs;
		uint32_t L_3 = L_2.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___0_lhs;
		uint32_t L_5 = L_4.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___1_rhs;
		uint32_t L_7 = L_6.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = ___0_lhs;
		uint32_t L_9 = L_8.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12 = ___0_lhs;
		uint32_t L_13 = L_12.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = ___1_rhs;
		uint32_t L_15 = L_14.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_16), ((int32_t)((int32_t)L_1&(int32_t)L_3)), ((int32_t)((int32_t)L_5&(int32_t)L_7)), ((int32_t)((int32_t)L_9&(int32_t)L_11)), ((int32_t)((int32_t)L_13&(int32_t)L_15)), NULL);
		return L_16;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_BitwiseAnd_mEF924757B2BE1F41834950F7880B93C25B812044_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = ___0_lhs;
		uint32_t L_4 = L_3.___y;
		uint32_t L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_lhs;
		uint32_t L_7 = L_6.___z;
		uint32_t L_8 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = ___0_lhs;
		uint32_t L_10 = L_9.___w;
		uint32_t L_11 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_12), ((int32_t)((int32_t)L_1&(int32_t)L_2)), ((int32_t)((int32_t)L_4&(int32_t)L_5)), ((int32_t)((int32_t)L_7&(int32_t)L_8)), ((int32_t)((int32_t)L_10&(int32_t)L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_BitwiseAnd_mD7D621F58D732913D0B45E67C22F12A34E249C28_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = ___1_rhs;
		uint32_t L_2 = L_1.___x;
		uint32_t L_3 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___1_rhs;
		uint32_t L_5 = L_4.___y;
		uint32_t L_6 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7 = ___1_rhs;
		uint32_t L_8 = L_7.___z;
		uint32_t L_9 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_12), ((int32_t)((int32_t)L_0&(int32_t)L_2)), ((int32_t)((int32_t)L_3&(int32_t)L_5)), ((int32_t)((int32_t)L_6&(int32_t)L_8)), ((int32_t)((int32_t)L_9&(int32_t)L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_BitwiseOr_mDA88C9E25D0910D512ABABDC200D6E3A2E68B573_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___1_rhs;
		uint32_t L_3 = L_2.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___0_lhs;
		uint32_t L_5 = L_4.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___1_rhs;
		uint32_t L_7 = L_6.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = ___0_lhs;
		uint32_t L_9 = L_8.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12 = ___0_lhs;
		uint32_t L_13 = L_12.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = ___1_rhs;
		uint32_t L_15 = L_14.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_16), ((int32_t)((int32_t)L_1|(int32_t)L_3)), ((int32_t)((int32_t)L_5|(int32_t)L_7)), ((int32_t)((int32_t)L_9|(int32_t)L_11)), ((int32_t)((int32_t)L_13|(int32_t)L_15)), NULL);
		return L_16;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_BitwiseOr_mA3F6C811A2EF145F2D7240A9B07801089AC380BD_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = ___0_lhs;
		uint32_t L_4 = L_3.___y;
		uint32_t L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_lhs;
		uint32_t L_7 = L_6.___z;
		uint32_t L_8 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = ___0_lhs;
		uint32_t L_10 = L_9.___w;
		uint32_t L_11 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_12), ((int32_t)((int32_t)L_1|(int32_t)L_2)), ((int32_t)((int32_t)L_4|(int32_t)L_5)), ((int32_t)((int32_t)L_7|(int32_t)L_8)), ((int32_t)((int32_t)L_10|(int32_t)L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_BitwiseOr_mA1E41EFAAF04FDF3A9B4253C9BD99E55B447255C_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = ___1_rhs;
		uint32_t L_2 = L_1.___x;
		uint32_t L_3 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___1_rhs;
		uint32_t L_5 = L_4.___y;
		uint32_t L_6 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7 = ___1_rhs;
		uint32_t L_8 = L_7.___z;
		uint32_t L_9 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_12), ((int32_t)((int32_t)L_0|(int32_t)L_2)), ((int32_t)((int32_t)L_3|(int32_t)L_5)), ((int32_t)((int32_t)L_6|(int32_t)L_8)), ((int32_t)((int32_t)L_9|(int32_t)L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_ExclusiveOr_m5B57FB4F864B88CB06B4949AA275A70D02BF7889_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___1_rhs;
		uint32_t L_3 = L_2.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___0_lhs;
		uint32_t L_5 = L_4.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___1_rhs;
		uint32_t L_7 = L_6.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8 = ___0_lhs;
		uint32_t L_9 = L_8.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12 = ___0_lhs;
		uint32_t L_13 = L_12.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = ___1_rhs;
		uint32_t L_15 = L_14.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16;
		memset((&L_16), 0, sizeof(L_16));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_16), ((int32_t)((int32_t)L_1^(int32_t)L_3)), ((int32_t)((int32_t)L_5^(int32_t)L_7)), ((int32_t)((int32_t)L_9^(int32_t)L_11)), ((int32_t)((int32_t)L_13^(int32_t)L_15)), NULL);
		return L_16;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_ExclusiveOr_m4A4BB6F529F97A0027A7F9F989D21BC84F474391_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint32_t L_2 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3 = ___0_lhs;
		uint32_t L_4 = L_3.___y;
		uint32_t L_5 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_lhs;
		uint32_t L_7 = L_6.___z;
		uint32_t L_8 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_9 = ___0_lhs;
		uint32_t L_10 = L_9.___w;
		uint32_t L_11 = ___1_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_12), ((int32_t)((int32_t)L_1^(int32_t)L_2)), ((int32_t)((int32_t)L_4^(int32_t)L_5)), ((int32_t)((int32_t)L_7^(int32_t)L_8)), ((int32_t)((int32_t)L_10^(int32_t)L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 uint4_op_ExclusiveOr_m18704377E7E81E1741DAAB2CBCB23D7BB6A00CC6_inline (uint32_t ___0_lhs, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = ___1_rhs;
		uint32_t L_2 = L_1.___x;
		uint32_t L_3 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___1_rhs;
		uint32_t L_5 = L_4.___y;
		uint32_t L_6 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7 = ___1_rhs;
		uint32_t L_8 = L_7.___z;
		uint32_t L_9 = ___0_lhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___1_rhs;
		uint32_t L_11 = L_10.___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		memset((&L_12), 0, sizeof(L_12));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_12), ((int32_t)((int32_t)L_0^(int32_t)L_2)), ((int32_t)((int32_t)L_3^(int32_t)L_5)), ((int32_t)((int32_t)L_6^(int32_t)L_8)), ((int32_t)((int32_t)L_9^(int32_t)L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* __this, uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_rhs, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = __this->___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = ___0_rhs;
		uint32_t L_2 = L_1.___x;
		if ((!(((uint32_t)L_0) == ((uint32_t)L_2))))
		{
			goto IL_0039;
		}
	}
	{
		uint32_t L_3 = __this->___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___0_rhs;
		uint32_t L_5 = L_4.___y;
		if ((!(((uint32_t)L_3) == ((uint32_t)L_5))))
		{
			goto IL_0039;
		}
	}
	{
		uint32_t L_6 = __this->___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7 = ___0_rhs;
		uint32_t L_8 = L_7.___z;
		if ((!(((uint32_t)L_6) == ((uint32_t)L_8))))
		{
			goto IL_0039;
		}
	}
	{
		uint32_t L_9 = __this->___w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = ___0_rhs;
		uint32_t L_11 = L_10.___w;
		return (bool)((((int32_t)L_9) == ((int32_t)L_11))? 1 : 0);
	}

IL_0039:
	{
		return (bool)0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool uint4x4_Equals_m1DADC37353246BE77BB8D6E000FB2E79CD33D8CF_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_rhs, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_0 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c0);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_1 = ___0_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = L_1.___c0;
		bool L_3;
		L_3 = uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB_inline(L_0, L_2, NULL);
		if (!L_3)
		{
			goto IL_004b;
		}
	}
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_4 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c1);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_5 = ___0_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = L_5.___c1;
		bool L_7;
		L_7 = uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB_inline(L_4, L_6, NULL);
		if (!L_7)
		{
			goto IL_004b;
		}
	}
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_8 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c2);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___0_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c2;
		bool L_11;
		L_11 = uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB_inline(L_8, L_10, NULL);
		if (!L_11)
		{
			goto IL_004b;
		}
	}
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_12 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c3);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_13 = ___0_rhs;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_14 = L_13.___c3;
		bool L_15;
		L_15 = uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB_inline(L_12, L_14, NULL);
		return L_15;
	}

IL_004b:
	{
		return (bool)0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint32_t math_hash_m5DD32EF0CE916EDF69ED64D20E961217FB867527_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A ___0_v, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_1 = L_0.___c0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2;
		L_2 = math_uint4_m7F6A5341327B9EF6B80C82FD1B93E4F32287A336_inline(((int32_t)-1667299293), ((int32_t)1520214331), ((int32_t)-1345464849), ((int32_t)-1467148163), NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_3;
		L_3 = uint4_op_Multiply_mDD93D0730642A1089848321B9B0E5E923EE575ED_inline(L_1, L_2, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_4 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_5 = L_4.___c1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6;
		L_6 = math_uint4_m7F6A5341327B9EF6B80C82FD1B93E4F32287A336_inline(((int32_t)-814826979), ((int32_t)-1651972703), ((int32_t)-354482315), ((int32_t)1954192763), NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_7;
		L_7 = uint4_op_Multiply_mDD93D0730642A1089848321B9B0E5E923EE575ED_inline(L_5, L_6, NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_8;
		L_8 = uint4_op_Addition_m391200EB43B6C4CDD219079A0D3CDFFA35B0B652_inline(L_3, L_7, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_9 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_10 = L_9.___c2;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_11;
		L_11 = math_uint4_m7F6A5341327B9EF6B80C82FD1B93E4F32287A336_inline(((int32_t)1091696537), ((int32_t)-1242539279), ((int32_t)-41932533), ((int32_t)-1956270665), NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_12;
		L_12 = uint4_op_Multiply_mDD93D0730642A1089848321B9B0E5E923EE575ED_inline(L_10, L_11, NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_13;
		L_13 = uint4_op_Addition_m391200EB43B6C4CDD219079A0D3CDFFA35B0B652_inline(L_8, L_12, NULL);
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_14 = ___0_v;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_15 = L_14.___c3;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_16;
		L_16 = math_uint4_m7F6A5341327B9EF6B80C82FD1B93E4F32287A336_inline(((int32_t)-537594525), ((int32_t)1885959949), ((int32_t)-786283209), ((int32_t)-375466253), NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_17;
		L_17 = uint4_op_Multiply_mDD93D0730642A1089848321B9B0E5E923EE575ED_inline(L_15, L_16, NULL);
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_18;
		L_18 = uint4_op_Addition_m391200EB43B6C4CDD219079A0D3CDFFA35B0B652_inline(L_13, L_17, NULL);
		uint32_t L_19;
		L_19 = math_csum_m6A99E69A84442A729781A97F78B260223DD01D8F_inline(L_18, NULL);
		return ((int32_t)il2cpp_codegen_add((int32_t)L_19, ((int32_t)1209161033)));
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t uint4x4_GetHashCode_m812B7AA9E1BCBCCDEB7E00AE247C960964B3EB65_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, const RuntimeMethod* method) 
{
	{
		uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A L_0 = (*(uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A*)__this);
		uint32_t L_1;
		L_1 = math_hash_m5DD32EF0CE916EDF69ED64D20E961217FB867527_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* uint4x4_ToString_m636D074F6F88F38EB2590202338118CE9AA9468B_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBA12CD7184B95EC551FFCA240321EB14D0272CE2);
		s_Il2CppMethodInitialized = true;
	}
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_0 = (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)SZArrayNew(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var, (uint32_t)((int32_t)16));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_1 = L_0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_2 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c0);
		uint32_t L_3 = L_2->___x;
		uint32_t L_4 = L_3;
		RuntimeObject* L_5 = Box(il2cpp_defaults.uint32_class, &L_4);
		NullCheck(L_1);
		ArrayElementTypeCheck (L_1, L_5);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject*)L_5);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_6 = L_1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_7 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c1);
		uint32_t L_8 = L_7->___x;
		uint32_t L_9 = L_8;
		RuntimeObject* L_10 = Box(il2cpp_defaults.uint32_class, &L_9);
		NullCheck(L_6);
		ArrayElementTypeCheck (L_6, L_10);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(1), (RuntimeObject*)L_10);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_11 = L_6;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_12 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c2);
		uint32_t L_13 = L_12->___x;
		uint32_t L_14 = L_13;
		RuntimeObject* L_15 = Box(il2cpp_defaults.uint32_class, &L_14);
		NullCheck(L_11);
		ArrayElementTypeCheck (L_11, L_15);
		(L_11)->SetAt(static_cast<il2cpp_array_size_t>(2), (RuntimeObject*)L_15);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_16 = L_11;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_17 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c3);
		uint32_t L_18 = L_17->___x;
		uint32_t L_19 = L_18;
		RuntimeObject* L_20 = Box(il2cpp_defaults.uint32_class, &L_19);
		NullCheck(L_16);
		ArrayElementTypeCheck (L_16, L_20);
		(L_16)->SetAt(static_cast<il2cpp_array_size_t>(3), (RuntimeObject*)L_20);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_21 = L_16;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_22 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c0);
		uint32_t L_23 = L_22->___y;
		uint32_t L_24 = L_23;
		RuntimeObject* L_25 = Box(il2cpp_defaults.uint32_class, &L_24);
		NullCheck(L_21);
		ArrayElementTypeCheck (L_21, L_25);
		(L_21)->SetAt(static_cast<il2cpp_array_size_t>(4), (RuntimeObject*)L_25);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_26 = L_21;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_27 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c1);
		uint32_t L_28 = L_27->___y;
		uint32_t L_29 = L_28;
		RuntimeObject* L_30 = Box(il2cpp_defaults.uint32_class, &L_29);
		NullCheck(L_26);
		ArrayElementTypeCheck (L_26, L_30);
		(L_26)->SetAt(static_cast<il2cpp_array_size_t>(5), (RuntimeObject*)L_30);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_31 = L_26;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_32 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c2);
		uint32_t L_33 = L_32->___y;
		uint32_t L_34 = L_33;
		RuntimeObject* L_35 = Box(il2cpp_defaults.uint32_class, &L_34);
		NullCheck(L_31);
		ArrayElementTypeCheck (L_31, L_35);
		(L_31)->SetAt(static_cast<il2cpp_array_size_t>(6), (RuntimeObject*)L_35);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_36 = L_31;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_37 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c3);
		uint32_t L_38 = L_37->___y;
		uint32_t L_39 = L_38;
		RuntimeObject* L_40 = Box(il2cpp_defaults.uint32_class, &L_39);
		NullCheck(L_36);
		ArrayElementTypeCheck (L_36, L_40);
		(L_36)->SetAt(static_cast<il2cpp_array_size_t>(7), (RuntimeObject*)L_40);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_41 = L_36;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_42 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c0);
		uint32_t L_43 = L_42->___z;
		uint32_t L_44 = L_43;
		RuntimeObject* L_45 = Box(il2cpp_defaults.uint32_class, &L_44);
		NullCheck(L_41);
		ArrayElementTypeCheck (L_41, L_45);
		(L_41)->SetAt(static_cast<il2cpp_array_size_t>(8), (RuntimeObject*)L_45);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_46 = L_41;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_47 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c1);
		uint32_t L_48 = L_47->___z;
		uint32_t L_49 = L_48;
		RuntimeObject* L_50 = Box(il2cpp_defaults.uint32_class, &L_49);
		NullCheck(L_46);
		ArrayElementTypeCheck (L_46, L_50);
		(L_46)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)9)), (RuntimeObject*)L_50);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_51 = L_46;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_52 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c2);
		uint32_t L_53 = L_52->___z;
		uint32_t L_54 = L_53;
		RuntimeObject* L_55 = Box(il2cpp_defaults.uint32_class, &L_54);
		NullCheck(L_51);
		ArrayElementTypeCheck (L_51, L_55);
		(L_51)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)10)), (RuntimeObject*)L_55);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_56 = L_51;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_57 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c3);
		uint32_t L_58 = L_57->___z;
		uint32_t L_59 = L_58;
		RuntimeObject* L_60 = Box(il2cpp_defaults.uint32_class, &L_59);
		NullCheck(L_56);
		ArrayElementTypeCheck (L_56, L_60);
		(L_56)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)11)), (RuntimeObject*)L_60);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_61 = L_56;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_62 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c0);
		uint32_t L_63 = L_62->___w;
		uint32_t L_64 = L_63;
		RuntimeObject* L_65 = Box(il2cpp_defaults.uint32_class, &L_64);
		NullCheck(L_61);
		ArrayElementTypeCheck (L_61, L_65);
		(L_61)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)12)), (RuntimeObject*)L_65);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_66 = L_61;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_67 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c1);
		uint32_t L_68 = L_67->___w;
		uint32_t L_69 = L_68;
		RuntimeObject* L_70 = Box(il2cpp_defaults.uint32_class, &L_69);
		NullCheck(L_66);
		ArrayElementTypeCheck (L_66, L_70);
		(L_66)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)13)), (RuntimeObject*)L_70);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_71 = L_66;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_72 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c2);
		uint32_t L_73 = L_72->___w;
		uint32_t L_74 = L_73;
		RuntimeObject* L_75 = Box(il2cpp_defaults.uint32_class, &L_74);
		NullCheck(L_71);
		ArrayElementTypeCheck (L_71, L_75);
		(L_71)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)14)), (RuntimeObject*)L_75);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_76 = L_71;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_77 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c3);
		uint32_t L_78 = L_77->___w;
		uint32_t L_79 = L_78;
		RuntimeObject* L_80 = Box(il2cpp_defaults.uint32_class, &L_79);
		NullCheck(L_76);
		ArrayElementTypeCheck (L_76, L_80);
		(L_76)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)15)), (RuntimeObject*)L_80);
		String_t* L_81;
		L_81 = String_Format_m918500C1EFB475181349A79989BB79BB36102894(_stringLiteralBA12CD7184B95EC551FFCA240321EB14D0272CE2, L_76, NULL);
		return L_81;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* uint4x4_ToString_m97543E4A4C7E05B6B1C86437E56F4246AA5FC456_inline (uint4x4_tB8C643F0E9FDEFEA2B05271B6917682F6BEE584A* __this, String_t* ___0_format, RuntimeObject* ___1_formatProvider, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBA12CD7184B95EC551FFCA240321EB14D0272CE2);
		s_Il2CppMethodInitialized = true;
	}
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_0 = (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)SZArrayNew(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918_il2cpp_TypeInfo_var, (uint32_t)((int32_t)16));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_1 = L_0;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_2 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c0);
		uint32_t* L_3 = (uint32_t*)(&L_2->___x);
		String_t* L_4 = ___0_format;
		RuntimeObject* L_5 = ___1_formatProvider;
		String_t* L_6;
		L_6 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_3, L_4, L_5, NULL);
		NullCheck(L_1);
		ArrayElementTypeCheck (L_1, L_6);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(0), (RuntimeObject*)L_6);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_7 = L_1;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_8 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c1);
		uint32_t* L_9 = (uint32_t*)(&L_8->___x);
		String_t* L_10 = ___0_format;
		RuntimeObject* L_11 = ___1_formatProvider;
		String_t* L_12;
		L_12 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_9, L_10, L_11, NULL);
		NullCheck(L_7);
		ArrayElementTypeCheck (L_7, L_12);
		(L_7)->SetAt(static_cast<il2cpp_array_size_t>(1), (RuntimeObject*)L_12);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_13 = L_7;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_14 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c2);
		uint32_t* L_15 = (uint32_t*)(&L_14->___x);
		String_t* L_16 = ___0_format;
		RuntimeObject* L_17 = ___1_formatProvider;
		String_t* L_18;
		L_18 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_15, L_16, L_17, NULL);
		NullCheck(L_13);
		ArrayElementTypeCheck (L_13, L_18);
		(L_13)->SetAt(static_cast<il2cpp_array_size_t>(2), (RuntimeObject*)L_18);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_19 = L_13;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_20 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c3);
		uint32_t* L_21 = (uint32_t*)(&L_20->___x);
		String_t* L_22 = ___0_format;
		RuntimeObject* L_23 = ___1_formatProvider;
		String_t* L_24;
		L_24 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_21, L_22, L_23, NULL);
		NullCheck(L_19);
		ArrayElementTypeCheck (L_19, L_24);
		(L_19)->SetAt(static_cast<il2cpp_array_size_t>(3), (RuntimeObject*)L_24);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_25 = L_19;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_26 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c0);
		uint32_t* L_27 = (uint32_t*)(&L_26->___y);
		String_t* L_28 = ___0_format;
		RuntimeObject* L_29 = ___1_formatProvider;
		String_t* L_30;
		L_30 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_27, L_28, L_29, NULL);
		NullCheck(L_25);
		ArrayElementTypeCheck (L_25, L_30);
		(L_25)->SetAt(static_cast<il2cpp_array_size_t>(4), (RuntimeObject*)L_30);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_31 = L_25;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_32 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c1);
		uint32_t* L_33 = (uint32_t*)(&L_32->___y);
		String_t* L_34 = ___0_format;
		RuntimeObject* L_35 = ___1_formatProvider;
		String_t* L_36;
		L_36 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_33, L_34, L_35, NULL);
		NullCheck(L_31);
		ArrayElementTypeCheck (L_31, L_36);
		(L_31)->SetAt(static_cast<il2cpp_array_size_t>(5), (RuntimeObject*)L_36);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_37 = L_31;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_38 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c2);
		uint32_t* L_39 = (uint32_t*)(&L_38->___y);
		String_t* L_40 = ___0_format;
		RuntimeObject* L_41 = ___1_formatProvider;
		String_t* L_42;
		L_42 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_39, L_40, L_41, NULL);
		NullCheck(L_37);
		ArrayElementTypeCheck (L_37, L_42);
		(L_37)->SetAt(static_cast<il2cpp_array_size_t>(6), (RuntimeObject*)L_42);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_43 = L_37;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_44 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c3);
		uint32_t* L_45 = (uint32_t*)(&L_44->___y);
		String_t* L_46 = ___0_format;
		RuntimeObject* L_47 = ___1_formatProvider;
		String_t* L_48;
		L_48 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_45, L_46, L_47, NULL);
		NullCheck(L_43);
		ArrayElementTypeCheck (L_43, L_48);
		(L_43)->SetAt(static_cast<il2cpp_array_size_t>(7), (RuntimeObject*)L_48);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_49 = L_43;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_50 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c0);
		uint32_t* L_51 = (uint32_t*)(&L_50->___z);
		String_t* L_52 = ___0_format;
		RuntimeObject* L_53 = ___1_formatProvider;
		String_t* L_54;
		L_54 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_51, L_52, L_53, NULL);
		NullCheck(L_49);
		ArrayElementTypeCheck (L_49, L_54);
		(L_49)->SetAt(static_cast<il2cpp_array_size_t>(8), (RuntimeObject*)L_54);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_55 = L_49;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_56 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c1);
		uint32_t* L_57 = (uint32_t*)(&L_56->___z);
		String_t* L_58 = ___0_format;
		RuntimeObject* L_59 = ___1_formatProvider;
		String_t* L_60;
		L_60 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_57, L_58, L_59, NULL);
		NullCheck(L_55);
		ArrayElementTypeCheck (L_55, L_60);
		(L_55)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)9)), (RuntimeObject*)L_60);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_61 = L_55;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_62 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c2);
		uint32_t* L_63 = (uint32_t*)(&L_62->___z);
		String_t* L_64 = ___0_format;
		RuntimeObject* L_65 = ___1_formatProvider;
		String_t* L_66;
		L_66 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_63, L_64, L_65, NULL);
		NullCheck(L_61);
		ArrayElementTypeCheck (L_61, L_66);
		(L_61)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)10)), (RuntimeObject*)L_66);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_67 = L_61;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_68 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c3);
		uint32_t* L_69 = (uint32_t*)(&L_68->___z);
		String_t* L_70 = ___0_format;
		RuntimeObject* L_71 = ___1_formatProvider;
		String_t* L_72;
		L_72 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_69, L_70, L_71, NULL);
		NullCheck(L_67);
		ArrayElementTypeCheck (L_67, L_72);
		(L_67)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)11)), (RuntimeObject*)L_72);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_73 = L_67;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_74 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c0);
		uint32_t* L_75 = (uint32_t*)(&L_74->___w);
		String_t* L_76 = ___0_format;
		RuntimeObject* L_77 = ___1_formatProvider;
		String_t* L_78;
		L_78 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_75, L_76, L_77, NULL);
		NullCheck(L_73);
		ArrayElementTypeCheck (L_73, L_78);
		(L_73)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)12)), (RuntimeObject*)L_78);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_79 = L_73;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_80 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c1);
		uint32_t* L_81 = (uint32_t*)(&L_80->___w);
		String_t* L_82 = ___0_format;
		RuntimeObject* L_83 = ___1_formatProvider;
		String_t* L_84;
		L_84 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_81, L_82, L_83, NULL);
		NullCheck(L_79);
		ArrayElementTypeCheck (L_79, L_84);
		(L_79)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)13)), (RuntimeObject*)L_84);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_85 = L_79;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_86 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c2);
		uint32_t* L_87 = (uint32_t*)(&L_86->___w);
		String_t* L_88 = ___0_format;
		RuntimeObject* L_89 = ___1_formatProvider;
		String_t* L_90;
		L_90 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_87, L_88, L_89, NULL);
		NullCheck(L_85);
		ArrayElementTypeCheck (L_85, L_90);
		(L_85)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)14)), (RuntimeObject*)L_90);
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_91 = L_85;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* L_92 = (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9*)(&__this->___c3);
		uint32_t* L_93 = (uint32_t*)(&L_92->___w);
		String_t* L_94 = ___0_format;
		RuntimeObject* L_95 = ___1_formatProvider;
		String_t* L_96;
		L_96 = UInt32_ToString_m464396B0FE2115F3CEA38AEECDDB0FACC3AADADE(L_93, L_94, L_95, NULL);
		NullCheck(L_91);
		ArrayElementTypeCheck (L_91, L_96);
		(L_91)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)15)), (RuntimeObject*)L_96);
		String_t* L_97;
		L_97 = String_Format_m918500C1EFB475181349A79989BB79BB36102894(_stringLiteralBA12CD7184B95EC551FFCA240321EB14D0272CE2, L_91, NULL);
		return L_97;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_min, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_max, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_min;
		__this->___Min = L_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___1_max;
		__this->___Max = L_1;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Multiply_m6E5DC552C8B0F9A180298BD9197FF47B14E0EA81_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float ___1_rhs, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x;
		float L_2 = ___1_rhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = ___0_lhs;
		float L_4 = L_3.___y;
		float L_5 = ___1_rhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___0_lhs;
		float L_7 = L_6.___z;
		float L_8 = ___1_rhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9;
		memset((&L_9), 0, sizeof(L_9));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_9), ((float)il2cpp_codegen_multiply(L_1, L_2)), ((float)il2cpp_codegen_multiply(L_4, L_5)), ((float)il2cpp_codegen_multiply(L_7, L_8)), NULL);
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 MinMaxAABB_CreateFromCenterAndHalfExtents_mF18074A621916107BF592378F72B6F113294F508_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_center, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_halfExtents, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_center;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___1_halfExtents;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_0, L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = ___0_center;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___1_halfExtents;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		L_5 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_3, L_4, NULL);
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_6;
		memset((&L_6), 0, sizeof(L_6));
		MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C_inline((&L_6), L_2, L_5, NULL);
		return L_6;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_rhs;
		float L_3 = L_2.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_lhs;
		float L_5 = L_4.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___1_rhs;
		float L_7 = L_6.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8 = ___0_lhs;
		float L_9 = L_8.___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___1_rhs;
		float L_11 = L_10.___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_12;
		memset((&L_12), 0, sizeof(L_12));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_12), ((float)il2cpp_codegen_subtract(L_1, L_3)), ((float)il2cpp_codegen_subtract(L_5, L_7)), ((float)il2cpp_codegen_subtract(L_9, L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_rhs;
		float L_3 = L_2.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_lhs;
		float L_5 = L_4.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___1_rhs;
		float L_7 = L_6.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8 = ___0_lhs;
		float L_9 = L_8.___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___1_rhs;
		float L_11 = L_10.___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_12;
		memset((&L_12), 0, sizeof(L_12));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_12), ((float)il2cpp_codegen_add(L_1, L_3)), ((float)il2cpp_codegen_add(L_5, L_7)), ((float)il2cpp_codegen_add(L_9, L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 float3_op_LessThanOrEqual_m18273875F0537224587D1622DD53562971D9FF48_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_rhs;
		float L_3 = L_2.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_lhs;
		float L_5 = L_4.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___1_rhs;
		float L_7 = L_6.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8 = ___0_lhs;
		float L_9 = L_8.___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___1_rhs;
		float L_11 = L_10.___z;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool3__ctor_m3683F21E6C110670CDDA02E4C1F6E063E936FEE2_inline((&L_12), (bool)((((int32_t)((!(((float)L_1) <= ((float)L_3)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((float)L_5) <= ((float)L_7)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((float)L_9) <= ((float)L_11)))? 1 : 0)) == ((int32_t)0))? 1 : 0), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool math_all_mB8957D1E684773F171F74448AD9591F3619890A4_inline (bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___0_x, const RuntimeMethod* method) 
{
	{
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_0 = ___0_x;
		bool L_1 = L_0.___x;
		if (!L_1)
		{
			goto IL_0017;
		}
	}
	{
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_2 = ___0_x;
		bool L_3 = L_2.___y;
		if (!L_3)
		{
			goto IL_0017;
		}
	}
	{
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_4 = ___0_x;
		bool L_5 = L_4.___z;
		return L_5;
	}

IL_0017:
	{
		return (bool)0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___y;
		float L_1 = __this->___z;
		float L_2 = __this->___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		memset((&L_3), 0, sizeof(L_3));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_3), L_0, L_1, L_2, NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_y, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_x;
		float L_1 = L_0.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_y;
		float L_3 = L_2.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_x;
		float L_5 = L_4.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___1_y;
		float L_7 = L_6.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8 = ___0_x;
		float L_9 = L_8.___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___1_y;
		float L_11 = L_10.___z;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_1, L_3)), ((float)il2cpp_codegen_multiply(L_5, L_7)))), ((float)il2cpp_codegen_multiply(L_9, L_11))));
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 float3_op_GreaterThanOrEqual_m01767B59951623AD803736AB63E12D9BC6FC1AAE_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_rhs;
		float L_3 = L_2.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_lhs;
		float L_5 = L_4.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___1_rhs;
		float L_7 = L_6.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8 = ___0_lhs;
		float L_9 = L_8.___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___1_rhs;
		float L_11 = L_10.___z;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool3__ctor_m3683F21E6C110670CDDA02E4C1F6E063E936FEE2_inline((&L_12), (bool)((((int32_t)((!(((float)L_1) >= ((float)L_3)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((float)L_5) >= ((float)L_7)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((float)L_9) >= ((float)L_11)))? 1 : 0)) == ((int32_t)0))? 1 : 0), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 bool3_op_BitwiseAnd_mE44CC5838094A40F1F605A7798994197165A63E1_inline (bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___0_lhs, bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___1_rhs, const RuntimeMethod* method) 
{
	{
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_0 = ___0_lhs;
		bool L_1 = L_0.___x;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_2 = ___1_rhs;
		bool L_3 = L_2.___x;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_4 = ___0_lhs;
		bool L_5 = L_4.___y;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_6 = ___1_rhs;
		bool L_7 = L_6.___y;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_8 = ___0_lhs;
		bool L_9 = L_8.___z;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_10 = ___1_rhs;
		bool L_11 = L_10.___z;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool3__ctor_m3683F21E6C110670CDDA02E4C1F6E063E936FEE2_inline((&L_12), (bool)((int32_t)((int32_t)L_1&(int32_t)L_3)), (bool)((int32_t)((int32_t)L_5&(int32_t)L_7)), (bool)((int32_t)((int32_t)L_9&(int32_t)L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MinMaxAABB_Contains_m507D89756D76CA0E2CBD8CAD23C6CE27A5F39089_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = __this->___Min;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_2;
		L_2 = float3_op_GreaterThanOrEqual_m01767B59951623AD803736AB63E12D9BC6FC1AAE_inline(L_0, L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = __this->___Max;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_5;
		L_5 = float3_op_LessThanOrEqual_m18273875F0537224587D1622DD53562971D9FF48_inline(L_3, L_4, NULL);
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_6;
		L_6 = bool3_op_BitwiseAnd_mE44CC5838094A40F1F605A7798994197165A63E1_inline(L_2, L_5, NULL);
		bool L_7;
		L_7 = math_all_mB8957D1E684773F171F74448AD9591F3619890A4_inline(L_6, NULL);
		return L_7;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MinMaxAABB_Contains_m8AB762A87FD22983A2A5ED2C96D3C559876D8823_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_1 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = L_1.___Min;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_3;
		L_3 = float3_op_LessThanOrEqual_m18273875F0537224587D1622DD53562971D9FF48_inline(L_0, L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = __this->___Max;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_5 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = L_5.___Max;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_7;
		L_7 = float3_op_GreaterThanOrEqual_m01767B59951623AD803736AB63E12D9BC6FC1AAE_inline(L_4, L_6, NULL);
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_8;
		L_8 = bool3_op_BitwiseAnd_mE44CC5838094A40F1F605A7798994197165A63E1_inline(L_3, L_7, NULL);
		bool L_9;
		L_9 = math_all_mB8957D1E684773F171F74448AD9591F3619890A4_inline(L_8, NULL);
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MinMaxAABB_Overlaps_mEEC801434D524543F9E6D5D77A642A5184F8C4D9_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Max;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_1 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = L_1.___Min;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_3;
		L_3 = float3_op_GreaterThanOrEqual_m01767B59951623AD803736AB63E12D9BC6FC1AAE_inline(L_0, L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = __this->___Min;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_5 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = L_5.___Max;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_7;
		L_7 = float3_op_LessThanOrEqual_m18273875F0537224587D1622DD53562971D9FF48_inline(L_4, L_6, NULL);
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_8;
		L_8 = bool3_op_BitwiseAnd_mE44CC5838094A40F1F605A7798994197165A63E1_inline(L_3, L_7, NULL);
		bool L_9;
		L_9 = math_all_mB8957D1E684773F171F74448AD9591F3619890A4_inline(L_8, NULL);
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Subtraction_m111BEEA770E140739DDC6A3410736DFF7EE32045_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float ___1_rhs, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x;
		float L_2 = ___1_rhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = ___0_lhs;
		float L_4 = L_3.___y;
		float L_5 = ___1_rhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___0_lhs;
		float L_7 = L_6.___z;
		float L_8 = ___1_rhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9;
		memset((&L_9), 0, sizeof(L_9));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_9), ((float)il2cpp_codegen_subtract(L_1, L_2)), ((float)il2cpp_codegen_subtract(L_4, L_5)), ((float)il2cpp_codegen_subtract(L_7, L_8)), NULL);
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Addition_mABF24BC9A16C272B9F5AB21A601B9D9A831F8C43_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float ___1_rhs, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x;
		float L_2 = ___1_rhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = ___0_lhs;
		float L_4 = L_3.___y;
		float L_5 = ___1_rhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___0_lhs;
		float L_7 = L_6.___z;
		float L_8 = ___1_rhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9;
		memset((&L_9), 0, sizeof(L_9));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_9), ((float)il2cpp_codegen_add(L_1, L_2)), ((float)il2cpp_codegen_add(L_4, L_5)), ((float)il2cpp_codegen_add(L_7, L_8)), NULL);
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MinMaxAABB_Expand_m8574B6375684AC91E413410881D03B71C400DC75_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float ___0_signedDistance, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min;
		float L_1 = ___0_signedDistance;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Subtraction_m111BEEA770E140739DDC6A3410736DFF7EE32045_inline(L_0, L_1, NULL);
		__this->___Min = L_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = __this->___Max;
		float L_4 = ___0_signedDistance;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		L_5 = float3_op_Addition_mABF24BC9A16C272B9F5AB21A601B9D9A831F8C43_inline(L_3, L_4, NULL);
		__this->___Max = L_5;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_min_m13CC8D5B7844D954C3125DD72831C693AB8A7FF5_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_y, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_x;
		float L_1 = L_0.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_y;
		float L_3 = L_2.___x;
		float L_4;
		L_4 = math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline(L_1, L_3, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5 = ___0_x;
		float L_6 = L_5.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_7 = ___1_y;
		float L_8 = L_7.___y;
		float L_9;
		L_9 = math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline(L_6, L_8, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___0_x;
		float L_11 = L_10.___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_12 = ___1_y;
		float L_13 = L_12.___z;
		float L_14;
		L_14 = math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline(L_11, L_13, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_15;
		memset((&L_15), 0, sizeof(L_15));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_15), L_4, L_9, L_14, NULL);
		return L_15;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_max_m247D41258606F80861E72309300DF6A3F8B50AE4_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_y, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_x;
		float L_1 = L_0.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_y;
		float L_3 = L_2.___x;
		float L_4;
		L_4 = math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline(L_1, L_3, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5 = ___0_x;
		float L_6 = L_5.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_7 = ___1_y;
		float L_8 = L_7.___y;
		float L_9;
		L_9 = math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline(L_6, L_8, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___0_x;
		float L_11 = L_10.___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_12 = ___1_y;
		float L_13 = L_12.___z;
		float L_14;
		L_14 = math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline(L_11, L_13, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_15;
		memset((&L_15), 0, sizeof(L_15));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_15), L_4, L_9, L_14, NULL);
		return L_15;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MinMaxAABB_Encapsulate_m7043627B976BE96D6D734288EEA09563352F753B_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_1 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = L_1.___Min;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = math_min_m13CC8D5B7844D954C3125DD72831C693AB8A7FF5_inline(L_0, L_2, NULL);
		__this->___Min = L_3;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = __this->___Max;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_5 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = L_5.___Max;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_7;
		L_7 = math_max_m247D41258606F80861E72309300DF6A3F8B50AE4_inline(L_4, L_6, NULL);
		__this->___Max = L_7;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MinMaxAABB_Encapsulate_mE01CED4767A6B50D846AC30AFCA49A91BA820CD5_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = math_min_m13CC8D5B7844D954C3125DD72831C693AB8A7FF5_inline(L_0, L_1, NULL);
		__this->___Min = L_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = __this->___Max;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		L_5 = math_max_m247D41258606F80861E72309300DF6A3F8B50AE4_inline(L_3, L_4, NULL);
		__this->___Max = L_5;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_rhs, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_rhs;
		float L_2 = L_1.___x;
		if ((!(((float)L_0) == ((float)L_2))))
		{
			goto IL_002b;
		}
	}
	{
		float L_3 = __this->___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_rhs;
		float L_5 = L_4.___y;
		if ((!(((float)L_3) == ((float)L_5))))
		{
			goto IL_002b;
		}
	}
	{
		float L_6 = __this->___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_7 = ___0_rhs;
		float L_8 = L_7.___z;
		return (bool)((((float)L_6) == ((float)L_8))? 1 : 0);
	}

IL_002b:
	{
		return (bool)0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MinMaxAABB_Equals_m6DC492AB1804679250EC6C97CC6DF32299EA8E11_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_other, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_0 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&__this->___Min);
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_1 = ___0_other;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = L_1.___Min;
		bool L_3;
		L_3 = float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A_inline(L_0, L_2, NULL);
		if (!L_3)
		{
			goto IL_0025;
		}
	}
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_4 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&__this->___Max);
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_5 = ___0_other;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = L_5.___Max;
		bool L_7;
		L_7 = float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A_inline(L_4, L_6, NULL);
		return L_7;
	}

IL_0025:
	{
		return (bool)0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* MinMaxAABB_ToString_mD299FEC6092F6072F7BC91773DA766E0E61DEC3F_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralFEB995DBF7004A48F3EDB181C599FB99B4A380FB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = L_0;
		RuntimeObject* L_2 = Box(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E_il2cpp_TypeInfo_var, &L_1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = __this->___Max;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = L_3;
		RuntimeObject* L_5 = Box(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E_il2cpp_TypeInfo_var, &L_4);
		String_t* L_6;
		L_6 = String_Format_mFB7DA489BD99F4670881FF50EC017BFB0A5C0987(_stringLiteralFEB995DBF7004A48F3EDB181C599FB99B4A380FB, L_2, L_5, NULL);
		return L_6;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x = L_0;
		float L_1 = ___1_y;
		__this->___y = L_1;
		float L_2 = ___2_z;
		__this->___z = L_2;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_rotate_m68CD27B1D0643EA356D0AB41ECB004CE094FDA3F_inline (quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 ___0_q, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_v, const RuntimeMethod* method) 
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_0 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&(&___0_q)->___value);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1;
		L_1 = float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline(L_0, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_v;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180_inline(L_1, L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4;
		L_4 = float3_op_Multiply_m38F52B61F8E5636955A1A6DF3A75BD0724148350_inline((2.0f), L_3, NULL);
		V_0 = L_4;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5 = ___1_v;
		quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 L_6 = ___0_q;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_7 = L_6.___value;
		float L_8 = L_7.___w;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10;
		L_10 = float3_op_Multiply_m38F52B61F8E5636955A1A6DF3A75BD0724148350_inline(L_8, L_9, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_11;
		L_11 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_5, L_10, NULL);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_12 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&(&___0_q)->___value);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_13;
		L_13 = float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline(L_12, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_14 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_15;
		L_15 = math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180_inline(L_13, L_14, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_16;
		L_16 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_abs_mC7F2BBD861835C82A0A47A47A44B73E704D7F63B_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_x;
		uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B L_1;
		L_1 = math_asuint_m4AEE8C17FEDA05D4C77C427818D1C9EF5E31521E_inline(L_0, NULL);
		uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B L_2;
		L_2 = uint3_op_BitwiseAnd_m772BFC3A60526C264937ABCA92F1CAAFC2B0D634_inline(L_1, ((int32_t)2147483647LL), NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = math_asfloat_m7A90E1FAABD250FCEC00839D01B098BB046F7933_inline(L_2, NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_transform_m5F6B69A9C0E6E1AF63D8112D8753394891972E44_inline (RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD ___0_a, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_pos, const RuntimeMethod* method) 
{
	{
		RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD L_0 = ___0_a;
		quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 L_1 = L_0.___rot;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_pos;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = math_mul_mE9E04B2868E4D4BA5BD873E4F876D550D36C2E99_inline(L_1, L_2, NULL);
		RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD L_4 = ___0_a;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5 = L_4.___pos;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6;
		L_6 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_3, L_5, NULL);
		return L_6;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 Math_Transform_m762480F0988F685C5039CBFBFA540C85385A53CC_inline (float3x3_tB318DB8C7E54B6CA9E14EB9AC7F5964C1189FC79 ___0_transform, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___1_aabb, const RuntimeMethod* method) 
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_0;
	memset((&V_0), 0, sizeof(V_0));
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_1;
	memset((&V_1), 0, sizeof(V_1));
	bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 V_2;
	memset((&V_2), 0, sizeof(V_2));
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 V_3;
	memset((&V_3), 0, sizeof(V_3));
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_0 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1;
		L_1 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_0, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_2 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Min);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = float3_get_xxx_mFD7DFB9FF23BB0B3437F12CC35DB3D1E0ADF7B20_inline(L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4;
		L_4 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_1, L_3, NULL);
		V_0 = L_4;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_5 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6;
		L_6 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_5, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_7 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8;
		L_8 = float3_get_xxx_mFD7DFB9FF23BB0B3437F12CC35DB3D1E0ADF7B20_inline(L_7, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9;
		L_9 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_6, L_8, NULL);
		V_1 = L_9;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_11 = V_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_12;
		L_12 = float3_op_LessThan_m540182EDEF57B5A865B1C22972CF5C3E862B9C51_inline(L_10, L_11, NULL);
		V_2 = L_12;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_13 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_14 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_15 = V_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_16;
		L_16 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_13, L_14, L_15, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_17 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_18 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_19 = V_2;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_20;
		L_20 = bool3_op_LogicalNot_m85C703CC4098B3731505A162957F91C0373548BD_inline(L_19, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_21;
		L_21 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_17, L_18, L_20, NULL);
		MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C_inline((&V_3), L_16, L_21, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_22 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_23;
		L_23 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_22, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_24 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Min);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_25;
		L_25 = float3_get_yyy_m6FCA12991237EDC77F7C4B6A7F73710338330CCD_inline(L_24, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_26;
		L_26 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_23, L_25, NULL);
		V_0 = L_26;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_27 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_28;
		L_28 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_27, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_29 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_30;
		L_30 = float3_get_yyy_m6FCA12991237EDC77F7C4B6A7F73710338330CCD_inline(L_29, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_31;
		L_31 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_28, L_30, NULL);
		V_1 = L_31;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_32 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_33 = V_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_34;
		L_34 = float3_op_LessThan_m540182EDEF57B5A865B1C22972CF5C3E862B9C51_inline(L_32, L_33, NULL);
		V_2 = L_34;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_35 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_3)->___Min);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_36 = L_35;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_37 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_36);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_38 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_39 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_40 = V_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_41;
		L_41 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_38, L_39, L_40, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_42;
		L_42 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_37, L_41, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_36 = L_42;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_43 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_3)->___Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_44 = L_43;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_45 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_44);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_46 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_47 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_48 = V_2;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_49;
		L_49 = bool3_op_LogicalNot_m85C703CC4098B3731505A162957F91C0373548BD_inline(L_48, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_50;
		L_50 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_46, L_47, L_49, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_51;
		L_51 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_45, L_50, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_44 = L_51;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_52 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c2);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_53;
		L_53 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_52, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_54 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Min);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_55;
		L_55 = float3_get_zzz_m1C7C995F170030A7EF534E2C99E6AFE6928AE9D4_inline(L_54, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_56;
		L_56 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_53, L_55, NULL);
		V_0 = L_56;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_57 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c2);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_58;
		L_58 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_57, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_59 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_60;
		L_60 = float3_get_zzz_m1C7C995F170030A7EF534E2C99E6AFE6928AE9D4_inline(L_59, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_61;
		L_61 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_58, L_60, NULL);
		V_1 = L_61;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_62 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_63 = V_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_64;
		L_64 = float3_op_LessThan_m540182EDEF57B5A865B1C22972CF5C3E862B9C51_inline(L_62, L_63, NULL);
		V_2 = L_64;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_65 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_3)->___Min);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_66 = L_65;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_67 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_66);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_68 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_69 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_70 = V_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_71;
		L_71 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_68, L_69, L_70, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_72;
		L_72 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_67, L_71, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_66 = L_72;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_73 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_3)->___Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_74 = L_73;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_75 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_74);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_76 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_77 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_78 = V_2;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_79;
		L_79 = bool3_op_LogicalNot_m85C703CC4098B3731505A162957F91C0373548BD_inline(L_78, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_80;
		L_80 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_76, L_77, L_79, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_81;
		L_81 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_75, L_80, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_74 = L_81;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_82 = V_3;
		return L_82;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___x;
		float L_1 = __this->___y;
		float L_2 = __this->___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		memset((&L_3), 0, sizeof(L_3));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_3), L_0, L_1, L_2, NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___x;
		float L_1 = __this->___y;
		float L_2 = __this->___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		memset((&L_3), 0, sizeof(L_3));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_3), L_0, L_1, L_2, NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_xxx_mFD7DFB9FF23BB0B3437F12CC35DB3D1E0ADF7B20_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___x;
		float L_1 = __this->___x;
		float L_2 = __this->___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		memset((&L_3), 0, sizeof(L_3));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_3), L_0, L_1, L_2, NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_rhs;
		float L_3 = L_2.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_lhs;
		float L_5 = L_4.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___1_rhs;
		float L_7 = L_6.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8 = ___0_lhs;
		float L_9 = L_8.___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___1_rhs;
		float L_11 = L_10.___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_12;
		memset((&L_12), 0, sizeof(L_12));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_12), ((float)il2cpp_codegen_multiply(L_1, L_3)), ((float)il2cpp_codegen_multiply(L_5, L_7)), ((float)il2cpp_codegen_multiply(L_9, L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 float3_op_LessThan_m540182EDEF57B5A865B1C22972CF5C3E862B9C51_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_rhs;
		float L_3 = L_2.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_lhs;
		float L_5 = L_4.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___1_rhs;
		float L_7 = L_6.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8 = ___0_lhs;
		float L_9 = L_8.___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___1_rhs;
		float L_11 = L_10.___z;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool3__ctor_m3683F21E6C110670CDDA02E4C1F6E063E936FEE2_inline((&L_12), (bool)((((float)L_1) < ((float)L_3))? 1 : 0), (bool)((((float)L_5) < ((float)L_7))? 1 : 0), (bool)((((float)L_9) < ((float)L_11))? 1 : 0), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_falseValue, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_trueValue, bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___2_test, const RuntimeMethod* method) 
{
	float G_B3_0 = 0.0f;
	float G_B5_0 = 0.0f;
	float G_B4_0 = 0.0f;
	float G_B6_0 = 0.0f;
	float G_B6_1 = 0.0f;
	float G_B8_0 = 0.0f;
	float G_B8_1 = 0.0f;
	float G_B7_0 = 0.0f;
	float G_B7_1 = 0.0f;
	float G_B9_0 = 0.0f;
	float G_B9_1 = 0.0f;
	float G_B9_2 = 0.0f;
	{
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_0 = ___2_test;
		bool L_1 = L_0.___x;
		if (L_1)
		{
			goto IL_0010;
		}
	}
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___0_falseValue;
		float L_3 = L_2.___x;
		G_B3_0 = L_3;
		goto IL_0016;
	}

IL_0010:
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___1_trueValue;
		float L_5 = L_4.___x;
		G_B3_0 = L_5;
	}

IL_0016:
	{
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_6 = ___2_test;
		bool L_7 = L_6.___y;
		if (L_7)
		{
			G_B5_0 = G_B3_0;
			goto IL_0026;
		}
		G_B4_0 = G_B3_0;
	}
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8 = ___0_falseValue;
		float L_9 = L_8.___y;
		G_B6_0 = L_9;
		G_B6_1 = G_B4_0;
		goto IL_002c;
	}

IL_0026:
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___1_trueValue;
		float L_11 = L_10.___y;
		G_B6_0 = L_11;
		G_B6_1 = G_B5_0;
	}

IL_002c:
	{
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_12 = ___2_test;
		bool L_13 = L_12.___z;
		if (L_13)
		{
			G_B8_0 = G_B6_0;
			G_B8_1 = G_B6_1;
			goto IL_003c;
		}
		G_B7_0 = G_B6_0;
		G_B7_1 = G_B6_1;
	}
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_14 = ___0_falseValue;
		float L_15 = L_14.___z;
		G_B9_0 = L_15;
		G_B9_1 = G_B7_0;
		G_B9_2 = G_B7_1;
		goto IL_0042;
	}

IL_003c:
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_16 = ___1_trueValue;
		float L_17 = L_16.___z;
		G_B9_0 = L_17;
		G_B9_1 = G_B8_0;
		G_B9_2 = G_B8_1;
	}

IL_0042:
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_18;
		memset((&L_18), 0, sizeof(L_18));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_18), G_B9_2, G_B9_1, G_B9_0, NULL);
		return L_18;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 bool3_op_LogicalNot_m85C703CC4098B3731505A162957F91C0373548BD_inline (bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___0_val, const RuntimeMethod* method) 
{
	{
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_0 = ___0_val;
		bool L_1 = L_0.___x;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_2 = ___0_val;
		bool L_3 = L_2.___y;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_4 = ___0_val;
		bool L_5 = L_4.___z;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_6;
		memset((&L_6), 0, sizeof(L_6));
		bool3__ctor_m3683F21E6C110670CDDA02E4C1F6E063E936FEE2_inline((&L_6), (bool)((((int32_t)L_1) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)L_3) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)L_5) == ((int32_t)0))? 1 : 0), NULL);
		return L_6;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_yyy_m6FCA12991237EDC77F7C4B6A7F73710338330CCD_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___y;
		float L_1 = __this->___y;
		float L_2 = __this->___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		memset((&L_3), 0, sizeof(L_3));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_3), L_0, L_1, L_2, NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_zzz_m1C7C995F170030A7EF534E2C99E6AFE6928AE9D4_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___z;
		float L_1 = __this->___z;
		float L_2 = __this->___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		memset((&L_3), 0, sizeof(L_3));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_3), L_0, L_1, L_2, NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E* __this, float ___0_x, float ___1_y, float ___2_z, float ___3_w, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x = L_0;
		float L_1 = ___1_y;
		__this->___y = L_1;
		float L_2 = ___2_z;
		__this->___z = L_2;
		float L_3 = ___3_w;
		__this->___w = L_3;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E Plane_Normalize_mFF53F95372AE2A79C71B546A70B0F152EC308544_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_planeCoefficients, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 V_1;
	memset((&V_1), 0, sizeof(V_1));
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0;
		L_0 = float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline((&___0_planeCoefficients), NULL);
		float L_1;
		L_1 = math_lengthsq_mC699F3F214F05B26BEBAF1B46E3AA3C00407A532_inline(L_0, NULL);
		float L_2;
		L_2 = math_rsqrt_mC67B3430EAADA7C5347E87B23859C569BC010E72_inline(L_1, NULL);
		V_0 = L_2;
		il2cpp_codegen_initobj((&V_1), sizeof(Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82));
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_3 = ___0_planeCoefficients;
		float L_4 = V_0;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_5;
		L_5 = float4_op_Multiply_m712573F441DA8AF0843DE2167927FB76E642B1EB_inline(L_3, L_4, NULL);
		(&V_1)->___NormalAndDistance = L_5;
		Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 L_6 = V_1;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_7;
		L_7 = Plane_op_Implicit_mD02477CC24787906751D3F5E401D2E11BF99AC98_inline(L_6, NULL);
		return L_7;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Plane__ctor_mE41939B3E3E2AE7802AA9571AB429BAB47C56A65_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float ___0_coefficientA, float ___1_coefficientB, float ___2_coefficientC, float ___3_coefficientD, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_coefficientA;
		float L_1 = ___1_coefficientB;
		float L_2 = ___2_coefficientC;
		float L_3 = ___3_coefficientD;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_4;
		memset((&L_4), 0, sizeof(L_4));
		float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D_inline((&L_4), L_0, L_1, L_2, L_3, NULL);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_5;
		L_5 = Plane_Normalize_mFF53F95372AE2A79C71B546A70B0F152EC308544_inline(L_4, NULL);
		__this->___NormalAndDistance = L_5;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_xyz, float ___1_w, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_xyz;
		float L_1 = L_0.___x;
		__this->___x = L_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___0_xyz;
		float L_3 = L_2.___y;
		__this->___y = L_3;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_xyz;
		float L_5 = L_4.___z;
		__this->___z = L_5;
		float L_6 = ___1_w;
		__this->___w = L_6;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Plane__ctor_mAEEAADCE34CB243E12A9FE0240D4E508913C1153_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_normal, float ___1_distance, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_normal;
		float L_1 = ___1_distance;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_2;
		memset((&L_2), 0, sizeof(L_2));
		float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_inline((&L_2), L_0, L_1, NULL);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_3;
		L_3 = Plane_Normalize_mFF53F95372AE2A79C71B546A70B0F152EC308544_inline(L_2, NULL);
		__this->___NormalAndDistance = L_3;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Plane__ctor_m645C0F13FB29D9E443284F1BC42C02CE3B5C27D4_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_normal, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_pointInPlane, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_normal;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_normal;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_pointInPlane;
		float L_3;
		L_3 = math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD_inline(L_1, L_2, NULL);
		Plane__ctor_mAEEAADCE34CB243E12A9FE0240D4E508913C1153_inline(__this, L_0, ((-L_3)), NULL);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_y, const RuntimeMethod* method) 
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1;
		L_1 = float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_inline((&___1_y), NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_0, L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_inline((&___0_x), NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___1_y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		L_5 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_3, L_4, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6;
		L_6 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_2, L_5, NULL);
		V_0 = L_6;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_7;
		L_7 = float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_inline((&V_0), NULL);
		return L_7;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Plane__ctor_m77B64CCE37D396DD70CD0A841F4E6E4F72D1B20A_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_vector1InPlane, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_vector2InPlane, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___2_pointInPlane, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_vector1InPlane;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___1_vector2InPlane;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180_inline(L_0, L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = ___2_pointInPlane;
		Plane__ctor_m645C0F13FB29D9E443284F1BC42C02CE3B5C27D4_inline(__this, L_2, L_3, NULL);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float4_set_xyz_m331D16059D51A5C6CA8AE8FD1E13A68C0570A9C7_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_value, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_value;
		float L_1 = L_0.___x;
		__this->___x = L_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___0_value;
		float L_3 = L_2.___y;
		__this->___y = L_3;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_value;
		float L_5 = L_4.___z;
		__this->___z = L_5;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_lengthsq_mC699F3F214F05B26BEBAF1B46E3AA3C00407A532_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_x;
		float L_2;
		L_2 = math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD_inline(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_rsqrt_mC67B3430EAADA7C5347E87B23859C569BC010E72_inline (float ___0_x, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		float L_1;
		L_1 = math_sqrt_mEF31DE7BD0179009683C5D7B0C58E6571B30CF4A_inline(L_0, NULL);
		return ((float)((1.0f)/L_1));
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E float4_op_Multiply_m712573F441DA8AF0843DE2167927FB76E642B1EB_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_lhs, float ___1_rhs, const RuntimeMethod* method) 
{
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_0 = ___0_lhs;
		float L_1 = L_0.___x;
		float L_2 = ___1_rhs;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_3 = ___0_lhs;
		float L_4 = L_3.___y;
		float L_5 = ___1_rhs;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_6 = ___0_lhs;
		float L_7 = L_6.___z;
		float L_8 = ___1_rhs;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_9 = ___0_lhs;
		float L_10 = L_9.___w;
		float L_11 = ___1_rhs;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_12;
		memset((&L_12), 0, sizeof(L_12));
		float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D_inline((&L_12), ((float)il2cpp_codegen_multiply(L_1, L_2)), ((float)il2cpp_codegen_multiply(L_4, L_5)), ((float)il2cpp_codegen_multiply(L_7, L_8)), ((float)il2cpp_codegen_multiply(L_10, L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E Plane_op_Implicit_mD02477CC24787906751D3F5E401D2E11BF99AC98_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 ___0_plane, const RuntimeMethod* method) 
{
	{
		Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 L_0 = ___0_plane;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_1 = L_0.___NormalAndDistance;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_dot_m20F2285F7227DC308D9CF2DCB8EAAD3E774501D4_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_x, float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___1_y, const RuntimeMethod* method) 
{
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_0 = ___0_x;
		float L_1 = L_0.___x;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_2 = ___1_y;
		float L_3 = L_2.___x;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_4 = ___0_x;
		float L_5 = L_4.___y;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_6 = ___1_y;
		float L_7 = L_6.___y;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_8 = ___0_x;
		float L_9 = L_8.___z;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_10 = ___1_y;
		float L_11 = L_10.___z;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_12 = ___0_x;
		float L_13 = L_12.___w;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_14 = ___1_y;
		float L_15 = L_14.___w;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_1, L_3)), ((float)il2cpp_codegen_multiply(L_5, L_7)))), ((float)il2cpp_codegen_multiply(L_9, L_11)))), ((float)il2cpp_codegen_multiply(L_13, L_15))));
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Plane_SignedDistanceToPoint_mE52778BC70A3A0FF9DDB0FE52D71C587D837F993_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) 
{
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_0 = __this->___NormalAndDistance;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_point;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_2;
		memset((&L_2), 0, sizeof(L_2));
		float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_inline((&L_2), L_1, (1.0f), NULL);
		float L_3;
		L_3 = math_dot_m20F2285F7227DC308D9CF2DCB8EAAD3E774501D4_inline(L_0, L_2, NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E Plane_Projection_mFF8C23401C366A3B4EB017B4DAAAF4E8A9132CFE_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1;
		L_1 = Plane_get_Normal_mAA5C1BEAEFB0848A4CD29E254CC9EF010DD6FE4B(__this, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___0_point;
		float L_3;
		L_3 = Plane_SignedDistanceToPoint_mE52778BC70A3A0FF9DDB0FE52D71C587D837F993_inline(__this, L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4;
		L_4 = float3_op_Multiply_m6E5DC552C8B0F9A180298BD9197FF47B14E0EA81_inline(L_1, L_3, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		L_5 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_0, L_4, NULL);
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E float4_op_UnaryNegation_m5A491FC1978650D62EBEDC51992CF4B2113C8C5B_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_val, const RuntimeMethod* method) 
{
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_0 = ___0_val;
		float L_1 = L_0.___x;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_2 = ___0_val;
		float L_3 = L_2.___y;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_4 = ___0_val;
		float L_5 = L_4.___z;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_6 = ___0_val;
		float L_7 = L_6.___w;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_8;
		memset((&L_8), 0, sizeof(L_8));
		float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D_inline((&L_8), ((-L_1)), ((-L_3)), ((-L_5)), ((-L_7)), NULL);
		return L_8;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4__ctor_m00DD9230DF75F4825012D055BBF5FCC3A08D78B3_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* __this, int32_t ___0_v, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_v;
		__this->___x = L_0;
		int32_t L_1 = ___0_v;
		__this->___y = L_1;
		int32_t L_2 = ___0_v;
		__this->___z = L_2;
		int32_t L_3 = ___0_v;
		__this->___w = L_3;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4__ctor_mE3E045BA2F8A6BADAF84CD437BC4123BAD640226_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* __this, int4_tBA77D4945786DE82C3A487B33955EA1004996052 ___0_v, const RuntimeMethod* method) 
{
	{
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_0 = ___0_v;
		int32_t L_1 = L_0.___x;
		__this->___x = L_1;
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_2 = ___0_v;
		int32_t L_3 = L_2.___y;
		__this->___y = L_3;
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_4 = ___0_v;
		int32_t L_5 = L_4.___z;
		__this->___z = L_5;
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_6 = ___0_v;
		int32_t L_7 = L_6.___w;
		__this->___w = L_7;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4__ctor_mC40F8DC4FDACF816F7989C9D2693B4FDE0116AFB_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* __this, float ___0_v, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_v;
		__this->___x = il2cpp_codegen_cast_floating_point<uint32_t, int32_t, float>(L_0);
		float L_1 = ___0_v;
		__this->___y = il2cpp_codegen_cast_floating_point<uint32_t, int32_t, float>(L_1);
		float L_2 = ___0_v;
		__this->___z = il2cpp_codegen_cast_floating_point<uint32_t, int32_t, float>(L_2);
		float L_3 = ___0_v;
		__this->___w = il2cpp_codegen_cast_floating_point<uint32_t, int32_t, float>(L_3);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4__ctor_m0FDC55AFC114988A17F4E6248D13C331E0F4C544_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* __this, float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_v, const RuntimeMethod* method) 
{
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_0 = ___0_v;
		float L_1 = L_0.___x;
		__this->___x = il2cpp_codegen_cast_floating_point<uint32_t, int32_t, float>(L_1);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_2 = ___0_v;
		float L_3 = L_2.___y;
		__this->___y = il2cpp_codegen_cast_floating_point<uint32_t, int32_t, float>(L_3);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_4 = ___0_v;
		float L_5 = L_4.___z;
		__this->___z = il2cpp_codegen_cast_floating_point<uint32_t, int32_t, float>(L_5);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_6 = ___0_v;
		float L_7 = L_6.___w;
		__this->___w = il2cpp_codegen_cast_floating_point<uint32_t, int32_t, float>(L_7);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4__ctor_m2B3C71B8C523717F9E57A2EC936A9DB6C147B73D_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* __this, double ___0_v, const RuntimeMethod* method) 
{
	{
		double L_0 = ___0_v;
		__this->___x = il2cpp_codegen_cast_floating_point<uint32_t, int32_t, double>(L_0);
		double L_1 = ___0_v;
		__this->___y = il2cpp_codegen_cast_floating_point<uint32_t, int32_t, double>(L_1);
		double L_2 = ___0_v;
		__this->___z = il2cpp_codegen_cast_floating_point<uint32_t, int32_t, double>(L_2);
		double L_3 = ___0_v;
		__this->___w = il2cpp_codegen_cast_floating_point<uint32_t, int32_t, double>(L_3);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint4__ctor_mD155EFD6C699622167DC52223E62E3C023D86CF8_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9* __this, double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 ___0_v, const RuntimeMethod* method) 
{
	{
		double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 L_0 = ___0_v;
		double L_1 = L_0.___x;
		__this->___x = il2cpp_codegen_cast_floating_point<uint32_t, int32_t, double>(L_1);
		double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 L_2 = ___0_v;
		double L_3 = L_2.___y;
		__this->___y = il2cpp_codegen_cast_floating_point<uint32_t, int32_t, double>(L_3);
		double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 L_4 = ___0_v;
		double L_5 = L_4.___z;
		__this->___z = il2cpp_codegen_cast_floating_point<uint32_t, int32_t, double>(L_5);
		double4_t82EF3F10905F7357C3F8C08F83AB6F8EC776FDC5 L_6 = ___0_v;
		double L_7 = L_6.___w;
		__this->___w = il2cpp_codegen_cast_floating_point<uint32_t, int32_t, double>(L_7);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void bool4__ctor_mF155096A6E6BF25B97648480B9A5224A22DFFF88_inline (bool4_tCEC5DC2AFA83CFCFFFD886434359A9A19056A619* __this, bool ___0_x, bool ___1_y, bool ___2_z, bool ___3_w, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_x;
		__this->___x = L_0;
		bool L_1 = ___1_y;
		__this->___y = L_1;
		bool L_2 = ___2_z;
		__this->___z = L_2;
		bool L_3 = ___3_w;
		__this->___w = L_3;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 math_uint4_m7F6A5341327B9EF6B80C82FD1B93E4F32287A336_inline (uint32_t ___0_x, uint32_t ___1_y, uint32_t ___2_z, uint32_t ___3_w, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_x;
		uint32_t L_1 = ___1_y;
		uint32_t L_2 = ___2_z;
		uint32_t L_3 = ___3_w;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4;
		memset((&L_4), 0, sizeof(L_4));
		uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_inline((&L_4), L_0, L_1, L_2, L_3, NULL);
		return L_4;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint32_t math_csum_m6A99E69A84442A729781A97F78B260223DD01D8F_inline (uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 ___0_x, const RuntimeMethod* method) 
{
	{
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_0 = ___0_x;
		uint32_t L_1 = L_0.___x;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_2 = ___0_x;
		uint32_t L_3 = L_2.___y;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_4 = ___0_x;
		uint32_t L_5 = L_4.___z;
		uint4_t6C69CBFAE9BF0F727D52B68779D4A3F0DBA8D5C9 L_6 = ___0_x;
		uint32_t L_7 = L_6.___w;
		return ((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_add((int32_t)L_1, (int32_t)L_3)), (int32_t)L_5)), (int32_t)L_7));
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void bool3__ctor_m3683F21E6C110670CDDA02E4C1F6E063E936FEE2_inline (bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861* __this, bool ___0_x, bool ___1_y, bool ___2_z, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_x;
		__this->___x = L_0;
		bool L_1 = ___1_y;
		__this->___y = L_1;
		bool L_2 = ___2_z;
		__this->___z = L_2;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline (float ___0_x, float ___1_y, const RuntimeMethod* method) 
{
	{
		float L_0 = ___1_y;
		bool L_1;
		L_1 = Single_IsNaN_mFE637F6ECA9F7697CE8EFF56427858F4C5EDF75D_inline(L_0, NULL);
		if (L_1)
		{
			goto IL_000e;
		}
	}
	{
		float L_2 = ___0_x;
		float L_3 = ___1_y;
		if ((((float)L_2) < ((float)L_3)))
		{
			goto IL_000e;
		}
	}
	{
		float L_4 = ___1_y;
		return L_4;
	}

IL_000e:
	{
		float L_5 = ___0_x;
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline (float ___0_x, float ___1_y, const RuntimeMethod* method) 
{
	{
		float L_0 = ___1_y;
		bool L_1;
		L_1 = Single_IsNaN_mFE637F6ECA9F7697CE8EFF56427858F4C5EDF75D_inline(L_0, NULL);
		if (L_1)
		{
			goto IL_000e;
		}
	}
	{
		float L_2 = ___0_x;
		float L_3 = ___1_y;
		if ((((float)L_2) > ((float)L_3)))
		{
			goto IL_000e;
		}
	}
	{
		float L_4 = ___1_y;
		return L_4;
	}

IL_000e:
	{
		float L_5 = ___0_x;
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Multiply_m38F52B61F8E5636955A1A6DF3A75BD0724148350_inline (float ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_lhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___1_rhs;
		float L_2 = L_1.___x;
		float L_3 = ___0_lhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___1_rhs;
		float L_5 = L_4.___y;
		float L_6 = ___0_lhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_7 = ___1_rhs;
		float L_8 = L_7.___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9;
		memset((&L_9), 0, sizeof(L_9));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_9), ((float)il2cpp_codegen_multiply(L_0, L_2)), ((float)il2cpp_codegen_multiply(L_3, L_5)), ((float)il2cpp_codegen_multiply(L_6, L_8)), NULL);
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B math_asuint_m4AEE8C17FEDA05D4C77C427818D1C9EF5E31521E_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, const RuntimeMethod* method) 
{
	{
		uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B L_0 = (*(uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B*)((uintptr_t)(&___0_x)));
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B uint3_op_BitwiseAnd_m772BFC3A60526C264937ABCA92F1CAAFC2B0D634_inline (uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x;
		uint32_t L_2 = ___1_rhs;
		uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B L_3 = ___0_lhs;
		uint32_t L_4 = L_3.___y;
		uint32_t L_5 = ___1_rhs;
		uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B L_6 = ___0_lhs;
		uint32_t L_7 = L_6.___z;
		uint32_t L_8 = ___1_rhs;
		uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B L_9;
		memset((&L_9), 0, sizeof(L_9));
		uint3__ctor_mEFEA14BBA36F53111474B0C3C3B729061F1ACCAF_inline((&L_9), ((int32_t)((int32_t)L_1&(int32_t)L_2)), ((int32_t)((int32_t)L_4&(int32_t)L_5)), ((int32_t)((int32_t)L_7&(int32_t)L_8)), NULL);
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_asfloat_m7A90E1FAABD250FCEC00839D01B098BB046F7933_inline (uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B ___0_x, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)((uintptr_t)(&___0_x)));
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_mul_mE9E04B2868E4D4BA5BD873E4F876D550D36C2E99_inline (quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 ___0_q, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_v, const RuntimeMethod* method) 
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_0 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&(&___0_q)->___value);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1;
		L_1 = float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline(L_0, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_v;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180_inline(L_1, L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4;
		L_4 = float3_op_Multiply_m38F52B61F8E5636955A1A6DF3A75BD0724148350_inline((2.0f), L_3, NULL);
		V_0 = L_4;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5 = ___1_v;
		quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 L_6 = ___0_q;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_7 = L_6.___value;
		float L_8 = L_7.___w;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10;
		L_10 = float3_op_Multiply_m38F52B61F8E5636955A1A6DF3A75BD0724148350_inline(L_8, L_9, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_11;
		L_11 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_5, L_10, NULL);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_12 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&(&___0_q)->___value);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_13;
		L_13 = float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline(L_12, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_14 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_15;
		L_15 = math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180_inline(L_13, L_14, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_16;
		L_16 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_sqrt_mEF31DE7BD0179009683C5D7B0C58E6571B30CF4A_inline (float ___0_x, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		float L_0 = ___0_x;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_1;
		L_1 = sqrt(((double)((float)L_0)));
		return ((float)L_1);
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Single_IsNaN_mFE637F6ECA9F7697CE8EFF56427858F4C5EDF75D_inline (float ___0_f, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_f;
		int32_t L_1;
		L_1 = BitConverter_SingleToInt32Bits_mC760C7CFC89725E3CF68DC45BE3A9A42A7E7DA73_inline(L_0, NULL);
		return (bool)((((int32_t)((int32_t)(L_1&((int32_t)2147483647LL)))) > ((int32_t)((int32_t)2139095040)))? 1 : 0);
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint3__ctor_mEFEA14BBA36F53111474B0C3C3B729061F1ACCAF_inline (uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B* __this, uint32_t ___0_x, uint32_t ___1_y, uint32_t ___2_z, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_x;
		__this->___x = L_0;
		uint32_t L_1 = ___1_y;
		__this->___y = L_1;
		uint32_t L_2 = ___2_z;
		__this->___z = L_2;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t BitConverter_SingleToInt32Bits_mC760C7CFC89725E3CF68DC45BE3A9A42A7E7DA73_inline (float ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = *((int32_t*)((uintptr_t)(&___0_value)));
		return L_0;
	}
}
