{"PlayerBuildProgramLibrary.FeatureExtractor+Data": {"ignoreSystemDlls": true, "featuresToCheck": [{"referenceName": "UnityEngine.WebCamTexture"}, {"referenceName": "UnityEngine.Microphone"}, {"referenceName": "UnityEngine.Networking"}, {"referenceName": "System.Net.Sockets"}, {"referenceName": "UnityEngine.Ping"}, {"referenceName": "UnityEngine.Networking.UnityWebRequest"}, {"referenceName": "UnityEngine.Input::set_multiTouchEnabled"}, {"referenceName": "UnityEngine.Handheld::Vibrate"}, {"referenceName": "UnityEngine.Application::get_internetReachability"}, {"referenceName": "UnityEngine.Input::get_location"}, {"referenceName": "UnityEngine.Input::get_acceleration"}, {"referenceName": "UnityEngine.Input::GetAccelerationEvent"}, {"referenceName": "UnityEngine.Input::get_accelerationEvents"}, {"referenceName": "UnityEngine.Input::get_accelerationEventCount"}, {"referenceName": "UnityEngine.Input::get_touches"}, {"referenceName": "UnityEngine.Input::GetTouch"}, {"referenceName": "UnityEngine.Input::get_touchCount"}, {"referenceName": "UnityEngine.Input::get_multiTouchEnabled"}, {"referenceName": "UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"}, {"referenceName": "UnityEngine.Android.AndroidGame::get_GameMode"}, {"referenceName": "UnityEngine.Android.AndroidGame::SetGameState"}, {"referenceName": "UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"}, {"referenceName": "UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"}, {"referenceName": "UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"}, {"referenceName": "UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"}, {"referenceName": "UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"}, {"referenceName": "UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"}, {"referenceName": "UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"}, {"referenceName": "UnityEngine.Android.AndroidGame::SetGameState"}, {"referenceName": "UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]}, "Bee.TundraBackend.CSharpActionInvocationInformation": {"typeFullName": "PlayerBuildProgramLibrary.FeatureExtractor", "methodName": "Run", "assemblyLocation": "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll", "targets": ["Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt"], "inputs": ["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.IMGUIModule.dll"], "targetDirectories": []}}