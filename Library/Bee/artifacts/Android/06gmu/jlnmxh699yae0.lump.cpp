//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/InternalThread.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/Monitor.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/Mutex.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/NativeEventCalls.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/OSSpecificSynchronizationContext.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/Thread.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/Timer.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/WaitHandle.cpp"
