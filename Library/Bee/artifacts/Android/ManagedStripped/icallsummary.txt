Unity.Burst.LowLevel.BurstCompilerService::LoadBurstLibrary_Injected
Unity.Burst.LowLevel.BurstCompilerService::CompileAsyncDelegateMethod_Injected
Unity.Burst.LowLevel.BurstCompilerService::GetCurrentExecutionMode
Unity.Burst.LowLevel.BurstCompilerService::GetDisassembly_Injected
Unity.Burst.LowLevel.BurstCompilerService::Log
Unity.Burst.LowLevel.BurstCompilerService::RuntimeLog
Unity.Burst.LowLevel.BurstCompilerService::SetCurrentExecutionMode
Unity.Burst.LowLevel.BurstCompilerService::GetAsyncCompiledAsyncDelegateMethod
Unity.Burst.LowLevel.BurstCompilerService::GetOrCreateSharedMemory
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::IsBlittable
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::GetScriptingTypeFlags
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::LeakErase
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::LeakRecord
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemCmp
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::SizeOf
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::Free
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::FreeTracked
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemCpy
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemCpyReplicate
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemCpyStride
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemMove
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemSet
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::Malloc
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MallocTracked
Unity.Hierarchy.Hierarchy::Exists_Injected
Unity.Hierarchy.Hierarchy::SetParent_Injected
Unity.Hierarchy.Hierarchy::get_UpdateNeeded_Injected
Unity.Hierarchy.Hierarchy::GetChildrenCount_Injected
Unity.Hierarchy.Hierarchy::GetNodeTypeHandlersBaseCount_Injected
Unity.Hierarchy.Hierarchy::GetNodeTypeHandlersBaseSpan_Injected
Unity.Hierarchy.Hierarchy::Create
Unity.Hierarchy.Hierarchy::EnumerateChildrenPtr_Injected
Unity.Hierarchy.Hierarchy::AddNode_Injected
Unity.Hierarchy.Hierarchy::Destroy
Unity.Hierarchy.Hierarchy::GetChildren_Injected
Unity.Hierarchy.Hierarchy::GetOrCreateProperty_Injected
Unity.Hierarchy.Hierarchy::GetParent_Injected
Unity.Hierarchy.Hierarchy::SetPropertyRaw_Injected
Unity.Hierarchy.Hierarchy::SetSortIndex_Injected
Unity.Hierarchy.Hierarchy::SortChildren_Injected
Unity.Hierarchy.Hierarchy::Update_Injected
Unity.Hierarchy.Hierarchy::GetPropertyRaw_Injected
Unity.Hierarchy.HierarchyCommandList::Destroy
Unity.Hierarchy.HierarchyFlattened::Contains_Injected
Unity.Hierarchy.HierarchyFlattened::get_UpdateNeeded_Injected
Unity.Hierarchy.HierarchyFlattened::GetChildrenCount_Injected
Unity.Hierarchy.HierarchyFlattened::IndexOf_Injected
Unity.Hierarchy.HierarchyFlattened::Create_Injected
Unity.Hierarchy.HierarchyFlattened::Destroy
Unity.Hierarchy.HierarchyFlattened::Update_Injected
Unity.Hierarchy.HierarchyNodeTypeHandlerBase::ChangesPending_Injected
Unity.Hierarchy.HierarchyNodeTypeHandlerBase::IntegrateChanges_Injected
Unity.Hierarchy.HierarchyNodeTypeHandlerBase::SearchMatch_Injected
Unity.Hierarchy.HierarchyNodeTypeHandlerBase::GetNodeTypeName_Injected
Unity.Hierarchy.HierarchyNodeTypeHandlerBase::SearchBegin_Injected
Unity.Hierarchy.HierarchyNodeTypeHandlerBase::SearchEnd_Injected
Unity.Hierarchy.HierarchyNodeTypeHandlerBase::GetDefaultNodeFlags_Injected
Unity.Hierarchy.HierarchyViewModel::Contains_Injected
Unity.Hierarchy.HierarchyViewModel::HasAllFlagsNode_Injected
Unity.Hierarchy.HierarchyViewModel::get_UpdateNeeded_Injected
Unity.Hierarchy.HierarchyViewModel::GetChildrenCount_Injected
Unity.Hierarchy.HierarchyViewModel::IndexOf_Injected
Unity.Hierarchy.HierarchyViewModel::Create_Injected
Unity.Hierarchy.HierarchyViewModel::ClearFlagsNode_Injected
Unity.Hierarchy.HierarchyViewModel::Destroy
Unity.Hierarchy.HierarchyViewModel::SetFlagsNode_Injected
Unity.Hierarchy.HierarchyViewModel::Update_Injected
Unity.Hierarchy.HierarchyViewModel::get_Query_Injected
Unity.IntegerTime.RationalTimeExtensions::Convert_Injected
Unity.IO.LowLevel.Unsafe.AsyncReadManager::CloseFileAsync_Injected
Unity.IO.LowLevel.Unsafe.AsyncReadManager::GetFileInfoInternal_Injected
Unity.IO.LowLevel.Unsafe.AsyncReadManager::OpenFileAsync_Internal_Injected
Unity.IO.LowLevel.Unsafe.AsyncReadManager::ReadWithHandlesInternal_NativeCopy_Injected
Unity.IO.LowLevel.Unsafe.FileHandle::IsFileHandleValid
Unity.IO.LowLevel.Unsafe.FileHandle::GetJobHandle_Internal_Injected
Unity.IO.LowLevel.Unsafe.ReadHandle::IsReadHandleValid_Injected
Unity.IO.LowLevel.Unsafe.ReadHandle::CancelInternal_Injected
Unity.IO.LowLevel.Unsafe.ReadHandle::GetJobHandle_Injected
Unity.IO.LowLevel.Unsafe.ReadHandle::ReleaseReadHandle_Injected
Unity.IO.LowLevel.Unsafe.ReadHandle::GetReadStatus_Injected
Unity.Jobs.JobHandle::ScheduleBatchedJobsAndIsCompleted
Unity.Jobs.JobHandle::CombineDependenciesInternal2_Injected
Unity.Jobs.JobHandle::CombineDependenciesInternalPtr_Injected
Unity.Jobs.JobHandle::ScheduleBatchedJobs
Unity.Jobs.JobHandle::ScheduleBatchedJobsAndComplete
Unity.Jobs.LowLevel.Unsafe.JobsUtility::GetWorkStealingRange
Unity.Jobs.LowLevel.Unsafe.JobsUtility::get_IsExecutingJob
Unity.Jobs.LowLevel.Unsafe.JobsUtility::GetJobQueueWorkerThreadCount
Unity.Jobs.LowLevel.Unsafe.JobsUtility::get_ThreadIndex
Unity.Jobs.LowLevel.Unsafe.JobsUtility::get_ThreadIndexCount
Unity.Jobs.LowLevel.Unsafe.JobsUtility::CreateJobReflectionData
Unity.Jobs.LowLevel.Unsafe.JobsUtility::ScheduleParallelForDeferArraySize_Injected
Unity.Jobs.LowLevel.Unsafe.JobsUtility::ScheduleParallelForTransform_Injected
Unity.Jobs.LowLevel.Unsafe.JobsUtility::ScheduleParallelFor_Injected
Unity.Jobs.LowLevel.Unsafe.JobsUtility::Schedule_Injected
Unity.Jobs.LowLevel.Unsafe.JobsUtility::set_JobCompilerEnabled
Unity.Profiling.LowLevel.Unsafe.ProfilerRecorderHandle::GetByName__Unmanaged_Injected
Unity.Profiling.LowLevel.Unsafe.ProfilerRecorderHandle::GetDescriptionInternal_Injected
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::CreateMarker_Injected
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::CreateMarker__Unmanaged
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::CreateCategory__Unmanaged
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::BeginSample
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::EndSample
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::GetCategoryDescription_Injected
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::SetMarkerMetadata__Unmanaged
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::CreateCounterValue__Unmanaged
Unity.Profiling.ProfilerRecorder::GetRunning_Injected
Unity.Profiling.ProfilerRecorder::GetValid_Injected
Unity.Profiling.ProfilerRecorder::GetCount_Injected
Unity.Profiling.ProfilerRecorder::GetLastValue_Injected
Unity.Profiling.ProfilerRecorder::Control_Injected
Unity.Profiling.ProfilerRecorder::Create_Injected
Unity.Profiling.ProfilerRecorder::GetSampleInternal_Injected
UnityEngine.Android.AndroidApplication::get_UnityPlayerRaw
UnityEngine.Android.AndroidGame::StopLoading
UnityEngine.AndroidJNI::CallBooleanMethodUnsafe
UnityEngine.AndroidJNI::CallStaticBooleanMethodUnsafe
UnityEngine.AndroidJNI::GetBooleanArrayElement
UnityEngine.AndroidJNI::GetBooleanField
UnityEngine.AndroidJNI::GetStaticBooleanField
UnityEngine.AndroidJNI::IsAssignableFrom
UnityEngine.AndroidJNI::IsInstanceOf
UnityEngine.AndroidJNI::IsSameObject
UnityEngine.AndroidJNI::CallCharMethodUnsafe
UnityEngine.AndroidJNI::CallStaticCharMethodUnsafe
UnityEngine.AndroidJNI::GetCharArrayElement
UnityEngine.AndroidJNI::GetCharField
UnityEngine.AndroidJNI::GetStaticCharField
UnityEngine.AndroidJNI::FromCharArray
UnityEngine.AndroidJNI::CallDoubleMethodUnsafe
UnityEngine.AndroidJNI::CallStaticDoubleMethodUnsafe
UnityEngine.AndroidJNI::GetDoubleArrayElement
UnityEngine.AndroidJNI::GetDoubleField
UnityEngine.AndroidJNI::GetStaticDoubleField
UnityEngine.AndroidJNI::FromDoubleArray
UnityEngine.AndroidJNI::CallShortMethodUnsafe
UnityEngine.AndroidJNI::CallStaticShortMethodUnsafe
UnityEngine.AndroidJNI::GetShortArrayElement
UnityEngine.AndroidJNI::GetShortField
UnityEngine.AndroidJNI::GetStaticShortField
UnityEngine.AndroidJNI::FromShortArray
UnityEngine.AndroidJNI::AttachCurrentThread
UnityEngine.AndroidJNI::CallIntMethodUnsafe
UnityEngine.AndroidJNI::CallStaticIntMethodUnsafe
UnityEngine.AndroidJNI::DetachCurrentThread
UnityEngine.AndroidJNI::EnsureLocalCapacity
UnityEngine.AndroidJNI::GetArrayLength
UnityEngine.AndroidJNI::GetIntArrayElement
UnityEngine.AndroidJNI::GetIntField
UnityEngine.AndroidJNI::GetStaticIntField
UnityEngine.AndroidJNI::GetStringLength
UnityEngine.AndroidJNI::GetStringUTFLength
UnityEngine.AndroidJNI::GetVersion
UnityEngine.AndroidJNI::PushLocalFrame
UnityEngine.AndroidJNI::RegisterNativesAndFree
UnityEngine.AndroidJNI::Throw
UnityEngine.AndroidJNI::ThrowNew_Injected
UnityEngine.AndroidJNI::UnregisterNatives
UnityEngine.AndroidJNI::FromIntArray
UnityEngine.AndroidJNI::CallLongMethodUnsafe
UnityEngine.AndroidJNI::CallStaticLongMethodUnsafe
UnityEngine.AndroidJNI::GetDirectBufferCapacity
UnityEngine.AndroidJNI::GetLongArrayElement
UnityEngine.AndroidJNI::GetLongField
UnityEngine.AndroidJNI::GetStaticLongField
UnityEngine.AndroidJNI::FromLongArray
UnityEngine.AndroidJNI::AllocObject
UnityEngine.AndroidJNI::CallObjectMethodUnsafe
UnityEngine.AndroidJNI::CallStaticObjectMethodUnsafe
UnityEngine.AndroidJNI::ConvertToBooleanArray_Injected
UnityEngine.AndroidJNI::ExceptionOccurred
UnityEngine.AndroidJNI::FindClass_Injected
UnityEngine.AndroidJNI::FromReflectedField
UnityEngine.AndroidJNI::FromReflectedMethod
UnityEngine.AndroidJNI::GetFieldID_Injected
UnityEngine.AndroidJNI::GetJavaVM
UnityEngine.AndroidJNI::GetMethodID_Injected
UnityEngine.AndroidJNI::GetObjectArrayElement
UnityEngine.AndroidJNI::GetObjectClass
UnityEngine.AndroidJNI::GetObjectField
UnityEngine.AndroidJNI::GetStaticFieldID_Injected
UnityEngine.AndroidJNI::GetStaticMethodID_Injected
UnityEngine.AndroidJNI::GetStaticObjectField
UnityEngine.AndroidJNI::GetSuperclass
UnityEngine.AndroidJNI::NewBooleanArray
UnityEngine.AndroidJNI::NewCharArray
UnityEngine.AndroidJNI::NewDirectByteBuffer
UnityEngine.AndroidJNI::NewDoubleArray
UnityEngine.AndroidJNI::NewFloatArray
UnityEngine.AndroidJNI::NewGlobalRef
UnityEngine.AndroidJNI::NewIntArray
UnityEngine.AndroidJNI::NewLocalRef
UnityEngine.AndroidJNI::NewLongArray
UnityEngine.AndroidJNI::NewObjectA
UnityEngine.AndroidJNI::NewObjectArray
UnityEngine.AndroidJNI::NewSByteArray
UnityEngine.AndroidJNI::NewShortArray
UnityEngine.AndroidJNI::NewStringFromStr_Injected
UnityEngine.AndroidJNI::NewStringUTF_Injected
UnityEngine.AndroidJNI::NewString_Injected
UnityEngine.AndroidJNI::NewWeakGlobalRef
UnityEngine.AndroidJNI::PopLocalFrame
UnityEngine.AndroidJNI::RegisterNativesAllocate
UnityEngine.AndroidJNI::ToByteArray_Injected
UnityEngine.AndroidJNI::ToCharArray
UnityEngine.AndroidJNI::ToDoubleArray
UnityEngine.AndroidJNI::ToFloatArray
UnityEngine.AndroidJNI::ToIntArray
UnityEngine.AndroidJNI::ToLongArray
UnityEngine.AndroidJNI::ToObjectArray
UnityEngine.AndroidJNI::ToReflectedField
UnityEngine.AndroidJNI::ToReflectedMethod
UnityEngine.AndroidJNI::ToSByteArray
UnityEngine.AndroidJNI::ToShortArray
UnityEngine.AndroidJNI::CallSByteMethodUnsafe
UnityEngine.AndroidJNI::CallStaticSByteMethodUnsafe
UnityEngine.AndroidJNI::GetSByteArrayElement
UnityEngine.AndroidJNI::GetSByteField
UnityEngine.AndroidJNI::GetStaticSByteField
UnityEngine.AndroidJNI::GetDirectBufferAddress
UnityEngine.AndroidJNI::FromSByteArray
UnityEngine.AndroidJNI::CallFloatMethodUnsafe
UnityEngine.AndroidJNI::CallStaticFloatMethodUnsafe
UnityEngine.AndroidJNI::GetFloatArrayElement
UnityEngine.AndroidJNI::GetFloatField
UnityEngine.AndroidJNI::GetStaticFloatField
UnityEngine.AndroidJNI::FromFloatArray
UnityEngine.AndroidJNI::GetQueueGlobalRefsCount
UnityEngine.AndroidJNI::CallStaticStringMethodUnsafeInternal_Injected
UnityEngine.AndroidJNI::CallStaticVoidMethodUnsafe
UnityEngine.AndroidJNI::CallStringMethodUnsafeInternal_Injected
UnityEngine.AndroidJNI::CallVoidMethodUnsafe
UnityEngine.AndroidJNI::DeleteGlobalRef
UnityEngine.AndroidJNI::DeleteLocalRef
UnityEngine.AndroidJNI::DeleteWeakGlobalRef
UnityEngine.AndroidJNI::ExceptionClear
UnityEngine.AndroidJNI::ExceptionDescribe
UnityEngine.AndroidJNI::FatalError_Injected
UnityEngine.AndroidJNI::FromBooleanArray_Injected
UnityEngine.AndroidJNI::FromByteArray_Injected
UnityEngine.AndroidJNI::FromObjectArray_Injected
UnityEngine.AndroidJNI::GetStaticStringFieldInternal_Injected
UnityEngine.AndroidJNI::GetStringCharsInternal_Injected
UnityEngine.AndroidJNI::GetStringFieldInternal_Injected
UnityEngine.AndroidJNI::GetStringUTFChars_Injected
UnityEngine.AndroidJNI::InvokeAttached
UnityEngine.AndroidJNI::QueueDeleteGlobalRef
UnityEngine.AndroidJNI::RegisterNativesSet_Injected
UnityEngine.AndroidJNI::ReleaseStringChars_Injected
UnityEngine.AndroidJNI::SetBooleanArrayElement
UnityEngine.AndroidJNI::SetBooleanField
UnityEngine.AndroidJNI::SetCharArrayElement
UnityEngine.AndroidJNI::SetCharField
UnityEngine.AndroidJNI::SetDoubleArrayElement
UnityEngine.AndroidJNI::SetDoubleField
UnityEngine.AndroidJNI::SetFloatArrayElement
UnityEngine.AndroidJNI::SetFloatField
UnityEngine.AndroidJNI::SetIntArrayElement
UnityEngine.AndroidJNI::SetIntField
UnityEngine.AndroidJNI::SetLongArrayElement
UnityEngine.AndroidJNI::SetLongField
UnityEngine.AndroidJNI::SetObjectArrayElement
UnityEngine.AndroidJNI::SetObjectField
UnityEngine.AndroidJNI::SetSByteArrayElement
UnityEngine.AndroidJNI::SetSByteField
UnityEngine.AndroidJNI::SetShortArrayElement
UnityEngine.AndroidJNI::SetShortField
UnityEngine.AndroidJNI::SetStaticBooleanField
UnityEngine.AndroidJNI::SetStaticCharField
UnityEngine.AndroidJNI::SetStaticDoubleField
UnityEngine.AndroidJNI::SetStaticFloatField
UnityEngine.AndroidJNI::SetStaticIntField
UnityEngine.AndroidJNI::SetStaticLongField
UnityEngine.AndroidJNI::SetStaticObjectField
UnityEngine.AndroidJNI::SetStaticSByteField
UnityEngine.AndroidJNI::SetStaticShortField
UnityEngine.AndroidJNI::SetStaticStringField_Injected
UnityEngine.AndroidJNI::SetStringField_Injected
UnityEngine.AndroidJNIHelper::get_debug
UnityEngine.AndroidJNIHelper::set_debug
UnityEngine.AnimationCurve::Internal_Equals_Injected
UnityEngine.AnimationCurve::AddKey_Injected
UnityEngine.AnimationCurve::AddKey_Internal_Injected
UnityEngine.AnimationCurve::GetHashCode_Injected
UnityEngine.AnimationCurve::MoveKey_Injected
UnityEngine.AnimationCurve::get_length_Injected
UnityEngine.AnimationCurve::Internal_Create_Injected
UnityEngine.AnimationCurve::Evaluate_Injected
UnityEngine.AnimationCurve::ClearKeys_Injected
UnityEngine.AnimationCurve::GetKey_Injected
UnityEngine.AnimationCurve::GetKeys_Injected
UnityEngine.AnimationCurve::Internal_CopyFrom_Injected
UnityEngine.AnimationCurve::Internal_Destroy
UnityEngine.AnimationCurve::RemoveKey_Injected
UnityEngine.AnimationCurve::SetKeys_Injected
UnityEngine.AnimationCurve::SmoothTangents_Injected
UnityEngine.Animations.AnimationLayerMixerPlayable::SetSingleLayerOptimizationInternal
UnityEngine.Animator::get_hasBoundPlayables_Injected
UnityEngine.Animator::ResetTriggerString_Injected
UnityEngine.Animator::SetTriggerString_Injected
UnityEngine.Application::get_isBatchMode
UnityEngine.Application::get_isFocused
UnityEngine.Application::get_isPlaying
UnityEngine.Application::get_runInBackground
UnityEngine.Application::OpenURL_Injected
UnityEngine.Application::get_streamingAssetsPath_Injected
UnityEngine.Application::get_platform
UnityEngine.AudioSettings::StartAudioOutput
UnityEngine.AudioSettings::StopAudioOutput
UnityEngine.Awaitable::IsNativeAwaitableCompleted
UnityEngine.Awaitable::ReleaseNativeAwaitable
UnityEngine.Behaviour::get_enabled_Injected
UnityEngine.Behaviour::get_isActiveAndEnabled_Injected
UnityEngine.Behaviour::set_enabled_Injected
UnityEngine.Bindings.BindingsAllocator::Free
UnityEngine.Bindings.BindingsAllocator::FreeNativeOwnedMemory
UnityEngine.Camera::GetCullingParameters_Internal_Injected
UnityEngine.Camera::get_allowDynamicResolution_Injected
UnityEngine.Camera::get_allowHDR_Injected
UnityEngine.Camera::get_allowMSAA_Injected
UnityEngine.Camera::get_orthographic_Injected
UnityEngine.Camera::get_stereoEnabled_Injected
UnityEngine.Camera::get_usePhysicalProperties_Injected
UnityEngine.Camera::GetAllCamerasCount
UnityEngine.Camera::GetAllCamerasImpl_Injected
UnityEngine.Camera::GetFilterMode_Injected
UnityEngine.Camera::get_cullingMask_Injected
UnityEngine.Camera::get_eventMask_Injected
UnityEngine.Camera::get_pixelHeight_Injected
UnityEngine.Camera::get_pixelWidth_Injected
UnityEngine.Camera::get_targetDisplay_Injected
UnityEngine.Camera::get_currentInternal_Injected
UnityEngine.Camera::get_main_Injected
UnityEngine.Camera::get_targetTexture_Injected
UnityEngine.Camera::get_aspect_Injected
UnityEngine.Camera::get_depth_Injected
UnityEngine.Camera::get_farClipPlane_Injected
UnityEngine.Camera::get_fieldOfView_Injected
UnityEngine.Camera::get_nearClipPlane_Injected
UnityEngine.Camera::get_orthographicSize_Injected
UnityEngine.Camera::ScreenPointToRay_Injected
UnityEngine.Camera::ScreenToViewportPoint_Injected
UnityEngine.Camera::SetStereoProjectionMatrix_Injected
UnityEngine.Camera::SetStereoViewMatrix_Injected
UnityEngine.Camera::SetupCurrent_Injected
UnityEngine.Camera::WorldToScreenPoint_Injected
UnityEngine.Camera::get_backgroundColor_Injected
UnityEngine.Camera::get_cameraToWorldMatrix_Injected
UnityEngine.Camera::get_pixelRect_Injected
UnityEngine.Camera::get_projectionMatrix_Injected
UnityEngine.Camera::get_rect_Injected
UnityEngine.Camera::get_worldToCameraMatrix_Injected
UnityEngine.Camera::set_backgroundColor_Injected
UnityEngine.Camera::set_clearFlags_Injected
UnityEngine.Camera::set_depthTextureMode_Injected
UnityEngine.Camera::set_fieldOfView_Injected
UnityEngine.Camera::set_rect_Injected
UnityEngine.Camera::set_targetTexture_Injected
UnityEngine.Camera::get_clearFlags_Injected
UnityEngine.Camera::get_cameraType_Injected
UnityEngine.Camera::get_depthTextureMode_Injected
UnityEngine.Camera::get_opaqueSortMode_Injected
UnityEngine.CameraRaycastHelper::RaycastTry2D_Injected
UnityEngine.CameraRaycastHelper::RaycastTry_Injected
UnityEngine.Canvas::get_isRootCanvas_Injected
UnityEngine.Canvas::get_overrideSorting_Injected
UnityEngine.Canvas::get_pixelPerfect_Injected
UnityEngine.Canvas::get_renderOrder_Injected
UnityEngine.Canvas::get_sortingLayerID_Injected
UnityEngine.Canvas::get_sortingOrder_Injected
UnityEngine.Canvas::get_targetDisplay_Injected
UnityEngine.Canvas::GetDefaultCanvasMaterial_Injected
UnityEngine.Canvas::GetETC1SupportedCanvasMaterial_Injected
UnityEngine.Canvas::get_rootCanvas_Injected
UnityEngine.Canvas::get_worldCamera_Injected
UnityEngine.Canvas::get_referencePixelsPerUnit_Injected
UnityEngine.Canvas::get_scaleFactor_Injected
UnityEngine.Canvas::SetExternalCanvasEnabled
UnityEngine.Canvas::get_pixelRect_Injected
UnityEngine.Canvas::get_renderingDisplaySize_Injected
UnityEngine.Canvas::set_additionalShaderChannels_Injected
UnityEngine.Canvas::set_overrideSorting_Injected
UnityEngine.Canvas::set_referencePixelsPerUnit_Injected
UnityEngine.Canvas::set_scaleFactor_Injected
UnityEngine.Canvas::set_sortingLayerID_Injected
UnityEngine.Canvas::set_sortingOrder_Injected
UnityEngine.Canvas::get_additionalShaderChannels_Injected
UnityEngine.Canvas::get_renderMode_Injected
UnityEngine.CanvasGroup::get_blocksRaycasts_Injected
UnityEngine.CanvasGroup::get_ignoreParentGroups_Injected
UnityEngine.CanvasGroup::get_interactable_Injected
UnityEngine.CanvasGroup::get_alpha_Injected
UnityEngine.CanvasGroup::set_alpha_Injected
UnityEngine.CanvasGroup::set_ignoreParentGroups_Injected
UnityEngine.CanvasRenderer::get_cullTransparentMesh_Injected
UnityEngine.CanvasRenderer::get_cull_Injected
UnityEngine.CanvasRenderer::get_hasMoved_Injected
UnityEngine.CanvasRenderer::get_absoluteDepth_Injected
UnityEngine.CanvasRenderer::get_materialCount_Injected
UnityEngine.CanvasRenderer::GetMaterial_Injected
UnityEngine.CanvasRenderer::Clear_Injected
UnityEngine.CanvasRenderer::CreateUIVertexStreamInternal
UnityEngine.CanvasRenderer::DisableRectClipping_Injected
UnityEngine.CanvasRenderer::EnableRectClipping_Injected
UnityEngine.CanvasRenderer::GetColor_Injected
UnityEngine.CanvasRenderer::SetAlphaTexture_Injected
UnityEngine.CanvasRenderer::SetColor_Injected
UnityEngine.CanvasRenderer::SetMaterial_Injected
UnityEngine.CanvasRenderer::SetMesh_Injected
UnityEngine.CanvasRenderer::SetPopMaterial_Injected
UnityEngine.CanvasRenderer::SetTexture_Injected
UnityEngine.CanvasRenderer::SplitIndicesStreamsInternal
UnityEngine.CanvasRenderer::SplitUIVertexStreamsInternal
UnityEngine.CanvasRenderer::set_clippingSoftness_Injected
UnityEngine.CanvasRenderer::set_cullTransparentMesh_Injected
UnityEngine.CanvasRenderer::set_cull_Injected
UnityEngine.CanvasRenderer::set_hasPopInstruction_Injected
UnityEngine.CanvasRenderer::set_materialCount_Injected
UnityEngine.CanvasRenderer::set_popMaterialCount_Injected
UnityEngine.Collider::get_enabled_Injected
UnityEngine.Collider::ClosestPoint_Injected
UnityEngine.ColorGamutUtility::GetColorPrimaries
UnityEngine.ColorGamutUtility::GetTransferFunction
UnityEngine.ColorGamutUtility::GetWhitePoint
UnityEngine.ColorUtility::DoTryParseHtmlColor_Injected
UnityEngine.Component::get_gameObject_Injected
UnityEngine.Component::get_transform_Injected
UnityEngine.Component::BroadcastMessage_Injected
UnityEngine.Component::GetComponentFastPath_Injected
UnityEngine.Component::GetComponentsForListInternal_Injected
UnityEngine.Component::SendMessage_Injected
UnityEngine.ComputeBuffer::get_count_Injected
UnityEngine.ComputeBuffer::get_stride_Injected
UnityEngine.ComputeBuffer::InitBuffer
UnityEngine.ComputeBuffer::DestroyBuffer_Injected
UnityEngine.ComputeBuffer::InternalSetData_Injected
UnityEngine.ComputeBuffer::InternalSetNativeData_Injected
UnityEngine.ComputeShader::FindKernel_Injected
UnityEngine.ComputeShader::DisableKeyword_Injected
UnityEngine.ComputeShader::Dispatch_Injected
UnityEngine.ComputeShader::EnableKeyword_Injected
UnityEngine.ComputeShader::Internal_SetBuffer_Injected
UnityEngine.ComputeShader::Internal_SetGraphicsBuffer_Injected
UnityEngine.ComputeShader::SetConstantComputeBuffer_Injected
UnityEngine.ComputeShader::SetIntArray_Injected
UnityEngine.ComputeShader::SetInt_Injected
UnityEngine.ComputeShader::SetShaderKeywords_Injected
UnityEngine.ComputeShader::get_keywordSpace_Injected
UnityEngine.ContactFilter2D::CheckConsistency
UnityEngine.Coroutine::ReleaseCoroutine
UnityEngine.Cubemap::Internal_CreateImpl
UnityEngine.Cubemap::get_isReadable_Injected
UnityEngine.Cubemap::ApplyImpl_Injected
UnityEngine.Cubemap::SetPixelImpl_Injected
UnityEngine.CubemapArray::Internal_CreateImpl
UnityEngine.CubemapArray::get_isReadable_Injected
UnityEngine.CubemapArray::ApplyImpl_Injected
UnityEngine.CubemapArray::SetPixels_Injected
UnityEngine.CullingGroup::QueryIndices_Injected
UnityEngine.CullingGroup::Init
UnityEngine.CullingGroup::DisposeInternal_Injected
UnityEngine.CullingGroup::FinalizerFailure_Injected
UnityEngine.CullingGroup::SetBoundingDistances_Injected
UnityEngine.CullingGroup::SetBoundingSphereCount_Injected
UnityEngine.CullingGroup::SetBoundingSpheres_Injected
UnityEngine.CullingGroup::SetDistanceReferencePoint_InternalVector3_Injected
UnityEngine.CullingGroup::set_targetCamera_Injected
UnityEngine.Cursor::SetCursor_Injected
UnityEngine.Cursor::get_lockState
UnityEngine.Debug::get_isDebugBuild
UnityEngine.Debug::ExtractStackTraceNoAlloc_Injected
UnityEngine.Debug::DrawLine_Injected
UnityEngine.DebugLogHandler::Internal_LogException_Injected
UnityEngine.DebugLogHandler::Internal_Log_Injected
UnityEngine.Display::RequiresSrgbBlitToBackbufferImpl
UnityEngine.Display::RelativeMouseAtImpl
UnityEngine.Display::GetRenderingExtImpl
UnityEngine.Display::GetSystemExtImpl
UnityEngine.Event::PopEvent_Injected
UnityEngine.Event::get_character_Injected
UnityEngine.Event::GetDoubleClickTime
UnityEngine.Event::GetEventCount
UnityEngine.Event::get_button_Injected
UnityEngine.Event::get_clickCount_Injected
UnityEngine.Event::get_displayIndex_Injected
UnityEngine.Event::Internal_Create
UnityEngine.Event::get_pressure_Injected
UnityEngine.Event::get_twist_Injected
UnityEngine.Event::CopyFromPtr_Injected
UnityEngine.Event::GetEventAtIndex_Injected
UnityEngine.Event::Internal_Destroy
UnityEngine.Event::Internal_SetNativeEvent
UnityEngine.Event::Internal_Use_Injected
UnityEngine.Event::get_commandName_Injected
UnityEngine.Event::get_delta_Injected
UnityEngine.Event::get_mousePosition_Injected
UnityEngine.Event::get_tilt_Injected
UnityEngine.Event::set_Internal_keyCode_Injected
UnityEngine.Event::set_character_Injected
UnityEngine.Event::set_commandName_Injected
UnityEngine.Event::set_delta_Injected
UnityEngine.Event::set_displayIndex_Injected
UnityEngine.Event::set_modifiers_Injected
UnityEngine.Event::set_mousePosition_Injected
UnityEngine.Event::set_type_Injected
UnityEngine.Event::get_modifiers_Injected
UnityEngine.Event::get_rawType_Injected
UnityEngine.Event::get_type_Injected
UnityEngine.Event::get_Internal_keyCode_Injected
UnityEngine.Event::get_penStatus_Injected
UnityEngine.Event::get_pointerType_Injected
UnityEngine.Experimental.Rendering.BuiltinRuntimeReflectionSystem::BuiltinUpdate
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::CanDecompressFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::HasAlphaChannel
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsAlphaOnlyFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsCompressedFormat_Native_TextureFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsCrunchFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsDepthFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsDepthStencilFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsFloatFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsHalfFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsPVRTCFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsSRGBFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsStencilFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetDepthBits
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetAlphaComponentCount
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetBlockSize
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetComponentCount
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetFormatString_Injected
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetDepthStencilFormatFromBitsLegacy_Native
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetFormat_Injected
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetGraphicsFormat_Native_RenderTextureFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetGraphicsFormat_Native_TextureFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetLinearFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetSRGBFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetRenderTextureFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetSwizzleA
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetSwizzleB
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetSwizzleG
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetSwizzleR
UnityEngine.Experimental.Rendering.ScriptableRuntimeReflectionSystemSettings::ScriptingDirtyReflectionSystemInstance
UnityEngine.Font::HasCharacter_Injected
UnityEngine.Font::get_dynamic_Injected
UnityEngine.Font::get_fontSize_Injected
UnityEngine.Font::get_material_Injected
UnityEngine.Font::GetOSFallbacks
UnityEngine.Font::GetPathsToOSFonts
UnityEngine.Font::Internal_CreateFont_Injected
UnityEngine.FrameTimingManager::GetLatestTimings_Injected
UnityEngine.FrameTimingManager::CaptureFrameTimings
UnityEngine.GameObject::GetComponentsInternal_Injected
UnityEngine.GameObject::get_activeInHierarchy_Injected
UnityEngine.GameObject::get_activeSelf_Injected
UnityEngine.GameObject::get_layer_Injected
UnityEngine.GameObject::GetComponentInChildren_Injected
UnityEngine.GameObject::GetComponentInParent_Injected
UnityEngine.GameObject::GetComponent_Injected
UnityEngine.GameObject::Internal_AddComponentWithType_Injected
UnityEngine.GameObject::TryGetComponentInternal_Injected
UnityEngine.GameObject::get_transform_Injected
UnityEngine.GameObject::get_sceneCullingMask_Injected
UnityEngine.GameObject::GetComponentFastPath_Injected
UnityEngine.GameObject::Internal_CreateGameObject_Injected
UnityEngine.GameObject::SendMessage_Injected
UnityEngine.GameObject::SetActive_Injected
UnityEngine.GameObject::TryGetComponentFastPath_Injected
UnityEngine.GameObject::get_scene_Injected
UnityEngine.GameObject::set_layer_Injected
UnityEngine.GeometryUtility::TestPlanesAABB_Injected
UnityEngine.GeometryUtility::Internal_ExtractPlanes_Injected
UnityEngine.Gizmos::DrawIcon_Injected
UnityEngine.Gizmos::DrawLine_Injected
UnityEngine.Gizmos::set_color_Injected
UnityEngine.Gizmos::set_matrix_Injected
UnityEngine.GL::get_wireframe
UnityEngine.GL::Begin
UnityEngine.GL::End
UnityEngine.GL::GLClear_Injected
UnityEngine.GL::GLLoadPixelMatrixScript
UnityEngine.GL::GetGPUProjectionMatrix_Injected
UnityEngine.GL::ImmediateColor
UnityEngine.GL::LoadOrtho
UnityEngine.GL::LoadProjectionMatrix_Injected
UnityEngine.GL::PopMatrix
UnityEngine.GL::PushMatrix
UnityEngine.GL::SetViewMatrix_Injected
UnityEngine.GL::TexCoord3
UnityEngine.GL::Vertex3
UnityEngine.GL::Viewport_Injected
UnityEngine.Gradient::Internal_Equals_Injected
UnityEngine.Gradient::Init
UnityEngine.Gradient::Cleanup_Injected
UnityEngine.Gradient::Evaluate_Injected
UnityEngine.Gradient::SetKeys_Injected
UnityEngine.Gradient::get_alphaKeys_Injected
UnityEngine.Gradient::get_colorKeys_Injected
UnityEngine.Gradient::set_colorSpace_Injected
UnityEngine.Gradient::set_mode_Injected
UnityEngine.Gradient::get_colorSpace_Injected
UnityEngine.Gradient::get_mode_Injected
UnityEngine.Graphics::GetPreserveFramebufferAlpha
UnityEngine.Graphics::Internal_GetMaxDrawMeshInstanceCount
UnityEngine.Graphics::Blit4_Injected
UnityEngine.Graphics::ClearRandomWriteTargets
UnityEngine.Graphics::CopyTexture_Region_Injected
UnityEngine.Graphics::CopyTexture_Slice_Injected
UnityEngine.Graphics::ExecuteCommandBuffer_Injected
UnityEngine.Graphics::Internal_DrawMeshInstanced_Injected
UnityEngine.Graphics::Internal_DrawMesh_Injected
UnityEngine.Graphics::Internal_SetNullRT
UnityEngine.Graphics::Internal_SetRTSimple_Injected
UnityEngine.Graphics::get_activeTier
UnityEngine.Graphics::GetMinOpenGLESVersion
UnityEngine.GraphicsBuffer::IsValidBuffer_Injected
UnityEngine.GraphicsBuffer::get_count_Injected
UnityEngine.GraphicsBuffer::get_stride_Injected
UnityEngine.GraphicsBuffer::InitBuffer
UnityEngine.GraphicsBuffer::DestroyBuffer_Injected
UnityEngine.GraphicsBuffer::EndBufferWrite_Injected
UnityEngine.GraphicsBuffer::InternalSetData_Injected
UnityEngine.GraphicsBuffer::InternalSetNativeData_Injected
UnityEngine.GraphicsBuffer::SetName_Injected
UnityEngine.GraphicsBuffer::get_bufferHandle_Injected
UnityEngine.GraphicsBuffer::BeginBufferWrite_Injected
UnityEngine.GraphicsBuffer::GetUsageFlags_Injected
UnityEngine.GUI::get_changed
UnityEngine.GUI::get_enabled
UnityEngine.GUI::get_backgroundColor_Injected
UnityEngine.GUI::get_color_Injected
UnityEngine.GUI::get_contentColor_Injected
UnityEngine.GUI::set_backgroundColor_Injected
UnityEngine.GUI::set_changed
UnityEngine.GUI::set_color_Injected
UnityEngine.GUI::set_contentColor_Injected
UnityEngine.GUI::set_enabled
UnityEngine.GUIClip::Internal_GetCount
UnityEngine.GUIClip::GetMatrix_Injected
UnityEngine.GUIClip::Internal_Pop
UnityEngine.GUIClip::Internal_PopParentClip
UnityEngine.GUIClip::Internal_PushParentClip_Injected
UnityEngine.GUIClip::SetMatrix_Injected
UnityEngine.GUIClip::get_visibleRect_Injected
UnityEngine.GUILayoutUtility::Internal_GetWindowRect_Injected
UnityEngine.GUILayoutUtility::Internal_MoveWindow_Injected
UnityEngine.GUIStyle::IsTooltipActive_Injected
UnityEngine.GUIStyle::get_richText_Injected
UnityEngine.GUIStyle::get_stretchHeight_Injected
UnityEngine.GUIStyle::get_stretchWidth_Injected
UnityEngine.GUIStyle::get_wordWrap_Injected
UnityEngine.GUIStyle::get_fontSize_Injected
UnityEngine.GUIStyle::GetDefaultFont_Injected
UnityEngine.GUIStyle::GetRectOffsetPtr_Injected
UnityEngine.GUIStyle::GetStyleStatePtr_Injected
UnityEngine.GUIStyle::Internal_Create
UnityEngine.GUIStyle::get_font_Injected
UnityEngine.GUIStyle::get_fixedHeight_Injected
UnityEngine.GUIStyle::get_fixedWidth_Injected
UnityEngine.GUIStyle::Internal_Destroy
UnityEngine.GUIStyle::Internal_DestroyTextGenerator
UnityEngine.GUIStyle::Internal_Draw2_Injected
UnityEngine.GUIStyle::Internal_Draw_Injected
UnityEngine.GUIStyle::Internal_GetTextRectOffset_Injected
UnityEngine.GUIStyle::SetDefaultFont_Injected
UnityEngine.GUIStyle::SetMouseTooltip_Injected
UnityEngine.GUIStyle::get_contentOffset_Injected
UnityEngine.GUIStyle::get_rawName_Injected
UnityEngine.GUIStyle::set_alignment_Injected
UnityEngine.GUIStyle::set_rawName_Injected
UnityEngine.GUIStyle::set_stretchHeight_Injected
UnityEngine.GUIStyle::get_fontStyle_Injected
UnityEngine.GUIStyle::get_imagePosition_Injected
UnityEngine.GUIStyle::get_alignment_Injected
UnityEngine.GUIStyle::get_clipping_Injected
UnityEngine.GUIStyleState::Init
UnityEngine.GUIStyleState::Cleanup_Injected
UnityEngine.GUIStyleState::set_textColor_Injected
UnityEngine.GUIUtility::HasFocusableControls
UnityEngine.GUIUtility::OwnsId
UnityEngine.GUIUtility::get_textFieldInput
UnityEngine.GUIUtility::CheckForTabEvent_Injected
UnityEngine.GUIUtility::Internal_GetControlID_Injected
UnityEngine.GUIUtility::Internal_GetHotControl
UnityEngine.GUIUtility::Internal_GetKeyboardControl
UnityEngine.GUIUtility::get_guiDepth
UnityEngine.GUIUtility::Internal_GetDefaultSkin
UnityEngine.GUIUtility::get_pixelsPerPoint
UnityEngine.GUIUtility::AlignRectToDevice_Injected
UnityEngine.GUIUtility::BeginContainerFromOwner_Injected
UnityEngine.GUIUtility::BeginContainer_Injected
UnityEngine.GUIUtility::Internal_EndContainer
UnityEngine.GUIUtility::Internal_ExitGUI
UnityEngine.GUIUtility::Internal_SetHotControl
UnityEngine.GUIUtility::Internal_SetKeyboardControl
UnityEngine.GUIUtility::SetKeyboardControlToFirstControlId
UnityEngine.GUIUtility::SetKeyboardControlToLastControlId
UnityEngine.GUIUtility::get_compositionString_Injected
UnityEngine.GUIUtility::get_systemCopyBuffer_Injected
UnityEngine.GUIUtility::set_compositionCursorPos_Injected
UnityEngine.GUIUtility::set_imeCompositionMode
UnityEngine.GUIUtility::set_pixelsPerPoint
UnityEngine.GUIUtility::set_systemCopyBuffer_Injected
UnityEngine.Hash128::ComputeFromPtr
UnityEngine.Hash128::Hash128ToStringImpl_Injected
UnityEngine.Hash128::Parse_Injected
UnityEngine.HDROutputSettings::GetActive
UnityEngine.HDROutputSettings::GetAutomaticHDRTonemapping
UnityEngine.HDROutputSettings::GetAvailable
UnityEngine.HDROutputSettings::GetHDRModeChangeRequested
UnityEngine.HDROutputSettings::GetMaxFullFrameToneMapLuminance
UnityEngine.HDROutputSettings::GetMaxToneMapLuminance
UnityEngine.HDROutputSettings::GetMinToneMapLuminance
UnityEngine.HDROutputSettings::GetPaperWhiteNits
UnityEngine.HDROutputSettings::RequestHDRModeChangeInternal
UnityEngine.HDROutputSettings::SetAutomaticHDRTonemapping
UnityEngine.HDROutputSettings::GetDisplayColorGamut
UnityEngine.HDROutputSettings::GetGraphicsFormat
UnityEngine.Input::CheckDisabled
UnityEngine.Input::GetKeyInt
UnityEngine.Input::GetMouseButton
UnityEngine.Input::GetMouseButtonDown
UnityEngine.Input::GetMouseButtonUp
UnityEngine.Input::GetMousePresentInternal
UnityEngine.Input::GetTouchSupportedInternal
UnityEngine.Input::get_anyKey
UnityEngine.Input::get_touchCount
UnityEngine.Input::ClearLastPenContactEvent
UnityEngine.Input::GetLastPenContactEvent_Injected
UnityEngine.Input::GetTouch_Injected
UnityEngine.Input::get_compositionCursorPos_Injected
UnityEngine.Input::get_compositionString_Injected
UnityEngine.Input::get_mousePosition_Injected
UnityEngine.Input::get_mouseScrollDelta_Injected
UnityEngine.Input::set_compositionCursorPos_Injected
UnityEngine.Input::set_imeCompositionMode
UnityEngine.Input::get_imeCompositionMode
UnityEngine.IntegratedSubsystem::IsRunning_Injected
UnityEngine.IntegratedSubsystem::SetHandle_Injected
UnityEngine.Internal.InputUnsafeUtility::GetButtonDown_Injected
UnityEngine.Internal.InputUnsafeUtility::GetButtonUp__Unmanaged
UnityEngine.Internal.InputUnsafeUtility::GetButton__Unmanaged
UnityEngine.Internal.InputUnsafeUtility::GetKeyDownString__Unmanaged
UnityEngine.Internal.InputUnsafeUtility::GetKeyString__Unmanaged
UnityEngine.Internal.InputUnsafeUtility::GetKeyUpString__Unmanaged
UnityEngine.Internal.InputUnsafeUtility::GetButtonDown__Unmanaged
UnityEngine.Internal.InputUnsafeUtility::GetAxisRaw_Injected
UnityEngine.Internal.InputUnsafeUtility::GetAxisRaw__Unmanaged
UnityEngine.Internal.InputUnsafeUtility::GetAxis_Injected
UnityEngine.Internal.InputUnsafeUtility::GetAxis__Unmanaged
UnityEngine.Jobs.TransformAccess::GetLocalScale
UnityEngine.Jobs.TransformAccess::GetLocalToWorldMatrix
UnityEngine.Jobs.TransformAccess::GetPosition
UnityEngine.Jobs.TransformAccess::GetRotation
UnityEngine.Jobs.TransformAccessArray::GetLength
UnityEngine.Jobs.TransformAccessArray::Create
UnityEngine.Jobs.TransformAccessArray::GetSortedToUserIndex
UnityEngine.Jobs.TransformAccessArray::GetSortedTransformAccess
UnityEngine.Jobs.TransformAccessArray::GetTransform_Injected
UnityEngine.Jobs.TransformAccessArray::Add_Injected
UnityEngine.Jobs.TransformAccessArray::DestroyTransformAccessArray
UnityEngine.Jobs.TransformAccessArray::RemoveAtSwapBack
UnityEngine.JsonUtility::FromJsonInternal_Injected
UnityEngine.JsonUtility::ToJsonInternal_Injected
UnityEngine.Light::get_enableSpotReflector_Injected
UnityEngine.Light::get_useColorTemperature_Injected
UnityEngine.Light::get_cookie_Injected
UnityEngine.Light::get_bounceIntensity_Injected
UnityEngine.Light::get_colorTemperature_Injected
UnityEngine.Light::get_cookieSize_Injected
UnityEngine.Light::get_dilatedRange_Injected
UnityEngine.Light::get_innerSpotAngle_Injected
UnityEngine.Light::get_intensity_Injected
UnityEngine.Light::get_luxAtDistance_Injected
UnityEngine.Light::get_range_Injected
UnityEngine.Light::get_shadowBias_Injected
UnityEngine.Light::get_shadowNearPlane_Injected
UnityEngine.Light::get_shadowNormalBias_Injected
UnityEngine.Light::get_shadowStrength_Injected
UnityEngine.Light::get_spotAngle_Injected
UnityEngine.Light::get_areaSize_Injected
UnityEngine.Light::get_bakingOutput_Injected
UnityEngine.Light::get_color_Injected
UnityEngine.Light::set_renderingLayerMask_Injected
UnityEngine.Light::get_shadows_Injected
UnityEngine.Light::get_type_Injected
UnityEngine.Light::get_shadowResolution_Injected
UnityEngine.LightProbesQuery::Create
UnityEngine.LightProbesQuery::CalculateInterpolatedLightAndOcclusionProbes
UnityEngine.LightProbesQuery::Destroy
UnityEngine.LODGroup::get_size_Injected
UnityEngine.LODGroup::get_localReferencePoint_Injected
UnityEngine.LowLevel.PlayerLoop::SetPlayerLoopInternal
UnityEngine.LowLevel.PlayerLoop::GetCurrentPlayerLoopInternal
UnityEngine.Material::HasFloatImpl_Injected
UnityEngine.Material::HasProperty_Injected
UnityEngine.Material::SetPass_Injected
UnityEngine.Material::get_enableInstancing_Injected
UnityEngine.Material::ComputeCRC_Injected
UnityEngine.Material::FindPass_Injected
UnityEngine.Material::GetFirstPropertyNameIdByAttribute_Injected
UnityEngine.Material::get_passCount_Injected
UnityEngine.Material::GetTextureImpl_Injected
UnityEngine.Material::get_shader_Injected
UnityEngine.Material::GetFloatImpl_Injected
UnityEngine.Material::GetShaderKeywords_Injected
UnityEngine.Material::CopyPropertiesFromMaterial_Injected
UnityEngine.Material::CreateWithMaterial_Injected
UnityEngine.Material::CreateWithShader_Injected
UnityEngine.Material::CreateWithString
UnityEngine.Material::DisableKeyword_Injected
UnityEngine.Material::DisableLocalKeyword_Injected
UnityEngine.Material::EnableKeyword_Injected
UnityEngine.Material::EnableLocalKeyword_Injected
UnityEngine.Material::GetColorImpl_Injected
UnityEngine.Material::SetColorImpl_Injected
UnityEngine.Material::SetConstantBufferImpl_Injected
UnityEngine.Material::SetEnabledKeywords_Injected
UnityEngine.Material::SetFloatArrayImpl_Injected
UnityEngine.Material::SetFloatImpl_Injected
UnityEngine.Material::SetGraphicsBufferImpl_Injected
UnityEngine.Material::SetIntImpl_Injected
UnityEngine.Material::SetMatrixArrayImpl_Injected
UnityEngine.Material::SetMatrixImpl_Injected
UnityEngine.Material::SetShaderKeywords_Injected
UnityEngine.Material::SetTextureImpl_Injected
UnityEngine.Material::SetVectorArrayImpl_Injected
UnityEngine.Material::set_enableInstancing_Injected
UnityEngine.Material::set_renderQueue_Injected
UnityEngine.Material::set_shader_Injected
UnityEngine.MaterialPropertyBlock::CreateImpl
UnityEngine.MaterialPropertyBlock::Clear_Injected
UnityEngine.MaterialPropertyBlock::DestroyImpl
UnityEngine.MaterialPropertyBlock::SetBufferImpl_Injected
UnityEngine.MaterialPropertyBlock::SetConstantBufferImpl_Injected
UnityEngine.MaterialPropertyBlock::SetFloatArrayImpl_Injected
UnityEngine.MaterialPropertyBlock::SetFloatImpl_Injected
UnityEngine.MaterialPropertyBlock::SetIntImpl_Injected
UnityEngine.MaterialPropertyBlock::SetMatrixArrayImpl_Injected
UnityEngine.MaterialPropertyBlock::SetMatrixImpl_Injected
UnityEngine.MaterialPropertyBlock::SetRenderTextureImpl_Injected
UnityEngine.MaterialPropertyBlock::SetTextureImpl_Injected
UnityEngine.MaterialPropertyBlock::SetVectorArrayImpl_Injected
UnityEngine.MaterialPropertyBlock::SetVectorImpl_Injected
UnityEngine.Mathf::GammaToLinearSpace
UnityEngine.Mathf::LinearToGammaSpace
UnityEngine.Mathf::FloatToHalf
UnityEngine.Mathf::CorrelatedColorTemperatureToRGB_Injected
UnityEngine.Matrix4x4::Inverse3DAffine_Injected
UnityEngine.Matrix4x4::DecomposeProjection_Injected
UnityEngine.Matrix4x4::Frustum_Injected
UnityEngine.Matrix4x4::GetLossyScale_Injected
UnityEngine.Matrix4x4::GetRotation_Injected
UnityEngine.Matrix4x4::Inverse_Injected
UnityEngine.Matrix4x4::LookAt_Injected
UnityEngine.Matrix4x4::Ortho_Injected
UnityEngine.Matrix4x4::Perspective_Injected
UnityEngine.Matrix4x4::TRS_Injected
UnityEngine.Matrix4x4::Transpose_Injected
UnityEngine.Mesh::GetAllocArrayFromChannelImpl_Injected
UnityEngine.Mesh::HasVertexAttribute_Injected
UnityEngine.Mesh::get_canAccess_Injected
UnityEngine.Mesh::get_subMeshCount_Injected
UnityEngine.Mesh::get_vertexCount_Injected
UnityEngine.Mesh::GetIndexCountImpl_Injected
UnityEngine.Mesh::ClearImpl_Injected
UnityEngine.Mesh::GetArrayFromChannelImpl_Injected
UnityEngine.Mesh::GetIndicesImpl_Injected
UnityEngine.Mesh::Internal_Create
UnityEngine.Mesh::MarkDynamicImpl_Injected
UnityEngine.Mesh::PrintErrorCantAccessChannel_Injected
UnityEngine.Mesh::RecalculateBoundsImpl_Injected
UnityEngine.Mesh::SetArrayForChannelImpl_Injected
UnityEngine.Mesh::SetIndicesImpl_Injected
UnityEngine.Mesh::UploadMeshDataImpl_Injected
UnityEngine.Mesh::get_bounds_Injected
UnityEngine.Mesh::set_bounds_Injected
UnityEngine.Mesh::set_indexFormat_Injected
UnityEngine.MeshFilter::set_sharedMesh_Injected
UnityEngine.MonoBehaviour::Internal_IsInvokingAll_Injected
UnityEngine.MonoBehaviour::IsInvoking_Injected
UnityEngine.MonoBehaviour::IsObjectMonoBehaviour_Injected
UnityEngine.MonoBehaviour::get_didAwake_Injected
UnityEngine.MonoBehaviour::get_didStart_Injected
UnityEngine.MonoBehaviour::get_useGUILayout_Injected
UnityEngine.MonoBehaviour::CancelInvoke_Injected
UnityEngine.MonoBehaviour::GetScriptClassName_Injected
UnityEngine.MonoBehaviour::Internal_CancelInvokeAll_Injected
UnityEngine.MonoBehaviour::InvokeDelayed_Injected
UnityEngine.MonoBehaviour::OnCancellationTokenCreated_Injected
UnityEngine.MonoBehaviour::StopAllCoroutines_Injected
UnityEngine.MonoBehaviour::StopCoroutineFromEnumeratorManaged_Injected
UnityEngine.MonoBehaviour::StopCoroutineManaged_Injected
UnityEngine.MonoBehaviour::StopCoroutine_Injected
UnityEngine.MonoBehaviour::set_useGUILayout_Injected
UnityEngine.MonoBehaviour::StartCoroutineManaged2_Injected
UnityEngine.MonoBehaviour::StartCoroutineManaged_Injected
UnityEngine.NameFormatter::FormatVariableName_Injected
UnityEngine.Object::IsPersistent_Injected
UnityEngine.Object::GetOffsetOfInstanceIDInCPlusPlusObject
UnityEngine.Object::FindObjectFromInstanceID_Injected
UnityEngine.Object::ForceLoadFromInstanceID_Injected
UnityEngine.Object::Internal_CloneSingleWithParent_Injected
UnityEngine.Object::Internal_CloneSingle_Injected
UnityEngine.Object::DestroyImmediate_Injected
UnityEngine.Object::Destroy_Injected
UnityEngine.Object::DontDestroyOnLoad_Injected
UnityEngine.Object::GetName_Injected
UnityEngine.Object::SetName_Injected
UnityEngine.Object::ToString_Injected
UnityEngine.Object::set_hideFlags_Injected
UnityEngine.Object::get_hideFlags_Injected
UnityEngine.Object::FindObjectsByType
UnityEngine.ObjectDispatcher::CreateDispatchSystemHandle
UnityEngine.ObjectDispatcher::DestroyDispatchSystemHandle
UnityEngine.ObjectDispatcher::DispatchTransformDataChangesAndClear
UnityEngine.ObjectDispatcher::DispatchTypeChangesAndClear
UnityEngine.ObjectDispatcher::EnableTransformTracking
UnityEngine.ObjectDispatcher::EnableTypeTracking
UnityEngine.ObjectGUIState::Internal_Create
UnityEngine.ObjectGUIState::Internal_Destroy
UnityEngine.Physics::get_invokeCollisionCallbacks
UnityEngine.Physics::get_reuseCollisionCallbacks
UnityEngine.Physics::GetBodyByInstanceID_Injected
UnityEngine.Physics::GetColliderByInstanceID_Injected
UnityEngine.Physics::Internal_RaycastAll_Injected
UnityEngine.Physics::SendOnCollisionEnter_Injected
UnityEngine.Physics::SendOnCollisionExit_Injected
UnityEngine.Physics::SendOnCollisionStay_Injected
UnityEngine.Physics::get_defaultPhysicsScene_Injected
UnityEngine.Physics2D::get_queriesHitTriggers
UnityEngine.Physics2D::GetRayIntersectionAll_Internal_Injected
UnityEngine.PhysicsScene::Internal_RaycastTest_Injected
UnityEngine.PhysicsScene::Internal_Raycast_Injected
UnityEngine.PhysicsScene::Internal_RaycastNonAlloc_Injected
UnityEngine.PhysicsScene2D::GetRayIntersectionArray_Internal_Injected
UnityEngine.PhysicsScene2D::RaycastArray_Internal_Injected
UnityEngine.PhysicsScene2D::RaycastList_Internal_Injected
UnityEngine.PhysicsScene2D::GetRayIntersection_Internal_Injected
UnityEngine.PhysicsScene2D::Raycast_Internal_Injected
UnityEngine.Playables.PlayableHandle::IsValid
UnityEngine.Playables.PlayableHandle::GetPlayableType
UnityEngine.PlayerConnectionInternal::IsConnected
UnityEngine.PlayerConnectionInternal::TrySendMessage_Injected
UnityEngine.PlayerConnectionInternal::DisconnectAll
UnityEngine.PlayerConnectionInternal::Initialize
UnityEngine.PlayerConnectionInternal::PollInternal
UnityEngine.PlayerConnectionInternal::RegisterInternal_Injected
UnityEngine.PlayerConnectionInternal::SendMessage_Injected
UnityEngine.PlayerConnectionInternal::UnregisterInternal_Injected
UnityEngine.Profiling.Profiler::GetRuntimeMemorySizeLong_Injected
UnityEngine.PropertyNameUtils::PropertyNameFromString_Injected
UnityEngine.QualitySettings::get_billboardsFaceCameraPosition
UnityEngine.QualitySettings::get_antiAliasing
UnityEngine.QualitySettings::get_maximumLODLevel
UnityEngine.QualitySettings::get_lodBias
UnityEngine.QualitySettings::set_antiAliasing
UnityEngine.QualitySettings::set_enableLODCrossFade
UnityEngine.QualitySettings::get_activeColorSpace
UnityEngine.QualitySettings::get_shadowmaskMode
UnityEngine.Quaternion::AngleAxis_Injected
UnityEngine.Quaternion::Internal_FromEulerRad_Injected
UnityEngine.Quaternion::Internal_ToAxisAngleRad_Injected
UnityEngine.Quaternion::Internal_ToEulerRad_Injected
UnityEngine.Quaternion::Inverse_Injected
UnityEngine.Quaternion::LookRotation_Injected
UnityEngine.Random::Range
UnityEngine.Random::get_value
UnityEngine.Random::InitState
UnityEngine.Random::get_state_Injected
UnityEngine.Random::set_state_Injected
UnityEngine.RectOffset::get_bottom_Injected
UnityEngine.RectOffset::get_horizontal_Injected
UnityEngine.RectOffset::get_left_Injected
UnityEngine.RectOffset::get_right_Injected
UnityEngine.RectOffset::get_top_Injected
UnityEngine.RectOffset::get_vertical_Injected
UnityEngine.RectOffset::InternalCreate
UnityEngine.RectOffset::InternalDestroy
UnityEngine.RectOffset::Remove_Injected
UnityEngine.RectTransform::get_anchorMax_Injected
UnityEngine.RectTransform::get_anchorMin_Injected
UnityEngine.RectTransform::get_anchoredPosition_Injected
UnityEngine.RectTransform::get_pivot_Injected
UnityEngine.RectTransform::get_rect_Injected
UnityEngine.RectTransform::get_sizeDelta_Injected
UnityEngine.RectTransform::set_anchorMax_Injected
UnityEngine.RectTransform::set_anchorMin_Injected
UnityEngine.RectTransform::set_anchoredPosition_Injected
UnityEngine.RectTransform::set_pivot_Injected
UnityEngine.RectTransform::set_sizeDelta_Injected
UnityEngine.RectTransformUtility::PointInRectangle_Injected
UnityEngine.RectTransformUtility::PixelAdjustPoint_Injected
UnityEngine.RectTransformUtility::PixelAdjustRect_Injected
UnityEngine.ReflectionProbe::get_defaultTexture_Injected
UnityEngine.ReflectionProbe::get_defaultTextureHDRDecodeValues_Injected
UnityEngine.ReflectionProbe::get_refreshMode_Injected
UnityEngine.Renderer::get_enabled_Injected
UnityEngine.Renderer::get_sortingGroupID_Injected
UnityEngine.Renderer::get_sortingGroupOrder_Injected
UnityEngine.Renderer::get_sortingLayerID_Injected
UnityEngine.Renderer::get_sortingOrder_Injected
UnityEngine.Renderer::GetMaterial_Injected
UnityEngine.Renderer::GetSharedMaterial_Injected
UnityEngine.Renderer::SetMaterial_Injected
UnityEngine.Renderer::set_allowGPUDrivenRendering_Injected
UnityEngine.Renderer::set_enabled_Injected
UnityEngine.Renderer::set_localBounds_Injected
UnityEngine.Renderer::set_receiveShadows_Injected
UnityEngine.Renderer::set_shadowCastingMode_Injected
UnityEngine.Renderer::set_smallMeshCulling_Injected
UnityEngine.Renderer::set_sortingLayerID_Injected
UnityEngine.Renderer::set_sortingOrder_Injected
UnityEngine.Rendering.AsyncGPUReadback::Request_Internal_GraphicsBuffer_1_Injected
UnityEngine.Rendering.AsyncGPUReadback::Request_Internal_GraphicsBuffer_2_Injected
UnityEngine.Rendering.AsyncGPUReadbackRequest::HasError
UnityEngine.Rendering.AsyncGPUReadbackRequest::IsDone
UnityEngine.Rendering.AsyncGPUReadbackRequest::GetLayerCount
UnityEngine.Rendering.AsyncGPUReadbackRequest::GetLayerDataSize
UnityEngine.Rendering.AsyncGPUReadbackRequest::GetDataRaw
UnityEngine.Rendering.AsyncGPUReadbackRequest::SetScriptingCallback
UnityEngine.Rendering.AsyncGPUReadbackRequest::WaitForCompletion
UnityEngine.Rendering.BatchRendererGroup::OcclusionTestAABB_Injected
UnityEngine.Rendering.BatchRendererGroup::Create
UnityEngine.Rendering.BatchRendererGroup::AddDrawCommandBatch_Injected
UnityEngine.Rendering.BatchRendererGroup::Destroy
UnityEngine.Rendering.BatchRendererGroup::RegisterMaterials_Injected
UnityEngine.Rendering.BatchRendererGroup::RegisterMeshes_Injected
UnityEngine.Rendering.BatchRendererGroup::RemoveDrawCommandBatch_Injected
UnityEngine.Rendering.BatchRendererGroup::UnregisterMaterial_Injected
UnityEngine.Rendering.BatchRendererGroup::UnregisterMesh_Injected
UnityEngine.Rendering.BatchRendererGroup::GetBufferTarget
UnityEngine.Rendering.CommandBuffer::ValidateAgainstExecutionFlags_Injected
UnityEngine.Rendering.CommandBuffer::get_sizeInBytes_Injected
UnityEngine.Rendering.CommandBuffer::CreateGPUFence_Internal_Injected
UnityEngine.Rendering.CommandBuffer::InitBuffer
UnityEngine.Rendering.CommandBuffer::BeginRenderPass_Internal_Injected
UnityEngine.Rendering.CommandBuffer::BeginSample_CustomSampler_Injected
UnityEngine.Rendering.CommandBuffer::BeginSample_Injected
UnityEngine.Rendering.CommandBuffer::Blit_Identifier_Injected
UnityEngine.Rendering.CommandBuffer::Blit_Texture_Injected
UnityEngine.Rendering.CommandBuffer::ClearRandomWriteTargets_Injected
UnityEngine.Rendering.CommandBuffer::ClearRenderTargetMulti_Internal_Injected
UnityEngine.Rendering.CommandBuffer::ClearRenderTargetSingle_Internal_Injected
UnityEngine.Rendering.CommandBuffer::Clear_Injected
UnityEngine.Rendering.CommandBuffer::ConfigureFoveatedRendering_Injected
UnityEngine.Rendering.CommandBuffer::CopyCounterValueCC_Injected
UnityEngine.Rendering.CommandBuffer::CopyCounterValueCG_Injected
UnityEngine.Rendering.CommandBuffer::CopyCounterValueGC_Injected
UnityEngine.Rendering.CommandBuffer::CopyCounterValueGG_Injected
UnityEngine.Rendering.CommandBuffer::CopyTexture_Internal_Injected
UnityEngine.Rendering.CommandBuffer::DisableComputeKeyword_Injected
UnityEngine.Rendering.CommandBuffer::DisableGlobalKeyword_Injected
UnityEngine.Rendering.CommandBuffer::DisableMaterialKeyword_Injected
UnityEngine.Rendering.CommandBuffer::DisableScissorRect_Injected
UnityEngine.Rendering.CommandBuffer::DisableShaderKeyword_Injected
UnityEngine.Rendering.CommandBuffer::EnableComputeKeyword_Injected
UnityEngine.Rendering.CommandBuffer::EnableGlobalKeyword_Injected
UnityEngine.Rendering.CommandBuffer::EnableMaterialKeyword_Injected
UnityEngine.Rendering.CommandBuffer::EnableScissorRect_Injected
UnityEngine.Rendering.CommandBuffer::EnableShaderKeyword_Injected
UnityEngine.Rendering.CommandBuffer::EndRenderPass_Internal_Injected
UnityEngine.Rendering.CommandBuffer::EndSample_CustomSampler_Injected
UnityEngine.Rendering.CommandBuffer::EndSample_Injected
UnityEngine.Rendering.CommandBuffer::IncrementUpdateCount_Injected
UnityEngine.Rendering.CommandBuffer::InternalSetComputeBufferCounterValue_Injected
UnityEngine.Rendering.CommandBuffer::InternalSetComputeBufferData_Injected
UnityEngine.Rendering.CommandBuffer::InternalSetComputeBufferNativeData_Injected
UnityEngine.Rendering.CommandBuffer::InternalSetGraphicsBufferCounterValue_Injected
UnityEngine.Rendering.CommandBuffer::InternalSetGraphicsBufferData_Injected
UnityEngine.Rendering.CommandBuffer::InternalSetGraphicsBufferNativeData_Injected
UnityEngine.Rendering.CommandBuffer::Internal_BuildRayTracingAccelerationStructure_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DispatchComputeIndirectGraphicsBuffer_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DispatchComputeIndirect_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DispatchCompute_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DispatchRays_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DrawMeshInstancedIndirectGraphicsBuffer_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DrawMeshInstancedIndirect_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DrawMeshInstancedProcedural_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DrawMeshInstanced_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DrawMesh_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DrawMultipleMeshes_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DrawOcclusionMesh_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DrawProceduralIndexedIndirectGraphicsBuffer_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DrawProceduralIndexedIndirect_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DrawProceduralIndexed_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DrawProceduralIndirectGraphicsBuffer_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DrawProceduralIndirect_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DrawProcedural_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DrawRendererList_Injected
UnityEngine.Rendering.CommandBuffer::Internal_DrawRenderer_Injected
UnityEngine.Rendering.CommandBuffer::Internal_RequestAsyncReadback_1_Injected
UnityEngine.Rendering.CommandBuffer::Internal_RequestAsyncReadback_2_Injected
UnityEngine.Rendering.CommandBuffer::Internal_RequestAsyncReadback_3_Injected
UnityEngine.Rendering.CommandBuffer::Internal_RequestAsyncReadback_4_Injected
UnityEngine.Rendering.CommandBuffer::Internal_RequestAsyncReadback_5_Injected
UnityEngine.Rendering.CommandBuffer::Internal_RequestAsyncReadback_6_Injected
UnityEngine.Rendering.CommandBuffer::Internal_RequestAsyncReadback_7_Injected
UnityEngine.Rendering.CommandBuffer::Internal_RequestAsyncReadback_8_Injected
UnityEngine.Rendering.CommandBuffer::Internal_RequestAsyncReadback_9_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetComputeBufferParam_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetComputeConstantComputeBufferParam_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetComputeConstantGraphicsBufferParam_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetComputeFloats_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetComputeGraphicsBufferHandleParam_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetComputeGraphicsBufferParam_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetComputeInts_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetComputeRayTracingAccelerationStructure_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetComputeTextureParam_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetRayTracingAccelerationStructure_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetRayTracingComputeBufferParam_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetRayTracingConstantComputeBufferParam_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetRayTracingConstantGraphicsBufferParam_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetRayTracingFloatParam_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetRayTracingFloats_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetRayTracingGraphicsBufferHandleParam_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetRayTracingGraphicsBufferParam_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetRayTracingIntParam_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetRayTracingInts_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetRayTracingMatrixArrayParam_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetRayTracingMatrixParam_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetRayTracingTextureParam_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetRayTracingVectorArrayParam_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetRayTracingVectorParam_Injected
UnityEngine.Rendering.CommandBuffer::Internal_SetSinglePassStereo_Injected
UnityEngine.Rendering.CommandBuffer::InvokeOnRenderObjectCallbacks_Internal_Injected
UnityEngine.Rendering.CommandBuffer::IssuePluginCustomBlitInternal_Injected
UnityEngine.Rendering.CommandBuffer::IssuePluginCustomTextureUpdateInternal_Injected
UnityEngine.Rendering.CommandBuffer::IssuePluginEventAndDataInternal_Injected
UnityEngine.Rendering.CommandBuffer::IssuePluginEventInternal_Injected
UnityEngine.Rendering.CommandBuffer::MarkLateLatchMatrixShaderPropertyID_Injected
UnityEngine.Rendering.CommandBuffer::NextSubPass_Internal_Injected
UnityEngine.Rendering.CommandBuffer::ReleaseBuffer_Injected
UnityEngine.Rendering.CommandBuffer::SetComputeFloatParam_Injected
UnityEngine.Rendering.CommandBuffer::SetComputeIntParam_Injected
UnityEngine.Rendering.CommandBuffer::SetComputeKeyword_Injected
UnityEngine.Rendering.CommandBuffer::SetComputeMatrixArrayParam_Injected
UnityEngine.Rendering.CommandBuffer::SetComputeMatrixParam_Injected
UnityEngine.Rendering.CommandBuffer::SetComputeVectorArrayParam_Injected
UnityEngine.Rendering.CommandBuffer::SetComputeVectorParam_Injected
UnityEngine.Rendering.CommandBuffer::SetExecutionFlags_Injected
UnityEngine.Rendering.CommandBuffer::SetFoveatedRenderingMode_Injected
UnityEngine.Rendering.CommandBuffer::SetGlobalBufferInternal_Injected
UnityEngine.Rendering.CommandBuffer::SetGlobalColor_Injected
UnityEngine.Rendering.CommandBuffer::SetGlobalConstantBufferInternal_Injected
UnityEngine.Rendering.CommandBuffer::SetGlobalConstantGraphicsBufferInternal_Injected
UnityEngine.Rendering.CommandBuffer::SetGlobalDepthBias_Injected
UnityEngine.Rendering.CommandBuffer::SetGlobalFloatArrayListImpl_Injected
UnityEngine.Rendering.CommandBuffer::SetGlobalFloatArray_Injected
UnityEngine.Rendering.CommandBuffer::SetGlobalFloat_Injected
UnityEngine.Rendering.CommandBuffer::SetGlobalGraphicsBufferInternal_Injected
UnityEngine.Rendering.CommandBuffer::SetGlobalInt_Injected
UnityEngine.Rendering.CommandBuffer::SetGlobalInteger_Injected
UnityEngine.Rendering.CommandBuffer::SetGlobalKeyword_Injected
UnityEngine.Rendering.CommandBuffer::SetGlobalMatrixArrayListImpl_Injected
UnityEngine.Rendering.CommandBuffer::SetGlobalMatrixArray_Injected
UnityEngine.Rendering.CommandBuffer::SetGlobalMatrix_Injected
UnityEngine.Rendering.CommandBuffer::SetGlobalTexture_Impl_Injected
UnityEngine.Rendering.CommandBuffer::SetGlobalVectorArrayListImpl_Injected
UnityEngine.Rendering.CommandBuffer::SetGlobalVectorArray_Injected
UnityEngine.Rendering.CommandBuffer::SetGlobalVector_Injected
UnityEngine.Rendering.CommandBuffer::SetInstanceMultiplier_Injected
UnityEngine.Rendering.CommandBuffer::SetInvertCulling_Injected
UnityEngine.Rendering.CommandBuffer::SetLateLatchProjectionMatrices_Injected
UnityEngine.Rendering.CommandBuffer::SetMaterialKeyword_Injected
UnityEngine.Rendering.CommandBuffer::SetRandomWriteTarget_GraphicsBuffer_Injected
UnityEngine.Rendering.CommandBuffer::SetRandomWriteTarget_Texture_Injected
UnityEngine.Rendering.CommandBuffer::SetRenderTargetColorDepthSubtarget_Injected
UnityEngine.Rendering.CommandBuffer::SetRenderTargetColorDepth_Internal_Injected
UnityEngine.Rendering.CommandBuffer::SetRenderTargetMultiSubtarget_Injected
UnityEngine.Rendering.CommandBuffer::SetRenderTargetMulti_Internal_Injected
UnityEngine.Rendering.CommandBuffer::SetRenderTargetSingle_Internal_Injected
UnityEngine.Rendering.CommandBuffer::SetShadowSamplingMode_Impl_Injected
UnityEngine.Rendering.CommandBuffer::SetViewProjectionMatrices_Injected
UnityEngine.Rendering.CommandBuffer::SetViewport_Injected
UnityEngine.Rendering.CommandBuffer::SetWireframe_Injected
UnityEngine.Rendering.CommandBuffer::SetupCameraProperties_Internal_Injected
UnityEngine.Rendering.CommandBuffer::UnmarkLateLatchMatrix_Injected
UnityEngine.Rendering.CommandBuffer::WaitOnGPUFence_Internal_Injected
UnityEngine.Rendering.CommandBuffer::get_name_Injected
UnityEngine.Rendering.CommandBuffer::set_name_Injected
UnityEngine.Rendering.CommandBufferExtensions::Internal_SwitchIntoFastMemory_Injected
UnityEngine.Rendering.CommandBufferExtensions::Internal_SwitchOutOfFastMemory_Injected
UnityEngine.Rendering.CullingResults::ComputeDirectionalShadowMatricesAndCullingPrimitives_Injected
UnityEngine.Rendering.CullingResults::ComputePointShadowMatricesAndCullingPrimitives
UnityEngine.Rendering.CullingResults::ComputeSpotShadowMatricesAndCullingPrimitives
UnityEngine.Rendering.CullingResults::GetShadowCasterBounds
UnityEngine.Rendering.CullingResults::GetLightIndexCount
UnityEngine.Rendering.CullingResults::GetLightIndexMapSize
UnityEngine.Rendering.CullingResults::GetReflectionProbeIndexCount
UnityEngine.Rendering.CullingResults::FillLightAndReflectionProbeIndices_Injected
UnityEngine.Rendering.CullingResults::FillLightIndexMap
UnityEngine.Rendering.CullingResults::SetLightIndexMap
UnityEngine.Rendering.GlobalKeyword::GetGlobalKeywordCount
UnityEngine.Rendering.GlobalKeyword::GetGlobalKeywordIndex_Injected
UnityEngine.Rendering.GlobalKeyword::CreateGlobalKeyword_Injected
UnityEngine.Rendering.GPUDrivenProcessor::ClassifyMaterialsImpl_Injected
UnityEngine.Rendering.GPUDrivenProcessor::Internal_Create
UnityEngine.Rendering.GPUDrivenProcessor::ClearMaterialFilters_Injected
UnityEngine.Rendering.GPUDrivenProcessor::DisableGPUDrivenRendering_Injected
UnityEngine.Rendering.GPUDrivenProcessor::DispatchLODGroupData_Injected
UnityEngine.Rendering.GPUDrivenProcessor::EnableGPUDrivenRenderingAndDispatchRendererData_Injected
UnityEngine.Rendering.GPUDrivenProcessor::Internal_Destroy
UnityEngine.Rendering.GPUDrivenProcessor::set_enablePartialRendering_Injected
UnityEngine.Rendering.GraphicsFence::GetVersionNumber
UnityEngine.Rendering.GraphicsSettings::HasShaderDefine
UnityEngine.Rendering.GraphicsSettings::get_lightsUseLinearIntensity
UnityEngine.Rendering.GraphicsSettings::Internal_GetSettingsForRenderPipeline_Injected
UnityEngine.Rendering.GraphicsSettings::get_INTERNAL_currentRenderPipeline_Injected
UnityEngine.Rendering.GraphicsSettings::set_lightsUseColorTemperature
UnityEngine.Rendering.GraphicsSettings::set_lightsUseLinearIntensity
UnityEngine.Rendering.GraphicsSettings::set_useScriptableRenderPipelineBatching
UnityEngine.Rendering.LocalKeyword::GetComputeShaderKeywordCount_Injected
UnityEngine.Rendering.LocalKeyword::GetComputeShaderKeywordIndex_Injected
UnityEngine.Rendering.LocalKeyword::GetShaderKeywordCount_Injected
UnityEngine.Rendering.LocalKeyword::GetShaderKeywordIndex_Injected
UnityEngine.Rendering.RayTracingAccelerationStructure::Destroy_Injected
UnityEngine.Rendering.RendererList::get_isValid
UnityEngine.Rendering.ScriptableRenderContext::HasInvokeOnRenderObjectCallbacks_Internal
UnityEngine.Rendering.ScriptableRenderContext::SubmitForRenderPassValidation_Internal
UnityEngine.Rendering.ScriptableRenderContext::BeginRenderPass_Internal
UnityEngine.Rendering.ScriptableRenderContext::BeginSubPass_Internal
UnityEngine.Rendering.ScriptableRenderContext::CreateGizmoRendererList_Internal_Injected
UnityEngine.Rendering.ScriptableRenderContext::CreateRendererList_Internal_Injected
UnityEngine.Rendering.ScriptableRenderContext::CreateShadowRendererList_Internal_Injected
UnityEngine.Rendering.ScriptableRenderContext::CreateSkyboxRendererList_Internal_Injected
UnityEngine.Rendering.ScriptableRenderContext::CreateUIOverlayRendererList_Internal_Injected
UnityEngine.Rendering.ScriptableRenderContext::CreateWireOverlayRendererList_Internal_Injected
UnityEngine.Rendering.ScriptableRenderContext::DrawWireOverlay_Impl_Injected
UnityEngine.Rendering.ScriptableRenderContext::EmitGeometryForCamera_Injected
UnityEngine.Rendering.ScriptableRenderContext::EndRenderPass_Internal
UnityEngine.Rendering.ScriptableRenderContext::EndSubPass_Internal
UnityEngine.Rendering.ScriptableRenderContext::ExecuteCommandBufferAsync_Internal_Injected
UnityEngine.Rendering.ScriptableRenderContext::ExecuteCommandBuffer_Internal_Injected
UnityEngine.Rendering.ScriptableRenderContext::GetCameras_Internal
UnityEngine.Rendering.ScriptableRenderContext::InitializeSortSettings_Injected
UnityEngine.Rendering.ScriptableRenderContext::Internal_CullShadowCasters_Injected
UnityEngine.Rendering.ScriptableRenderContext::Internal_Cull_Injected
UnityEngine.Rendering.ScriptableRenderContext::PrepareRendererListsAsync_Internal
UnityEngine.Rendering.ScriptableRenderContext::SetupCameraProperties_Internal_Injected
UnityEngine.Rendering.ScriptableRenderContext::Submit_Internal
UnityEngine.Rendering.ScriptableRenderContext::QueryRendererListStatus_Internal_Injected
UnityEngine.Rendering.ShaderKeyword::GetGlobalKeywordCount
UnityEngine.Rendering.ShaderKeyword::GetGlobalKeywordIndex_Injected
UnityEngine.Rendering.ShaderKeyword::CreateGlobalKeyword_Injected
UnityEngine.Rendering.ShaderKeywordSet::IsKeywordNameEnabled_Injected
UnityEngine.Rendering.SortingGroup::get_invalidSortingGroupID
UnityEngine.Rendering.SortingGroup::get_sortingLayerID_Injected
UnityEngine.Rendering.SortingGroup::get_sortingOrder_Injected
UnityEngine.Rendering.SortingGroup::GetSortingGroupByIndex_Injected
UnityEngine.Rendering.SpeedTreeWindManager::UpdateWindAndWriteBufferWindParams_Injected
UnityEngine.Rendering.Watermark::IsVisible
UnityEngine.RenderingLayerMask::GetDefinedRenderingLayerCount
UnityEngine.RenderingLayerMask::GetRenderingLayerCount
UnityEngine.RenderingLayerMask::GetDefinedRenderingLayerNames
UnityEngine.RenderingLayerMask::GetDefinedRenderingLayersCombinedMaskValue
UnityEngine.RenderingLayerMask::RenderingLayerToName_Injected
UnityEngine.RenderSettings::get_fog
UnityEngine.RenderSettings::get_skybox_Injected
UnityEngine.RenderSettings::get_sun_Injected
UnityEngine.RenderSettings::get_reflectionIntensity
UnityEngine.RenderSettings::get_ambientEquatorColor_Injected
UnityEngine.RenderSettings::get_ambientGroundColor_Injected
UnityEngine.RenderSettings::get_ambientProbe_Injected
UnityEngine.RenderSettings::get_ambientSkyColor_Injected
UnityEngine.RenderSettings::get_subtractiveShadowColor_Injected
UnityEngine.RenderTexture::Create_Injected
UnityEngine.RenderTexture::get_bindTextureMS_Injected
UnityEngine.RenderTexture::get_sRGB_Injected
UnityEngine.RenderTexture::get_useDynamicScaleExplicit_Injected
UnityEngine.RenderTexture::get_useDynamicScale_Injected
UnityEngine.RenderTexture::get_useMipMap_Injected
UnityEngine.RenderTexture::get_antiAliasing_Injected
UnityEngine.RenderTexture::get_height_Injected
UnityEngine.RenderTexture::get_volumeDepth_Injected
UnityEngine.RenderTexture::get_width_Injected
UnityEngine.RenderTexture::GetActive_Injected
UnityEngine.RenderTexture::GetTemporary_Internal_Injected
UnityEngine.RenderTexture::ApplyDynamicScale_Injected
UnityEngine.RenderTexture::GetColorBuffer_Injected
UnityEngine.RenderTexture::GetDepthBuffer_Injected
UnityEngine.RenderTexture::GetDescriptor_Injected
UnityEngine.RenderTexture::Internal_Create
UnityEngine.RenderTexture::ReleaseTemporary_Injected
UnityEngine.RenderTexture::Release_Injected
UnityEngine.RenderTexture::SetActive_Injected
UnityEngine.RenderTexture::SetColorFormat_Injected
UnityEngine.RenderTexture::SetMipMapCount_Injected
UnityEngine.RenderTexture::SetRenderTextureDescriptor_Injected
UnityEngine.RenderTexture::SetSRGBReadWrite_Injected
UnityEngine.RenderTexture::SetShadowSamplingMode_Injected
UnityEngine.RenderTexture::set_autoGenerateMips_Injected
UnityEngine.RenderTexture::set_depthStencilFormat_Injected
UnityEngine.RenderTexture::set_dimension_Injected
UnityEngine.RenderTexture::set_enableRandomWrite_Injected
UnityEngine.RenderTexture::set_height_Injected
UnityEngine.RenderTexture::set_useDynamicScale_Injected
UnityEngine.RenderTexture::set_useMipMap_Injected
UnityEngine.RenderTexture::set_volumeDepth_Injected
UnityEngine.RenderTexture::set_width_Injected
UnityEngine.RenderTexture::GetColorFormat_Injected
UnityEngine.RenderTexture::get_depthStencilFormat_Injected
UnityEngine.RenderTexture::get_dimension_Injected
UnityEngine.Resources::GetBuiltinResource_Injected
UnityEngine.ResourcesAPIInternal::FindShaderByName_Injected
UnityEngine.ResourcesAPIInternal::Load_Injected
UnityEngine.ResourcesAPIInternal::FindObjectsOfTypeAll
UnityEngine.ScalableBufferManager::get_heightScaleFactor
UnityEngine.ScalableBufferManager::get_widthScaleFactor
UnityEngine.ScalableBufferManager::ResizeBuffers
UnityEngine.SceneManagement.Scene::GetGUIDInternal_Injected
UnityEngine.SceneManagement.SceneManager::GetActiveScene_Injected
UnityEngine.Screen::get_fullScreen
UnityEngine.Screen::GetMSAASamples
UnityEngine.Screen::get_height
UnityEngine.Screen::get_width
UnityEngine.Screen::get_dpi
UnityEngine.Screen::SetMSAASamples
UnityEngine.Screen::get_safeArea_Injected
UnityEngine.Screen::GetScreenOrientation
UnityEngine.ScriptableObject::CreateScriptableObjectInstanceFromType_Injected
UnityEngine.ScriptableObject::CreateScriptableObject
UnityEngine.ScriptingRuntime::GetAllUserAssemblies
UnityEngine.Shader::get_isSupported_Injected
UnityEngine.Shader::PropertyToID_Injected
UnityEngine.Shader::TagToID_Injected
UnityEngine.Shader::get_passCount_Injected
UnityEngine.Shader::DisableKeyword_Injected
UnityEngine.Shader::EnableKeyword_Injected
UnityEngine.Shader::SetGlobalConstantBufferImpl_Injected
UnityEngine.Shader::SetGlobalFloatImpl
UnityEngine.Shader::SetGlobalTextureImpl_Injected
UnityEngine.Shader::SetGlobalVectorImpl_Injected
UnityEngine.Shader::get_keywordSpace_Injected
UnityEngine.Shader::set_globalRenderPipeline_Injected
UnityEngine.Skybox::get_material_Injected
UnityEngine.SortingLayer::GetLayerValueFromID
UnityEngine.Sprite::GetPacked_Injected
UnityEngine.Sprite::GetPackingRotation_Injected
UnityEngine.Sprite::CreateSprite_Injected
UnityEngine.Sprite::get_associatedAlphaSplitTexture_Injected
UnityEngine.Sprite::get_texture_Injected
UnityEngine.Sprite::get_pixelsPerUnit_Injected
UnityEngine.Sprite::get_triangles_Injected
UnityEngine.Sprite::GetInnerUVs_Injected
UnityEngine.Sprite::GetOuterUVs_Injected
UnityEngine.Sprite::GetPadding_Injected
UnityEngine.Sprite::get_border_Injected
UnityEngine.Sprite::get_bounds_Injected
UnityEngine.Sprite::get_pivot_Injected
UnityEngine.Sprite::get_rect_Injected
UnityEngine.Sprite::get_uv_Injected
UnityEngine.Sprite::get_vertices_Injected
UnityEngine.SubsystemDescriptorBindings::GetId_Injected
UnityEngine.SubsystemManager::StaticConstructScriptingClassMap
UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore::ReportSingleSubsystemAnalytics_Injected
UnityEngine.SystemInfo::GetGraphicsUVStartsAtTop
UnityEngine.SystemInfo::HasHiddenSurfaceRemovalOnGPU
UnityEngine.SystemInfo::HasRenderTextureNative
UnityEngine.SystemInfo::IsFormatSupported
UnityEngine.SystemInfo::SupportsComputeShaders
UnityEngine.SystemInfo::SupportsGPUFence
UnityEngine.SystemInfo::SupportsIndirectArgumentsBuffer
UnityEngine.SystemInfo::SupportsInstancing
UnityEngine.SystemInfo::SupportsMultisampleAutoResolve
UnityEngine.SystemInfo::SupportsMultisampleResolveDepth
UnityEngine.SystemInfo::SupportsMultisampleResolveStencil
UnityEngine.SystemInfo::SupportsMultisampledBackBuffer
UnityEngine.SystemInfo::SupportsMultiview
UnityEngine.SystemInfo::SupportsRenderTargetArrayIndexFromVertexShader
UnityEngine.SystemInfo::SupportsShadows
UnityEngine.SystemInfo::SupportsStoreAndResolveAction
UnityEngine.SystemInfo::SupportsTextureFormatNative
UnityEngine.SystemInfo::UsesLoadStoreActions
UnityEngine.SystemInfo::UsesReversedZBuffer
UnityEngine.SystemInfo::GetGraphicsDeviceVendorID
UnityEngine.SystemInfo::GetGraphicsShaderLevel
UnityEngine.SystemInfo::GetMaxRenderTextureSize
UnityEngine.SystemInfo::GetMaxTextureSize
UnityEngine.SystemInfo::GetRenderTextureSupportedMSAASampleCount_Injected
UnityEngine.SystemInfo::SupportedRenderTargetCount
UnityEngine.SystemInfo::SupportsMultisampledTextures
UnityEngine.SystemInfo::MaxGraphicsBufferSize
UnityEngine.SystemInfo::GetDeviceModel_Injected
UnityEngine.SystemInfo::GetGraphicsDeviceName_Injected
UnityEngine.SystemInfo::GetGraphicsDeviceVendor_Injected
UnityEngine.SystemInfo::GetOperatingSystem_Injected
UnityEngine.SystemInfo::GetProcessorType_Injected
UnityEngine.SystemInfo::GetDeviceType
UnityEngine.SystemInfo::GetCompatibleFormat
UnityEngine.SystemInfo::GetGraphicsFormat
UnityEngine.SystemInfo::GetHDRDisplaySupportFlags
UnityEngine.SystemInfo::GetOperatingSystemFamily
UnityEngine.SystemInfo::GetCopyTextureSupport
UnityEngine.SystemInfo::GetFoveatedRenderingCaps
UnityEngine.SystemInfo::GetGraphicsDeviceType
UnityEngine.Terrain::get_allowAutoConnect_Injected
UnityEngine.Terrain::get_groupingID_Injected
UnityEngine.Terrain::get_terrainData_Injected
UnityEngine.Terrain::SetNeighbors_Injected
UnityEngine.Terrain::get_activeTerrains
UnityEngine.TerrainData::GetBoundaryValue
UnityEngine.TerrainData::GetAlphamapResolutionInternal_Injected
UnityEngine.TerrainData::get_size_Injected
UnityEngine.TerrainData::get_users_Injected
UnityEngine.TextAsset::get_bytes_Injected
UnityEngine.TextAsset::GetDataSize_Injected
UnityEngine.TextAsset::GetDataPtr_Injected
UnityEngine.TextAsset::Internal_CreateInstance_Injected
UnityEngine.TextCore.LowLevel.FontEngine::TryAddGlyphToTexture_Internal_Injected
UnityEngine.TextCore.LowLevel.FontEngine::TryAddGlyphsToTexture_Internal_Injected
UnityEngine.TextCore.LowLevel.FontEngine::TryGetGlyphWithIndexValue_Internal
UnityEngine.TextCore.LowLevel.FontEngine::TryGetGlyphWithUnicodeValue_Internal
UnityEngine.TextCore.LowLevel.FontEngine::TryGetSystemFontReference_Internal_Injected
UnityEngine.TextCore.LowLevel.FontEngine::GetFaceInfo_Internal
UnityEngine.TextCore.LowLevel.FontEngine::GetLigatureSubstitutionRecordsFromMarshallingArray
UnityEngine.TextCore.LowLevel.FontEngine::GetMarkToBaseAdjustmentRecordsFromMarshallingArray_Injected
UnityEngine.TextCore.LowLevel.FontEngine::GetMarkToMarkAdjustmentRecordsFromMarshallingArray_Injected
UnityEngine.TextCore.LowLevel.FontEngine::GetPairAdjustmentRecordsFromMarshallingArray_Injected
UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace_Internal_Injected
UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace_With_Size_And_FaceIndex_Internal_Injected
UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace_With_Size_and_FaceIndex_FromFont_Internal_Injected
UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace_With_Size_by_FamilyName_and_StyleName_Internal_Injected
UnityEngine.TextCore.LowLevel.FontEngine::PopulateLigatureSubstitutionRecordMarshallingArray_Injected
UnityEngine.TextCore.LowLevel.FontEngine::PopulateMarkToBaseAdjustmentRecordMarshallingArray_Injected
UnityEngine.TextCore.LowLevel.FontEngine::PopulateMarkToMarkAdjustmentRecordMarshallingArray_Injected
UnityEngine.TextCore.LowLevel.FontEngine::PopulatePairAdjustmentRecordMarshallingArray_Injected
UnityEngine.TextCore.LowLevel.FontEngine::PopulatePairAdjustmentRecordMarshallingArray_from_KernTable_Injected
UnityEngine.TextCore.LowLevel.FontEngine::UnloadFontFace_Internal
UnityEngine.TextCore.LowLevel.FontEngine::GetFontFaces_Internal
UnityEngine.TextCore.LowLevel.FontEngine::GetGlyphIndex
UnityEngine.TextCore.LowLevel.FontEngine::GetVariantGlyphIndex
UnityEngine.TextCore.LowLevel.FontEngine::GetAllMarkToBaseAdjustmentRecords_Injected
UnityEngine.TextCore.LowLevel.FontEngine::GetAllMarkToMarkAdjustmentRecords_Injected
UnityEngine.TextCore.LowLevel.FontEngine::GetAllPairAdjustmentRecords_Injected
UnityEngine.TextCore.LowLevel.FontEngine::ResetAtlasTexture_Injected
UnityEngine.TextCore.LowLevel.FontEngine::SetTextureUploadMode
UnityEngine.TextCore.LowLevel.FontEngine::GetAllLigatureSubstitutionRecords
UnityEngine.TextCore.Text.FontAsset::Create_Injected
UnityEngine.TextCore.Text.FontAsset::Destroy
UnityEngine.TextCore.Text.FontAsset::UpdateFaceInfo_Injected
UnityEngine.TextCore.Text.FontAsset::UpdateFallbacks_Injected
UnityEngine.TextCore.Text.FontAsset::UpdateWeightFallbacks_Injected
UnityEngine.TextCore.Text.TextGenerationInfo::Create
UnityEngine.TextCore.Text.TextGenerationInfo::Destroy
UnityEngine.TextCore.Text.TextLib::FindIntersectingLink_Injected
UnityEngine.TextCore.Text.TextLib::GetInstance_Injected
UnityEngine.TextCore.Text.TextLib::GenerateTextInternal_Injected
UnityEngine.TextCore.Text.TextLib::MeasureText_Injected
UnityEngine.TextCore.Text.TextSelectionService::GetCursorLogicalIndexFromPosition_Injected
UnityEngine.TextCore.Text.TextSelectionService::GetEndOfPreviousWord
UnityEngine.TextCore.Text.TextSelectionService::GetFirstCharacterIndexOnLine
UnityEngine.TextCore.Text.TextSelectionService::GetLastCharacterIndexOnLine
UnityEngine.TextCore.Text.TextSelectionService::GetLineNumber
UnityEngine.TextCore.Text.TextSelectionService::GetStartOfNextWord
UnityEngine.TextCore.Text.TextSelectionService::LineDownCharacterPosition
UnityEngine.TextCore.Text.TextSelectionService::LineUpCharacterPosition
UnityEngine.TextCore.Text.TextSelectionService::NextCodePointIndex
UnityEngine.TextCore.Text.TextSelectionService::PreviousCodePointIndex
UnityEngine.TextCore.Text.TextSelectionService::GetCharacterHeightFromIndex
UnityEngine.TextCore.Text.TextSelectionService::GetLineHeight
UnityEngine.TextCore.Text.TextSelectionService::GetCursorPositionFromLogicalIndex_Injected
UnityEngine.TextCore.Text.TextSelectionService::GetHighlightRectangles_Injected
UnityEngine.TextCore.Text.TextSelectionService::SelectCurrentParagraph
UnityEngine.TextCore.Text.TextSelectionService::SelectCurrentWord
UnityEngine.TextCore.Text.TextSelectionService::SelectToEndOfParagraph
UnityEngine.TextCore.Text.TextSelectionService::SelectToNextParagraph
UnityEngine.TextCore.Text.TextSelectionService::SelectToPreviousParagraph
UnityEngine.TextCore.Text.TextSelectionService::SelectToStartOfParagraph
UnityEngine.TextCore.Text.TextSelectionService::Substring_Injected
UnityEngine.TextGenerator::Populate_Internal_Injected
UnityEngine.TextGenerator::get_characterCount_Injected
UnityEngine.TextGenerator::get_lineCount_Injected
UnityEngine.TextGenerator::Internal_Create
UnityEngine.TextGenerator::GetCharactersInternal_Injected
UnityEngine.TextGenerator::GetLinesInternal_Injected
UnityEngine.TextGenerator::GetVerticesInternal_Injected
UnityEngine.TextGenerator::Internal_Destroy
UnityEngine.TextGenerator::get_rectExtents_Injected
UnityEngine.Texture::get_isReadable_Injected
UnityEngine.Texture::get_streamingTextureDiscardUnusedMips
UnityEngine.Texture::GetDataHeight_Injected
UnityEngine.Texture::GetDataWidth_Injected
UnityEngine.Texture::Internal_GetActiveTextureColorSpace_Injected
UnityEngine.Texture::get_anisoLevel_Injected
UnityEngine.Texture::get_mipmapCount_Injected
UnityEngine.Texture::get_mipMapBias_Injected
UnityEngine.Texture::get_updateCount_Injected
UnityEngine.Texture::GetPixelDataOffset_Injected
UnityEngine.Texture::GetPixelDataSize_Injected
UnityEngine.Texture::IncrementUpdateCount_Injected
UnityEngine.Texture::SetStreamingTextureMaterialDebugPropertiesWithSlot
UnityEngine.Texture::get_texelSize_Injected
UnityEngine.Texture::set_anisoLevel_Injected
UnityEngine.Texture::set_filterMode_Injected
UnityEngine.Texture::set_mipMapBias_Injected
UnityEngine.Texture::set_streamingTextureDiscardUnusedMips
UnityEngine.Texture::set_wrapModeU_Injected
UnityEngine.Texture::set_wrapModeV_Injected
UnityEngine.Texture::set_wrapModeW_Injected
UnityEngine.Texture::set_wrapMode_Injected
UnityEngine.Texture::get_filterMode_Injected
UnityEngine.Texture::GetDimension_Injected
UnityEngine.Texture::get_wrapMode_Injected
UnityEngine.Texture2D::Internal_CreateImpl_Injected
UnityEngine.Texture2D::ReinitializeImpl_Injected
UnityEngine.Texture2D::ReinitializeWithTextureFormatImpl_Injected
UnityEngine.Texture2D::get_isReadable_Injected
UnityEngine.Texture2D::GetWritableImageData_Injected
UnityEngine.Texture2D::get_blackTexture_Injected
UnityEngine.Texture2D::get_whiteTexture_Injected
UnityEngine.Texture2D::GetImageDataSize_Injected
UnityEngine.Texture2D::ApplyImpl_Injected
UnityEngine.Texture2D::GetPixelBilinearImpl_Injected
UnityEngine.Texture2D::SetAllPixels32_Injected
UnityEngine.Texture2D::SetPixelImpl_Injected
UnityEngine.Texture2D::SetPixelsImpl_Injected
UnityEngine.Texture2D::get_format_Injected
UnityEngine.Texture2DArray::Internal_CreateImpl_Injected
UnityEngine.Texture2DArray::get_isReadable_Injected
UnityEngine.Texture2DArray::get_allSlices
UnityEngine.Texture3D::Internal_CreateImpl
UnityEngine.Texture3D::get_isReadable_Injected
UnityEngine.Texture3D::GetImageData_Injected
UnityEngine.Texture3D::ApplyImpl_Injected
UnityEngine.Texture3D::SetPixelImpl_Injected
UnityEngine.Texture3D::SetPixels_Injected
UnityEngine.Time::get_realtimeSinceStartupAsDouble
UnityEngine.Time::get_frameCount
UnityEngine.Time::get_renderedFrameCount
UnityEngine.Time::get_deltaTime
UnityEngine.Time::get_fixedUnscaledTime
UnityEngine.Time::get_realtimeSinceStartup
UnityEngine.Time::get_smoothDeltaTime
UnityEngine.Time::get_time
UnityEngine.Time::get_unscaledDeltaTime
UnityEngine.Time::get_unscaledTime
UnityEngine.Time::get_timeAsRational_Injected
UnityEngine.TouchScreenKeyboard::IsInPlaceEditingAllowed
UnityEngine.TouchScreenKeyboard::get_active_Injected
UnityEngine.TouchScreenKeyboard::get_canGetSelection_Injected
UnityEngine.TouchScreenKeyboard::get_canSetSelection_Injected
UnityEngine.TouchScreenKeyboard::TouchScreenKeyboard_InternalConstructorHelper_Injected
UnityEngine.TouchScreenKeyboard::GetSelection
UnityEngine.TouchScreenKeyboard::Internal_Destroy
UnityEngine.TouchScreenKeyboard::SetSelection
UnityEngine.TouchScreenKeyboard::get_text_Injected
UnityEngine.TouchScreenKeyboard::set_active_Injected
UnityEngine.TouchScreenKeyboard::set_characterLimit_Injected
UnityEngine.TouchScreenKeyboard::set_hideInput
UnityEngine.TouchScreenKeyboard::set_text_Injected
UnityEngine.TouchScreenKeyboard::get_inputFieldAppearance
UnityEngine.TouchScreenKeyboard::get_status_Injected
UnityEngine.Transform::IsChildOf_Injected
UnityEngine.Transform::get_hasChanged_Injected
UnityEngine.Transform::get_childCount_Injected
UnityEngine.Transform::FindRelativeTransformWithPath_Injected
UnityEngine.Transform::GetChild_Injected
UnityEngine.Transform::GetParent_Injected
UnityEngine.Transform::InverseTransformPoint_Injected
UnityEngine.Transform::SetAsFirstSibling_Injected
UnityEngine.Transform::SetLocalPositionAndRotation_Injected
UnityEngine.Transform::SetParent_Injected
UnityEngine.Transform::TransformDirection_Injected
UnityEngine.Transform::TransformPoint_Injected
UnityEngine.Transform::get_localPosition_Injected
UnityEngine.Transform::get_localRotation_Injected
UnityEngine.Transform::get_localScale_Injected
UnityEngine.Transform::get_localToWorldMatrix_Injected
UnityEngine.Transform::get_lossyScale_Injected
UnityEngine.Transform::get_position_Injected
UnityEngine.Transform::get_rotation_Injected
UnityEngine.Transform::get_worldToLocalMatrix_Injected
UnityEngine.Transform::set_hasChanged_Injected
UnityEngine.Transform::set_localPosition_Injected
UnityEngine.Transform::set_localRotation_Injected
UnityEngine.Transform::set_localScale_Injected
UnityEngine.Transform::set_position_Injected
UnityEngine.Transform::set_rotation_Injected
UnityEngine.U2D.SpriteAtlas::CanBindTo_Injected
UnityEngine.U2D.SpriteAtlasManager::Register_Injected
UnityEngine.UIElements.Layout.LayoutNative::CalculateLayout
UnityEngine.UIElements.MeshBuilderNative::MakeBorder_Injected
UnityEngine.UIElements.MeshBuilderNative::MakeSolidRect_Injected
UnityEngine.UIElements.MeshBuilderNative::MakeTexturedRect_Injected
UnityEngine.UIElements.MeshBuilderNative::MakeVectorGraphics9SliceBackground_Injected
UnityEngine.UIElements.MeshBuilderNative::MakeVectorGraphicsStretchBackground_Injected
UnityEngine.UIElements.UIElementsRuntimeUtilityNative::RegisterPlayerloopCallback
UnityEngine.UIElements.UIElementsRuntimeUtilityNative::UnregisterPlayerloopCallback
UnityEngine.UIElements.UIElementsRuntimeUtilityNative::VisualElementCreation
UnityEngine.UIElements.UIPainter2D::Create
UnityEngine.UIElements.UIPainter2D::ClearSnapshots
UnityEngine.UIElements.UIPainter2D::Destroy
UnityEngine.UIElements.UIPainter2D::ExecuteSnapshotFromJob_Injected
UnityEngine.UIElements.UIPainter2D::Reset
UnityEngine.UIElements.UIR.JobProcessor::ScheduleConvertMeshJobs_Injected
UnityEngine.UIElements.UIR.JobProcessor::ScheduleCopyMeshJobs_Injected
UnityEngine.UIElements.UIR.JobProcessor::ScheduleNudgeJobs_Injected
UnityEngine.UIElements.UIR.Utility::CPUFencePassed
UnityEngine.UIElements.UIR.Utility::HasMappedBufferRange
UnityEngine.UIElements.UIR.Utility::AllocateBuffer
UnityEngine.UIElements.UIR.Utility::CreateStencilState_Injected
UnityEngine.UIElements.UIR.Utility::GetVertexDeclaration_Injected
UnityEngine.UIElements.UIR.Utility::InsertCPUFence
UnityEngine.UIElements.UIR.Utility::DisableScissor
UnityEngine.UIElements.UIR.Utility::DrawRanges
UnityEngine.UIElements.UIR.Utility::FreeBuffer
UnityEngine.UIElements.UIR.Utility::GetActiveViewport_Injected
UnityEngine.UIElements.UIR.Utility::GetUnityProjectionMatrix_Injected
UnityEngine.UIElements.UIR.Utility::NotifyOfUIREvents
UnityEngine.UIElements.UIR.Utility::ProfileDrawChainBegin
UnityEngine.UIElements.UIR.Utility::ProfileDrawChainEnd
UnityEngine.UIElements.UIR.Utility::SetPropertyBlock_Injected
UnityEngine.UIElements.UIR.Utility::SetScissorRect_Injected
UnityEngine.UIElements.UIR.Utility::SetStencilState
UnityEngine.UIElements.UIR.Utility::SyncRenderThread
UnityEngine.UIElements.UIR.Utility::UpdateBufferRanges
UnityEngine.UIElements.UIR.Utility::WaitForCPUFencePassed
UnityEngine.UIElements.UIRenderer::SetNativeData_Injected
UnityEngine.UISystemProfilerApi::AddMarker_Injected
UnityEngine.UISystemProfilerApi::BeginSample
UnityEngine.UISystemProfilerApi::EndSample
UnityEngine.UnityLogWriter::WriteStringToUnityLogImpl_Injected
UnityEngine.VFX.VFXEventAttribute::Internal_Create
UnityEngine.VFX.VFXEventAttribute::Internal_Destroy
UnityEngine.VFX.VFXEventAttribute::Internal_InitFromAsset_Injected
UnityEngine.VFX.VFXManager::Internal_ProcessCameraCommand_Injected
UnityEngine.VFX.VFXManager::SetCameraBuffer_Injected
UnityEngine.VFX.VFXManager::IsCameraBufferNeeded_Injected
UnityEngine.VFX.VFXSpawnerState::Internal_Destroy
UnityEngine.VFX.VisualEffect::get_visualEffectAsset_Injected
UnityEngine.XR.XRDevice::DisableAutoXRCameraTracking_Injected
UnityEngine.XR.XRDisplaySubsystem::AddGraphicsThreadMirrorViewBlit_Injected
UnityEngine.XR.XRDisplaySubsystem::GetMirrorViewBlitDesc_Injected
UnityEngine.XR.XRDisplaySubsystem::Internal_TryBeginRecordingIfLateLatched_Injected
UnityEngine.XR.XRDisplaySubsystem::Internal_TryEndRecordingIfLateLatched_Injected
UnityEngine.XR.XRDisplaySubsystem::Internal_TryGetCullingParams_Injected
UnityEngine.XR.XRDisplaySubsystem::Internal_TryGetRenderPass_Injected
UnityEngine.XR.XRDisplaySubsystem::GetPreferredMirrorBlitMode_Injected
UnityEngine.XR.XRDisplaySubsystem::GetRenderPassCount_Injected
UnityEngine.XR.XRDisplaySubsystem::get_scaleOfAllViewports_Injected
UnityEngine.XR.XRDisplaySubsystem::SetMSAALevel_Injected
UnityEngine.XR.XRDisplaySubsystem::SetPreferredMirrorBlitMode_Injected
UnityEngine.XR.XRDisplaySubsystem::set_disableLegacyRenderer_Injected
UnityEngine.XR.XRDisplaySubsystem::set_sRGB_Injected
UnityEngine.XR.XRDisplaySubsystem::set_scaleOfAllRenderTargets_Injected
UnityEngine.XR.XRDisplaySubsystem::set_textureLayout_Injected
UnityEngine.XR.XRDisplaySubsystem::set_zFar_Injected
UnityEngine.XR.XRDisplaySubsystem::set_zNear_Injected
UnityEngine.XR.XRDisplaySubsystem/XRMirrorViewBlitDesc::GetBlitParameter
UnityEngine.XR.XRDisplaySubsystem/XRRenderPass::GetRenderParameterCount
UnityEngine.XR.XRDisplaySubsystem/XRRenderPass::GetRenderParameter_Injected
UnityEngine.XR.XRMeshSubsystem/MeshTransformList::Dispose
UnityEngine.XR.XRSettings::get_enabled
UnityEngine.XR.XRSettings::get_isDeviceActive
UnityEngine.XR.XRSettings::get_eyeTextureHeight
UnityEngine.XR.XRSettings::get_eyeTextureWidth
UnityEngine.XR.XRSettings::get_supportedDevices
UnityEngine.XR.XRSettings::get_eyeTextureDesc_Injected
UnityEngine.XR.XRSettings::get_loadedDeviceName_Injected
UnityEngineInternal.Input.NativeInputSystem::get_normalizeScrollWheelDelta
UnityEngineInternal.Input.NativeInputSystem::get_currentTime
UnityEngineInternal.Input.NativeInputSystem::get_currentTimeOffsetToRealtimeSinceStartup
UnityEngineInternal.Input.NativeInputSystem::AllocateDeviceId
UnityEngineInternal.Input.NativeInputSystem::IOCTL
UnityEngineInternal.Input.NativeInputSystem::GetScrollWheelDeltaPerTick
UnityEngineInternal.Input.NativeInputSystem::QueueInputEvent
UnityEngineInternal.Input.NativeInputSystem::SetPollingFrequency
UnityEngineInternal.Input.NativeInputSystem::Update
UnityEngineInternal.Input.NativeInputSystem::set_hasDeviceDiscoveredCallback
UnityEngineInternal.Input.NativeInputSystem::set_normalizeScrollWheelDelta
