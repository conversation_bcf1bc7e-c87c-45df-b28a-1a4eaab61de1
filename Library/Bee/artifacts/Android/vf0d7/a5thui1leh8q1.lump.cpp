//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Array.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Assembly.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/MarshalAlloc.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Object.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/PlatformInvoke.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Reflection.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/WaitHandle.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/WeakReference.cpp"
