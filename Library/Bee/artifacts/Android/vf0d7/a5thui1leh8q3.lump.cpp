//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/AndroidRuntime.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/CCW.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/ClassLibraryPAL.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Domain.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/GlobalMetadata.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/LastError.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/MemoryInformation.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Method.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Monitor.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Random.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/ScopedThreadAttacher.cpp"
#include "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Thread.cpp"
