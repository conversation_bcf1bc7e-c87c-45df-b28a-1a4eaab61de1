﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mB1996B7A6E79AE47EC82CFA7894FAD8235AA70D0 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m7B536875EDB6C17E12CAC10ADC0C0D4E57F9112A (void);
extern void AnimationTriggers_get_normalTrigger_mDB8A7C5C4B69515C514BB138724E25F16FCED43F (void);
extern void AnimationTriggers_set_normalTrigger_m43C30CD216A826221653CF8648B1C1823EBBA8E6 (void);
extern void AnimationTriggers_get_highlightedTrigger_m492093112EC52907A3B5ED518BAD366B0052EDCA (void);
extern void AnimationTriggers_set_highlightedTrigger_mCAD700865DD27254AFCA56FE85E2457ECF4CE6B7 (void);
extern void AnimationTriggers_get_pressedTrigger_mFB9A652E90CCCB38FCF47EE464D20FACE41399B9 (void);
extern void AnimationTriggers_set_pressedTrigger_m1AEC4AC3C6CF43AE7EFE2B8E9A7561A5C2F7A5A0 (void);
extern void AnimationTriggers_get_selectedTrigger_m631840BD0D4ED252C62E11405B29826F9BC419E8 (void);
extern void AnimationTriggers_set_selectedTrigger_m3AC876A19515E1901E0986781756F339A13E45A9 (void);
extern void AnimationTriggers_get_disabledTrigger_mC91BD8165E64C82F9DCB7E0549ACB2D0537CE376 (void);
extern void AnimationTriggers_set_disabledTrigger_m20289A9FDAF524362F32320D57EF4E5A323299C1 (void);
extern void AnimationTriggers__ctor_mDF3C8571BACB06DACEE75D9E5899B53C1D429A02 (void);
extern void Button__ctor_m0A1FC265F589989085C1909BC8D93E33A698D8E5 (void);
extern void Button_get_onClick_m701712A7F7F000CC80D517C4510697E15722C35C (void);
extern void Button_set_onClick_m4CD77BD99635400BA18692D591BEA79A7ECC66C3 (void);
extern void Button_Press_mEF76F32CD5C01C1D8B00B80BDFC0C6CEEEF2C993 (void);
extern void Button_OnPointerClick_mB76B80D7374811C7BBE11DA188E2656904AE5422 (void);
extern void Button_OnSubmit_m700E3D529DEE2FB6214BD698C61A3C7CDA08F0A8 (void);
extern void Button_OnFinishSubmit_m72771C6FF84DA31AE09E936D2D7FB6771FE5A7D9 (void);
extern void ButtonClickedEvent__ctor_m2B38CD66BDA4E63A19DB233BFA32C828A3D5290D (void);
extern void U3COnFinishSubmitU3Ed__9__ctor_m52988E24A7D4CE00B0F8A0F60A6EC20166027553 (void);
extern void U3COnFinishSubmitU3Ed__9_System_IDisposable_Dispose_m15AFD67ECC7FD7739798F4F063F3978AA19D41EA (void);
extern void U3COnFinishSubmitU3Ed__9_MoveNext_m1D344B644399C92B28851F8630337135752BDE2A (void);
extern void U3COnFinishSubmitU3Ed__9_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m82819377766A051FA756675E7EDB47088418FD96 (void);
extern void U3COnFinishSubmitU3Ed__9_System_Collections_IEnumerator_Reset_m3C1D75C11B17DE106053B97EC52822B74591061F (void);
extern void U3COnFinishSubmitU3Ed__9_System_Collections_IEnumerator_get_Current_m26114B1DB5CA45B24C00C1B5596D29F76A4AED57 (void);
extern void CanvasUpdateRegistry__ctor_m2265E2D6AE72DCF25E4A8E52A03A390012CDB01C (void);
extern void CanvasUpdateRegistry_get_instance_m57BA2006B09FA35EA4CE228078E7EAF0E01509DE (void);
extern void CanvasUpdateRegistry_ObjectValidForUpdate_mFF8ACAA818FA7F73C5A6447C8E1E61631690660A (void);
extern void CanvasUpdateRegistry_CleanInvalidItems_mFDBE5D212F6B9649B6EB619AA8860DB72F3AA80E (void);
extern void CanvasUpdateRegistry_PerformUpdate_mA5CAEDD4452E5C8B58B688C08BC491AC2DAFD51A (void);
extern void CanvasUpdateRegistry_ParentCount_mA10504532DE021BBA642052EDB70F46BAD4A55D2 (void);
extern void CanvasUpdateRegistry_SortLayoutList_m28074D05490A0F8B9D4E5699BEB357F92212141F (void);
extern void CanvasUpdateRegistry_RegisterCanvasElementForLayoutRebuild_mB9571A1C6F0E32E1A0B07C46A1E68366E2A598AB (void);
extern void CanvasUpdateRegistry_TryRegisterCanvasElementForLayoutRebuild_mCF214BCE7C81643D9684D93C2CC40FEC599E72EB (void);
extern void CanvasUpdateRegistry_InternalRegisterCanvasElementForLayoutRebuild_m609303A2651E3AC6B866A9ACDEAE0773F4CCAAC0 (void);
extern void CanvasUpdateRegistry_RegisterCanvasElementForGraphicRebuild_mEBBD04C3B001E80801966E3347E70A35FCEBE8B1 (void);
extern void CanvasUpdateRegistry_TryRegisterCanvasElementForGraphicRebuild_mFA63F8841FECC69E9EC84B9F4D7EAF4B0EBFE375 (void);
extern void CanvasUpdateRegistry_InternalRegisterCanvasElementForGraphicRebuild_m6CA79E8E3D6217779BF91B50C8D4C9FCF7492B60 (void);
extern void CanvasUpdateRegistry_UnRegisterCanvasElementForRebuild_m61F9979AB8AFBA924430757FE09967D7A335D916 (void);
extern void CanvasUpdateRegistry_DisableCanvasElementForRebuild_mC1A68AC220C3755789E3CB51E8DBAC81CC61D62F (void);
extern void CanvasUpdateRegistry_InternalUnRegisterCanvasElementForLayoutRebuild_m2DA109CB4DE672A779EB3531D2E727D683E3A00A (void);
extern void CanvasUpdateRegistry_InternalUnRegisterCanvasElementForGraphicRebuild_m2A2FAEE11B508953630961D186E379ED890DA589 (void);
extern void CanvasUpdateRegistry_InternalDisableCanvasElementForLayoutRebuild_m64780D6E94F424BEF771EC84E3F2A8D328B9CB6C (void);
extern void CanvasUpdateRegistry_InternalDisableCanvasElementForGraphicRebuild_m6AC0A40EDD8462EEA49F355BFA793AD565DB5D57 (void);
extern void CanvasUpdateRegistry_IsRebuildingLayout_m3C037968252136E38CF1AF8716DC671CE13917EA (void);
extern void CanvasUpdateRegistry_IsRebuildingGraphics_m424672693DCCC18C324436EDD483753B8137A482 (void);
extern void CanvasUpdateRegistry__cctor_m19922681B4E153B487D8E81BE3A583CACBF32858 (void);
extern void ColorBlock_get_normalColor_m08A07A74ED743B4B0C1B5A5C35774F2D78F1F20E (void);
extern void ColorBlock_set_normalColor_m3EBF594F6FA2C6494ACA9FCB9B458807D85B96F8 (void);
extern void ColorBlock_get_highlightedColor_m4D1A3D268CB00B351F56934F7F244DBC68855301 (void);
extern void ColorBlock_set_highlightedColor_m04E97DF2CCE7CAC47120D8F486E18BF62F16FF86 (void);
extern void ColorBlock_get_pressedColor_m5EDADBA10824D08BFB67D02099A9FC6A4235AC62 (void);
extern void ColorBlock_set_pressedColor_m644C938090857AB07C57B25FE53F6DC2BB0DD5A8 (void);
extern void ColorBlock_get_selectedColor_m41CD59090E997A5982EE5AB9D23811FEB35C82CF (void);
extern void ColorBlock_set_selectedColor_m76FEFB1148798B7A356C974CDEA3BA2E2E3C1D21 (void);
extern void ColorBlock_get_disabledColor_m2E20FC772B592ADD71CE1336D29B3C3C1523669E (void);
extern void ColorBlock_set_disabledColor_m4D10D1F8525CCC7E8E200E3994AFB28ADABB1D8E (void);
extern void ColorBlock_get_colorMultiplier_m54C8F6B7E5ACF45D70F9EAAED78AB4606999C187 (void);
extern void ColorBlock_set_colorMultiplier_m920A048B95541DB0E92AF4AF3894BE7CD2D37102 (void);
extern void ColorBlock_get_fadeDuration_m506A0FCC2819DA313BE033640C8FEDC5331D1C39 (void);
extern void ColorBlock_set_fadeDuration_m8519A304808384CE873377EC104969A6B9FBB6C5 (void);
extern void ColorBlock__cctor_mE6D6008EFBF7B20ECDFC69AD0FBAAF745BBFEB7A (void);
extern void ColorBlock_Equals_m20D958BB28F6FDC12D612279AF6B50679C0C1E67 (void);
extern void ColorBlock_Equals_m52DCE246EA23904A3EF0FCD3ADAB81BC20DC1BE5 (void);
extern void ColorBlock_op_Equality_m5925207B6FDD0CE013BEB0269B4407B9C3A54276 (void);
extern void ColorBlock_op_Inequality_m73B2B54AA18CB45290F99B1A870BC43E08209AC7 (void);
extern void ColorBlock_GetHashCode_m3CCB4E1E5EE93B905650E24BD4753096082270D7 (void);
extern void ClipperRegistry__ctor_m664370B6F6A28646681B08A723C4EEE7737599A4 (void);
extern void ClipperRegistry_get_instance_m709E4407231F3C616FCE693389AE2BC0121FCE40 (void);
extern void ClipperRegistry_Cull_mE2BBF0B75900B6780EDE22699476542FC5B62730 (void);
extern void ClipperRegistry_Register_m4C47388806CA8A75538144365809137FB61C965B (void);
extern void ClipperRegistry_Unregister_mEEF92721B30BDF97F454512C32D1DF8E24834F42 (void);
extern void ClipperRegistry_Disable_m4541BB1A762E730709A65D7CDA917CF0D56CA687 (void);
extern void Clipping_FindCullAndClipWorldRect_mE367B99A2BEBA67F6B394B7E95346C9F6416C4B5 (void);
extern void RectangularVertexClipper_GetCanvasRect_m9C9A5CAF396D20925E1394FA188E71D3B825B383 (void);
extern void RectangularVertexClipper__ctor_m159190C771C2A7F406769365B902A228DE585C7A (void);
extern void DefaultControls_get_factory_m5E94C746BC7C05A928A9886519FC4FB32C2EB57D (void);
extern void DefaultControls_CreateUIElementRoot_m29E6F31D5BEA9AA602BFDFA423DB403DFE429683 (void);
extern void DefaultControls_CreateUIObject_mBBA2C8A5C8BB80104251200395B2947A5CB45071 (void);
extern void DefaultControls_SetDefaultTextValues_mE667D7E0A8BB03B6EBA7E3A5917A5F0B5DF1D0BB (void);
extern void DefaultControls_SetDefaultColorTransitionValues_mA11DBF31257A877F2C3CFE2FC555F77DE4AB27B5 (void);
extern void DefaultControls_SetParentAndAlign_mA6632B61C5C5C33B4901129DC16919E8C5158952 (void);
extern void DefaultControls_SetLayerRecursively_mE0BFC051766E26BC0118555582A6579179281CBA (void);
extern void DefaultControls_CreatePanel_m69F2B388CE41646797A582D4AF52CC91B45BB7C0 (void);
extern void DefaultControls_CreateButton_m3551B134B8B79ADCE1DB85D5A77B4C9F32E43619 (void);
extern void DefaultControls_CreateText_mA23D7B0D56561FA174752601AEFFEB04F26E7C3E (void);
extern void DefaultControls_CreateImage_m0D5C35201D8D12B1A42685CE23772F5C39864331 (void);
extern void DefaultControls_CreateRawImage_m1CF70C988DE4276C35037BB56479E805EAAB567F (void);
extern void DefaultControls_CreateSlider_m46C7D78861271433489674E7B9A09D018E33911C (void);
extern void DefaultControls_CreateScrollbar_m78FC29513D3DAF700CB9205882F58F59B3F5620E (void);
extern void DefaultControls_CreateToggle_mE5D3F00385DD0F468F8218D520C0C0D300BE58F4 (void);
extern void DefaultControls_CreateInputField_m0381BFDF0D84EC0A896C639EAB732F39A36B8ED2 (void);
extern void DefaultControls_CreateDropdown_m94CE7639D609D6341FBCF0D49D6494A5901FFDBD (void);
extern void DefaultControls_CreateScrollView_m091D81394C468627D85DDAB8236665E837C89AA7 (void);
extern void DefaultControls__cctor_m712ED7CCB7CEEE815F424F553E03BC3BA4F6E80B (void);
extern void DefaultRuntimeFactory_CreateGameObject_m0D5F91ACE140C39C3139BAF1437792D42CC0C389 (void);
extern void DefaultRuntimeFactory__ctor_m5467830A8AA017398C392E147A4582857EFD0710 (void);
extern void DefaultRuntimeFactory__cctor_m09225594AC1F1C4EE5EBF6E5FBC7C8760C34AF2D (void);
extern void Dropdown_get_template_m6714116D7DA3F457F184B004785B4F017D50987A (void);
extern void Dropdown_set_template_m13FE93E0ED2414A5D8D4595D3123DDFD726DB619 (void);
extern void Dropdown_get_captionText_m0A8DEACA15F0DDFEE339462E03DF511B87389EF4 (void);
extern void Dropdown_set_captionText_mD314CF798D1B85726553E124496A7EE226BB1830 (void);
extern void Dropdown_get_captionImage_mA4C6EF8312F06B6190FC4E78583975145274168B (void);
extern void Dropdown_set_captionImage_mF6B9BCAF2C8C0018E2F94600F9C8DE2412F5F184 (void);
extern void Dropdown_get_itemText_m8E98EB1B2B2F8D5C14F0D4A02E620E9240966681 (void);
extern void Dropdown_set_itemText_m901981335866C0A856E31D7D1C87C7D8E76FBFAA (void);
extern void Dropdown_get_itemImage_mA30A3B51B8C9B6E364B57A3FB277B364B6E0EF1B (void);
extern void Dropdown_set_itemImage_m6F4BBC06449E2EAF073D495871BB29F4B35BD7FE (void);
extern void Dropdown_get_options_m30F757DBA22980CB77DADB8315207D5B87307816 (void);
extern void Dropdown_set_options_mEC30A0E3E0819485B1EACF8624D0C1974857D368 (void);
extern void Dropdown_get_onValueChanged_mAC49CE9A83E258FEC024662127057567275CAC12 (void);
extern void Dropdown_set_onValueChanged_m59337E2E2975F5F4338C5B03C50347A23343C0E0 (void);
extern void Dropdown_get_alphaFadeSpeed_m17C37664CEDBA2950ACDA7FCB1DFCBAD1A9C82E9 (void);
extern void Dropdown_set_alphaFadeSpeed_m67E1A7B551D3592380C6EA34355B94C461790984 (void);
extern void Dropdown_get_value_m386913162D5E273B762657FE5156DC567602BC3C (void);
extern void Dropdown_set_value_m0764A5E2023E34705ADD422689BF6C0074449FEE (void);
extern void Dropdown_SetValueWithoutNotify_m3D2B40BC16D305309D68D9FF093BF25FF66E4ABA (void);
extern void Dropdown_Set_m2F7DFBF09261A4C4CB1AFCF939907716191D8F07 (void);
extern void Dropdown__ctor_m1AF791E4615DB8F00045A3713730CD45E66A7CD4 (void);
extern void Dropdown_Awake_m1A9102FB62C5393F695E5A0FB44A0CFEC5B947FF (void);
extern void Dropdown_Start_m93BFFE8C88FF09265315FE8B145FE165467CBB35 (void);
extern void Dropdown_OnDisable_mB9CBBE366F5F5EDA29C064DB5D7A6EA7C711C70E (void);
extern void Dropdown_RefreshShownValue_mA112A95E8653859FC2B6C2D0CC89660D36E8970E (void);
extern void Dropdown_AddOptions_mE535B30A30D77024D10AB2AB71CE3FD280CD0327 (void);
extern void Dropdown_AddOptions_mCFB763400FA1BCA695C168E7FBCDE20C9B8E7839 (void);
extern void Dropdown_AddOptions_mD4460EE082AB7BE36CB54DBB67BFEB4BC172707E (void);
extern void Dropdown_ClearOptions_m3EE71BFE47AB96BC7F731C4EE6BC728ED0E6EE56 (void);
extern void Dropdown_SetupTemplate_m6F68B1CAC7C39B2C3415B46FD2CF8F91DCF48901 (void);
extern void Dropdown_OnPointerClick_m3AE64082F83B272B4880935125784442E107939C (void);
extern void Dropdown_OnSubmit_m3535A89BE2130B54653DB2A6BA850F2055DA7F6D (void);
extern void Dropdown_OnCancel_m50A25AA76B6A92E72820E97C4C9DF2295B45FC2A (void);
extern void Dropdown_Show_m103EDF14CFC2B5284C92037B097F015DAB1340DC (void);
extern void Dropdown_CreateBlocker_mA27CE256509155DAC14FBB8549074ACFF5976DDB (void);
extern void Dropdown_DestroyBlocker_mE0B298F69E3343D0551E7B42B28312EEE28C553B (void);
extern void Dropdown_CreateDropdownList_mD6A55CE0786F7A418C6FC001798F660D1D2CFF95 (void);
extern void Dropdown_DestroyDropdownList_mB8F81B723F9C08AF3D303D8CDB395B4474B1846C (void);
extern void Dropdown_CreateItem_m2E8E7B65A324DF3CB783D219F7ADA70E28CD8FAA (void);
extern void Dropdown_DestroyItem_mD48C6E656F3CE04FE1C26E1F92F599B1F0EAD778 (void);
extern void Dropdown_AddItem_m16017A91D142FECFE69FB38FAA311636348B499C (void);
extern void Dropdown_AlphaFadeList_mF73F53EB84546666A4DB382173BEFEA23DFD9D64 (void);
extern void Dropdown_AlphaFadeList_m5727C00B9A1FF385C5A4B65799E1CFAE49F29F86 (void);
extern void Dropdown_SetAlpha_mE367D2B2798F4F7FC0281D772AB4DC7417A2077C (void);
extern void Dropdown_Hide_m49F29E7BC614DB6E04512F762399A9AACCDAFCB7 (void);
extern void Dropdown_DelayedDestroyDropdownList_m5840A3EACBCDA1F7EB89E36A44EA502243E87F8F (void);
extern void Dropdown_ImmediateDestroyDropdownList_mAC289C54114CD256FE7F34B8D62EFDA947C00272 (void);
extern void Dropdown_OnSelectItem_m17D380C68C04FE4125D32EA8494D8F98442150F9 (void);
extern void Dropdown__cctor_m4A014C9379610C7598BED3E900FD22040E2F9C2C (void);
extern void DropdownItem_get_text_m29C926466BC0BE39A7EA282A627E1F8459C53E0D (void);
extern void DropdownItem_set_text_mE5F27F83326429B6056B686682BBC9911546DAA0 (void);
extern void DropdownItem_get_image_m415346A4FB0E83932E4043D41B0AE837F2C3EE75 (void);
extern void DropdownItem_set_image_mED01F92D19AA3B5C0CACBCE2D1C9A70AFC7049EA (void);
extern void DropdownItem_get_rectTransform_mAFF594D5FE8280F8E4CF8D246654C1EC04C892EB (void);
extern void DropdownItem_set_rectTransform_m62744FF037D3E7044EDA139CA6BB6FBB11E1061E (void);
extern void DropdownItem_get_toggle_m9E93C07903AF29C0D66C48B217575A65CD4CB471 (void);
extern void DropdownItem_set_toggle_mD58F68764A433037C8F42483BE4F95973EBA3535 (void);
extern void DropdownItem_OnPointerEnter_mB9464C1CE0EBF0A4F3A7979B37AEF2283E738A34 (void);
extern void DropdownItem_OnCancel_mFEA3928E939D387662E21AD7496DD64FF40B9FC7 (void);
extern void DropdownItem__ctor_mB55660FE9B66C2A5E7E8587450729BB691EDAC03 (void);
extern void OptionData_get_text_m147C3EFE4B7D157914D2C6CF653B32CE2D987AF1 (void);
extern void OptionData_set_text_mA6022A455FC38025B0CA97B4E3629DA10FDE259E (void);
extern void OptionData_get_image_m4E10E9C1338C69EF43C240AB6866AD99CA63451F (void);
extern void OptionData_set_image_mE503B098325797C5AA91F3BD71A182CAFF878C9D (void);
extern void OptionData__ctor_m6321993E5D83F3A7E52ADC14C9276508D1129166 (void);
extern void OptionData__ctor_m0BB22D3B9A2443D8D51CE88AD6B4DAEAF11B59E6 (void);
extern void OptionData__ctor_m59495D34418035A84F4985F134B7557294689252 (void);
extern void OptionData__ctor_mFF7263F2503D3F2D1E395450A62CAAB48CA9AFDE (void);
extern void OptionDataList_get_options_m0400B4F545E0EF3D00D50B701720B5D2F732A00E (void);
extern void OptionDataList_set_options_mE730DD2A6EB4DEE150450E52C0C2869CE4573E1C (void);
extern void OptionDataList__ctor_mEDE3FBBEC8C69BAB71DC8A4EEBA4DD92A19D2E6E (void);
extern void DropdownEvent__ctor_m40067CAE88519F3B3B9991621A3EA5DC89682145 (void);
extern void U3CU3Ec__DisplayClass63_0__ctor_mA6669AA99E56F2DEE6C1E1ECB173C7BE4DE1CD64 (void);
extern void U3CU3Ec__DisplayClass63_0_U3CShowU3Eb__0_m2D40C4419DA54F2340E2A0BE7E7E6BD57113B71C (void);
extern void U3CDelayedDestroyDropdownListU3Ed__75__ctor_m80FA88C604962EB1BCF0453E39809E4AD856564F (void);
extern void U3CDelayedDestroyDropdownListU3Ed__75_System_IDisposable_Dispose_m86610E303C691865AD8CA51A944E0DD22CD76646 (void);
extern void U3CDelayedDestroyDropdownListU3Ed__75_MoveNext_m4635FEBE76913C9F4A0D60DF2DEFBABE071481D4 (void);
extern void U3CDelayedDestroyDropdownListU3Ed__75_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD7843A1E586805C8BA4165718774F1579F775077 (void);
extern void U3CDelayedDestroyDropdownListU3Ed__75_System_Collections_IEnumerator_Reset_mD5684D5117ECD0043294091F4CBB9EEC17957CC2 (void);
extern void U3CDelayedDestroyDropdownListU3Ed__75_System_Collections_IEnumerator_get_Current_m746DC8D79704E5D1B78C1DC62CDECC7274409D13 (void);
extern void FontData_get_defaultFontData_mE91EA0AE923A4988ECEF06F608BA8DE764541B6F (void);
extern void FontData_get_font_m449DE7A18F42B85D427608E88BC17B528D974D93 (void);
extern void FontData_set_font_mDF16F5058F749DA9A80B7203BE1E007A21258089 (void);
extern void FontData_get_fontSize_m6695DDD7FECD4BAC1147A15D26B7F16B78E2B2D3 (void);
extern void FontData_set_fontSize_m00594E7340206777E0CF1F038943724B8DA9FD53 (void);
extern void FontData_get_fontStyle_m7671598F11D5C2AE55AA46D28794C78D0D690EC3 (void);
extern void FontData_set_fontStyle_m90E8DF52C663489F43FB185780D38A3E99A30C29 (void);
extern void FontData_get_bestFit_m230FD8F27172E1A020BFDDC2D89932DFD01788FC (void);
extern void FontData_set_bestFit_m15B4E1EC2E3AA912718466F0C098BF0C22E7B46B (void);
extern void FontData_get_minSize_mD8AD04F4CF85C79BEA14710F3AD85228E3DC2D97 (void);
extern void FontData_set_minSize_mAAC06D3C29D5210054B3DC3FDE58358648460F91 (void);
extern void FontData_get_maxSize_mA8FDA877D8D459C0C97F1AE7FD8D5F7C27391872 (void);
extern void FontData_set_maxSize_m3EC43E7AB5A4C022DE729371D8AACFC7D702B527 (void);
extern void FontData_get_alignment_mC3C237BFE74D104BE4502D0D6BEF6D400AC509F4 (void);
extern void FontData_set_alignment_m25795B35CBF298D966B5C9A73A4A58F075C17563 (void);
extern void FontData_get_alignByGeometry_m193ADE84986D74A91F46B31C1F961BC9D688CDFF (void);
extern void FontData_set_alignByGeometry_m580D8D1B9D4396C648C9180BB891DAF561E37A2F (void);
extern void FontData_get_richText_m76956F1C2063841C77172F1CB404F3C6C81052A1 (void);
extern void FontData_set_richText_mB37DCE83CBD25C93A3AA4AA9C0C3A7AE332753DC (void);
extern void FontData_get_horizontalOverflow_mEF56759973C6722FDE71032861BC0713628E5EA8 (void);
extern void FontData_set_horizontalOverflow_m8B75EB2EB0241423F50E31C023729BDBAAA019E1 (void);
extern void FontData_get_verticalOverflow_m306AE42FED4B302C133CC899B55D92FB86C1ED8F (void);
extern void FontData_set_verticalOverflow_m857D9882EC486696EE3898EB5BFFFE04053C9D17 (void);
extern void FontData_get_lineSpacing_mE9627A4D01D54115F8AE42EC1F12CFBB86FAC5E0 (void);
extern void FontData_set_lineSpacing_m034F2A307093DCAACE71D610550C3306C1389FB5 (void);
extern void FontData_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_mCA5C2ADF6B05942D58C400752E8D175DAC008399 (void);
extern void FontData_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_mAB5158604FD53556402CD7297F9797747088EC6F (void);
extern void FontData__ctor_m90225BC9FF97C82F911B775CD3EB54B0C95839C8 (void);
extern void FontUpdateTracker_TrackText_mE52366E2C5DF0BA4E24F35D7DA246FBF32332007 (void);
extern void FontUpdateTracker_RebuildForFont_mC9A828F534DBBCE1E70BFF4A5034C7B37F7D65EE (void);
extern void FontUpdateTracker_UntrackText_m813D712F66E05727FE0CEFAB4438EE7DF5483738 (void);
extern void FontUpdateTracker__cctor_m82550106869CBCE1C5D8D7AC9F211AD71DBEE5C7 (void);
extern void Graphic_get_defaultGraphicMaterial_mC3D98DC8F6E8826633B17BB4AC6E38DF20A74E78 (void);
extern void Graphic_get_color_mA6639AC2B77A8F1B7F27656B69320E7A0FD4F315 (void);
extern void Graphic_set_color_mC9B90A387A37946AF295D7BCDA1FBC1DBD2D4865 (void);
extern void Graphic_get_raycastTarget_mA3E3A3A0C7A12EB550D0BCD5DC68F5A40C6D7844 (void);
extern void Graphic_set_raycastTarget_mE3D3CBB94E605C13362A592F17420AEAAC771448 (void);
extern void Graphic_get_raycastPadding_m44CC4DC7030C46D15519AAFA7523E9AD4DC462B7 (void);
extern void Graphic_set_raycastPadding_m5EBFEDD522BD4E1EC0202FEA1D7A0273E25FD5E5 (void);
extern void Graphic_get_useLegacyMeshGeneration_m2057231F53432FC95BA40EA485E85F5DAF21F423 (void);
extern void Graphic_set_useLegacyMeshGeneration_m8069890AE2F389C73D944941BB8462C44EB32EC9 (void);
extern void Graphic__ctor_m61FAEBEC21F22FE00B8CF39A8498AD31F62C0D6D (void);
extern void Graphic_SetAllDirty_mE93D6326AF09CED62858980A38F571F01A567E17 (void);
extern void Graphic_SetLayoutDirty_m707188E6F05B8977FBA14C6269420EAE045A728B (void);
extern void Graphic_SetVerticesDirty_m8DBAF14DE97CB50DC54E768A2C120F8F4B3C647E (void);
extern void Graphic_SetMaterialDirty_m19E23BAD2FAF23CEF776F467AA8A453C3320473E (void);
extern void Graphic_SetRaycastDirty_m07F00097DD9C6278923A1CC204770A4141F4B400 (void);
extern void Graphic_OnRectTransformDimensionsChange_m2A42F124936B2F377BE4A07BC9586C38CF15EB74 (void);
extern void Graphic_OnBeforeTransformParentChanged_mFEE7DB7653CD70C7279F397DFF1A5C9B702B36BE (void);
extern void Graphic_OnTransformParentChanged_m5FAC5BEDE05D6969B7F7AD15C0A8C5715129EED7 (void);
extern void Graphic_get_depth_m16A82C751AE0497941048A3715D48A1066939460 (void);
extern void Graphic_get_rectTransform_mF4752E8934267D630810E84CE02CDFB81EB1FD6D (void);
extern void Graphic_get_canvas_mEA2161DF3BD736541DE41F9B814C4860FEB76419 (void);
extern void Graphic_CacheCanvas_m3F8A1EE9BE3F17297B5E5B9EA02CCA8AF53E34DD (void);
extern void Graphic_get_canvasRenderer_m62AB727277A28728264860232642DA6EC20DEAB1 (void);
extern void Graphic_get_defaultMaterial_m1F258214F9C1F431922BAA0028374FF6F3F81661 (void);
extern void Graphic_get_material_m7E92B4A77B5454BCC2507952561F12EA88AB9240 (void);
extern void Graphic_set_material_m49252B02E3CB2C0A17C1A74783F615E50C8801B5 (void);
extern void Graphic_get_materialForRendering_m4B0017B2B59D2EF578D32ABFCF84A97A835B6B22 (void);
extern void Graphic_get_mainTexture_mC38AAAD7BB2E9ED5CD1606FB0BB076CCB5F4B70D (void);
extern void Graphic_OnEnable_mD7544FBC0068D0C74181E6E66C7EC167B7C6309E (void);
extern void Graphic_OnDisable_m2DF81EFCEA05C2E6605C027DA3ABD79945C94F16 (void);
extern void Graphic_OnDestroy_mDA1CEBC665EEC946C60519596C396477F2E348D9 (void);
extern void Graphic_OnCanvasHierarchyChanged_m7062158DC1477AF8A5BC2B07755314ED7A184C5C (void);
extern void Graphic_OnCullingChanged_m50D153DBBF9C9F17AE177E6ED6D157D847120621 (void);
extern void Graphic_Rebuild_mEA8B7052D925874A5C8A3F3733F5027CA946EFAD (void);
extern void Graphic_LayoutComplete_m42E63C813BCE631365012B856473439ABD49A726 (void);
extern void Graphic_GraphicUpdateComplete_m02387ED9D65BF3C90D58BD5D2A9614736ABD7D5F (void);
extern void Graphic_UpdateMaterial_m0FE63FE57725F78FA05D9C85F8457B6CA06EF665 (void);
extern void Graphic_UpdateGeometry_m29DD64EA8C3600E9B5A50DAAA8A79D63B9FC1BE5 (void);
extern void Graphic_DoMeshGeneration_m9A226AB1660C68B8C2ED56845686600741CF7BB9 (void);
extern void Graphic_DoLegacyMeshGeneration_m6B3FFD836E8904FE9ED48633DED556CB8CEC0156 (void);
extern void Graphic_get_workerMesh_m31789D0370C0CCCFC9A160714835CAD44CEEB877 (void);
extern void Graphic_OnFillVBO_m327876DCE662B10A36B5DD71A891F75599186FE4 (void);
extern void Graphic_OnPopulateMesh_mA35BBAE4555A20A302AABD4EF1AB4F4C9D565160 (void);
extern void Graphic_OnPopulateMesh_mDD8F1B97C1AB94FB2C61D82080DE06DBAE2C0EEF (void);
extern void Graphic_OnDidApplyAnimationProperties_m75AB831FC70C61BF140CFA69D337C48E8762B1CF (void);
extern void Graphic_SetNativeSize_m9D5D0610B602745DA5BED808B20A07214CC18991 (void);
extern void Graphic_Raycast_mEEE1690786A5894545C42BF6143936237BFE61A0 (void);
extern void Graphic_PixelAdjustPoint_mBC4AFC26628D498B9872314726561D72F6DD2F28 (void);
extern void Graphic_GetPixelAdjustedRect_m70D7B527D04C0B88C23E7C6661A8FF1ECC4B4BA1 (void);
extern void Graphic_CrossFadeColor_m6BF11EA2B9F62DF8D9421292EF974D7D548829C5 (void);
extern void Graphic_CrossFadeColor_m0D1181CC2BF5CE521C14C85BE9CEB22EC0129D43 (void);
extern void Graphic_CreateColorFromAlpha_mFFAB1C85CFC981F357FCBFE84DDCFC623E2C804A (void);
extern void Graphic_CrossFadeAlpha_mB3D045B48E9DDE6CE23F4368B875F1307765B192 (void);
extern void Graphic_RegisterDirtyLayoutCallback_m870D9C225888AF117EAB7DCFBC5E629797D22B7E (void);
extern void Graphic_UnregisterDirtyLayoutCallback_m2284BC352FE69018BB15978CB3218C673F29AD9B (void);
extern void Graphic_RegisterDirtyVerticesCallback_m46034B2100B5D28BDBCCB34C1283B1B9B2DB9A9E (void);
extern void Graphic_UnregisterDirtyVerticesCallback_mA36A388BF7DDB2D71596D6F13CEFCA79B4199B5C (void);
extern void Graphic_RegisterDirtyMaterialCallback_m5EDBA1E08656A49997538A1C7DE29201FDE0A013 (void);
extern void Graphic_UnregisterDirtyMaterialCallback_m62B9DB9B9021EC647E1B3D5122FE3693F8FC9BD2 (void);
extern void Graphic__cctor_mF2A854B88E328E94B0091B2E9ACC67559BFD3514 (void);
extern void Graphic_UnityEngine_UI_ICanvasElement_get_transform_m171A3F16EAE82D42EF768C3B091DC87174D5E768 (void);
extern void GraphicRaycaster_get_sortOrderPriority_m0F064AFD3551ABC89DE649D406B032AFA6E3D83F (void);
extern void GraphicRaycaster_get_renderOrderPriority_m1E6278AF3B98742F9F5A293DAF89F75B06E7441D (void);
extern void GraphicRaycaster_get_ignoreReversedGraphics_mC501DBD2D4BD9594F4A5591AFD76AE307EA6BACE (void);
extern void GraphicRaycaster_set_ignoreReversedGraphics_m5CFA68408D296EDCC9230AF7CFB53589BE9F1CCB (void);
extern void GraphicRaycaster_get_blockingObjects_m54343002F72E2C27919DDF5F4088934891AC13FF (void);
extern void GraphicRaycaster_set_blockingObjects_m0CB3F62ABC27BDB348B09B6CF0E6AB4D42A6FBC7 (void);
extern void GraphicRaycaster_get_blockingMask_mDD3BC80288E6B12D2480B40788BA3B69D6F863C5 (void);
extern void GraphicRaycaster_set_blockingMask_mCE08DF88D4D4BFD17358C75DE9F0A8F68DB3BB00 (void);
extern void GraphicRaycaster__ctor_m863917ADCD9732623EBDF53A0CEDDEEB6EA4C42A (void);
extern void GraphicRaycaster_get_canvas_mD4D82F397DA3E82EBA7052E93A20562C2263339F (void);
extern void GraphicRaycaster_Raycast_mCBF5513CAA3AB70569DA3BE50DCF8980819A6D7F (void);
extern void GraphicRaycaster_get_eventCamera_m2EF53324CF216839FA622884418FA77EFB9B3879 (void);
extern void GraphicRaycaster_Raycast_m06B8EF9AC17F7B4FBDB687E3A2C748EF575CCFCC (void);
extern void GraphicRaycaster__cctor_mCE7CB78EE668443FB78303E46D3D62EE92814FBD (void);
extern void U3CU3Ec__cctor_m6A476FFBD4558E7BA60882D6696252685DD826F5 (void);
extern void U3CU3Ec__ctor_mA1FCF997C2A1BC3278AFD9072B0CA4C4273F8F39 (void);
extern void U3CU3Ec_U3CRaycastU3Eb__27_0_m81E2CE6D45AE93300AF014EA75EF4A4B2E4C059A (void);
extern void GraphicRegistry__ctor_m26893FC7AC6ED439CDD999168C66667E27C0B48F (void);
extern void GraphicRegistry_get_instance_mB6879C75347DA916BAECEF49280C8A32375BAC60 (void);
extern void GraphicRegistry_RegisterGraphicForCanvas_m0C0DEF1D00EE4D074927B2592AA0E39EBBC5C935 (void);
extern void GraphicRegistry_RegisterRaycastGraphicForCanvas_m10218EBBB9EBD098CB0E4954902E94C1862222A9 (void);
extern void GraphicRegistry_UnregisterGraphicForCanvas_m31671D141DBAF5B11D8F005E90D6E826362FDC3B (void);
extern void GraphicRegistry_UnregisterRaycastGraphicForCanvas_mAB5A50A86219AE4AE5DD135C93AADC22989B5CD4 (void);
extern void GraphicRegistry_DisableGraphicForCanvas_m9AFAD2245A25194017FDDF31DE9D6F6DD9B7A506 (void);
extern void GraphicRegistry_DisableRaycastGraphicForCanvas_mA4F6606E0E337C952C61773DD6C6109BE27D2115 (void);
extern void GraphicRegistry_GetGraphicsForCanvas_m72A429EAD15F1CFA7F84BE394A3ECA1A00BE7087 (void);
extern void GraphicRegistry_GetRaycastableGraphicsForCanvas_mF0EABC1F1DDCAB05BA144A1C37F1EC0EB606E668 (void);
extern void GraphicRegistry__cctor_m63428B4F697EE7B38C6A4F6C6C724E3A2B4FEC45 (void);
extern void Image_get_sprite_mB2AA377708722E100574F6F75BC102513BB3BCB1 (void);
extern void Image_set_sprite_mC0C248340BA27AAEE56855A3FAFA0D8CA12956DE (void);
extern void Image_DisableSpriteOptimizations_m94966D77FEEF830B1B97C44EAF74843EB94E7C25 (void);
extern void Image_get_overrideSprite_mE3FDFDD768A99DA4F19356E1D3F158A29E7A3C65 (void);
extern void Image_set_overrideSprite_m05036DA9D0E7A173E3A5D2A2156E8E0A50A7983E (void);
extern void Image_get_activeSprite_m0F639A03B26FD25CA1D8EEA006D0B0C322037034 (void);
extern void Image_get_type_m7CE3AA14B38E1C50AC8362176AE842992DA8C639 (void);
extern void Image_set_type_mECB8D34772AA393FFBC867B03D18EA0F1A8546BF (void);
extern void Image_get_preserveAspect_mCF10199F127659628F58CDC7C91E686816B34B5F (void);
extern void Image_set_preserveAspect_mF465AFD1313C0F002B37C8B86C75F98CB72A4098 (void);
extern void Image_get_fillCenter_m4951647922C5C7B1A0243C9536F8CF5A8FDDDC6E (void);
extern void Image_set_fillCenter_m3A5E856A3F877649590F678ED6DDE38B64B14FE4 (void);
extern void Image_get_fillMethod_mAFB1FAAFA913DB0EE050C4053DBBA6FAAD68A5F1 (void);
extern void Image_set_fillMethod_m5361D29BA950BEFE72E7270AC3BFA0B00AE7E294 (void);
extern void Image_get_fillAmount_mDEE52490D07124E21E7CB36718A5E3714D8B9788 (void);
extern void Image_set_fillAmount_m8A9B55F47F966A3214EAC4ACBFE198776A98FAA7 (void);
extern void Image_get_fillClockwise_mD18612EBF815BC5C238D1591039BF9F1D28DF2C0 (void);
extern void Image_set_fillClockwise_mB5DBAFC66370F906EA2CC1D49D49FCC366B64646 (void);
extern void Image_get_fillOrigin_mC9778E141C67C15EC865F6648E5B2545BCC30389 (void);
extern void Image_set_fillOrigin_m2D89BA820DABB26123A33059CA266212E7970B4E (void);
extern void Image_get_eventAlphaThreshold_m19B026C80DB547E702E22A1053FBD0A1BFF2F51A (void);
extern void Image_set_eventAlphaThreshold_m999376263E8A9914F5D69E71B4650D76F283AB6D (void);
extern void Image_get_alphaHitTestMinimumThreshold_m5F6F90EEC3D06F719E9C360A6813A49CDD7EC4BA (void);
extern void Image_set_alphaHitTestMinimumThreshold_m007F9F1C5FD0331E1EDADF4EEE3CB16F6B43F843 (void);
extern void Image_get_useSpriteMesh_m3157E0D7DB2F54EA7B13284F53FA9013F316F7F8 (void);
extern void Image_set_useSpriteMesh_mFA81C2E108CEB33E5F92A9142B2C83B871C3A81B (void);
extern void Image__ctor_m8F922348981CDB74700D89D833FE39611FA4BC37 (void);
extern void Image_get_defaultETC1GraphicMaterial_mCEFD3237CA0090EBED29A81983DC3FE78BAFBAB3 (void);
extern void Image_get_mainTexture_m16CAAF3A2CBF5B3BBB19AC8BD99CE9187C47D3FD (void);
extern void Image_get_hasBorder_m9B09E5452FE8CF13958D7301B01A3A8124ADDDC0 (void);
extern void Image_get_pixelsPerUnitMultiplier_m2B008CF7C16C195A24FDBC5CC7B34531E18F1A18 (void);
extern void Image_set_pixelsPerUnitMultiplier_m05DA43C7FD5B7B162FCB1ED6FCA850FD41AF7DC1 (void);
extern void Image_get_pixelsPerUnit_m319197FFB69E9E8661F46B0DF652F3B3F25D16D5 (void);
extern void Image_get_multipliedPixelsPerUnit_m6F99237811BE288035A4133833611A446BEE6A8A (void);
extern void Image_get_material_m62CEA51BA237569FDB47573CDC125CC3E643A3E7 (void);
extern void Image_set_material_mC1B5D07666D4CF7C4531F2E8424EB2B62A445D19 (void);
extern void Image_OnBeforeSerialize_mF6D870DBB1C6826A6AFBD2F23D5181A2BE47994A (void);
extern void Image_OnAfterDeserialize_mAD5F5C236B40A266EED00C838164502E253957DD (void);
extern void Image_PreserveSpriteAspectRatio_mF56B000B224C2EF11A2EAB4BF465EEA158C5BE1D (void);
extern void Image_GetDrawingDimensions_mE33EF5C86703080A13063FAD318B6C114B80CB1B (void);
extern void Image_SetNativeSize_mC769A2A62A1F5ED648FC64918182CA40D5518817 (void);
extern void Image_OnPopulateMesh_m5B662B655BB6DD663AFBF9DF440DF6C6C2EEF9EB (void);
extern void Image_TrackSprite_m77BFAC0425F494ED236E393B60E6BD26D5B6A5AA (void);
extern void Image_OnEnable_m35B953599A5E65EFEA059E93772D73ACA91BD073 (void);
extern void Image_OnDisable_m453B2333D529FD5359E1F687BFE2D949207AA58C (void);
extern void Image_UpdateMaterial_m3EF2E1AA8D38FAA067FB5AF887B88855EBF1AE9C (void);
extern void Image_OnCanvasHierarchyChanged_m3B34FE2B1BDEE8A04854E9C1ADAC49934FC7EDA8 (void);
extern void Image_GenerateSimpleSprite_m32C9150574E952AE9F5B846AD11A5F0BC8521CC9 (void);
extern void Image_GenerateSprite_mE58FCD6A8B78A30794664E9DEA81A51C5CF6FD03 (void);
extern void Image_GenerateSlicedSprite_mE27E793AAF0D0E30BD1B02A12C7FF08566132EF1 (void);
extern void Image_GenerateTiledSprite_mD6AD2832573EB7AFDDDAC9D31C243AABEA7489B5 (void);
extern void Image_AddQuad_m53D28C1CA949F7C8B1214D15298BDA5E21AFFD21 (void);
extern void Image_AddQuad_m39CF7AAE0605E563F3D0C6CE62639E44BCAACA42 (void);
extern void Image_GetAdjustedBorders_mF3AEDCD9810B2DE6038FF269245899F325366CF6 (void);
extern void Image_GenerateFilledSprite_m3C13BE8BEBBF021D40B2A6AF6A4170055E621915 (void);
extern void Image_RadialCut_m0D5FED1F2A3FFE1985A19E8C8AE990EDFA42C2E4 (void);
extern void Image_RadialCut_m9F8E2FE769EE906D4327A856B6DE9ED73B1AE338 (void);
extern void Image_CalculateLayoutInputHorizontal_m2B3C913A12F299D2ADBC79DCBC2FD533B24E1EC8 (void);
extern void Image_CalculateLayoutInputVertical_mA3259ED5830198EF68B2FE1490491D6761C9CAF4 (void);
extern void Image_get_minWidth_m55A550B01D2E2AA928D77B836B6DDD159EF8B9EA (void);
extern void Image_get_preferredWidth_m8AB595CC948924C3C0014873E4C32FC60CA7F27E (void);
extern void Image_get_flexibleWidth_m76B50FB439854C2E3850E4D1988029BFCD85EEB5 (void);
extern void Image_get_minHeight_m40CDD49A5304B1E96FBA3325A9865F16C782CA4F (void);
extern void Image_get_preferredHeight_m3A6C0CA2FF3F09FD072ABA13D0553783DD5B0A5E (void);
extern void Image_get_flexibleHeight_mF47948629BAA50EC3FC818AD668411A0061EEE6C (void);
extern void Image_get_layoutPriority_m1D4FFA04DF71939657E16CDFFC81A5453ECE0C67 (void);
extern void Image_IsRaycastLocationValid_mB71CF2A446BE3F4C6CF896E8BCA9A36BDF676D21 (void);
extern void Image_MapCoordinate_m11D428E63DF2AEB1A5866A0AE778E5287F4776FF (void);
extern void Image_RebuildImage_m5BDCACEE109C4EF96B8F783BCB71FEA9A72E0E45 (void);
extern void Image_TrackImage_m24AE9D703DB406780DA6975F648C587CA1F62EDC (void);
extern void Image_UnTrackImage_m59DCA4A9F6ABE55046D24006FCC7373FC0717A0C (void);
extern void Image_OnDidApplyAnimationProperties_mA079140EDEA8341023066DC950E94F38C61EEE27 (void);
extern void Image__cctor_m67595BC3057DCFD5A6593929CA673CE415F5803C (void);
extern void Image_U3Cset_spriteU3Eg__ResetAlphaHitThresholdIfNeededU7C11_0_m0ECCFF2F9B90F50D42D720113D585420A1AB661D (void);
extern void Image_U3Cset_spriteU3Eg__SpriteSupportsAlphaHitTestU7C11_1_mF1454043FC6ED87CE73456C4A45CE2070D6099D3 (void);
extern void InputField_get_input_m23129FACBD4CDCEA3FC9A977D7DA5BBD4BBB0B2B (void);
extern void InputField_get_compositionString_m5E9F323DE7B62EBB69AFC569C05ABC00F619FC4A (void);
extern void InputField__ctor_m06B9629E3C878D578A8B43C1A8835B526629D6E5 (void);
extern void InputField_get_mesh_m89CB1A4155FF8E7C42D5D97178DD00A3A7D8888E (void);
extern void InputField_get_cachedInputTextGenerator_m42F16837E9BC49BB43F58163B827C4260303E48E (void);
extern void InputField_set_shouldHideMobileInput_mC3759A3E3DED19B9EC01E30CB810922772894C76 (void);
extern void InputField_get_shouldHideMobileInput_mA752B065435F4062EFB931119C34FDB5B35157E2 (void);
extern void InputField_set_shouldActivateOnSelect_m5F21C9511D040820CFF661E56145C25D147D17A5 (void);
extern void InputField_get_shouldActivateOnSelect_m4DA84FAEB2FFB6F036A3821675730842FF86245F (void);
extern void InputField_get_text_m6E0796350FF559505E4DF17311803962699D6704 (void);
extern void InputField_set_text_m28B1C806BBCAC44F3ACCDC3B550509CA0C7D257F (void);
extern void InputField_SetTextWithoutNotify_m2CD8DAC2A298CBABFCEC654A17294427DDD238A3 (void);
extern void InputField_SetText_m66574324D7550D728E41F71DD704CDCDEADF9E66 (void);
extern void InputField_get_isFocused_m19BD51E842077CA087824025F294C4078B2DAC50 (void);
extern void InputField_get_caretBlinkRate_m5D6172BA3B84F25897444A1A469AA53FC5CE5613 (void);
extern void InputField_set_caretBlinkRate_mCE440AA4049C7A1EDEDB63E5B0AE4005563C5226 (void);
extern void InputField_get_caretWidth_m6D85BF105006F28ABF2940033BEED2E595C89E55 (void);
extern void InputField_set_caretWidth_mD71B00146099D90D920F4F63A7032E8AEDD39915 (void);
extern void InputField_get_textComponent_m319EF4B9B24056AF25327874A2455362FF7B7A85 (void);
extern void InputField_set_textComponent_m09DF6BBF8544028D98D68D3F905AAAE17486D272 (void);
extern void InputField_get_placeholder_m84C2F2E414B8A03B372C7CEB3C97A2AE72F3A39F (void);
extern void InputField_set_placeholder_m64F47B180F584EB1049CF8B501DAC3FCA9029F25 (void);
extern void InputField_get_caretColor_m92C8BB7D9BD4B4DAE361494F85418F834EE87832 (void);
extern void InputField_set_caretColor_mF9C606AA2F9F123CB6AD078DF616DE35061FF830 (void);
extern void InputField_get_customCaretColor_mB1D8A9DE8CD1787B3614BAF3E50E27B2428C7215 (void);
extern void InputField_set_customCaretColor_m7CA0470187246247EEC354FEB7053E4B4911DC13 (void);
extern void InputField_get_selectionColor_m988C5ACE38195B9B6397352B5A226FF3867A6E54 (void);
extern void InputField_set_selectionColor_m2B7800A90FCE0840800CC01EC2C17059634B015E (void);
extern void InputField_get_onEndEdit_m92C86FF7CA6108C4B14392CED20C9ED9D39AD9A3 (void);
extern void InputField_set_onEndEdit_m0AA121171524CB10C4BE4692117839A97E6AAD08 (void);
extern void InputField_get_onSubmit_m66A3BFEC3D3D5C261558043FD606D4FBCC7D478D (void);
extern void InputField_set_onSubmit_m1763F344243E5E3CF28F07872A80AAF809FC1988 (void);
extern void InputField_get_onValueChange_mF6915B4F33F4B24A91D8E56DE20EFFAE25C59756 (void);
extern void InputField_set_onValueChange_mA1AEDDDB12CEC499949DB0605A83D8F383212CEA (void);
extern void InputField_get_onValueChanged_mA9ABE178FE3EB05AEF3DC20C11349427C59916AE (void);
extern void InputField_set_onValueChanged_m2B2F8D1E8F5FE418CE0797F2534B61A1A45B8A85 (void);
extern void InputField_get_onValidateInput_m370D93274B6040422092981DD3A34E4B88E96EBC (void);
extern void InputField_set_onValidateInput_m3A3FA74285B9BBA68325A91AA862201AF3A18CE4 (void);
extern void InputField_get_characterLimit_m7FE26FC66741545B89BFFDCAD8E8B34EB1274403 (void);
extern void InputField_set_characterLimit_m98A2187FF493DB170821C39A6D069731F3AFFF2B (void);
extern void InputField_get_contentType_m8C589B15987EB8852D5F4948A79084186935B19B (void);
extern void InputField_set_contentType_m5C3DDD7C14781E963BFFC88F7A8A537919F34C59 (void);
extern void InputField_get_lineType_m6CEA63D8FCACAEC05D3499577ED0771EFFF33377 (void);
extern void InputField_set_lineType_m06BE148366DF8F17E0F91C3CF094628C201B5FD8 (void);
extern void InputField_get_inputType_mC324081499638BC8AAA45CC110536C016C707BD0 (void);
extern void InputField_set_inputType_mB2A3B667DC710AD1F9E1C046659AC35720AB0313 (void);
extern void InputField_get_touchScreenKeyboard_m99338FA7655276193EE1BA8FCB821C7F1928B3D8 (void);
extern void InputField_get_keyboardType_mCF9432AC88C35E77546235909346C5689682E620 (void);
extern void InputField_set_keyboardType_m9DD165B20CF12F93BD85140D8D1F54371FF4E9F3 (void);
extern void InputField_get_characterValidation_m02AD706E70817147BAADD487DAC73D79547BCBBF (void);
extern void InputField_set_characterValidation_m9DE08B33714B9D97F570853ADB56C070C2DD4072 (void);
extern void InputField_get_readOnly_m37800B8623CB744D99E5F5607C80AEBE6C7043B3 (void);
extern void InputField_set_readOnly_mD70582D7F885929AD7CF28BF083623991C5F543F (void);
extern void InputField_get_multiLine_m4AF37C1E2560778A214C50E91C472430D8F777B6 (void);
extern void InputField_get_asteriskChar_m2556CE9FA8ABF5C00552BA665299F71EAC7D55C5 (void);
extern void InputField_set_asteriskChar_m26FC4CE6C8637E49ADE854769F6C777A6BEF5CB6 (void);
extern void InputField_get_wasCanceled_m75E09A773352839E08B04B33F966ED3E849436E9 (void);
extern void InputField_ClampPos_m8939841884C3CD51A6169F5DA05A85CC3C16A371 (void);
extern void InputField_get_caretPositionInternal_mF01180C72008CCDD2A5371EE45B84D7745CB6BC0 (void);
extern void InputField_set_caretPositionInternal_mA35B05D5FF035A060967C6E456610D659367C3EA (void);
extern void InputField_get_caretSelectPositionInternal_mBAE2F71F18603A0C4A6AA08F0BFE5831CBBBA461 (void);
extern void InputField_set_caretSelectPositionInternal_mCA096AAD610587421E739BDD195A1680FD93A75A (void);
extern void InputField_get_hasSelection_m3E8EF152E7A7238C8F0631FFC16727800CF16B24 (void);
extern void InputField_get_caretPosition_mC43674CCFF5BF7D047C2D4682B2CD7DE8A179EA7 (void);
extern void InputField_set_caretPosition_mF502AA3301C39D4397C7BF809D1F3A18D0603BD7 (void);
extern void InputField_get_selectionAnchorPosition_mF5CB19025C29DECEA0EBA8C6EC3D6D5687A1D65E (void);
extern void InputField_set_selectionAnchorPosition_mE57B85DBF03991E694729ED36283B44A8D7D1E68 (void);
extern void InputField_get_selectionFocusPosition_m14D662A0A20FF6952E73CFAB7C1F21FD7CF4298A (void);
extern void InputField_set_selectionFocusPosition_mE9E0E491C5AC1B89B4F9272EC3B67617A4F7DFEB (void);
extern void InputField_Awake_m7253E5687FD0D44982BA34EA523894C0CBE927A6 (void);
extern void InputField_OnEnable_m00FE61194E553F736B0C1AABC73A79EEDE81D9AF (void);
extern void InputField_OnDisable_mA79B9B02E48BE7F1AA6C94C6CECB7A6AB323AB8B (void);
extern void InputField_OnDestroy_m551000531722FAD0D2DEB4CA9A76EF25A7067EAA (void);
extern void InputField_CaretBlink_m030EE72571B48D2CD7E346D68B0F236C9BB25CB5 (void);
extern void InputField_SetCaretVisible_m9DB05703AF6B427F53FB4948BB592CF061AA37AB (void);
extern void InputField_SetCaretActive_mC91972AACD936D757447E3F7967CE2DAD4B46D0E (void);
extern void InputField_UpdateCaretMaterial_mA2C86C0AFC38D35509A3BD66A10411AF7D13FFD4 (void);
extern void InputField_OnFocus_m5EC2CB19FBDAA84FB317F5ADA86548D78B550F37 (void);
extern void InputField_SelectAll_mC3A2CAB32B290BC43782A61452760BD127E729EA (void);
extern void InputField_MoveTextEnd_m1C20AF9DB90F79CD85C4DAB179DA4EDB4D971810 (void);
extern void InputField_MoveTextStart_mE56A94C2D4AE751A3BE1035250880D9B592BF130 (void);
extern void InputField_get_clipboard_m4ACB240747BB6AF77A3FEF28A63A5C2B2A049543 (void);
extern void InputField_set_clipboard_mA8C4BC1DA5B1C12F8A7E7880E0F74185E2D8BCDB (void);
extern void InputField_TouchScreenKeyboardShouldBeUsed_m56104E5B7C58A89C552D4CF8FD7A1B1D93D7340A (void);
extern void InputField_InPlaceEditing_m1F71173373CC2A21034D23ECA0060FA4E5A89F11 (void);
extern void InputField_InPlaceEditingChanged_mE02AC706260B93670AF1380BE4060F3AA4063C47 (void);
extern void InputField_GetInternalSelection_m35675AC10C34AD29A54A6E980CDA925CB1BFC6F3 (void);
extern void InputField_UpdateKeyboardCaret_mF290D51F06E5AC063A8FE6FD20F81C5C70792680 (void);
extern void InputField_UpdateCaretFromKeyboard_mCFB186696BE23B347D7AA94DF50A13555C31F8B4 (void);
extern void InputField_LateUpdate_mA1C1B81011E3D2F3D6F0769C0FE0D4B0A8E71020 (void);
extern void InputField_ScreenToLocal_m3ABFAAAC443370A1621926D80EA665CF421CAF9E (void);
extern void InputField_GetUnclampedCharacterLineFromPosition_mDD25BDEA1097899537A5D7E8881F23D3D49327DC (void);
extern void InputField_GetCharacterIndexFromPosition_m9C0D9CBB43A1CCC47F7B4234379668E46AE3EB32 (void);
extern void InputField_MayDrag_m72ED9A80A46F59B07697E415E1D691084BC133E6 (void);
extern void InputField_OnBeginDrag_m3A945C4E07937EDA5E99447572F5F167F1143691 (void);
extern void InputField_OnDrag_mEF28C06EFB5024C1E236C5A21E715B62CA87BE84 (void);
extern void InputField_MouseDragOutsideRect_m464392D721204B540DC92E449B48BCB04BCFDABC (void);
extern void InputField_OnEndDrag_m0BAA34E5BDBC9A3E241F8BC7DBA8172DD5D9651B (void);
extern void InputField_OnPointerDown_m4A3A77DDBA95CB4E50A4BFDF0EDD59B5A9191BF2 (void);
extern void InputField_KeyPressed_mD6FAC314D8211F43C4C041AE87B3290665A05D28 (void);
extern void InputField_IsValidChar_mDFF88F1042D52286FDCD5D7302706C837265876D (void);
extern void InputField_ProcessEvent_mF905BEF5A4CFF9144159FA40DE2F9DFD4A967358 (void);
extern void InputField_OnUpdateSelected_m36FFEE16138CDDCA30643962A4C5A41763FE2E55 (void);
extern void InputField_GetSelectedString_mDF15471A4398D6D7B391105A8549F09DC03DA283 (void);
extern void InputField_FindtNextWordBegin_m1152E725F12932E30E304F4F10A42B0733201A18 (void);
extern void InputField_MoveRight_m0D51E23BE4EF55EA54DED277573263BB2A5B1D38 (void);
extern void InputField_FindtPrevWordBegin_m54E76FA4BF8AE95109D2F78EA0814751837F5AF7 (void);
extern void InputField_MoveLeft_mD7E3870F7E54009522CF9412764FD5FD9212BBAA (void);
extern void InputField_DetermineCharacterLine_mD80BD8A0F49EE45FA6E512796D3A4A15462D97BC (void);
extern void InputField_LineUpCharacterPosition_m6E5C0F57795B5CF3D588EFF099A65D90E60848A0 (void);
extern void InputField_LineDownCharacterPosition_m3212B8EC92092E97AC60D072EFBD385FE72CA829 (void);
extern void InputField_MoveDown_m365DDF603B2D68FD98B0240F3302886FF7CFF16E (void);
extern void InputField_MoveDown_m13622D37FC022939623A9DBC447E49F5D9F43C80 (void);
extern void InputField_MoveUp_m4703516BEB5B1A3C4020895BABD0558427BE7895 (void);
extern void InputField_MoveUp_m7F41FF9D5EA2BF64B36C1ACABB67169722C668DD (void);
extern void InputField_Delete_m12AD40195316F01879910401E6E0DCEC7F5A8132 (void);
extern void InputField_ForwardSpace_m4CF251F5CE00CF4918EA0C2D322770A4B556D4E7 (void);
extern void InputField_Backspace_m4BDCF533ECD04258884076830CB4F0907FCED3E6 (void);
extern void InputField_Insert_m925B9FADD75785B8FDD886477F0B0CC1E0B4C718 (void);
extern void InputField_UpdateTouchKeyboardFromEditChanges_m68C429349526101B885D038FFD0C2935151E0772 (void);
extern void InputField_SendOnValueChangedAndUpdateLabel_mEB064D57921681BB49F55AA796E046A951DAA7BA (void);
extern void InputField_SendOnValueChanged_m52131907987E99A872F6007B599345A2ADD244AC (void);
extern void InputField_SendOnEndEdit_m79E2689DD75F72FDA8157EECD3F17391D187094B (void);
extern void InputField_SendOnSubmit_m933C160291FD9118A9EC7FD7AED5E805B998BA27 (void);
extern void InputField_Append_m78F45E67DDB94E034940730969D199A971C7D1F1 (void);
extern void InputField_Append_m22A6348E74FB83932286AC1CDD73322C05BBC63F (void);
extern void InputField_UpdateLabel_mDBE25D21A1021AE4563539586438B5EA89511D58 (void);
extern void InputField_IsSelectionVisible_m2A7FD156812466D2D2397B57959BF91BACC52EB0 (void);
extern void InputField_GetLineStartPosition_m6ABF6AFB8A9495D7A5446B577EB2ECA8770A9660 (void);
extern void InputField_GetLineEndPosition_m690864C08F9250D76D718D5D54611C886AAA0A79 (void);
extern void InputField_SetDrawRangeToContainCaretPosition_m0F3F4E0179627915136B2B2927CD234304E8432C (void);
extern void InputField_ForceLabelUpdate_m49441594294B33C5DC10D717198A476B523EE1C8 (void);
extern void InputField_MarkGeometryAsDirty_m71DCE40033F96C4A842885A7601E3882FF0BD4F4 (void);
extern void InputField_Rebuild_m4974AB56B494186177AB4BA6C2626BCB0EF93304 (void);
extern void InputField_LayoutComplete_m7953946E63BF48E14CE1FF13D76FCAA832735C7F (void);
extern void InputField_GraphicUpdateComplete_m25B7375B32DC3384EF8684ADDAB6996359668DBF (void);
extern void InputField_UpdateGeometry_mABF2E288AF71AF5C8E608F30745D6BAE40A9CB4D (void);
extern void InputField_AssignPositioningIfNeeded_m114957547C208AD107279D1B6E8A855D18915E36 (void);
extern void InputField_OnFillVBO_m84E1576406EFFC37D6EFDDD4604B393E281C5BA2 (void);
extern void InputField_GenerateCaret_m401461627986E86804E31BE16332003BDCD9EF98 (void);
extern void InputField_CreateCursorVerts_m2170881250E5F316805946E87EA1F1A794E6AB23 (void);
extern void InputField_GenerateHighlight_mD1A67441901D78AE29E17A655791754A92EEC072 (void);
extern void InputField_Validate_mBB63D4E37F8CD96C0F57270259DDE69E3BCB7656 (void);
extern void InputField_ActivateInputField_m4986DE5488FE44D93DE1D906C140D6500134DF05 (void);
extern void InputField_ActivateInputFieldInternal_m5B89A6BBCE9D7DD6F0A3DF4B6296533507170119 (void);
extern void InputField_OnSelect_m723C2F0E81FAFF8264CFE4596CA2AF30B7D9E307 (void);
extern void InputField_OnPointerClick_mCADA1FE2E0B6EA1F6A9B69DB3790E752243BA4F3 (void);
extern void InputField_DeactivateInputField_m58D0B3BF095094A0963A9CE8BABF1979F7D1254D (void);
extern void InputField_OnDeselect_mA31D1383106BAF91CB638C04E508322FBEB2EFDC (void);
extern void InputField_OnSubmit_mFEBD3EF3B76741F19E84A12FBBF9B5BB60E5952C (void);
extern void InputField_EnforceContentType_mB8A7743C77E3EAE952426EF14BB5BE5B80E7488A (void);
extern void InputField_EnforceTextHOverflow_m7F0E61391D942F47B4AD128C0C8B9B204BBE14B8 (void);
extern void InputField_SetToCustomIfContentTypeIsNot_m8E1B8AF7133B6B42F9E6BA3951AE2AA4D2AF1071 (void);
extern void InputField_SetToCustom_m1D8B546B458993E86A24A05B868C57286E8C6BF4 (void);
extern void InputField_DoStateTransition_m51CFACBDB11404C6F10D0BA3AACB394036CB35A8 (void);
extern void InputField_CalculateLayoutInputHorizontal_m291256FA87BF5E7F0D7CD64205B58E6B7E88809B (void);
extern void InputField_CalculateLayoutInputVertical_m4102477D8FA249BA49FDF9C0CE5F45A42752B083 (void);
extern void InputField_get_minWidth_mE316201A4474E22FA455CFD381C0A73B76CF5B06 (void);
extern void InputField_get_preferredWidth_m13ACB831ECB400033C936A46342FF10E8A96D05B (void);
extern void InputField_get_flexibleWidth_mCCBC75043CD2BF11B0E38D71A00A5CE790DD9E8C (void);
extern void InputField_get_minHeight_mC742ED6E8E46602EE8C085F724AD5442A24DB1D7 (void);
extern void InputField_get_preferredHeight_m7C3EAA7E8DC12397B9C83A72582C8FC219BA63DA (void);
extern void InputField_get_flexibleHeight_mE4CA2B68F90E91C6B884D87FF98D3CA062332A6D (void);
extern void InputField_get_layoutPriority_m88277B59E761DA55E6DF1AA803B0DC629ECDFE3C (void);
extern void InputField__cctor_m963ABF5968D8C97B8286CD633B0B0B4691ACEBBD (void);
extern void InputField_UnityEngine_UI_ICanvasElement_get_transform_m68143981855D6B92BF815F3058EA2F063A63D59A (void);
extern void OnValidateInput__ctor_mDC0454BF264F87154EF8694821905B5A6A587A29 (void);
extern void OnValidateInput_Invoke_m6A7776E0E91552E39F207A90C7E33A4D4479F076 (void);
extern void OnValidateInput_BeginInvoke_m2A003B257BC355C137B83FB37B3D9DD34821F9D2 (void);
extern void OnValidateInput_EndInvoke_mE13A5F2C1260AD59F20BFBD7CCC9BE42F84EF6DA (void);
extern void SubmitEvent__ctor_mE8908589516FD77AA786BDACC7BEBC2182A87EE3 (void);
extern void EndEditEvent__ctor_mEAA90FD69A3F6F34EF977AF11A424CEEFF441953 (void);
extern void OnChangeEvent__ctor_m3D387EF9F415EC6E177649A23DAA137AB98F3E05 (void);
extern void U3CCaretBlinkU3Ed__172__ctor_mD71554D61758324CCBD8F37F5CE8249169AA88F6 (void);
extern void U3CCaretBlinkU3Ed__172_System_IDisposable_Dispose_m4B3174F229D803FBEC9FE749FE1A76E0A17A7AF1 (void);
extern void U3CCaretBlinkU3Ed__172_MoveNext_m725171803230FB9AB7A1FD06EA915CE483335D82 (void);
extern void U3CCaretBlinkU3Ed__172_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m6F0710FF54BBA7F57D031169B56C02C9D0503926 (void);
extern void U3CCaretBlinkU3Ed__172_System_Collections_IEnumerator_Reset_m880C7F0BD8A9138228E0D9C61A53D0C7FF616BCA (void);
extern void U3CCaretBlinkU3Ed__172_System_Collections_IEnumerator_get_Current_m047B5A2A48DE0ADB8ADA21A1B9C70A81F7ADD9CE (void);
extern void U3CMouseDragOutsideRectU3Ed__196__ctor_m4D872B98EC92F270BC3DBA1CAC11BB23B4E3A701 (void);
extern void U3CMouseDragOutsideRectU3Ed__196_System_IDisposable_Dispose_mCC11E5EB4E68249708984A0913B46AB4CB922E61 (void);
extern void U3CMouseDragOutsideRectU3Ed__196_MoveNext_m612C8A3B05E3FBF76D294B1B25E4BBFFA5CEF388 (void);
extern void U3CMouseDragOutsideRectU3Ed__196_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA1214C3CE058B3933969D9AB9FBE5C6F90D6C4A7 (void);
extern void U3CMouseDragOutsideRectU3Ed__196_System_Collections_IEnumerator_Reset_m7D896FDF203405E7EAADF1F2C6FC180531BA98D6 (void);
extern void U3CMouseDragOutsideRectU3Ed__196_System_Collections_IEnumerator_get_Current_m0EDF9E4AFA373A221F99E8A9C102A72D8D82D5FC (void);
extern void AspectRatioFitter_get_aspectMode_m530AE9878F26D7C1166C2AC3C2B2547CED122B27 (void);
extern void AspectRatioFitter_set_aspectMode_m1CDA777FF728BD01AB939C074D03F9C18675FB65 (void);
extern void AspectRatioFitter_get_aspectRatio_m72A1972D15B7435EF895562EEF0AE8C689ED120E (void);
extern void AspectRatioFitter_set_aspectRatio_m4192E203648BE0ACA39D9C0540C982331CEA91D9 (void);
extern void AspectRatioFitter_get_rectTransform_m20FD6C51C01C8BBC2C8223C255F9C28E00689496 (void);
extern void AspectRatioFitter__ctor_m277805022C03480F2EDA97E7AA48D33839EBD102 (void);
extern void AspectRatioFitter_OnEnable_m5B6FCCB531F87ABBAEFEB38AEDA1340E10EDEFDD (void);
extern void AspectRatioFitter_Start_mB88F08EC9C3453EAB41607D91825BD90C6391F64 (void);
extern void AspectRatioFitter_OnDisable_m43DAB6B9ADAE9683A99395FCD7B769879A75477F (void);
extern void AspectRatioFitter_OnTransformParentChanged_mD5F9B727921A4416EB663B4152CCB499EFB8B111 (void);
extern void AspectRatioFitter_Update_mE1F6FB785AB83C3416B67EEC58CDB74144583230 (void);
extern void AspectRatioFitter_OnRectTransformDimensionsChange_mE94570689C2A5CCB2C2EB33F9C5F240E780A2784 (void);
extern void AspectRatioFitter_UpdateRect_mFAB08DEB6F064E439934183C92038428ADA0F235 (void);
extern void AspectRatioFitter_GetSizeDeltaToProduceSize_m467FB832F233366AC4B795E932F05740E6429A4D (void);
extern void AspectRatioFitter_GetParentSize_mA3CFC47B4F43532BCA0267F99911DC2CA1FFE9B0 (void);
extern void AspectRatioFitter_SetLayoutHorizontal_m49115A35A48158B48CD198028034214F95793FAE (void);
extern void AspectRatioFitter_SetLayoutVertical_mAC5B9A89C0E8659BB590BD62608D22349B90D612 (void);
extern void AspectRatioFitter_SetDirty_mCCB04E2ECBD43C874822C58BEEAC00AB7EA8A58A (void);
extern void AspectRatioFitter_IsComponentValidOnObject_m163BC9DE4258B1C308B850BC259704A3D285A3B0 (void);
extern void AspectRatioFitter_IsAspectModeValid_m0311FF067288EAD9D2A81ED3A4151C406F0A30B7 (void);
extern void AspectRatioFitter_DoesParentExists_m9475339C8EE94000A65B9F39AFB08A867940D925 (void);
extern void CanvasScaler_get_uiScaleMode_m8E92609E011796E8CC23B1739F95CE7BE2631525 (void);
extern void CanvasScaler_set_uiScaleMode_m064C83FFA35E2AED4E9FA7D5EC1AD19630D8FC2A (void);
extern void CanvasScaler_get_referencePixelsPerUnit_mE0A7FECC27003A4A2BE6AE6E70747FAC8C19A008 (void);
extern void CanvasScaler_set_referencePixelsPerUnit_m8817BAEB73BE78DD7C87EAB7D2FE2983B2300628 (void);
extern void CanvasScaler_get_scaleFactor_mB2BFA22B99AEC96F09886F490DA9EE2F825D3431 (void);
extern void CanvasScaler_set_scaleFactor_mD53E8CAE41E8C1B0DF53CCF14D5941FF8EA3488B (void);
extern void CanvasScaler_get_referenceResolution_m79C03DD8CE6759B045928C5339A3C5E6220276B5 (void);
extern void CanvasScaler_set_referenceResolution_m793679B8505AF9BBF64F45D80AFE39F3F99FAB8D (void);
extern void CanvasScaler_get_screenMatchMode_mA07ABCCF6AFE98C16651EBD5AB24BFF08B10F768 (void);
extern void CanvasScaler_set_screenMatchMode_m926C437B408D2F2CA4900723BEEEE09504A6768F (void);
extern void CanvasScaler_get_matchWidthOrHeight_m9C40FBA943172874FD27F3F7B880E2D5D5862C9B (void);
extern void CanvasScaler_set_matchWidthOrHeight_m44635DC3E4424255C312814C325A48E37E6B6E30 (void);
extern void CanvasScaler_get_physicalUnit_mD4B04FD2D68F8C3CA39550C056A7AFC836DEB6EA (void);
extern void CanvasScaler_set_physicalUnit_m6A759A32FFBEBC43A51C98621A3F505289670C5C (void);
extern void CanvasScaler_get_fallbackScreenDPI_m966C603918C0420EAB4C3048591DE408190FFAA2 (void);
extern void CanvasScaler_set_fallbackScreenDPI_m01E7CB32B519FBC9F5A77F060EE0B2DF7D6895AC (void);
extern void CanvasScaler_get_defaultSpriteDPI_m2F1CDF6DE4F2B2E3DED10D50D6E674699120C50A (void);
extern void CanvasScaler_set_defaultSpriteDPI_m742DFE7A3315C0B33763D2E3FB2424BCFF35D3DE (void);
extern void CanvasScaler_get_dynamicPixelsPerUnit_m6DFC581EFFD626F6815BA8C9579DD736514626AB (void);
extern void CanvasScaler_set_dynamicPixelsPerUnit_m7A081D5FD963F751140DCF1E5190ED4E51308CA2 (void);
extern void CanvasScaler__ctor_m0D60150B065E8CFBCB4BC324F364A0FF08762493 (void);
extern void CanvasScaler_OnEnable_m9F50E6AF109CE6227FD9E523B0698925B89D29F8 (void);
extern void CanvasScaler_Canvas_preWillRenderCanvases_mDBBF36EADD3DFBE62E1E5F14D0DC9BB86FC21E6A (void);
extern void CanvasScaler_OnDisable_mE0CE97F651B806DD2B2565203A00E97A6A781B2E (void);
extern void CanvasScaler_Handle_m0EF8A30C92B8A90A54D2B0BB06E7698E74AD5967 (void);
extern void CanvasScaler_HandleWorldCanvas_m3E325EB0AC3221EA44B3D81360DFE63C36C13190 (void);
extern void CanvasScaler_HandleConstantPixelSize_m7C504A9281A98E3473F0113CD74A9305AE4C5CD0 (void);
extern void CanvasScaler_HandleScaleWithScreenSize_m3F436166B074013EDBEE38B7009C338650CF942C (void);
extern void CanvasScaler_HandleConstantPhysicalSize_m44CEBEFEE2AAD54993DA3A43047E86AE07B32DD7 (void);
extern void CanvasScaler_SetScaleFactor_m195FFD8019696523653CA6CB1B8531ECE4020636 (void);
extern void CanvasScaler_SetReferencePixelsPerUnit_m77B9E51B468EC9750355687AA6E25564D60BE9B5 (void);
extern void ContentSizeFitter_get_horizontalFit_mA5FBF6AB42F551272B94A7B89A372B1AA1ADBC0D (void);
extern void ContentSizeFitter_set_horizontalFit_m7B0DB223B08B8D578F749DEC381349E7D66DCDE4 (void);
extern void ContentSizeFitter_get_verticalFit_m3F2848F19A5F8F30F55E0B5D930EFEF4E5EFAFF5 (void);
extern void ContentSizeFitter_set_verticalFit_m8F61CFD01D4C3D3DC253F30BA8FC2F44F8F927CF (void);
extern void ContentSizeFitter_get_rectTransform_m757AAC9852D5C462C083FDA80390813E4FF06467 (void);
extern void ContentSizeFitter__ctor_m60693679801693DCDEC5BF0FD45590BD66F2434A (void);
extern void ContentSizeFitter_OnEnable_m31DA9C05A1B5FAB9BD1BE05C43192B427C156CD3 (void);
extern void ContentSizeFitter_OnDisable_mA11B1667210796F7DEE199F2B78844A6CA0C720F (void);
extern void ContentSizeFitter_OnRectTransformDimensionsChange_m427809780F5D59796CDB386A8CD5B4DB985D7691 (void);
extern void ContentSizeFitter_HandleSelfFittingAlongAxis_mA050224EA492DF6C8B339DC36FC3BB8ED5D09A85 (void);
extern void ContentSizeFitter_SetLayoutHorizontal_m694E40D536D88366735B3838FA040EB2D2144320 (void);
extern void ContentSizeFitter_SetLayoutVertical_mB58DDF80917329DFAE202DA73472AD39BF37E561 (void);
extern void ContentSizeFitter_SetDirty_m5A4C67937A3C77E467881648D5B9D7AB4E8C5C59 (void);
extern void GridLayoutGroup_get_startCorner_m0796B782C9F3981B6E97F83A6815102A5176657D (void);
extern void GridLayoutGroup_set_startCorner_mCE5A1E957B06BF34173119A5C62B832E279DA78A (void);
extern void GridLayoutGroup_get_startAxis_mADFB75A761550B3141256B0130655A6703FF3FF5 (void);
extern void GridLayoutGroup_set_startAxis_m2C9BCD2A1CD3ECFDDF3B0A8B7EE28C48179A7739 (void);
extern void GridLayoutGroup_get_cellSize_m30D8A051F44C8EE0C87B6D6CDDC00C2592A78B6D (void);
extern void GridLayoutGroup_set_cellSize_m0A3FF07694BDBF52D973597978FC87B0941BE5F9 (void);
extern void GridLayoutGroup_get_spacing_m19BC15652BF18D051B0998C14F13DB83191F3E58 (void);
extern void GridLayoutGroup_set_spacing_mA5550A683F7B4A7A1510B267B5D4CACEB8981306 (void);
extern void GridLayoutGroup_get_constraint_mAEC0A95B4DF9F48E07B5403CC5F954AFDE503029 (void);
extern void GridLayoutGroup_set_constraint_m632CB37D0D79A12DE81372EE819348CD1226B84A (void);
extern void GridLayoutGroup_get_constraintCount_m63AE4B7889A27D8CAA8EB04A40B1FE53D80CC318 (void);
extern void GridLayoutGroup_set_constraintCount_m685F6D5254B6D77AF8BE070EF3DCA5F049B3D043 (void);
extern void GridLayoutGroup__ctor_mBC2ADB7B7F092C83138425C82DEDBB6701F73F7D (void);
extern void GridLayoutGroup_CalculateLayoutInputHorizontal_mFDEDFB79ECF5C03713EE1C128362D3AC0D48ED8E (void);
extern void GridLayoutGroup_CalculateLayoutInputVertical_m41E33CD0EBF75155C0B842E9EDA2C66EB68AA9EA (void);
extern void GridLayoutGroup_SetLayoutHorizontal_m16F35F3DA5B7AED47787C0EBEC723723DC9034F0 (void);
extern void GridLayoutGroup_SetLayoutVertical_mAF83C49C8BBA29EC4465B1BC2A8A39B0321FB038 (void);
extern void GridLayoutGroup_SetCellsAlongAxis_m815D9BF1B794A46C96CFE3E069C49274FCB66739 (void);
extern void HorizontalLayoutGroup__ctor_m811D870AB5F67030CD9A3C1FC02FFE69298131BC (void);
extern void HorizontalLayoutGroup_CalculateLayoutInputHorizontal_mB2C54B2F51CB18A490867DE302D6444C93ADC537 (void);
extern void HorizontalLayoutGroup_CalculateLayoutInputVertical_m8739924AF17AA7FD9061BBDEBECFC3E2C946D27E (void);
extern void HorizontalLayoutGroup_SetLayoutHorizontal_mA4203F549D73128EB605594C74DA47CA07278A25 (void);
extern void HorizontalLayoutGroup_SetLayoutVertical_m6B8A658837C88E6A29A9850725734F9C5CA67B82 (void);
extern void HorizontalOrVerticalLayoutGroup_get_spacing_m916C9BF57D4AB0EF76E6BC4EC5E1EA54B7918782 (void);
extern void HorizontalOrVerticalLayoutGroup_set_spacing_m90373F54D37DA8DFA90E102DC60EC33E542FD859 (void);
extern void HorizontalOrVerticalLayoutGroup_get_childForceExpandWidth_m07A6B6378938DA69E365DCFB2794EEE7D71CC510 (void);
extern void HorizontalOrVerticalLayoutGroup_set_childForceExpandWidth_m351827AA1A453ACD17C2EAC7B4DAB9C5DB1760E5 (void);
extern void HorizontalOrVerticalLayoutGroup_get_childForceExpandHeight_mFCBB20057EDC1E7B2DFD56FB6ABFE9A462560741 (void);
extern void HorizontalOrVerticalLayoutGroup_set_childForceExpandHeight_mA144CF421614F41813DE346AA9D1C64621C6C2E5 (void);
extern void HorizontalOrVerticalLayoutGroup_get_childControlWidth_mBA38BDC393C180CFC30DA02478B493D6CCD92AB1 (void);
extern void HorizontalOrVerticalLayoutGroup_set_childControlWidth_m0B9A78B8284E17C438645684984796AC0E2D1BD8 (void);
extern void HorizontalOrVerticalLayoutGroup_get_childControlHeight_m867F7E1D52F29ED8F9E5F060089800295E186AA4 (void);
extern void HorizontalOrVerticalLayoutGroup_set_childControlHeight_m8DD189C9B1F926641F4A2FD41F41F2097E4D7751 (void);
extern void HorizontalOrVerticalLayoutGroup_get_childScaleWidth_mF5057406C963AB6CB70DC1B2B213A1F5F7C97E91 (void);
extern void HorizontalOrVerticalLayoutGroup_set_childScaleWidth_m96A12D7E1C6BCDD510EC08FC470FA5F69B90922D (void);
extern void HorizontalOrVerticalLayoutGroup_get_childScaleHeight_mA5AD05DFD31E25C5C014C24B5B11DC5492A2E893 (void);
extern void HorizontalOrVerticalLayoutGroup_set_childScaleHeight_m519A990CF97DE1C974DD1F48466763E4AEC648BC (void);
extern void HorizontalOrVerticalLayoutGroup_get_reverseArrangement_m245D8EC788EDA70DCB831FE62DAB8DB806BE7EA3 (void);
extern void HorizontalOrVerticalLayoutGroup_set_reverseArrangement_m2AF5AC83D6FE8AE364C626C0518B2ECCEE9C0477 (void);
extern void HorizontalOrVerticalLayoutGroup_CalcAlongAxis_m12CA995AB887ED06762B07E97953D456B316647A (void);
extern void HorizontalOrVerticalLayoutGroup_SetChildrenAlongAxis_m4D7B06435A66102659B2372B48D49B2117D57F09 (void);
extern void HorizontalOrVerticalLayoutGroup_GetChildSizes_mE555CFCDBD0CD9913829BB56457F939A166BA383 (void);
extern void HorizontalOrVerticalLayoutGroup__ctor_m778C23DD9F3973AFACD3C6CCEDABF81902665D3F (void);
extern void LayoutElement_get_ignoreLayout_m32A9F0BACBC8E6BAE46F35E570DF71E937924412 (void);
extern void LayoutElement_set_ignoreLayout_mF3D4AF6214FD719979E4BA6A120494E7226FF18C (void);
extern void LayoutElement_CalculateLayoutInputHorizontal_mD6645A83B5E234C1EA5C764E48CAD4F3C135C4D7 (void);
extern void LayoutElement_CalculateLayoutInputVertical_m1D25D380F32BD322135C80C41D407BD81C5D88F6 (void);
extern void LayoutElement_get_minWidth_m6943ECF36A67019A485C4A7AFFC0BF7FD94480CE (void);
extern void LayoutElement_set_minWidth_mC140AB11DDA8F8FD299A5A7E3A9674FB21E827E4 (void);
extern void LayoutElement_get_minHeight_mC1951830B9F43C57AC4A287E9AF3A62A0871E9C3 (void);
extern void LayoutElement_set_minHeight_m8B794B9E92B440D9B88FEACD95492DC5257D628F (void);
extern void LayoutElement_get_preferredWidth_m214B11641CBD652E174F42133EF7CDC413CF6CE0 (void);
extern void LayoutElement_set_preferredWidth_m9D8F8097227D2EBAC03BB0E2E6B0E0A6C8887BA6 (void);
extern void LayoutElement_get_preferredHeight_mE630312564CC2A3E459C9C3E5FFDC2138D35EC88 (void);
extern void LayoutElement_set_preferredHeight_m0F5874AD74B74F2A8F1CE86ED0477FEA9555433F (void);
extern void LayoutElement_get_flexibleWidth_m2E51EA4DC58A4740702314E253FCA8816A1B98A8 (void);
extern void LayoutElement_set_flexibleWidth_m29E6E303E19AE180FD805D6E5481A00FC49E2983 (void);
extern void LayoutElement_get_flexibleHeight_m8A7B16E85F304CAA03BF6417BE1D0F6C0212E2E4 (void);
extern void LayoutElement_set_flexibleHeight_m39C426C07583BE074F9B71DA9ECA1216860A43D2 (void);
extern void LayoutElement_get_layoutPriority_m20D5C7FC2019146C2FFD09CF1A3D908703763510 (void);
extern void LayoutElement_set_layoutPriority_m8EAEC716134A0536F1E96F8C3AB0980D5416E2BD (void);
extern void LayoutElement__ctor_m31C173AFE1B1749B6957B578C9463044BA22624A (void);
extern void LayoutElement_OnEnable_mCD4984C5E35B4658AAB3224795209A92DAD65C6B (void);
extern void LayoutElement_OnTransformParentChanged_m7495A830D24B032BBCE6FC2F540CDCE8B713C330 (void);
extern void LayoutElement_OnDisable_m5DBCC5762DB101EA70B19A24F8A41BCDE450AB87 (void);
extern void LayoutElement_OnDidApplyAnimationProperties_m3D225CF42A2339702431CEB9F43DC769567E1535 (void);
extern void LayoutElement_OnBeforeTransformParentChanged_mC3BA3EA166CF4AE74B9A00799DE1C2869A9261D6 (void);
extern void LayoutElement_SetDirty_m9ECC494A5A6C3764AAB0D3E2C61C6050FC517879 (void);
extern void LayoutGroup_get_padding_m91ABA3C588704717EDC82E72BA6D1B82711FE83C (void);
extern void LayoutGroup_set_padding_m9F415F3402E5E4AE684FD153493CE3E8D64D3EB7 (void);
extern void LayoutGroup_get_childAlignment_m45C0D32DB91FD92852CA50278904034A26ADEFC1 (void);
extern void LayoutGroup_set_childAlignment_mA97DF1F2CF43C0CD1B83CFE7883626AA86ABB0AF (void);
extern void LayoutGroup_get_rectTransform_mE9AD2CFD78229C631BF21260FDB40C2D0D895974 (void);
extern void LayoutGroup_get_rectChildren_mEB00A4F0B86326AA9BE3D5E5DD7E4C9E3A032391 (void);
extern void LayoutGroup_CalculateLayoutInputHorizontal_mAB313A3646FC94E9FA98E5C4EA19DBAA7F3754FD (void);
extern void LayoutGroup_get_minWidth_m3EFD1527249470CC4F71588466BFB17D4A632229 (void);
extern void LayoutGroup_get_preferredWidth_mDE70B887487494986C9A5621C9F19488154EE2CA (void);
extern void LayoutGroup_get_flexibleWidth_mB4DCC3B208370CF2A2FE276A56D011922BC08609 (void);
extern void LayoutGroup_get_minHeight_mE2FA1D3B4B40AAD5CD4493087C5B63C7BCAE9B3C (void);
extern void LayoutGroup_get_preferredHeight_m055B2270ECB1C9C0FCCCA396FD7E9F8EFBDBDBA8 (void);
extern void LayoutGroup_get_flexibleHeight_m5F911708AAE2DDEF9ABF8EC7894F2B7A7264EB0A (void);
extern void LayoutGroup_get_layoutPriority_mC86CB36BF49A18F09F6577A9B298CB639F1FEC4A (void);
extern void LayoutGroup__ctor_m3F10CB94B64D503325A8EE097A94261C08AA2337 (void);
extern void LayoutGroup_OnEnable_m49EF8F43626DCBD10EB37D7F95BDEF2817DECC72 (void);
extern void LayoutGroup_OnDisable_mC10A4F2B949F44688E26D0F1499BE39B0655DB42 (void);
extern void LayoutGroup_OnDidApplyAnimationProperties_m7E426AAB3C937005BF074ABCF5A1C9FB2D67BB95 (void);
extern void LayoutGroup_GetTotalMinSize_mFBD1A44880D3390EFC7AF2441D556C9FAD49059A (void);
extern void LayoutGroup_GetTotalPreferredSize_mEFFC79C79FC70A3BDD06E46C6188827E0F7EABC3 (void);
extern void LayoutGroup_GetTotalFlexibleSize_m0750BE35A8B466C0CB82460B0A490139B8BE1E2A (void);
extern void LayoutGroup_GetStartOffset_m3748EE96F01312488AD6B764B01171AB2F5E309B (void);
extern void LayoutGroup_GetAlignmentOnAxis_m14E9D80D22AFAE88909D806F5439BCB9EF194A45 (void);
extern void LayoutGroup_SetLayoutInputForAxis_m3704D7673470CF7CF1F2B145F226C9C30C25E660 (void);
extern void LayoutGroup_SetChildAlongAxis_m25F11D4F93E0D31E68F7227D74000FFB067A8FDC (void);
extern void LayoutGroup_SetChildAlongAxisWithScale_mDCF850DCCD115F9B2ED8AC9D5D7EF8EA0B42EA94 (void);
extern void LayoutGroup_SetChildAlongAxis_mBE88585F9D066C2997499871D934C0A4E9AE871F (void);
extern void LayoutGroup_SetChildAlongAxisWithScale_mC1910181779269C2656D954DE36F384D19F11C22 (void);
extern void LayoutGroup_get_isRootLayoutGroup_mFB0EC6A489F3847C38599F1187755B6E04301B04 (void);
extern void LayoutGroup_OnRectTransformDimensionsChange_m32A8C9D736F6096B93235870A9623D63C2CBCA74 (void);
extern void LayoutGroup_OnTransformChildrenChanged_mF55AB48380641070CF0F92AC633357266D14A04A (void);
extern void LayoutGroup_SetDirty_m32F20D8BB5C4B4DF350AF5F35A5917660FF9CE60 (void);
extern void LayoutGroup_DelayedSetDirty_m67C0D880E25888F274BE8AE9D3F4C28EA4A22D0C (void);
extern void U3CDelayedSetDirtyU3Ed__56__ctor_mF6AE811754CADB1402BABF82639E38DB56C9AFCB (void);
extern void U3CDelayedSetDirtyU3Ed__56_System_IDisposable_Dispose_m347957DDD38BB5EFEE268E592550D258E5189F75 (void);
extern void U3CDelayedSetDirtyU3Ed__56_MoveNext_mE69F0D45357F390412D5833F35A0C4B4F3E47420 (void);
extern void U3CDelayedSetDirtyU3Ed__56_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m581E5D52D9508F8B755213FC2BFFC15412352F79 (void);
extern void U3CDelayedSetDirtyU3Ed__56_System_Collections_IEnumerator_Reset_m7C5E44C5235E8C18D0066EF7464A6165F6D4B1C0 (void);
extern void U3CDelayedSetDirtyU3Ed__56_System_Collections_IEnumerator_get_Current_m83BB521EA7AF720756B95AA6D637557DFCDCA2A3 (void);
extern void LayoutRebuilder_Initialize_m3186A381CF387FC04A9D52BF6ED9982B0150E562 (void);
extern void LayoutRebuilder_Clear_m2BCF887531F7BA60FB962F7153564A055A136AA1 (void);
extern void LayoutRebuilder__cctor_m5AE721B6C2738FA3FD23CF5103CEF96E6AEA578B (void);
extern void LayoutRebuilder_ReapplyDrivenProperties_m2FAE70C6B03F93BDF9484BC4674FA80D956BE45F (void);
extern void LayoutRebuilder_get_transform_m885E49969AFEF977B384D517212E68ABFDDB6555 (void);
extern void LayoutRebuilder_IsDestroyed_mEB8D2E6A0E61BD035965F28D30FB5BB41AAB2149 (void);
extern void LayoutRebuilder_StripDisabledBehavioursFromList_mB6E476924D6DDDA8050F40300A58696303F0753A (void);
extern void LayoutRebuilder_ForceRebuildLayoutImmediate_mCCA094579654469919EFA4B5AA5D9AF93CD67B4A (void);
extern void LayoutRebuilder_Rebuild_mE0477A991681D208BF504CCAABFE01D7FCD8E137 (void);
extern void LayoutRebuilder_PerformLayoutControl_mA6EB813FBAC300966A6357D248FAADD947C92D4B (void);
extern void LayoutRebuilder_PerformLayoutCalculation_m0733192A11C335EEF72298A2321937CDA97A1C34 (void);
extern void LayoutRebuilder_MarkLayoutForRebuild_m37F415D59609E9D18D49423D9C33E7EA6D859EBD (void);
extern void LayoutRebuilder_ValidController_m28AC31CE8158B1D2D9656A99ACE3F259F5212C70 (void);
extern void LayoutRebuilder_MarkLayoutRootForRebuild_m72542D06667BE02C021D13ADC4C77094614552FF (void);
extern void LayoutRebuilder_LayoutComplete_mB93ADFB170DD29029D271710D946FE0B08665380 (void);
extern void LayoutRebuilder_GraphicUpdateComplete_m177178144B034919E062EB9C205AB65CB437BC6D (void);
extern void LayoutRebuilder_GetHashCode_m408B8FD9884FA8F7F967F1E8C7015055B8F780D3 (void);
extern void LayoutRebuilder_Equals_mD6E988174B451D9E832E3FC8B1EBBA0DD1FFB92E (void);
extern void LayoutRebuilder_ToString_m7F8066428103BE4A1E0A71F45B1D0E725E377A58 (void);
extern void LayoutRebuilder__ctor_m685B1B7449046E3467525550996AFB6A4565219E (void);
extern void U3CU3Ec__cctor_m0D6AD1DB52B49A72650F253F02B465D02C18BE04 (void);
extern void U3CU3Ec__ctor_mD28F0F8B5399F1C60A8E4575F9DCDC847D2CAA23 (void);
extern void U3CU3Ec_U3C_cctorU3Eb__5_0_mD2D37BAA0BED9121AC22FDFC48D7CA35BF400E14 (void);
extern void U3CU3Ec_U3C_cctorU3Eb__5_1_mE8F9670037E944B0EB22487F1640407057B8A290 (void);
extern void U3CU3Ec_U3CStripDisabledBehavioursFromListU3Eb__10_0_m03CF501571C52837064AFBD196238DB52B64BC5E (void);
extern void U3CU3Ec_U3CRebuildU3Eb__12_0_mDB44431ACB66295B4B09C0132E9EF267DB0090F1 (void);
extern void U3CU3Ec_U3CRebuildU3Eb__12_1_m08B6A90656111D084CCF7AF3B07D50C03B95BF9F (void);
extern void U3CU3Ec_U3CRebuildU3Eb__12_2_m516829AF584D4F446B08587F777088C704548C40 (void);
extern void U3CU3Ec_U3CRebuildU3Eb__12_3_mDEE0C1EB02D33044B945FF479F81E7A3DD85684B (void);
extern void LayoutUtility_GetMinSize_mE0421687F579243D5252F065A737268EF736374C (void);
extern void LayoutUtility_GetPreferredSize_mCAFD360B8490CD02AF5F99E93E09D8625BD85F52 (void);
extern void LayoutUtility_GetFlexibleSize_m2D6400EB342AE2E811DAFFF0CD2AA2F8AA5DD655 (void);
extern void LayoutUtility_GetMinWidth_m920875D5D911EF3FAC615C045D9178630697A0F3 (void);
extern void LayoutUtility_GetPreferredWidth_mFF51E72881BE14E8C59521A71188E458475D4052 (void);
extern void LayoutUtility_GetFlexibleWidth_mFE4684C5AC223E1D8E4369AA3460461A3964D9BE (void);
extern void LayoutUtility_GetMinHeight_mB428914EB41682FC709CDBB27A0CB42D42EC4D9E (void);
extern void LayoutUtility_GetPreferredHeight_m3E8CDE02CC980080BBD4BBA1D6BFDFD42F7CF706 (void);
extern void LayoutUtility_GetFlexibleHeight_m13C0A91CEB1B4DE827C4B9E5230D4AA73FFC23DC (void);
extern void LayoutUtility_GetLayoutProperty_m3A5BD03879B0B223BE78DDD6AB7595BDF14CF7A7 (void);
extern void LayoutUtility_GetLayoutProperty_mEE37FED419E8D6C3B799296DC6712D312AA5261F (void);
extern void U3CU3Ec__cctor_m0CA720C96E971F17E2E861D8AFE6982FD1F59F44 (void);
extern void U3CU3Ec__ctor_m5E4858F8F2E57AAC48D706CFDE697DB16DE163D3 (void);
extern void U3CU3Ec_U3CGetMinWidthU3Eb__3_0_mB230985725E48B130593DCE9618576ABD38544E9 (void);
extern void U3CU3Ec_U3CGetPreferredWidthU3Eb__4_0_mB8A573FB7B4D32CBC78F6AF30C42FDD204D18862 (void);
extern void U3CU3Ec_U3CGetPreferredWidthU3Eb__4_1_m21A4B6CC6110B2FE507ECD670D1C701BC9E1C9F9 (void);
extern void U3CU3Ec_U3CGetFlexibleWidthU3Eb__5_0_mDAFF6CD37904716095CE8E674BB7D2586ED17A95 (void);
extern void U3CU3Ec_U3CGetMinHeightU3Eb__6_0_m92D133D332D83DAECA59B06DD94CE6390C617162 (void);
extern void U3CU3Ec_U3CGetPreferredHeightU3Eb__7_0_m869A0C6EBD3699CA96970C944D80A94779D58FEA (void);
extern void U3CU3Ec_U3CGetPreferredHeightU3Eb__7_1_m6851C73E5D55417BF6A9A29D3DD22EA033D40EEB (void);
extern void U3CU3Ec_U3CGetFlexibleHeightU3Eb__8_0_m55A0D46B9D7CD1002AE7F803E496E6F1A98F3E24 (void);
extern void VerticalLayoutGroup__ctor_m2EF3851AD1D83E3ADBB5053D1FAEF84A773E1D5B (void);
extern void VerticalLayoutGroup_CalculateLayoutInputHorizontal_mF9483E90B39BDCE71C90F9B27CBF76F2A10E4D28 (void);
extern void VerticalLayoutGroup_CalculateLayoutInputVertical_m360446A9D17C1B798BBB0B777EA1BEEE297807BA (void);
extern void VerticalLayoutGroup_SetLayoutHorizontal_mD074FE8C9B3187AA23AF6578E8C81B23404D58B2 (void);
extern void VerticalLayoutGroup_SetLayoutVertical_m27D87A8FEBBBD9226B95F250A076B3AB7BAA155F (void);
extern void Mask_get_rectTransform_m4D1933DACBE7B0F93B1B83F1B3B0A09F65B24209 (void);
extern void Mask_get_showMaskGraphic_m87FD20C72F514AB305E05A5B104B180D9B35601B (void);
extern void Mask_set_showMaskGraphic_m9F288D22259CFD781D5A4D9B9747C2A2895E7D67 (void);
extern void Mask_get_graphic_mDC288968F569C492F1E18F82229ECB7AA3804AD2 (void);
extern void Mask__ctor_mB4AF8A6FD9496A1E8EEB7631D43F8A0548134DB9 (void);
extern void Mask_MaskEnabled_m330C9AE8B8A642ECF3C4E88687E9F297969B6351 (void);
extern void Mask_OnSiblingGraphicEnabledDisabled_m38D50E90213834C6E06D449F364C7A802964FC74 (void);
extern void Mask_OnEnable_m928342074FD21B3A58E1370F681DC762BD64B095 (void);
extern void Mask_OnDisable_m7B533EC440BB28CB80AB8AE914BFA501FAB3ADA5 (void);
extern void Mask_IsRaycastLocationValid_mE12C460DF4AF0C65082DBBA6F46A2259687A2534 (void);
extern void Mask_GetModifiedMaterial_m5D7DE1884428D7EBC6A7AA6376650E4FB966B1F4 (void);
extern void MaskableGraphic_get_onCullStateChanged_m8452945E93AF20B975D85E61999B51039CAF6538 (void);
extern void MaskableGraphic_set_onCullStateChanged_m4284F81D75D8F8293FE2FB5FC236FDF63579BBF7 (void);
extern void MaskableGraphic_get_maskable_m34B87CD87CFF73FF4E09D892ADB316E412F22660 (void);
extern void MaskableGraphic_set_maskable_mC2486FDC0636C83AC3BDBFF11E6E85CC27F15689 (void);
extern void MaskableGraphic_get_isMaskingGraphic_m8C4270841AF6071FD5AC4EB7225AF259053DF55E (void);
extern void MaskableGraphic_set_isMaskingGraphic_m350EDFCF390CF594B939BBEE3A0D634F2EA48A78 (void);
extern void MaskableGraphic_GetModifiedMaterial_mBE4C5B18ED4221E0A6C026C750B6A04E9B35312A (void);
extern void MaskableGraphic_Cull_mF6948476960E33BD174FD3723101650E3C344CC7 (void);
extern void MaskableGraphic_UpdateCull_mAC0798E6376F7B103BB36929AC4DD69729E30E86 (void);
extern void MaskableGraphic_SetClipRect_m19317C49A4CC99A991A3F0135756DB94020930C2 (void);
extern void MaskableGraphic_SetClipSoftness_mF11957AB91E1BA19B6008ACEF95C5F9AD930CAE4 (void);
extern void MaskableGraphic_OnEnable_m4BF46ECE5E57E2EE11ED4CE41AD50DADF141C9BC (void);
extern void MaskableGraphic_OnDisable_m9123E729FA7BE001037CDE14E8A75B69AD68E16C (void);
extern void MaskableGraphic_OnTransformParentChanged_mE5ABE137F670FBA7E6FCD2A67616E4A8097AD876 (void);
extern void MaskableGraphic_ParentMaskStateChanged_m1353B87D25271925B6ED342FDC06B05F7EAD3992 (void);
extern void MaskableGraphic_OnCanvasHierarchyChanged_mB30092A7276A921F711E466E9CE85C04ED982E77 (void);
extern void MaskableGraphic_get_rootCanvasRect_mB7F5E772A53CBCCF920CD924E84634CD8155F6D8 (void);
extern void MaskableGraphic_UpdateClipParent_mEFEEC27574B12503C1D8B694BA61C7166828F6A2 (void);
extern void MaskableGraphic_RecalculateClipping_mFDD980F0A3AC1BEFF0BC9EDE95EF063AA9C282F7 (void);
extern void MaskableGraphic_RecalculateMasking_m76F4A84B87AD4938F8A68B022A5A2BB4B5F343AF (void);
extern void MaskableGraphic__ctor_mD2E256F950AAAE0E2445971361B5C54D2066E4C2 (void);
extern void MaskableGraphic_UnityEngine_UI_IClippable_get_gameObject_m17FD7D774DA4D9D0F2E23240D9E17FF5C7DC4A44 (void);
extern void CullStateChangedEvent__ctor_m885AD59B4D0D6075AB6DFA71AD69A7BB48640CE4 (void);
extern void MaskUtilities_Notify2DMaskStateChanged_mBD5C9FCE2AC1327C599BF0D7390BFD86FAE06937 (void);
extern void MaskUtilities_NotifyStencilStateChanged_m112CACEF914385BC2F96F4D66D4038AF1E0FCD6B (void);
extern void MaskUtilities_FindRootSortOverrideCanvas_mCB7DABA799F6C5BDF659D4CA60BA2FE8141A65AA (void);
extern void MaskUtilities_GetStencilDepth_m782D2795F76F569F4FB261C5BFB6D9EF241C0EE9 (void);
extern void MaskUtilities_IsDescendantOrSelf_mBCC8B1428F599BAF1EAFA16E9586639A11B87C23 (void);
extern void MaskUtilities_GetRectMaskForClippable_m7AEB8F89DFD994A487EED33DDD8C59E5A784245C (void);
extern void MaskUtilities_GetRectMasksForClip_mDEC4D04BA24F5C4C5828A4E8A677BE4F3CC6FAAF (void);
extern void MaskUtilities__ctor_m32B6A8721369418CAA95A8EF5D65E0B8CD89DA82 (void);
extern void Misc_Destroy_mA812AD936D10BCABA81E04C6C4C190034995214F (void);
extern void Misc_DestroyImmediate_m7E53D180A6459C9577D115A345D59C26FC05F919 (void);
extern void MultipleDisplayUtilities_GetRelativeMousePositionForDrag_m3C283E331437CB72CF86C5C98B9E61D2317B8F4A (void);
extern void MultipleDisplayUtilities_GetRelativeMousePositionForRaycast_mBD9CBF4855B536FF62D12DA8D774B196C2E7EC1C (void);
extern void MultipleDisplayUtilities_RelativeMouseAtScaled_m80E65BE9255F7D65D9341FDFDB0CA11470703E71 (void);
extern void Navigation_get_mode_m3B574F1549B3806753EC33228EB3FF3031F4E809 (void);
extern void Navigation_set_mode_m0BEF999F733332AD994CF3CA4AC17B2A47531207 (void);
extern void Navigation_get_wrapAround_mA24021791B1C67F665065B5A415434837CEA86DD (void);
extern void Navigation_set_wrapAround_m9D808EC49EE5F3AFA7F0D13E86FF9F72AA20A081 (void);
extern void Navigation_get_selectOnUp_mD24FC0BAB97E5DBB28C9C7209BAC2ACC9419B183 (void);
extern void Navigation_set_selectOnUp_mCB04000FDFC05D3BAC497602E4BA346A536152E5 (void);
extern void Navigation_get_selectOnDown_m1D36E990CDB38C4BB78745587668F94BBE8A1285 (void);
extern void Navigation_set_selectOnDown_m0EBBAB8C51107F75F63FFBC3DF88D9010E6A44BB (void);
extern void Navigation_get_selectOnLeft_mA4F7DA341D7C660A7E15520B34847B0757C65F81 (void);
extern void Navigation_set_selectOnLeft_mA4E7480D7CBDA9A5ECA93BAFCD1CF1976A994FCB (void);
extern void Navigation_get_selectOnRight_m7A781F4050AE064DC0473E68AA6D07CFFF0A8FF9 (void);
extern void Navigation_set_selectOnRight_mD0B38024BB628CDC801EA93E9FF7C438ECE2055B (void);
extern void Navigation_get_defaultNavigation_m142FA3A8F52EE3DD355FFE30061771FB9A86671E (void);
extern void Navigation_Equals_mE25B4E3D0AB85C1469B99971E6AB16E2039E6B4D (void);
extern void RawImage__ctor_mB9515043B2286A9012B98913D023EA0ACEF57401 (void);
extern void RawImage_get_mainTexture_mDA4701244E31799D8897FBB6E0A1FA41EF5B81E9 (void);
extern void RawImage_get_texture_m84CCFDF78F6886F73EBE5A7C78D6E9C3CA903813 (void);
extern void RawImage_set_texture_mC016318C95CC17A826D57DD219DBCB6DFD295C02 (void);
extern void RawImage_get_uvRect_m83D2C4632C6AE437D1DC775904AC2FA8CB83D823 (void);
extern void RawImage_set_uvRect_m9DF6BBBC6AC46F7F3290A220ED6F076CAB4BC52F (void);
extern void RawImage_SetNativeSize_m02ACAE096422EE2D5E17FCF89CC7BBB74A64FD6A (void);
extern void RawImage_OnPopulateMesh_mA85FE8B6123F6B3D013E8DEFB9DE3CAAF0C08F6D (void);
extern void RawImage_OnDidApplyAnimationProperties_m571F0CB106D9060554503E87FCA700A6B6C997A6 (void);
extern void RectMask2D_get_padding_m37CA7BF6DA7386AB4D9A6449CAC48ED6BC4B7777 (void);
extern void RectMask2D_set_padding_m2E8CADD2DC7A40E78586118453CFE2D8795C997A (void);
extern void RectMask2D_get_softness_m2638D596B2600278FF2D3225B14038624DA19E34 (void);
extern void RectMask2D_set_softness_m2857F567959455CA644277BC644A2EE0984089D4 (void);
extern void RectMask2D_get_Canvas_m689A6760F58FD683B7A5EA6A92691AAA521D4634 (void);
extern void RectMask2D_get_canvasRect_m81DEFAC3250A9F3FE4B97981335E406B43CFF4F4 (void);
extern void RectMask2D_get_rectTransform_m6EF34408BB7A5763A590F36D65DE7974E6C996DD (void);
extern void RectMask2D__ctor_mC7257CF022267C2E98E8F04CFC28CA37CF8C64FD (void);
extern void RectMask2D_OnEnable_m2C52D2F840A9E7462488AB028C21803D3BE14A51 (void);
extern void RectMask2D_OnDisable_m2CF7F93D68B6ADC28322024E7A9AD4102832F4CC (void);
extern void RectMask2D_OnDestroy_m950120AD49BDAFF20E783C22AF863741897015BF (void);
extern void RectMask2D_IsRaycastLocationValid_m9ADA1029D511D9A62CFC1B576F396EDD0A31E4FF (void);
extern void RectMask2D_get_rootCanvasRect_mC644AE792D28558B8260E23A87C8E6645D33224A (void);
extern void RectMask2D_PerformClipping_mD89C9AEAC139EA7AFBB189608D02ABB87F3D7AB0 (void);
extern void RectMask2D_UpdateClipSoftness_m84A9BCB92DEB1654703D0084C5A3F0BCD2E1BFF2 (void);
extern void RectMask2D_AddClippable_m90A9698CD91A2A08EBE86AB60B05E76AFA38EAA4 (void);
extern void RectMask2D_RemoveClippable_m2247DBCAD9B09980191AB791A7CB83FF9C355C2D (void);
extern void RectMask2D_OnTransformParentChanged_m593E595A4C1293CEFB17764B55C96E2EC41E4648 (void);
extern void RectMask2D_OnCanvasHierarchyChanged_m232F0056ED310EAB18C3BA314A666ABF13B4353B (void);
extern void Scrollbar_get_handleRect_mEC95A981B744C4DB961D8B5DF6D2B81132CBB238 (void);
extern void Scrollbar_set_handleRect_m2B621325A0EEA1EDCB71402FCBC7DBEB9C2BD4B0 (void);
extern void Scrollbar_get_direction_m1950D7EE42DDD0E3DBEABCDD59DD7E0FEC164C4C (void);
extern void Scrollbar_set_direction_m1C307CE73857CD7D3FBB160FE66875CA6BA6A3C6 (void);
extern void Scrollbar__ctor_m65C96C26AB7CBC074ACDC19557E1982155CA30A4 (void);
extern void Scrollbar_get_value_mC2F43475C89766DA596FFAA019CA59F94CC89A35 (void);
extern void Scrollbar_set_value_m8F7815DB02D4A69B33B091FC5F674609F070D804 (void);
extern void Scrollbar_SetValueWithoutNotify_m6E2A4BE4DA16EBA596D2E6E40E4AC2DAC8B6C162 (void);
extern void Scrollbar_get_size_mD88FDA836274F40EC8A97237C72B7E3C4906DB5F (void);
extern void Scrollbar_set_size_m5376982465D6013425FAB0CA8EFC620C3E1458FB (void);
extern void Scrollbar_get_numberOfSteps_mC3CEFF66E82BEF0473A82581CA7ACE08AA93B999 (void);
extern void Scrollbar_set_numberOfSteps_m59EA2D1FDFB3D5E91CC5630254E319605B67E095 (void);
extern void Scrollbar_get_onValueChanged_m14356CECC1A2BA96576EB73279AF2ECF28B26D6A (void);
extern void Scrollbar_set_onValueChanged_m4167C1B411C38C2BCF9967840102723367B35AAF (void);
extern void Scrollbar_get_stepSize_m76926AD1E9F264A61B9BF098BC90F1E1335FA7A5 (void);
extern void Scrollbar_Rebuild_mB6BEE134B0B018A07FD5DE27A353DC4F8834EE85 (void);
extern void Scrollbar_LayoutComplete_m62E02A6865F74A44F1301CC085D1D4CA4CC90797 (void);
extern void Scrollbar_GraphicUpdateComplete_mD1DB8FC7C34AC5454CDF41D39483122DA7118876 (void);
extern void Scrollbar_OnEnable_m80353998984F644C00DFC51861A9ACE4134D2C86 (void);
extern void Scrollbar_OnDisable_mB78DB94C4093312BBBE28F78FE21B16F8485D2B5 (void);
extern void Scrollbar_Update_m758EF18E62B3A8D6F319D5CEC9ACDFB005CD1AC3 (void);
extern void Scrollbar_UpdateCachedReferences_m63BD63A223E31DF89731186F8204993FE707F0AE (void);
extern void Scrollbar_Set_m9A15F05D06D200A038C20B1F1C6A4DFA5B17D0A4 (void);
extern void Scrollbar_OnRectTransformDimensionsChange_m06E846A58CBE1B1006AA3453784789F1A56B8CC6 (void);
extern void Scrollbar_get_axis_m7C529809A9A4246CAA1F7417AC3418270B7D7ADB (void);
extern void Scrollbar_get_reverseValue_mDEEB7F6EC4FD16FD6B1F6806335463FDBC417571 (void);
extern void Scrollbar_UpdateVisuals_m262B64133E8C98F2B1FF1A075AEACF0F8CBFF72C (void);
extern void Scrollbar_UpdateDrag_mD7B02B0A326AF4BB20B66423F3EAEC8FD4BCC787 (void);
extern void Scrollbar_UpdateDrag_mF99B7B888CC0AE9DF0AF9A72563C5C4DB114D7CE (void);
extern void Scrollbar_DoUpdateDrag_mC0C9D56DA7F9AAF3E8941206448DEF1FF2E4BC3E (void);
extern void Scrollbar_MayDrag_m19259CC2C45110C1951E59E7E0F8CB207DD69430 (void);
extern void Scrollbar_OnBeginDrag_m9B628433953BE38D64DB2AE5A3A14A82CDD789CE (void);
extern void Scrollbar_OnDrag_m79EAA59922BB2ED61C042ACCCCF9EE14B0990675 (void);
extern void Scrollbar_OnPointerDown_m8A4C9EDFECF2503F92F57D70C8D71842A3165A27 (void);
extern void Scrollbar_ClickRepeat_mB3CD100CB06D4687F163B47B1BE806F5519FD8C8 (void);
extern void Scrollbar_ClickRepeat_m9805A27D61BE928E0A8CC8B6CF6D7DD0A2256830 (void);
extern void Scrollbar_OnPointerUp_m957C480C8DE9E46E381A800B4B60B07FF12F64B7 (void);
extern void Scrollbar_OnMove_m17725BD4A3BB30209D66B1938BDF15172F05AD51 (void);
extern void Scrollbar_FindSelectableOnLeft_m4D775883935EA4A06A67C452C47971BDA90FEFE9 (void);
extern void Scrollbar_FindSelectableOnRight_mD77EA6CD469357D8E014C5075301A5752A0CA052 (void);
extern void Scrollbar_FindSelectableOnUp_m44369416317D6AF92FC5CD29CF3B4D4CB44D247D (void);
extern void Scrollbar_FindSelectableOnDown_mA0C3C3970272025DE78D382CCDB96721B4EBDD6D (void);
extern void Scrollbar_OnInitializePotentialDrag_m7B2840ACB1D2A6D3DA0F03DF9677D2DCF790E065 (void);
extern void Scrollbar_SetDirection_mA62DC964AA698D058BC84FA1DCAFA46BCA6A8182 (void);
extern void Scrollbar_UnityEngine_UI_ICanvasElement_get_transform_m402A9522ECD918080DBBE778E8BEF58415E41B44 (void);
extern void ScrollEvent__ctor_m8875FD9430D9657557F83634E0BDAC8A4C280C10 (void);
extern void U3CClickRepeatU3Ed__59__ctor_m8345ADC433BB6228E8D107936D6B88E0F658E0E6 (void);
extern void U3CClickRepeatU3Ed__59_System_IDisposable_Dispose_m94D37A486F75C0142FE08E84A273AEF4BCA7B104 (void);
extern void U3CClickRepeatU3Ed__59_MoveNext_m1E013AEC306260D4360A63440D9C073D276F4816 (void);
extern void U3CClickRepeatU3Ed__59_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m781C1E7B3BA449470506588C16DAED846A1F3230 (void);
extern void U3CClickRepeatU3Ed__59_System_Collections_IEnumerator_Reset_m20693B941CFFE66C958912A917AD64638D196651 (void);
extern void U3CClickRepeatU3Ed__59_System_Collections_IEnumerator_get_Current_m4AEB23A992754C7BE244B0A9FFAFAB8A75B2EF86 (void);
extern void ScrollRect_get_content_m7878BCA28A96B7FBA02DC466A1ED2C9E191C6996 (void);
extern void ScrollRect_set_content_m01BF6FE0205985CBD16C6D3BB4B6F345B3AF484E (void);
extern void ScrollRect_get_horizontal_mDA4358EF29CE64E6B346D6CC5D70E08F00D3D05B (void);
extern void ScrollRect_set_horizontal_m99C076AF2B2B596C87435E1465EF0B104281B150 (void);
extern void ScrollRect_get_vertical_m43F2C650302CB71D53A0A373934CA9F9921CC38B (void);
extern void ScrollRect_set_vertical_m972088E788E72690AAE139E7C0F8F634C325E7CE (void);
extern void ScrollRect_get_movementType_m0672A0BA382BC5479398DE95C551530FE5B38621 (void);
extern void ScrollRect_set_movementType_m2A900C10E6C005FD6866EFF1DA2DF78AA957534A (void);
extern void ScrollRect_get_elasticity_mF0DE000D57AA94F2A5D9E1C48EC6F6514C1F4565 (void);
extern void ScrollRect_set_elasticity_mCA1500D31E9A8DE62FA03EA3E1276BFFB7F6094B (void);
extern void ScrollRect_get_inertia_m10C8837B3E43787E1FA94C71683D19638FCEFFBF (void);
extern void ScrollRect_set_inertia_m8A17589561A5E7A2F5F543B8F2F6149458C68AC2 (void);
extern void ScrollRect_get_decelerationRate_mDE7178B7D5AEA48B258A328ED352C7A8AF9065AF (void);
extern void ScrollRect_set_decelerationRate_m7DB02F71AC6E7C519ADB3FA88F9B46EF187FCD61 (void);
extern void ScrollRect_get_scrollSensitivity_m36A71A35CCAE99F83DE336A51520BB2657686E4C (void);
extern void ScrollRect_set_scrollSensitivity_m07A6D8B94625BC52775BED72633CCBEA41E27E1D (void);
extern void ScrollRect_get_viewport_m85092216DD476F77E78F5CE50F9C4E70063ECCF9 (void);
extern void ScrollRect_set_viewport_m53D91C0869950B18953E163E9A3CE5E7AFB0A262 (void);
extern void ScrollRect_get_horizontalScrollbar_mDE0EC3FD5C1AC8FDB4D8E8EF4B093A77218DF534 (void);
extern void ScrollRect_set_horizontalScrollbar_m38777B9083CABE5B05EE674DF59867247613F6CA (void);
extern void ScrollRect_get_verticalScrollbar_mCEB62CC858B43CE7FB07D287CAFC1363668E78C6 (void);
extern void ScrollRect_set_verticalScrollbar_m3A3503567D1ED44E21A452FE51B12691E084426C (void);
extern void ScrollRect_get_horizontalScrollbarVisibility_m3BB3586EBE511EEB0946353153D4818D5207A91C (void);
extern void ScrollRect_set_horizontalScrollbarVisibility_mA00C9BDAC3704BEEE76986BCD1D2DFB7F2E2D818 (void);
extern void ScrollRect_get_verticalScrollbarVisibility_m8F8691067DFB8070BDB2A15D40C6E98E858B1E77 (void);
extern void ScrollRect_set_verticalScrollbarVisibility_m40A791E57B3FD37CEB97D2FD29639C4EC5B49ABF (void);
extern void ScrollRect_get_horizontalScrollbarSpacing_mA61BE48D8F60FA41696D3854501BD6931297DFB6 (void);
extern void ScrollRect_set_horizontalScrollbarSpacing_mF3FDBF169F96C109BCC75EE62AAC265D23E30D63 (void);
extern void ScrollRect_get_verticalScrollbarSpacing_mB3FB9008708D488CCC4EE2753B4EE74953CBEB7C (void);
extern void ScrollRect_set_verticalScrollbarSpacing_m27BECB09BC4EE6BC91EAABEF50657182A637C1E7 (void);
extern void ScrollRect_get_onValueChanged_mA6AF3832A97E82D31BB8C20BCD6E87A300E56C05 (void);
extern void ScrollRect_set_onValueChanged_mB3D669EB2351EDDEBEF2D0F85FBE6279BE905288 (void);
extern void ScrollRect_get_viewRect_m3E97A12D75F8D1CBE409EFD5D550141B0DA326C3 (void);
extern void ScrollRect_get_velocity_m8F7DDB02F52BFF2503F079C216FC5C89AA4875DC (void);
extern void ScrollRect_set_velocity_mBC8D4BC0A0184FCC3AEB359AE68E9130E811AFC2 (void);
extern void ScrollRect_get_rectTransform_mB34A69B7E6E21FFF066786508974D89B5A6D4E4C (void);
extern void ScrollRect__ctor_m71A7660A30496E9D4937AE250FBAB722BF0747C7 (void);
extern void ScrollRect_Rebuild_mC15C5A090517F09F981F12DFD46BCCAE96FF9660 (void);
extern void ScrollRect_LayoutComplete_mA3AB518DD92641DF7F01CE8108EBFC4C0424A115 (void);
extern void ScrollRect_GraphicUpdateComplete_mF50A0A85D39C499126C7305CCCF055360091EE22 (void);
extern void ScrollRect_UpdateCachedData_m5E25EF1E36AB04D01FEE66C8E0CD30C0E6CCA933 (void);
extern void ScrollRect_OnEnable_m5A4AE9FF349A1F5C9780F2DC17CEF3304B795AE9 (void);
extern void ScrollRect_OnDisable_m0C287FAF83174051A941BA2F90F4D0E38B3ECFDC (void);
extern void ScrollRect_IsActive_mBACF2D3F35080C325C5D6A54CF86D17C19FF9A70 (void);
extern void ScrollRect_EnsureLayoutHasRebuilt_mDEA99980960C5429B17B200EFB3B2EB13B01956A (void);
extern void ScrollRect_StopMovement_mA278F4EBDE715F61F9D38F88E71E364E82870851 (void);
extern void ScrollRect_OnScroll_m86BA4041DE7B1B13101BCC01D90752143A5A28F6 (void);
extern void ScrollRect_OnInitializePotentialDrag_m35BB18E5EB6B50B7CC4B44171433E1493A5F8A10 (void);
extern void ScrollRect_OnBeginDrag_m6B0948CCD12A89B43E4F2596E3C7220A6D426868 (void);
extern void ScrollRect_OnEndDrag_m7CB3145874E1930FEBD50874DF31280FC35B480B (void);
extern void ScrollRect_OnDrag_m1BA80F29441E3761A294E32C7CCE52C35F1B6E5C (void);
extern void ScrollRect_SetContentAnchoredPosition_m4C8EC3F85A2B1011985E7583AFDC15A69FF90ACE (void);
extern void ScrollRect_LateUpdate_m7E003F1E2C34057F6B802003E77AABF54526C0EE (void);
extern void ScrollRect_UpdatePrevData_m4BF4AF6ACB7DC3E4A3F7DA8F468B784D1320ED8D (void);
extern void ScrollRect_UpdateScrollbars_m9D6268FD19434213F7BCE166722A9B36346C755B (void);
extern void ScrollRect_get_normalizedPosition_m4B05A9E790891D503C2B65953728278C7FF8CB58 (void);
extern void ScrollRect_set_normalizedPosition_m8CFC50007450856E3B1FEB9E61A6311FBC0E709E (void);
extern void ScrollRect_get_horizontalNormalizedPosition_mC2C3A7F67E27AA7470A81042AD2B0AD0B5F1AF93 (void);
extern void ScrollRect_set_horizontalNormalizedPosition_m9B268C9AE7891FC73623DC7BE6B9900640C029B6 (void);
extern void ScrollRect_get_verticalNormalizedPosition_m4FE766F04272C1805FDE2A4B72D80F6190841FA1 (void);
extern void ScrollRect_set_verticalNormalizedPosition_m4AF461113925E6710BF04F46A49CF1F856F7738C (void);
extern void ScrollRect_SetHorizontalNormalizedPosition_m3F43FC307A146E534DC3F73F4DE38386AAC10405 (void);
extern void ScrollRect_SetVerticalNormalizedPosition_m4E9F3559FA6369389C1B70D3E94AA35AEC7903E5 (void);
extern void ScrollRect_SetNormalizedPosition_m99C3731F06EEEF281E68D5D448914B1A3C5636FB (void);
extern void ScrollRect_RubberDelta_m5A4BE5FAAA0C39B318A422F236C898D1008AE248 (void);
extern void ScrollRect_OnRectTransformDimensionsChange_mD41D649A067BFD8DC067FC612C04E48518D691BF (void);
extern void ScrollRect_get_hScrollingNeeded_m426A4490F146A56FF76349CBBA4B587EDA5F78DB (void);
extern void ScrollRect_get_vScrollingNeeded_m96BA5B252797DF209A1784D1DE3C09AAFEFB25B2 (void);
extern void ScrollRect_CalculateLayoutInputHorizontal_mEC706200EAB973A2333279BA6C2EE7F6DAA884A6 (void);
extern void ScrollRect_CalculateLayoutInputVertical_mF708C890C569C942921A2ED809FC0294E13CC9A4 (void);
extern void ScrollRect_get_minWidth_m3824272990612610DDDCA8D35C23EDC0E97A6751 (void);
extern void ScrollRect_get_preferredWidth_m16914F16D3F8F1102428267D62CCBF5E8B1EF131 (void);
extern void ScrollRect_get_flexibleWidth_m6C7F8AC0595D6B5179BF02EAFEF3126731B162D6 (void);
extern void ScrollRect_get_minHeight_m3D973E3759C8D35899E2F62CFA7677834E6050B4 (void);
extern void ScrollRect_get_preferredHeight_m90993A52773D1214E648E8DC937D89317F6D4F72 (void);
extern void ScrollRect_get_flexibleHeight_m91767E81456CA1069B6BBEFCD140BE65962C421F (void);
extern void ScrollRect_get_layoutPriority_m19C83DF0ACE68769627C6FB8E09F92FDF63E80E9 (void);
extern void ScrollRect_SetLayoutHorizontal_m26167C6091ECF4AFB6A4747575592C2923CA4EE5 (void);
extern void ScrollRect_SetLayoutVertical_mAC8DF5F2CEB21C69D993846A3AF307C6217B83C8 (void);
extern void ScrollRect_UpdateScrollbarVisibility_mC4E22621A76C4FED36EFA5421BA4006DCB4E5140 (void);
extern void ScrollRect_UpdateOneScrollbarVisibility_mB2A129E7AE74E39D6080389679DFDB99D1A65FD7 (void);
extern void ScrollRect_UpdateScrollbarLayout_m41BFD2C6E126A96E99A6892EB88249D2F44530D2 (void);
extern void ScrollRect_UpdateBounds_m71C0450FC4E45F3A60CAEC0D3ABE21702364BA92 (void);
extern void ScrollRect_AdjustBounds_mF4ADDB84F572E72668E1FA1E699F84A4A89E9F96 (void);
extern void ScrollRect_GetBounds_m867D453097CBE1F32BF2F9D74F88255542F692A2 (void);
extern void ScrollRect_InternalGetBounds_m678E51D17A614402FEA0D24741A37EBE45B31817 (void);
extern void ScrollRect_CalculateOffset_mAFCC1C71DF0F848130BBF11C914E2333B8E5155D (void);
extern void ScrollRect_InternalCalculateOffset_m47D8A586D3069AA701718AB516A5F2FECC8AE1C0 (void);
extern void ScrollRect_SetDirty_mAE263F4AB8A126B60FECCB4A20A6DE1C0A7EB8FE (void);
extern void ScrollRect_SetDirtyCaching_m8E5F2F8A20AE671802C2ABA400E9125CF60FF19F (void);
extern void ScrollRect_UnityEngine_UI_ICanvasElement_get_transform_m92CB3091979234EDC51D449A75CC22C2F9223AD8 (void);
extern void ScrollRectEvent__ctor_m1A1148AF5CFAEA289C3F017565F6B1261CDB95AC (void);
extern void Selectable_get_allSelectablesArray_m1071647E8ED4DDE7162EE56B3D730468D09454B3 (void);
extern void Selectable_get_allSelectableCount_m2C8D64447141260C734038679940C8D9DB39A6CA (void);
extern void Selectable_get_allSelectables_m0B3507A121322D32AC9E8EE45424F84B3653D8AF (void);
extern void Selectable_AllSelectablesNoAlloc_m1583EDE9D566FA98A92F1AFC543519E3A8BE56BC (void);
extern void Selectable_get_navigation_mA0E5FC6B1D19C2DCABA5C82EC33C49CF7F17103E (void);
extern void Selectable_set_navigation_m706D254813B084B60F07980607D7AE43AC44AFEF (void);
extern void Selectable_get_transition_mBDC7F9FCA36E707B6D77E2F33FCEFA344A3E5005 (void);
extern void Selectable_set_transition_m67F9584736EB6891A314C9804489368C430F0F59 (void);
extern void Selectable_get_colors_mB53E365D02351D4B64084295C4B2A7AF2DEC4750 (void);
extern void Selectable_set_colors_m0A49ED3ACD6647B7E5A2DA10B3D417E8FE1BE55A (void);
extern void Selectable_get_spriteState_m7388F8F08AB8A03CB56516A7C9713733A737629A (void);
extern void Selectable_set_spriteState_mE0E2CDA8757045FE0D35BC4D9E827857F64E19ED (void);
extern void Selectable_get_animationTriggers_m58213BBD3E4D5B7C8A25F1DAC51F2B06176A08DA (void);
extern void Selectable_set_animationTriggers_m564A90FBE85D0F3A5055AEA255E753EF58C2B1D8 (void);
extern void Selectable_get_targetGraphic_m659A2940226EC644AAFC2D5CCC326ABEE6384388 (void);
extern void Selectable_set_targetGraphic_m23DB0DF4E5F2DABD50C662C708B4555162171FB9 (void);
extern void Selectable_get_interactable_m17DD0484DC62DCB4467109488D7A599BC85EC112 (void);
extern void Selectable_set_interactable_m8DD581C1AD99B2EFA8B3EE9AF69EDDF26688B492 (void);
extern void Selectable_get_isPointerInside_mB31AB05760CDC4A72B7E5D7B86061C9829BE5DF0 (void);
extern void Selectable_set_isPointerInside_mF82515A016E440225E31092AC6CB63EA09D71D4D (void);
extern void Selectable_get_isPointerDown_m61C9ECC7F52547B6638CD046CD7FF61A7FA1F778 (void);
extern void Selectable_set_isPointerDown_m02FB181F4C59A8477243C9971AA17CD77A86A70C (void);
extern void Selectable_get_hasSelection_m7F81F2A77E32862AE18BB0459A0732275EFFA11A (void);
extern void Selectable_set_hasSelection_m9EBB907C29E5BB0DAB3066EFCC728595B125D235 (void);
extern void Selectable__ctor_m340EDFEA07F025166175C3ECB1BD2EEDD81C8638 (void);
extern void Selectable_get_image_m88664022F6BC90E7B8D4BFCBA7FE24B48E90C639 (void);
extern void Selectable_set_image_mE9DDDBE46C5A435F9788E88EEF0187B5E09A30A8 (void);
extern void Selectable_get_animator_mE0AB180AF3936F681535220F4344FF3016C96C34 (void);
extern void Selectable_Awake_m55439376D9E09A622C61C4BD7DA413E1E0EFD469 (void);
extern void Selectable_OnCanvasGroupChanged_mC30124BB26D8462F8E163F33A57388B443A1BBA0 (void);
extern void Selectable_ParentGroupAllowsInteraction_m1625A67D30524BAEE9D5D6F39C131712268928AE (void);
extern void Selectable_IsInteractable_mEF8BE44216120C4200B619E9BEE7ABF608D5246D (void);
extern void Selectable_OnDidApplyAnimationProperties_m62471EC7970DF938373D7E63BB1D4DFB74EA7330 (void);
extern void Selectable_OnEnable_mBE48F9440061AFFCEA53B103F7C7A059AC115FA7 (void);
extern void Selectable_OnTransformParentChanged_mC802FD6123F88D70845E1FDE93FD38D38315EB27 (void);
extern void Selectable_OnSetProperty_m9070CBEB5C95931EFC0DA7BCA038461CDF835010 (void);
extern void Selectable_OnDisable_m293DB718E1101FC77E655E4A2C4F2DE1DBD4663C (void);
extern void Selectable_OnApplicationFocus_mE3ADCB53E6FD825F59B51BD0390F0C81AAD8E8F4 (void);
extern void Selectable_get_currentSelectionState_mD8AC0B7BF3C5AFB574C57BDC81274F621978FABC (void);
extern void Selectable_InstantClearState_m8D5BD204B502945CC1AB73A0C45CF8DE199A041B (void);
extern void Selectable_DoStateTransition_mE74A03CC2A2DBCA9C07559B168FA6A77FFE57942 (void);
extern void Selectable_FindSelectable_m332211FC94618A05817C0C62A9C36C60F787179E (void);
extern void Selectable_GetPointOnRectEdge_m3E2D149816CB643503988036FEA4E25F914788C2 (void);
extern void Selectable_Navigate_mF67643CAEFF8AFFB80A1EC743CF6B2C1121556C5 (void);
extern void Selectable_FindSelectableOnLeft_m1DB05BA9AB4FBED7AAD646526926BCC9BC99E134 (void);
extern void Selectable_FindSelectableOnRight_m9F76D3B04DD85E9A2C6DC3F1041DE0C9200F307E (void);
extern void Selectable_FindSelectableOnUp_m3B25FCB3C7EBEA5A777325A7ECB7985A4B7345CC (void);
extern void Selectable_FindSelectableOnDown_mF1715CEA701C504DA775E4A22373881031F851B3 (void);
extern void Selectable_OnMove_m0801D5433615BD3163659A17B1DB2B23886AF05A (void);
extern void Selectable_StartColorTween_m13B3BCF55A09B8C4CD56C25018C93E97F2B51097 (void);
extern void Selectable_DoSpriteSwap_mDE447BE74A0240AE366AA9C0D9701B2E4689ECD5 (void);
extern void Selectable_TriggerAnimation_mDA4462FAF2B2DE28945F4AD30E9F8904F4AC4D8E (void);
extern void Selectable_IsHighlighted_m889908FCD27411E02267021F8A1B0C72525EF96F (void);
extern void Selectable_IsPressed_m2B38EC61FF57A3C4161EDAA8DB4CA5757A3196FA (void);
extern void Selectable_EvaluateAndTransitionToSelectionState_mD0648A10DDF70A60B8B707507CC1DBF6A148F9B2 (void);
extern void Selectable_OnPointerDown_m4425D3C7641AAD2430A7E666F35047E2F3B623D3 (void);
extern void Selectable_OnPointerUp_mF7B6987EE86DD7079DDA835339A17BCFC6E7A4C9 (void);
extern void Selectable_OnPointerEnter_m4AEEEAFB92045B8D8794C65890965E9CC8870860 (void);
extern void Selectable_OnPointerExit_mA288BF802AD6844F51CE19C120DF5CCEBF487929 (void);
extern void Selectable_OnSelect_m50BA6D8F185CEA3211F9DEFE68AB6439AF685242 (void);
extern void Selectable_OnDeselect_m43A2F451FC100ACAFA88D67331CD4537994B8262 (void);
extern void Selectable_Select_mE52B485BB3714E96666F913358BAB9D57588F9A7 (void);
extern void Selectable__cctor_m6D5E8673B9C3F2CE492ECCF88960CBC03C9D8E51 (void);
extern void SetPropertyUtility_SetColor_m05C71F5D62DDB2E51E35EDACC0060205B2BA7237 (void);
extern void Slider_get_fillRect_m35EE2868F52084F9543158A2EAD99476E5C13D9A (void);
extern void Slider_set_fillRect_m2CB86D7C94EA17486DACA010B643F9FE308B6AA3 (void);
extern void Slider_get_handleRect_mF6564572F3D7074E01D17661BD012F5987D328D9 (void);
extern void Slider_set_handleRect_m572400ADF8F03B8931302F709BB638745CAD5111 (void);
extern void Slider_get_direction_mEB650B873607C3D4E49EC4AB25EE9CE2554B33D5 (void);
extern void Slider_set_direction_mD219E6B22DA729C74E1594C8571B926C4A96871D (void);
extern void Slider_get_minValue_m4443221B443E357866F07B062CE39944134C794C (void);
extern void Slider_set_minValue_mC4D1F7709276A9A418F9284A04799FF767DEDC4F (void);
extern void Slider_get_maxValue_mB34C0C9337F5D00ECB2915E8008BCAEB8E7C5FB6 (void);
extern void Slider_set_maxValue_m43F3BF47C6D7063D80C578FD9B95AD88494203BE (void);
extern void Slider_get_wholeNumbers_mF1A52AF2845985E1FC462236783B3E5BE83F9928 (void);
extern void Slider_set_wholeNumbers_m8A76CC011B30B0281F47F8ED085DDE62EACA0EC5 (void);
extern void Slider_get_value_m92843A0FEE9FBF1FC5228C7F677E74642D860010 (void);
extern void Slider_set_value_mA6DC34301E7F76E7FD9C964D61A7B06C95A05D0C (void);
extern void Slider_SetValueWithoutNotify_mD60D03926D6D1CBA6F162DE01296B239CF9A03BA (void);
extern void Slider_get_normalizedValue_mC839197322275EF1318B6E49B7573FDB30F74D83 (void);
extern void Slider_set_normalizedValue_mD8E0F3B3EC5CA862BCD1B2AB42DC1CDFCC381A1C (void);
extern void Slider_get_onValueChanged_m4DA3FD0F8D7BB838F442C07F7796EEE584D0D4F6 (void);
extern void Slider_set_onValueChanged_mBFB4D238D7D0B4C686BF47E944A4F873A82939C9 (void);
extern void Slider_get_stepSize_m55FF421B05B5995454F93AF1D3A5313A6C58977D (void);
extern void Slider__ctor_m205FDE3F1A59F98FE8E0E138FA96C107F79BDDB0 (void);
extern void Slider_Rebuild_mD7FD3AE545912A90D82A84164779FAD30448BDE0 (void);
extern void Slider_LayoutComplete_mD2E60699C63B82C9164232EA8A2669B5436A2DA5 (void);
extern void Slider_GraphicUpdateComplete_m2BFE85324EE12F00974D6034CCEBD058911DBC6D (void);
extern void Slider_OnEnable_m7B8D2AD29196D07E85830E4D17E01EB7D2151E8A (void);
extern void Slider_OnDisable_m3FCB26AAD286DAC967219251AF81A676436A3D39 (void);
extern void Slider_Update_m522C74C2E16CB8923FFBB2F67A39DC92812A2283 (void);
extern void Slider_OnDidApplyAnimationProperties_m09393E7CF5C36A5C28AFDF3B767BB8F7B8B75FBC (void);
extern void Slider_UpdateCachedReferences_m377F41A1442EBDA6661A3E7E40E92B6D4CD4F5AE (void);
extern void Slider_ClampValue_m78647872AACF7C1DADF80CE1355C4FA72E17F91E (void);
extern void Slider_Set_m8407F7245321EAA46ED3E5F6CC9B2F04B9A2FDD5 (void);
extern void Slider_OnRectTransformDimensionsChange_mAD826A7F943BB26DA36F11310D286422ADA5E69A (void);
extern void Slider_get_axis_m1EBD05C9A3C34B1859FEA0192B4569AD45EC7DED (void);
extern void Slider_get_reverseValue_mE0B463C7174C203F870866456E0EF2AD39D8E834 (void);
extern void Slider_UpdateVisuals_mF0D5A86EE4352DBFE092CA49479F1AAD9212B00E (void);
extern void Slider_UpdateDrag_m704D10BF17D39858193BDD4E946558C520DAB304 (void);
extern void Slider_MayDrag_mBC1025F6079EF65A36594E8BC2C0647A7F809576 (void);
extern void Slider_OnPointerDown_m37122B2271F5C26BA2A36ABB70D07B8620A0961F (void);
extern void Slider_OnDrag_mC19612CC7EA3D02F3D338ECD6C534B7E402A3856 (void);
extern void Slider_OnMove_m3FAB8435346C2D8DBA59AB5CC59D569B06CC1500 (void);
extern void Slider_FindSelectableOnLeft_mB3F6A127C5B758ED1CB9E850FEE3BBE0D4CF3F85 (void);
extern void Slider_FindSelectableOnRight_m109193687A0ADDDF1BA1AEA2E6B49A1399AF18D4 (void);
extern void Slider_FindSelectableOnUp_m3523976BA934C07123A7E28CCE8302CAF321115A (void);
extern void Slider_FindSelectableOnDown_mC50638D5F0EE1A5D2E9322F0C7F88ED33F6161F1 (void);
extern void Slider_OnInitializePotentialDrag_m509A0273020AF65D2DA0DB25EC35FDB748F75DC9 (void);
extern void Slider_SetDirection_m84FCDE9EB319203D0C5C4069A1BFC40447760101 (void);
extern void Slider_UnityEngine_UI_ICanvasElement_get_transform_mA7875ACE1B6F89DDBF76529E25C1E122F027C33E (void);
extern void SliderEvent__ctor_m5FD31BB6BB3FAF583C0A555FCF3733EAD6A6C319 (void);
extern void SpriteState_get_highlightedSprite_m5D24B628AB2E4DEBF67E094CCA059BDADAB952BB (void);
extern void SpriteState_set_highlightedSprite_mEECDB7C62DE0C6A0B2A7D5D7ADF54EB8CDDB20B0 (void);
extern void SpriteState_get_pressedSprite_m89052B1818D1659DA7E594F218485F1DEB8128BD (void);
extern void SpriteState_set_pressedSprite_mD01568B87B1BC1374CCFB5CD190D7CD62A6FDAA3 (void);
extern void SpriteState_get_selectedSprite_m5316836E91F7EB454E953CADD439FF69AA198BA5 (void);
extern void SpriteState_set_selectedSprite_m902ACABEC203C0A2408B4ECD7B74C10DFE7BB340 (void);
extern void SpriteState_get_disabledSprite_m6BE5A2231E20BE1600328082B4EFE53EE7F3E12C (void);
extern void SpriteState_set_disabledSprite_m624499C245DC34D314FF0326FE5ADCF35DA28E27 (void);
extern void SpriteState_Equals_mAF58D9F36662F5A8196071690175AAFCC4506653 (void);
extern void StencilMaterial_Add_m84AA68A912ABDADC5187B9E9BC0E4FE6DB4E2220 (void);
extern void StencilMaterial_Add_m4FBC1C2732C3B161EE38767ABE2020105E0BF7F4 (void);
extern void StencilMaterial_LogWarningWhenNotInBatchmode_mDA6F8CA72D4AAD9C9B480AD2AB4FEFE0C73CD8E3 (void);
extern void StencilMaterial_Add_m7BF719F0507970D16D11F47019761391ACE55766 (void);
extern void StencilMaterial_Remove_m828D3D85F213AD5B3E4FE6A230981E9115007412 (void);
extern void StencilMaterial_ClearAll_mB1977688C5675CB7C32AD21537795223742B7084 (void);
extern void StencilMaterial__cctor_m18A5D42EF758C1549A3DA3C6871A47042E5E547B (void);
extern void MatEntry__ctor_mEB63E7AA0A179AF5EE93EE6DCAC4E91BFAEF2CBA (void);
extern void Text__ctor_mE28BC6E42B4715F23401A9379C9681867A0631C1 (void);
extern void Text_get_cachedTextGenerator_mFC242539F7380F54696D431B126B69DC4EFC821E (void);
extern void Text_get_cachedTextGeneratorForLayout_m409B96DB358F900C531F543CE351B02B0974A077 (void);
extern void Text_get_mainTexture_m8EF8E897193467EF8B839C99B5F388AA3241315D (void);
extern void Text_FontTextureChanged_mD716EBECCAFA43F8D01D90FF9F869C69E484A763 (void);
extern void Text_get_font_mBF98ED39D5C5081AF14A64170EC3391D206CCAFD (void);
extern void Text_set_font_mA0D2999281A72029A5BC7294A886C5674F07DC5F (void);
extern void Text_get_text_mE71474D219ECCE472FD9A08679168E859577A3D1 (void);
extern void Text_set_text_m6872BDD62D0904C075F06A19CF5AD96A2B2FE23F (void);
extern void Text_get_supportRichText_mE5B61670099BB2611BB60D84ADB72C9A54BAC68B (void);
extern void Text_set_supportRichText_mB4DB141150AEBCCADEFFF4EC7A799F85FD075265 (void);
extern void Text_get_resizeTextForBestFit_mA4EEC57C4C188C1598187D1E11A83B950883B011 (void);
extern void Text_set_resizeTextForBestFit_m1376BB9FDBAC5571E0F24564E22391AC8EB89A35 (void);
extern void Text_get_resizeTextMinSize_mAB17F2DA673C7A3860E6EA0746BFC0C919D5A659 (void);
extern void Text_set_resizeTextMinSize_m1DC5160514ED872A8C572024A94D7EA9D6357655 (void);
extern void Text_get_resizeTextMaxSize_m7B61DCEEA4D801C4B8149674B27DBE99098A38E3 (void);
extern void Text_set_resizeTextMaxSize_m25EB2C9302AA9354237A2F56BB3E019192C6015B (void);
extern void Text_get_alignment_m01C4D0437DF8A2E05BE4489779A8BEF231A2F2CC (void);
extern void Text_set_alignment_m9FAD6C1C270FA28C610AB1E07414FBF96403157A (void);
extern void Text_get_alignByGeometry_m68F41E942D6BC7AF8F134B3CCDF039A8D3D49DC3 (void);
extern void Text_set_alignByGeometry_mB427C41097943370E11579A3DA822A3295836CE2 (void);
extern void Text_get_fontSize_m837C0618E78D0FDA972D11DDE3015DC888E93993 (void);
extern void Text_set_fontSize_m426338B0A2CDA58609028FFD471EF5F2C9F364D4 (void);
extern void Text_get_horizontalOverflow_mC909085F76EF49D62A70A8E503C5BC14C30176F1 (void);
extern void Text_set_horizontalOverflow_m10AAFBA65FD7F4B1934B5D628B3E70D75D02FFD6 (void);
extern void Text_get_verticalOverflow_mEC72BD123A8B12278F6F7B89D29EB9D93D0A97FD (void);
extern void Text_set_verticalOverflow_m72A544DEAE0EBFCCBDE8174DF4C10C903DA8444F (void);
extern void Text_get_lineSpacing_m124405CE023E0E23D9040BAA84318408248DF9CF (void);
extern void Text_set_lineSpacing_m36CE565189BAF89DB1DA1E0DE5786521D4763D0E (void);
extern void Text_get_fontStyle_m7684B5FFE1DC8237FB573A012B482DDB04E3BA47 (void);
extern void Text_set_fontStyle_m5ABEF66BFC88E7E0A950E2817E4978FF472F6C1D (void);
extern void Text_get_pixelsPerUnit_mC48AE94D40662DE114A72B870DF77BF7B418925E (void);
extern void Text_OnEnable_m183EE6D534BE840F16D23DD36A0C1619AFC905F8 (void);
extern void Text_OnDisable_m94F10EBC54572DCD1D3DB7B6C7CBEC8CBE8AF60E (void);
extern void Text_UpdateGeometry_mEAEFCA5F05F983DC984FA1497A905A4B2DCF132F (void);
extern void Text_AssignDefaultFont_m475A3C848C9F8ADFBD5438E936E81B618FB4B398 (void);
extern void Text_AssignDefaultFontIfNecessary_mF5167C211C87E6DD62978C938F6521B287F371CF (void);
extern void Text_GetGenerationSettings_m620E0E5AFB30E3331A0371EB2361F587BB0A1E0F (void);
extern void Text_GetTextAnchorPivot_mD0734509B028EC6E42800FD73A6CB8476EDF0150 (void);
extern void Text_OnPopulateMesh_m6505569424B120C338EAF6840893E38530185ECE (void);
extern void Text_CalculateLayoutInputHorizontal_m42B464C2F7C1AE5A0B1311FC5E6BECE1C6EEAC5C (void);
extern void Text_CalculateLayoutInputVertical_m7A76D597BFFF1C68C3FEA03E26EE440B9A67E532 (void);
extern void Text_get_minWidth_m8C6D60E991BAABD25859D3C04AAB6107BAD4F139 (void);
extern void Text_get_preferredWidth_m1624ADC4EB2193885E4EA35D44E6A80C12A436BC (void);
extern void Text_get_flexibleWidth_m97C2D396445B82CD45260F21CD15CF6F6B279A4A (void);
extern void Text_get_minHeight_mB6B7E5A4426313C18A7F2CD28F3A7B5CE2DAA6F9 (void);
extern void Text_get_preferredHeight_mF95F444557BFB576AB0E1E876146E03174DA058C (void);
extern void Text_get_flexibleHeight_mC82DCE442BB670E3AC683BA1C660810FB4936FC8 (void);
extern void Text_get_layoutPriority_m99DD053FCD9F30FFCA40937B2DFBF796B67CBD12 (void);
extern void Toggle_get_group_mE182279EECC97BECAFFA919AA08E4D5B6E9C83FF (void);
extern void Toggle_set_group_mEE85FE3AB2ACFF9056DA613239DBACECA588507B (void);
extern void Toggle__ctor_mA84E212B567F21B617B480F90BC335B602523400 (void);
extern void Toggle_Rebuild_m101F36A2CD0C4ABD7BAF41262493A0D6ED0B0D3E (void);
extern void Toggle_LayoutComplete_mD0D33BD5078F2E190A61E306A4CF88E45F80C473 (void);
extern void Toggle_GraphicUpdateComplete_mE1636C76DF59E2B1483BB62FF0982EAAEE1185EA (void);
extern void Toggle_OnDestroy_mCC155BA7A5FE311B49536F9C904AC74EC6282E68 (void);
extern void Toggle_OnEnable_mA9EF315CBA63011213BBB13A9CA1EB84147DAF0D (void);
extern void Toggle_OnDisable_mB32FFD1AAE48A56205E782BC041A5EC86B66B536 (void);
extern void Toggle_OnDidApplyAnimationProperties_m1D3922CE86EA2AFB38F3204935BC15DCD534BFB3 (void);
extern void Toggle_SetToggleGroup_mDD819C46310559ADC2346EAEE7BE3BEEF51BB1B1 (void);
extern void Toggle_get_isOn_m89A609E936CD67F460E336CA8E03C4047BFB6619 (void);
extern void Toggle_set_isOn_m61D6AB073668E87530A9F49D990A3B3631D2061F (void);
extern void Toggle_SetIsOnWithoutNotify_mF5B19F1767B9EFF02335E41D3D2DC678642170C2 (void);
extern void Toggle_Set_mA2CCB1FBC23519004E2F47CA0F53CA6E1B368DDE (void);
extern void Toggle_PlayEffect_m728310FF62E7251958CC8D4016C2435AAC9DF0A2 (void);
extern void Toggle_Start_m3E085820286E51F69BD848C1EA1FCA7DFD07E3E7 (void);
extern void Toggle_InternalToggle_mD36F2575F4B2E26641C0E24A73B277E0C8BF25A1 (void);
extern void Toggle_OnPointerClick_m2D0D693EE40BDC56482DA6982C3CB42DBACD98E3 (void);
extern void Toggle_OnSubmit_mCD303693EDD107D676D55CABA07BA43F9328C9B2 (void);
extern void Toggle_UnityEngine_UI_ICanvasElement_get_transform_mD7C00596C9A48C3A7C40E285DFFF9C2B255221C2 (void);
extern void ToggleEvent__ctor_m8983544B67193810F8BAA820B2C408251CBEF145 (void);
extern void ToggleGroup_get_allowSwitchOff_mA6724BF0B3965330FE892FB9E144D761ACFA7364 (void);
extern void ToggleGroup_set_allowSwitchOff_m30C71C353E740F9B3F9641689A4ABA4AB8BAC9C3 (void);
extern void ToggleGroup__ctor_mED87CABB1682380A925DCC8FA41C739ED6ADF3EE (void);
extern void ToggleGroup_Start_mA5D0C8F6437723E98C53149716EA28C7984BDDA7 (void);
extern void ToggleGroup_OnEnable_m5679531D85D1CAC371A71AC5B1E980248A01F038 (void);
extern void ToggleGroup_ValidateToggleIsInGroup_mA9CAD4C4345BE7AE18351AB4F31D1574A900280C (void);
extern void ToggleGroup_NotifyToggleOn_m0676292BE46CBE477DAF139D9DABCE5DB72F7F45 (void);
extern void ToggleGroup_UnregisterToggle_m6A07803166E901CCDE4F23FCED1BD76CBB002307 (void);
extern void ToggleGroup_RegisterToggle_mADE82548BE13A13E9FDD00F488471D3416A97214 (void);
extern void ToggleGroup_EnsureValidState_m5A8B88CF91EEB8F7E7DECB87261EAFF5A556778B (void);
extern void ToggleGroup_AnyTogglesOn_mCE714D4DBDD9CF56D41C830719FCFC24008C1700 (void);
extern void ToggleGroup_ActiveToggles_m04CAF25D2C9DE5F310090D63B9841963954BF2BF (void);
extern void ToggleGroup_GetFirstActiveToggle_m07251AA447A7F06B082B685CD44F5C0465A323BA (void);
extern void ToggleGroup_SetAllTogglesOff_m770745001C6B4553805F3E333084F5DFCB08B78F (void);
extern void U3CU3Ec__cctor_m878A0225FF0D038AD88F4E89CB4DACC057132A26 (void);
extern void U3CU3Ec__ctor_m71A650578043739A4182051BA48B2792FA8C287F (void);
extern void U3CU3Ec_U3CAnyTogglesOnU3Eb__13_0_m060E207CF61E772B8A8C9B5262EA8867E086B397 (void);
extern void U3CU3Ec_U3CActiveTogglesU3Eb__14_0_m9567251B779C881881B16EB6ADEF9D7E71CD3B0D (void);
extern void ReflectionMethodsCache__ctor_m2BBCC7AC457DD7CB3836B01B5307F8B6285CADB3 (void);
extern void ReflectionMethodsCache_get_Singleton_m77EFD7AC0043333E09987BD601113B810453C42B (void);
extern void Raycast3DCallback__ctor_m8F06E2216CD1CCB88B9B94197226CE3F1EC67310 (void);
extern void Raycast3DCallback_Invoke_m9CDA4EB17B5A46853DEBC894E610B5C8FCE23415 (void);
extern void Raycast3DCallback_BeginInvoke_m2FED0375CEC6BC3E6545ED6A19E88D7913AB2E6F (void);
extern void Raycast3DCallback_EndInvoke_m4DB350E572F75C4D0C92DE38EC9B8B0368464871 (void);
extern void RaycastAllCallback__ctor_m971EC206409480AB580913F8E1E7E9850DC0DB03 (void);
extern void RaycastAllCallback_Invoke_mB30319766AF5F0D5145A6C0AFFEA829A31A5D4B7 (void);
extern void RaycastAllCallback_BeginInvoke_mFD486859C455F0E5198BC58E3505CD12ACDAC0AA (void);
extern void RaycastAllCallback_EndInvoke_mBF07366FEF9002D0A44A4E8982BA201699C864CE (void);
extern void GetRaycastNonAllocCallback__ctor_mBF3B05A56B530A13FFE0C8F66A12A94230598787 (void);
extern void GetRaycastNonAllocCallback_Invoke_m36411365E9622819DAB93D0BB0F169FEE99F07D9 (void);
extern void GetRaycastNonAllocCallback_BeginInvoke_m381F75A2BBE062F5DFB3B910D3B3066CA512D024 (void);
extern void GetRaycastNonAllocCallback_EndInvoke_m0A8CB6C1DBFAD95A2E3AC7686E161E1D6057F52D (void);
extern void Raycast2DCallback__ctor_m4C15F69C429322DFC4BDA9A9E99A500D4C9718BB (void);
extern void Raycast2DCallback_Invoke_m36A82ED39B674FC0D8D80D62948B729CC244C366 (void);
extern void Raycast2DCallback_BeginInvoke_m2135AC8F9004783AB5F6E92DFA11B187CBBC3DE8 (void);
extern void Raycast2DCallback_EndInvoke_mC51CF38067DEC9BBB94782FF1BB129A19EC602C4 (void);
extern void GetRayIntersectionAllCallback__ctor_mF6F153CE75C4728D934A766700346C408088ECFF (void);
extern void GetRayIntersectionAllCallback_Invoke_m917AA4108EBDC724AFEF39BFD06A586B7461F497 (void);
extern void GetRayIntersectionAllCallback_BeginInvoke_m9C824E08C6261803AAE7A1B39D7525A4B748679E (void);
extern void GetRayIntersectionAllCallback_EndInvoke_mE9CE6394D3F2C7C56DE578882666ADBFFC729965 (void);
extern void GetRayIntersectionAllNonAllocCallback__ctor_mAF9F997ABE4C3EECD5A402BAB7CB30B19CC50F9C (void);
extern void GetRayIntersectionAllNonAllocCallback_Invoke_mFAA36E9AF362DC72204EEF53B28DBFC3367D09A7 (void);
extern void GetRayIntersectionAllNonAllocCallback_BeginInvoke_mE93D1099CC919A75041D25C692B4BA8FB1F66061 (void);
extern void GetRayIntersectionAllNonAllocCallback_EndInvoke_m0C4A807380DA32E16F797AF910C2A69C06D1352C (void);
extern void VertexHelper__ctor_mE8DE438637116EA7AF8180E10E7641FD00DB64A5 (void);
extern void VertexHelper__ctor_mE42FAE63F4A3200C38ACFDD9C3F601FDC7E258F8 (void);
extern void VertexHelper_InitializeListIfRequired_mC7180B010A6DCC7C1115A33D29B8E00B92DB2542 (void);
extern void VertexHelper_Dispose_mAA41704ED960A368DA8BFB8D1506A3969A033653 (void);
extern void VertexHelper_Clear_mB19E51AD5AF1C04CB2C6E6A272D032D651EC40F5 (void);
extern void VertexHelper_get_currentVertCount_m45BFEBD6FCB7DF3BF9F76946D6002BDC58B173A4 (void);
extern void VertexHelper_get_currentIndexCount_mF409C3D4A6786E64AC4E8EC0D6D97E27597A900C (void);
extern void VertexHelper_PopulateUIVertex_m48FF05C38D56529E18A360D629F4842BE5D050BE (void);
extern void VertexHelper_SetUIVertex_m539A518867E7872E0893715AD372DC9A06334FD9 (void);
extern void VertexHelper_FillMesh_m524F00287F0A0C7683E2CC7768A77B5755544A0E (void);
extern void VertexHelper_AddVert_mC7596EEC59384AB7BFD12CA6F8350ACDC5BF5E56 (void);
extern void VertexHelper_AddVert_m5765AC8F13C86709B2EFEC613D492963BE1E1198 (void);
extern void VertexHelper_AddVert_m2187D76DC2CE7E9AF69280424660739858901287 (void);
extern void VertexHelper_AddVert_mB65D778E8E3C6916CDFF5382208890882C3031BA (void);
extern void VertexHelper_AddTriangle_mBA2504734E550C672A33168BE119D76D92C788A4 (void);
extern void VertexHelper_AddUIVertexQuad_m6AC21081F2A5A48D22BC3497E527D0A9AB8278B0 (void);
extern void VertexHelper_AddUIVertexStream_m213E27491ADDA2C603D40730E34F3AA6C5E7757D (void);
extern void VertexHelper_AddUIVertexTriangleStream_m29A217271BF2B3D3D60B7CBDA4114C7BB40C2841 (void);
extern void VertexHelper_GetUIVertexStream_m87D56EB5559CCCA150F68B1DD660FF4154CACBCE (void);
extern void VertexHelper__cctor_mEED5FDE0E235482F4F4A551E114358266128E0CC (void);
extern void BaseVertexEffect__ctor_m9458015A3EDD3D42F821F8608D0E595B85A70B6C (void);
extern void BaseMeshEffect_get_graphic_mE8226BAC46FDB49681BEAD2DE8A4EE3CEC18FF04 (void);
extern void BaseMeshEffect_OnEnable_m74592558CD0F70DC35EEFCBC1E23F84493CD77F7 (void);
extern void BaseMeshEffect_OnDisable_mE005F7A15BFE7127D274717C3C482561481D3603 (void);
extern void BaseMeshEffect_OnDidApplyAnimationProperties_m981407A626B008820D9554F3778DAA8E7959451E (void);
extern void BaseMeshEffect_ModifyMesh_mD98C4CF227E0EF63BD031824AABC5F3C1AB7BDEC (void);
extern void BaseMeshEffect__ctor_mFFF23FD89B32150DAC512C556A1CCF563D062427 (void);
extern void Outline__ctor_m1E8EF7C85B52E0DE3D67506C8F7C118A1E2B3552 (void);
extern void Outline_ModifyMesh_mC6D402BD2D65E27A163B68676F3102AF03BFC4C9 (void);
extern void PositionAsUV1__ctor_mE24D2DE5032BCAE8B065B8D4CAA90BA3256EB382 (void);
extern void PositionAsUV1_ModifyMesh_mFE5C4211D991E7A292A29DB85BFBAF3C31282F90 (void);
extern void Shadow__ctor_mDE7F89B477692F7FF0CCE6B8CE01A63D9942291E (void);
extern void Shadow_get_effectColor_m6E7751BB8792C85BE9DAD0D133D787317D9CF59B (void);
extern void Shadow_set_effectColor_mCCC5DB6B7D09C5DEE0C677DEB3B9B0C578F05AF1 (void);
extern void Shadow_get_effectDistance_mA87EB50066AFEBC13C69D27376E50033930FA58F (void);
extern void Shadow_set_effectDistance_m5E7B565C41CF2A8C84EC98319ACBF5C8E1FE47DA (void);
extern void Shadow_get_useGraphicAlpha_mD2A88F78B7B2E25905D1750788B0DFA3082AC616 (void);
extern void Shadow_set_useGraphicAlpha_m70CCAE5D643B2373A5ADC8BD04031D3CBF0AF722 (void);
extern void Shadow_ApplyShadowZeroAlloc_m010AE345D731FC53595A62CF8D0B401C2D6F4B58 (void);
extern void Shadow_ApplyShadow_mB615BBD368431C63B1407CAFD7DD32BE023E543E (void);
extern void Shadow_ModifyMesh_m7201FBFE56F97B440215E92064BFC59F00ACA9C6 (void);
extern void ColorTween_get_startColor_m9E33FB5C5F76BCF49A3B20201CD8006DBFB46012 (void);
extern void ColorTween_set_startColor_mD22349343421BD44F0C31E537718ED53BE4850DA (void);
extern void ColorTween_get_targetColor_m240A7018BDC3B44AB44BA674AA16C39960BC23FF (void);
extern void ColorTween_set_targetColor_m7D8E74B32AC3A9C17C3192096003B12A1500D749 (void);
extern void ColorTween_get_tweenMode_m06B83FB6E45A807F83FDD762A8241D478FD13F8B (void);
extern void ColorTween_set_tweenMode_m105EEB49F6632D6D105C63DA9919385233A5D4DE (void);
extern void ColorTween_get_duration_m40D8F08C13FF2FE7583746934C6A017A93398548 (void);
extern void ColorTween_set_duration_m1C278AB5A90B5C108CEB4870CAC90A9A9EAC19CB (void);
extern void ColorTween_get_ignoreTimeScale_mEDB15A4ADE3A0B9487D240964A7571247F974708 (void);
extern void ColorTween_set_ignoreTimeScale_m060FF3CED06F73EA1F555A37999D61DC58F99927 (void);
extern void ColorTween_TweenValue_mF5CBA9BDE7F73E47F9CF26DC4EC2419694049860 (void);
extern void ColorTween_AddOnChangedCallback_mAC2856A154604B4B6721DAC185B819A98D6F7438 (void);
extern void ColorTween_GetIgnoreTimescale_m679C83012235779A37DCCD0AA75CD6B0DAE5BCFA (void);
extern void ColorTween_GetDuration_mC40D6776769FDB79C7ADC42D59F059A2A9AE2F66 (void);
extern void ColorTween_ValidTarget_m1D7A682CE00048FAF1A3BDD55EB76F44C9122B4D (void);
extern void ColorTweenCallback__ctor_mFEB49A6A1ABACFE2351A63060F786B762E2DC6B9 (void);
extern void FloatTween_get_startValue_mCA121483CCF4C8F10991BB3306E3F2769EBB3A3C (void);
extern void FloatTween_set_startValue_m43B55D74B7B34D9C32439D6004F306BFA18E4A1A (void);
extern void FloatTween_get_targetValue_m6EFBD9EAB206F145959832269DC24C4B68FEE6B1 (void);
extern void FloatTween_set_targetValue_m4AE44CE862797E898CDE00A1B7D6A33CE0AFDCFB (void);
extern void FloatTween_get_duration_mB1496D38A618FF8282205FD3AA14CA9C6D76454D (void);
extern void FloatTween_set_duration_m40E10A7B796B4B54FFB8DA3889B09557BEC98456 (void);
extern void FloatTween_get_ignoreTimeScale_m6F6BDCBD59C19E68572370F9FE3D7373B4212B3B (void);
extern void FloatTween_set_ignoreTimeScale_m09041A4110040F9C86D24E1B4DED6E6B7FB206A8 (void);
extern void FloatTween_TweenValue_mE51344369BDDA58E9C3AEC62E1B1C1AC0349278E (void);
extern void FloatTween_AddOnChangedCallback_m13B1FFCAD78C7E690E70704311B20D5BB67D8224 (void);
extern void FloatTween_GetIgnoreTimescale_mA2463285D4524B70A46776FC60C4F939B3BCD045 (void);
extern void FloatTween_GetDuration_m3E981D91F15C36ED6F241117665E703F2BD2A6D4 (void);
extern void FloatTween_ValidTarget_m36EABC84C8FEFF79EBAC8E9C3C7A394F1377E311 (void);
extern void FloatTweenCallback__ctor_m3BD06E1999E88B4BAC7627A04B37300331CA210A (void);
extern void PanelEventHandler_get_panel_mA34094B6004584B051A7E9554DCF7CE3C18E2642 (void);
extern void PanelEventHandler_set_panel_m30302AED739E083827B25651029CB2F1563D2988 (void);
extern void PanelEventHandler_get_selectableGameObject_m327CAA6DDDE5191CF001B5FED18EC8857E6915FC (void);
extern void PanelEventHandler_get_eventSystem_m4AC951AFE51C13E82DF39BD27DDD6BE9258535A2 (void);
extern void PanelEventHandler_get_isCurrentFocusedPanel_mE9616A8F71808D5507809F0A6A5521DB25564587 (void);
extern void PanelEventHandler_get_currentFocusedElement_m7D1860148CB99DE9BE6923146289E97A6DD02C18 (void);
extern void PanelEventHandler_OnEnable_m75610BA601D59B6BCB212DB140580859B2C7B777 (void);
extern void PanelEventHandler_OnDisable_m7174BFEA2A756C36F810433F9D285D9D0B464CE4 (void);
extern void PanelEventHandler_RegisterCallbacks_mDA52FF8A14161DC03A7B09826079F60AEDBAC565 (void);
extern void PanelEventHandler_UnregisterCallbacks_m93894C7D3D238507B771066CC0025C98816D309C (void);
extern void PanelEventHandler_OnPanelDestroyed_m1E7871C24E171C75D64BA6F6FB8F70EF7B345366 (void);
extern void PanelEventHandler_OnElementFocus_m5EF528DB20E06FC6EBECE22E71F90B47660041F1 (void);
extern void PanelEventHandler_OnElementBlur_m7FB60FF1D38D571180F0FB9B740DE531E9F43A24 (void);
extern void PanelEventHandler_OnSelect_m01E0EBF7AF99013F9D7B0EE96F52CEA3B2C6FB68 (void);
extern void PanelEventHandler_OnDeselect_m6AF499D5E0F1F162B5BCEA063D4D285E086AF663 (void);
extern void PanelEventHandler_OnPointerMove_m753962E17CA7F9176FF96F765527BB093ED1F058 (void);
extern void PanelEventHandler_OnPointerUp_mC03E88905E10E8730E8211810EC98927A3B89F96 (void);
extern void PanelEventHandler_OnPointerDown_mB46E8626C4F1D143AA495ABAF6B5D57301D3C303 (void);
extern void PanelEventHandler_OnPointerExit_m2A57890B6822CBB0D51D61FEBA91B32FE269B1B4 (void);
extern void PanelEventHandler_OnPointerEnter_m0627B36F32B2C7D59783CF07C7781AA66F202C70 (void);
extern void PanelEventHandler_OnPointerClick_mD65AC5B586A941C8D235326E29A1DDE0B07B5D14 (void);
extern void PanelEventHandler_OnSubmit_m56C7D96593E7DC7B561AE24B741647431C75E84D (void);
extern void PanelEventHandler_OnCancel_mAC960731F19FB4522FD960CD51790361A9F26C8A (void);
extern void PanelEventHandler_OnMove_m3103CB2983C10B1E721004FDE9EAAF9E8C598DF4 (void);
extern void PanelEventHandler_OnScroll_mE4812293B72E54A268D49C31845DF17687E68DA4 (void);
extern void PanelEventHandler_SendEvent_m380CCD38E3E7949B65EE3001067AA9548B19B9F5 (void);
extern void PanelEventHandler_SendEvent_m8A731185591EB81DC398B72D4C081970A89D421B (void);
extern void PanelEventHandler_Update_mB47B3B1C74E2FE6EC56B7A6861D71DACD2FFC733 (void);
extern void PanelEventHandler_LateUpdate_mF537C32BA3237501B31627DEFCED28C934DDAF14 (void);
extern void PanelEventHandler_ProcessImguiEvents_m2C97483500700868379D4AF34C85D9CFE1EEA63A (void);
extern void PanelEventHandler_ProcessKeyboardEvent_m665D51CC6E7CDE6F1C130A49B89CD67C29A6F450 (void);
extern void PanelEventHandler_ProcessTabEvent_m57753FE73E0530973D9A9EF0D606B773CE16A337 (void);
extern void PanelEventHandler_SendTabEvent_m00C849500EAEFC5F544C811A1F6D6496EDDD3B8B (void);
extern void PanelEventHandler_SendKeyUpEvent_mE718C7FBA4F4936F9D3B0C3FB3E3629288AD0F44 (void);
extern void PanelEventHandler_SendKeyDownEvent_m41EE3C5B80C20F66A3056EC839FC60BCBACCE6B5 (void);
extern void PanelEventHandler_ReadPointerData_m655D52851C00124DBA14106CDBE322B7AE2F9372 (void);
extern void PanelEventHandler__ctor_mF90AF37F849E48687B1A1D3730E952A379A62C5B (void);
extern void PointerEvent_get_pointerId_mD4E22379BC076C3D75E103BC55ACFBA81BEF59BE (void);
extern void PointerEvent_set_pointerId_m1BFCE40A5AF978254069B94292CADC4B39CB4E6B (void);
extern void PointerEvent_get_pointerType_m6AB451260BF46DEFEFF3607498093DE56F8CF537 (void);
extern void PointerEvent_set_pointerType_m1BD8CE6C878A3FFB6441A60302634515525E1050 (void);
extern void PointerEvent_get_isPrimary_m5AF6EA62872F5E02DF4E88BCB078CAEDDB0813A6 (void);
extern void PointerEvent_set_isPrimary_m90DD30E4F4B1641C8F800C4EF04DF078F7F37D2E (void);
extern void PointerEvent_get_button_m72275A3B433F9433FAAC939B5776E908CBAC488C (void);
extern void PointerEvent_set_button_mBD5A4ADBC9FB28B3D78019091D0279C18AC5F248 (void);
extern void PointerEvent_get_pressedButtons_m809CC87D0F8B424423079C5DA3E4EFC87E829F02 (void);
extern void PointerEvent_set_pressedButtons_m0B5199481431978AC07CFEAE090907BA5E70FA68 (void);
extern void PointerEvent_get_position_m6CAC16F2273B6222BB18583B11B85BE3ECA8BB45 (void);
extern void PointerEvent_set_position_m24E0958379E88BDD173E563CC00B2523E77EE051 (void);
extern void PointerEvent_get_localPosition_m06C5B58432C1E806B885F67BA0FB8C90EAD71793 (void);
extern void PointerEvent_set_localPosition_m759E75018F6729AA6744C93A57907ACEB390727C (void);
extern void PointerEvent_get_deltaPosition_m185F5E48BD0879D48BADC520D64DDD68183B83D2 (void);
extern void PointerEvent_set_deltaPosition_m12E7B298A9EEDC1D623E72EA99758204F16B4A11 (void);
extern void PointerEvent_get_deltaTime_mD95C5D61E308ACD9D91FD202D274C2DF94780940 (void);
extern void PointerEvent_set_deltaTime_mEECBC843D749F429B1F72559BB488BE17BBFC3E0 (void);
extern void PointerEvent_get_clickCount_m1E9AE0EF81D8BC131012E0DEE2C4E169C8B1EE06 (void);
extern void PointerEvent_set_clickCount_m87C44B61E5E2154178CD4D4CD931C2C463971B89 (void);
extern void PointerEvent_get_pressure_mA7FA9AFBE607289D1A785889E0E2C8CEB705EDB2 (void);
extern void PointerEvent_set_pressure_m1D036CF601B6EEB97DBDB8DB75F0923D39303FD9 (void);
extern void PointerEvent_get_tangentialPressure_m585887790F2A05742888E412B19E0331C4402320 (void);
extern void PointerEvent_set_tangentialPressure_m7ADB233CDA686FCB10A995F2A6826EE5F54AB36D (void);
extern void PointerEvent_get_altitudeAngle_mFDE6773840B002EC90E34041817D96BB8F27A3C4 (void);
extern void PointerEvent_set_altitudeAngle_m8B963C51BB5DB8A14A943F4B1BEC39B175ABABEB (void);
extern void PointerEvent_get_azimuthAngle_mDB91EA27BE4126C4582A66DF75CB8012DE16254B (void);
extern void PointerEvent_set_azimuthAngle_m404D73D4BAF6C658A52B29DD2C1D5FBEDC174139 (void);
extern void PointerEvent_get_twist_mDE5D41083F1E9237B3B852B4E8EA778E2C5D2AE7 (void);
extern void PointerEvent_set_twist_mCA0ECFFE48E1771A1540212CABB34326C7AD6B5D (void);
extern void PointerEvent_get_tilt_m424372381952C6375821AE5652E0B61EA26EFB32 (void);
extern void PointerEvent_set_tilt_m48167AF2DE4D07C779878FBB802ED4995D2176D9 (void);
extern void PointerEvent_get_penStatus_mEF440062B56450BE46F12A8CA949CE0C919F7594 (void);
extern void PointerEvent_set_penStatus_m239C34E387BA4214C671F7F5E521E9840CBF9A89 (void);
extern void PointerEvent_get_radius_m2C6907BE1B20DE289E3C166F45FBBCEEAB095F32 (void);
extern void PointerEvent_set_radius_m387840E4830548F1B1DA865A5A062062D86590EC (void);
extern void PointerEvent_get_radiusVariance_mFED4A22BC0C0667DDC74F6046046A5DA315F4CA2 (void);
extern void PointerEvent_set_radiusVariance_m2627A414E6EFAE8132E2B4FBAC008D830CF0458D (void);
extern void PointerEvent_get_modifiers_m31E21D875E7EF1A47DB29878AA76698B0047BD6D (void);
extern void PointerEvent_set_modifiers_mB339D7800998DB09F5D8B47B7DDD365897FD61C5 (void);
extern void PointerEvent_get_shiftKey_mB459C1F6FA17DA9FF904A67473A19A1B22970631 (void);
extern void PointerEvent_get_ctrlKey_m6EEB9C3A61C998C00946B424121C7BB32CDA6BED (void);
extern void PointerEvent_get_commandKey_m750005DB9507733FAEE22D4DE58F28C11FD15DB3 (void);
extern void PointerEvent_get_altKey_m6306F34C315A6BF2B2B95448657A812817AE2B4E (void);
extern void PointerEvent_get_actionKey_mED4C6D96CBEE2F84F52354EFB3540A5759A47CA0 (void);
extern void PointerEvent_Read_mE5A2B332E857E3264562FF872625FB5ACE2E3248 (void);
extern void PointerEvent_SetPosition_mBFFE588EBFBDB353A634B4D6D544291073916B88 (void);
extern void PointerEvent__ctor_mE689E41BEA012A2914C799FECBCE48F7A58FCF73 (void);
extern void PointerEvent_U3CReadU3Eg__InRangeU7C90_0_m46008DBA5EB0F3B7C13C191A22B393A0046E2F7A (void);
extern void PanelRaycaster_get_panel_m9D8D3E52B0D7A2E4F71B997CC95FB8C808395B85 (void);
extern void PanelRaycaster_set_panel_m840C66DD38B96603B01E8FAA09C74CA1A67E602C (void);
extern void PanelRaycaster_RegisterCallbacks_m840C71BFC5351078CB6BE82C8510F596DC55616D (void);
extern void PanelRaycaster_UnregisterCallbacks_mD26ACD360F0C27CFB33A824ADE371742853D66F6 (void);
extern void PanelRaycaster_OnPanelDestroyed_mF761BC7FD349DB95EBBD8C8D55B404300E5D8AF2 (void);
extern void PanelRaycaster_get_selectableGameObject_m26B496BDA7A92AD0C66B4209171B56321308A628 (void);
extern void PanelRaycaster_get_sortOrderPriority_mBFB2EAC6C4F13CAA1E1723216A0B624B7652EA54 (void);
extern void PanelRaycaster_get_renderOrderPriority_m5E21F65FA1954268DBE3862EB754B20A8B48BE8F (void);
extern void PanelRaycaster_Raycast_mFD63FF3E65B14E412D6CD21A3A9455416CE5F895 (void);
extern void PanelRaycaster_get_eventCamera_m5CDBA1FA81F8BB62020925C81617830897793A2B (void);
extern void PanelRaycaster__ctor_m1F8C36B6C6A4A92394FFB160E6A084F8FA833F6C (void);
extern void AxisEventData_get_moveVector_m7979B5CF62B6B3B0C5F2DA8B328C499ED80ECC41 (void);
extern void AxisEventData_set_moveVector_mC744F8B3519A6EE5E60482E8FB39641181C62914 (void);
extern void AxisEventData_get_moveDir_mC8E219BB19708AC67C202C860DF2E6D08C29B8B9 (void);
extern void AxisEventData_set_moveDir_mD82A8AEB52FEFAC48CA064BB77A381B9A3E1B24B (void);
extern void AxisEventData__ctor_mD9AFBD293F84F7032BAC2BDCB47FF5A780418CC5 (void);
extern void AbstractEventData_Reset_mC3FF13B6FB1012E8FAB00250AE8CD2E1975EF6AC (void);
extern void AbstractEventData_Use_m5DBA1B649A757E09ACB14C3632998231C03795B8 (void);
extern void AbstractEventData_get_used_m0C95B1F392BD74E99F3AD87963647AA060EE5DDF (void);
extern void AbstractEventData__ctor_m3D5B26D1C8BC7ACDDF16F505CF7AE273B54584FC (void);
extern void BaseEventData__ctor_mE51C4DB618D8661AB2527EC5DE4D563D2284F558 (void);
extern void BaseEventData_get_currentInputModule_mA46B583FC6DAA697F2DAA91A73D14B3E914AF1A5 (void);
extern void BaseEventData_get_selectedObject_m0642DE5E08D7CCC49C67D66B296EEE060E357CE1 (void);
extern void BaseEventData_set_selectedObject_mF3EE53D700B0EA9444D1D7FAF0FB234B4D88A884 (void);
extern void PointerEventData_get_pointerEnter_m6CE76D5C0C36C4666CDDE348B57885C52D495A4B (void);
extern void PointerEventData_set_pointerEnter_m2DA660C24CBDE9B83DF2B2D09D9AF0E94A770D17 (void);
extern void PointerEventData_get_lastPress_m46720C62503214A44EE947679A8BA307BC2AEEDC (void);
extern void PointerEventData_set_lastPress_m0B9EDFBA95B410FBD8CA2A82306ED3EA6696AE64 (void);
extern void PointerEventData_get_rawPointerPress_m8B7A6235A116E26EDDBBDB24473BE0F9634C7B71 (void);
extern void PointerEventData_set_rawPointerPress_mEEC4E3C7CD00F1DDCD3DA98DA5837E71BB8455E3 (void);
extern void PointerEventData_get_pointerDrag_m36BF08A32216845A8095C5F74DFE6C9959A11E96 (void);
extern void PointerEventData_set_pointerDrag_m0E8D72362B07962843671C39ADC8F4D5E4915010 (void);
extern void PointerEventData_get_pointerClick_m2AFE23543BC381EC734E85ADB16DD63BA2017FEB (void);
extern void PointerEventData_set_pointerClick_m8FA5D91C9556A722BAE8ADBBB5353C79854D74C0 (void);
extern void PointerEventData_get_pointerCurrentRaycast_m1C6B7D707CEE9C6574DD443289D90102EDC7A2C4 (void);
extern void PointerEventData_set_pointerCurrentRaycast_m52E1E9E89BACACFA6E8F105191654C7E24A98667 (void);
extern void PointerEventData_get_pointerPressRaycast_mEB1B974F5543F78162984E2924EF908E18CE3B5D (void);
extern void PointerEventData_set_pointerPressRaycast_m55CA127474B4CBCA795A9C872B7630AAF766F852 (void);
extern void PointerEventData_get_eligibleForClick_m4B01A1640C694FD7421BDFB19CF763BC85672C8E (void);
extern void PointerEventData_set_eligibleForClick_m360125CB3E348F3CF64C39F163467A842E479C21 (void);
extern void PointerEventData_get_displayIndex_m8B2088561EA850F1CAD124797F0F6E3756F584FA (void);
extern void PointerEventData_set_displayIndex_mCEBCECBC08CCF4D1770189B0992B8A1847A8137D (void);
extern void PointerEventData_get_pointerId_m81DDB468147FE75C1474C9C6C35753BB53A21275 (void);
extern void PointerEventData_set_pointerId_m5B5FF54AB1DE7BD4454022A7C0535C618049BD9B (void);
extern void PointerEventData_get_position_m5BE71C28EB72EFB8435749E4E6E839213AEF458C (void);
extern void PointerEventData_set_position_m66E8DFE693F550372E6B085C6E2F887FDB092FAA (void);
extern void PointerEventData_get_delta_m7DC87C01EAE1D10282C37842ED215FDBFE2C1C5B (void);
extern void PointerEventData_set_delta_mD200AF7CCAEAD92D947091902AF864CB4ACE0F1D (void);
extern void PointerEventData_get_pressPosition_m8A6788DA6BF81481E4EBCBA2ED1838F786EBAE63 (void);
extern void PointerEventData_set_pressPosition_m85544FBAB798DABE70067508294A6C4841A95379 (void);
extern void PointerEventData_get_worldPosition_m296F53ACF7665D00DE12A18E1A91E3FFDEB42101 (void);
extern void PointerEventData_set_worldPosition_m917BBBF297B2D89BB6836985D466A93B863899FA (void);
extern void PointerEventData_get_worldNormal_m5C5939C06E4AAC48C134A59A9C8F03A6D6CD8884 (void);
extern void PointerEventData_set_worldNormal_mA342C6737631C9C902EFDF1F816AF5C6BE6B0EC7 (void);
extern void PointerEventData_get_clickTime_m5ABE0298E8CEF28B6BD7E750E940756CD78AB96E (void);
extern void PointerEventData_set_clickTime_m93D27EB35F490AC9100369A23002F09148F85996 (void);
extern void PointerEventData_get_clickCount_m3977011C09DB9F904B1AAC3708B821B8D6AC0F9F (void);
extern void PointerEventData_set_clickCount_m0A87C2C367987492F310786DC9C3DF1616EA4D49 (void);
extern void PointerEventData_get_scrollDelta_m38C419C3E84811D17D1A42973AF7B3A457B316EA (void);
extern void PointerEventData_set_scrollDelta_m58007CAE9A9B333B82C36B9E5431FBD926CB556C (void);
extern void PointerEventData_get_useDragThreshold_m3ED1F39E71630C9AB1F340C92F8FA39AA489E1C5 (void);
extern void PointerEventData_set_useDragThreshold_m63FE2034E4B240F1A0A902B1EB893B3DBA2D848B (void);
extern void PointerEventData_get_dragging_mE0AD837F228E3830D4A74657AD3D47F53F6C87E9 (void);
extern void PointerEventData_set_dragging_m43982B3F95F05986F40A736914CFBC45D2A9BB8E (void);
extern void PointerEventData_get_button_mA8CBDAF2E16927E6952BC60040D56630BCC95B0B (void);
extern void PointerEventData_set_button_m77DA0291BA43CB813FE83752D826AF3982C81601 (void);
extern void PointerEventData_get_pressure_m0745482FB0BD942F9615009C647765E3000F12C3 (void);
extern void PointerEventData_set_pressure_m4471D0EEC22789490EA12FE6521A620CF60A37CA (void);
extern void PointerEventData_get_tangentialPressure_m76ED73E8545F01660D6196DCEBAA6C63DDDE374C (void);
extern void PointerEventData_set_tangentialPressure_m66792087B044033F0FF0FA4B2BA316233755EEF4 (void);
extern void PointerEventData_get_altitudeAngle_m3D72F9EF9FF2238B1FE2E6B5870F8B0DD14B90FE (void);
extern void PointerEventData_set_altitudeAngle_m20F2AF2ADB0A20BF20C4B9A6AFE2566A0F4C8BD1 (void);
extern void PointerEventData_get_azimuthAngle_mBFF5F23355EEAB911D8FF55965CCFF9CB3DD3F42 (void);
extern void PointerEventData_set_azimuthAngle_mBE64BAD91A9A47E9D9163E25E9E0D1E677B0FC1B (void);
extern void PointerEventData_get_twist_m15A76D34614115A290B8FA90799752FBE00580B7 (void);
extern void PointerEventData_set_twist_mE49469F4F730BA43906F2167E7ADDB9CB2F946E4 (void);
extern void PointerEventData_get_tilt_m9F3341B8386EF98ECB4AA1F104DE90387DE25AF9 (void);
extern void PointerEventData_set_tilt_m51D7B90D6C480D7C39217BCBADBBE544D722B034 (void);
extern void PointerEventData_get_penStatus_mFDF49B3339E3F3A01407BE25CA2B3DF2F0E10996 (void);
extern void PointerEventData_set_penStatus_m875854DF53437651CADFA190BCE3ED14FF4D65BD (void);
extern void PointerEventData_get_radius_mA89C671E5F8CA0D0684113CF05E7FAF2961BF7D0 (void);
extern void PointerEventData_set_radius_mB2F29A6E8A14D1DE1162ECAB3398B539FEF83ABE (void);
extern void PointerEventData_get_radiusVariance_m5A3BC7FD6B455570A6535911E0F72F88B0F598BB (void);
extern void PointerEventData_set_radiusVariance_m62367BD7EE689AFF5BB5394D984E4AF026A2D15E (void);
extern void PointerEventData_get_fullyExited_m8A648782FBCC4F948B2D6DEC3B35AFF59A7C794C (void);
extern void PointerEventData_set_fullyExited_mDC23BED1E8A933E25E955A25109494A5D9F25C74 (void);
extern void PointerEventData_get_reentered_m8B88B2F3A8C9FBBE878B458560F5BFF2D7DD142B (void);
extern void PointerEventData_set_reentered_mE363C3D307806C3FF87DF730C14E82AF68A96D8A (void);
extern void PointerEventData__ctor_m63837790B68893F0022CCEFEF26ADD55A975F71C (void);
extern void PointerEventData_IsPointerMoving_m281B3698E618D116F3D1E7473BADFAE5B67C834E (void);
extern void PointerEventData_IsScrolling_mFB78E050A248CDF5221482334808B82500D0A564 (void);
extern void PointerEventData_get_enterEventCamera_m2EBF9CB2E5C1B169F6B6BB066C9CF5B99A7476CF (void);
extern void PointerEventData_get_pressEventCamera_m8D6A377D5CA730307D9F8ABB8656FFB8FCD56AE3 (void);
extern void PointerEventData_get_pointerPress_mEE815DDB67E40AA587090BCCE0E3CABA6405C50A (void);
extern void PointerEventData_set_pointerPress_m51241AAA6E5F87ADEBBB8DB7D4452CE45200BEE8 (void);
extern void PointerEventData_ToString_m49B5681669B6866A981884B774BC48E87D64B48D (void);
extern void EventSystem_get_current_mC87C69FB418563DC2A571A10E2F9DB59A6785016 (void);
extern void EventSystem_set_current_mCAAA4D0C90542AF31D363CC4ACE4D615D5D28233 (void);
extern void EventSystem_get_sendNavigationEvents_m8BA21E58E633B2C5B477E49DAABAD3C97A8158AF (void);
extern void EventSystem_set_sendNavigationEvents_m9309FBEDCBAA85162A202AADF3FDBB7A47D52D30 (void);
extern void EventSystem_get_pixelDragThreshold_m2F7B0D1B5ACC63EB507FD7CCFE74F2B2804FF2E3 (void);
extern void EventSystem_set_pixelDragThreshold_m2D2A087B9A9992D7B624CDB98A6E30BE9D10EF63 (void);
extern void EventSystem_get_currentInputModule_m30559FCECCCE1AAD97D801968B8BD1C483FBF7AC (void);
extern void EventSystem_get_firstSelectedGameObject_m15FB056EE7A99D8DD5891D40A6E3EF18719F0553 (void);
extern void EventSystem_set_firstSelectedGameObject_m626D151EC4AC93DE63E18689FDC13A03DCFB5AAE (void);
extern void EventSystem_get_currentSelectedGameObject_mD606FFACF3E72755298A523CBB709535CF08C98A (void);
extern void EventSystem_get_lastSelectedGameObject_m494BAB623DA90318F7B37C2FFEAD1D8E17FBE735 (void);
extern void EventSystem_get_isFocused_mB0BB5BE03F7203A06D2F351ACD28BA177079104A (void);
extern void EventSystem__ctor_mEEF6F5A0BCA90CC9AD827AA3F2522783B71C6E50 (void);
extern void EventSystem_UpdateModules_m2D91F02D546D50094DDB25BF0228A987E2EAFF91 (void);
extern void EventSystem_get_alreadySelecting_m3DB9F620A5E2976EBF1362F95C05C12031BACCC4 (void);
extern void EventSystem_SetSelectedGameObject_m9675415B7B3FE13B35E2CCB220F0C8AF04ECA173 (void);
extern void EventSystem_get_baseEventDataCache_mF9AFC01C9D2B055F0816F6EEA2CC0011F1D82B7F (void);
extern void EventSystem_SetSelectedGameObject_m91382EAC4D552C672CC07BE7EB1481F156045280 (void);
extern void EventSystem_RaycastComparer_mBF2582FBEDA9A1B604EE4281C61CB5E3DF676795 (void);
extern void EventSystem_RaycastAll_mE93CC75909438D20D17A0EF98348A064FBFEA528 (void);
extern void EventSystem_IsPointerOverGameObject_mC89BFEA46B0DA67F914B9B90356E63BFBE11EB38 (void);
extern void EventSystem_IsPointerOverGameObject_m238732B4FDEA343976D798FF04DB34C3221243C2 (void);
extern void EventSystem_get_isUIToolkitActiveEventSystem_m6FF1DA7E38D73742C5AEBF93C611723B9CC93FDE (void);
extern void EventSystem_get_sendUIToolkitEvents_m7E11CCC27DFE797BC4DFAEAE2D1C94BF845B08C9 (void);
extern void EventSystem_get_createUIToolkitPanelGameObjectsOnStart_mD617E1B0EA52D750421DE03A8F131CF2F5831712 (void);
extern void EventSystem_SetUITookitEventSystemOverride_m31B7776BD35EFB75371E2B860CF6E34FCDCD6A59 (void);
extern void EventSystem_StartTrackingUIToolkitPanels_m4207099C99B738BE1B513CCB34ABA08AE57BD232 (void);
extern void EventSystem_StopTrackingUIToolkitPanels_mB4F11691245874E390D2F76F72502904F08070E5 (void);
extern void EventSystem_CreateUIToolkitPanelGameObject_mFE582264FE41E29CA6BBCCA384E1B238671D3B4B (void);
extern void EventSystem_Start_m392BF40F247855AA4D87C74F2CB5F9AC175F5556 (void);
extern void EventSystem_OnEnable_m4A1E4BD3E26E6DD1150AF17B8A4E14DA9FDA2D9C (void);
extern void EventSystem_OnDisable_m7667186DBAD79874E4B7CE04A5F0291C35FBE240 (void);
extern void EventSystem_TickModules_mD3F159C0C33396BEB5789B633065005DE771028C (void);
extern void EventSystem_OnApplicationFocus_m85C0A5CBBCEC8D900365BDD4F3E3188ED0EE8DC9 (void);
extern void EventSystem_Update_m9D0AC1A7236F0DA1CCA0A8FFE0D8D33D960D433C (void);
extern void EventSystem_ChangeEventModule_m18F27ADCD2CF6656D771CB0413B7B4D768D38181 (void);
extern void EventSystem_ToString_m0C3906BF8A1C2D7BCC31B09224890BC89B2AF35B (void);
extern void EventSystem__cctor_mE933C88969E443D3DEE106C6E747F97F40D3B48F (void);
extern void U3CU3Ec__DisplayClass56_0__ctor_m180E6793CA9B0603F1D4ED924CDC89823C6D5ECD (void);
extern void U3CU3Ec__DisplayClass56_0_U3CCreateUIToolkitPanelGameObjectU3Eb__0_mF0E9A748CB11B0F45553197CA9F1F6FCA0B0E439 (void);
extern void EventTrigger_get_delegates_m0EF6EB8D0AB4964C9AB563D74387B1D5366B9004 (void);
extern void EventTrigger_set_delegates_m47AE262A9A8E4F2F2824F2C877597DC4CE2A979A (void);
extern void EventTrigger__ctor_m2A471D4099280D37183A1B668FF092B9517BA294 (void);
extern void EventTrigger_get_triggers_m2361511923086BCD40339097448A70AFB22C4647 (void);
extern void EventTrigger_set_triggers_m5F861F79BBA48C26CFB83BEA7E25580B21BDA815 (void);
extern void EventTrigger_Execute_m8F637065284AB93B0D2C1090C63830AFD9CE25BE (void);
extern void EventTrigger_OnPointerEnter_m78A0620B719E345A02F2A628EBC1D08ADAA5FD89 (void);
extern void EventTrigger_OnPointerExit_mF15D24467BCC9686CD9DC11C728632F7ED098BF4 (void);
extern void EventTrigger_OnDrag_mD4E2457101987E2E96C251EDBBAD8960BED20874 (void);
extern void EventTrigger_OnDrop_mF111804E0134C1C873156D4B22E8479CDDEC0C1B (void);
extern void EventTrigger_OnPointerDown_m91957FC65D1AE1C5FD6B0548682DEE1B4283ECC0 (void);
extern void EventTrigger_OnPointerUp_m63A37DEC73942B6C5863F79DED7A2BCDEF8B8DB6 (void);
extern void EventTrigger_OnPointerClick_m6006A8F9138007DF16AEA63968E865D8A2AF128E (void);
extern void EventTrigger_OnSelect_m191AD9E9C686FABEEF036AAC0D89F27D7BACC8E4 (void);
extern void EventTrigger_OnDeselect_m647B65049C6C332711233F0B2F72C99E4AE2DE46 (void);
extern void EventTrigger_OnScroll_m45ED61FD7F6FAD70C71502A38D2479DEE50B1370 (void);
extern void EventTrigger_OnMove_mEE11502B46693D5F5C8E23380E0DF0D4B75EE9CF (void);
extern void EventTrigger_OnUpdateSelected_mF5C79E9494D1F1F3D032FFB17B1D5B3701FB5BD7 (void);
extern void EventTrigger_OnInitializePotentialDrag_mF57EC5149D92811696046CACFA2CD4422890AE78 (void);
extern void EventTrigger_OnBeginDrag_m370891394066DA8891BFA458D335A4B878988E7B (void);
extern void EventTrigger_OnEndDrag_mFC87A35C4060855401A4C0C28612829D0894A8A8 (void);
extern void EventTrigger_OnSubmit_m2EC7EAB0AAD5AD0528511C8184A430FD91E95E0D (void);
extern void EventTrigger_OnCancel_m17724FAA28975B06DDDE55D06716DE33A1788144 (void);
extern void TriggerEvent__ctor_mBC60D36344FFB96FBE826D229CE25D4C25E08440 (void);
extern void Entry__ctor_m7325965EB4BD264BE16F837B6AA2693ECEDBB5E8 (void);
extern void ExecuteEvents_Execute_m316D0EE5A1936BFFD9999F4C145722DC6C121FF7 (void);
extern void ExecuteEvents_Execute_m554281680E2DBC534055073ECCE46230E488A3E6 (void);
extern void ExecuteEvents_Execute_m3DD6C7687A440E55EEF8B7D115DEF950728295B6 (void);
extern void ExecuteEvents_Execute_m36FF8B992CDB75A825077B7A52AA7BE72318B37F (void);
extern void ExecuteEvents_Execute_mD0811E5B0A4F7D5A88E7ACF0A845CA107485F579 (void);
extern void ExecuteEvents_Execute_m512ACDD06180A73819570FED3C2BEE0F0E2DA3F2 (void);
extern void ExecuteEvents_Execute_mCD88FE48772FC6DA5E9FE9CAF910402F63090C35 (void);
extern void ExecuteEvents_Execute_m3F3FEE80AD62CF4207EDA55D6998B98DFF8FFB64 (void);
extern void ExecuteEvents_Execute_mBEB42D218E11F4B9834CAC70894631C305E6AF18 (void);
extern void ExecuteEvents_Execute_m1A35D0185316601E2CE063420F4953C8D3D62D3A (void);
extern void ExecuteEvents_Execute_m6DD01624C34CF22057ECF1B0C7E561006DA6D2F3 (void);
extern void ExecuteEvents_Execute_m08AB6D464ED66E7D539C957D84076F79D8ED5563 (void);
extern void ExecuteEvents_Execute_mF1F3202132B706B56AE43B19577758BCA4EAEB88 (void);
extern void ExecuteEvents_Execute_mBDDBAF4DEB956C013CD19E514088B6AC086783B2 (void);
extern void ExecuteEvents_Execute_m070554B8CD391A49E8A51A4FC10C8CB8827E5627 (void);
extern void ExecuteEvents_Execute_mBC94A654B65C6B14834E3CD0FF0472DB5445E2F2 (void);
extern void ExecuteEvents_Execute_m6CE7DBF76F4858C3014295BB2EBBAD768EF5992E (void);
extern void ExecuteEvents_Execute_m30F76D861B01F5DE4671B93C23B57989889EC8AC (void);
extern void ExecuteEvents_get_pointerMoveHandler_m996E37A7026F03F8791EFBB69B72DE1FC4FA3A60 (void);
extern void ExecuteEvents_get_pointerEnterHandler_m9F921E3357CE38A925DF20E9CD94B4C3AEE9AE48 (void);
extern void ExecuteEvents_get_pointerExitHandler_m03735363884BC967C1B04246B51FE98886C9C6DE (void);
extern void ExecuteEvents_get_pointerDownHandler_mA67CE33D32540939A273DB88D6456C7FE43C13EF (void);
extern void ExecuteEvents_get_pointerUpHandler_m51B83B4AD7498D6F7A2CBD5F2331E91A37AE4CF2 (void);
extern void ExecuteEvents_get_pointerClickHandler_m0017F9D1EAF7C02CDDB4C148C92D6685D88EA8D5 (void);
extern void ExecuteEvents_get_initializePotentialDrag_m16F4CD40448FB1B78F6683AA26A9CC574F48AFBD (void);
extern void ExecuteEvents_get_beginDragHandler_mB0BEAC09E4A8F31438B07FE68A5BF7267FF8C2FC (void);
extern void ExecuteEvents_get_dragHandler_m9193926B9685C80C0560C2E105FF6150C4EAE507 (void);
extern void ExecuteEvents_get_endDragHandler_mE78FEEEAE114635E416FF1FE33C851E62B60555B (void);
extern void ExecuteEvents_get_dropHandler_m8E00FA7361DE68780ACEB365E6B14206A2180D03 (void);
extern void ExecuteEvents_get_scrollHandler_m51E902070611D3F81AD5F1F5762AE2C48E84AFE2 (void);
extern void ExecuteEvents_get_updateSelectedHandler_m8AF7543437082AD4F5690AAA77F2623AE28C3D09 (void);
extern void ExecuteEvents_get_selectHandler_mCF588328D11715E24BC54DF464EF4F3D694B3939 (void);
extern void ExecuteEvents_get_deselectHandler_m9CDD5F8B5269067CA38247DDD41B521A8FCEDFEF (void);
extern void ExecuteEvents_get_moveHandler_mF717824AB0018BBED3C2DF3C67486E3B2B3836D2 (void);
extern void ExecuteEvents_get_submitHandler_mDCAAA40F0F6AEA385B375C1839B4DC37E5FC4A7A (void);
extern void ExecuteEvents_get_cancelHandler_mBCDFD10C95FC2BBC5DC5A512FEA1BBEECC2DAE12 (void);
extern void ExecuteEvents_GetEventChain_m4FAD69B97B2D0AADFCA3AE06CA80E680B60632ED (void);
extern void ExecuteEvents__cctor_mFF9D727E7E8EEEE34D6861D20720411F1CACE9C5 (void);
extern void BaseInput_get_compositionString_m2F5F37C4A3E1CBFB779113D038802AC2BA3E556E (void);
extern void BaseInput_get_imeCompositionMode_m61F7F9CF12191DE6328F900458CB1886AEDA2B08 (void);
extern void BaseInput_set_imeCompositionMode_m10C9A03CA5BE2DD16C356603A1D03EE197B29085 (void);
extern void BaseInput_get_compositionCursorPos_m40F062B6C5FB3667DE689AA06AA0CA5DD52DBF7C (void);
extern void BaseInput_set_compositionCursorPos_m186F1B453469AC2FEE13F9B2144B1A59D4D7519E (void);
extern void BaseInput_get_mousePresent_mCB00324CF55402907B52C63213CF397E53D03E71 (void);
extern void BaseInput_GetMouseButtonDown_m6276D605B7C48000F3D61BF56BE9E3B5F86398AF (void);
extern void BaseInput_GetMouseButtonUp_mBB470F97111BB7BCC1B543CD282F898C9033DAE4 (void);
extern void BaseInput_GetMouseButton_mD6EAA726BE691C40052FEDBFF51AEAA1DACAAB57 (void);
extern void BaseInput_get_mousePosition_mD75C96534C8B4EFF48A732F4826E6C9E09CB4540 (void);
extern void BaseInput_get_mouseScrollDelta_mD764FCD7B05C6505AAB3161C3011A5EA51DEDC39 (void);
extern void BaseInput_get_mouseScrollDeltaPerTick_m0FB0F8CF77BB9FAB531E0174690A74416DA20C4B (void);
extern void BaseInput_get_touchSupported_mA46B82A5BCB1A1BE47FB15BD4ACD522694DBDC1C (void);
extern void BaseInput_get_touchCount_mCD103B50B46B7938389AE38F81C34F173B9F94FD (void);
extern void BaseInput_GetTouch_m1FA092A2BD2276B8F94A3058B3D8A9E301DDCBE6 (void);
extern void BaseInput_GetAxisRaw_m817F925099FC5D22086D616249C0CB5C7F21445B (void);
extern void BaseInput_GetButtonDown_m3B34561DB7A1E129F880B1E09CE8B844B1FF6FBC (void);
extern void BaseInput__ctor_m7E497239A0A78CCAC04BE6EE9AA81D49D887787C (void);
extern void BaseInputModule_get_sendPointerHoverToParent_m3C22EF19BA6E95672ACD15F7E3FCD11277EBBF47 (void);
extern void BaseInputModule_set_sendPointerHoverToParent_m1E97FE3C9AEECB53AFAB89429D6ABCFE7A9F1882 (void);
extern void BaseInputModule_get_input_mCB3F78528AA14A7AD7E957870DBB0152B4BF13FB (void);
extern void BaseInputModule_get_inputOverride_m92D66E309898C180BF887F6043CCE7AE63C6C44C (void);
extern void BaseInputModule_set_inputOverride_m876BAC421A4BC40FB5873FC386E361C4CFA53987 (void);
extern void BaseInputModule_get_eventSystem_m341B2378F61A58D5432906B9EE1E12265E2FAB33 (void);
extern void BaseInputModule_OnEnable_m2F440F226F94D4D79905CD403F08C3AEEE99D965 (void);
extern void BaseInputModule_OnDisable_m370643AD83FFAD10B9E67301355F63B4FF7FB389 (void);
extern void BaseInputModule_FindFirstRaycast_mE07BDA14A7C9A8E3DFBFDAF449E5896597C9F6F5 (void);
extern void BaseInputModule_DetermineMoveDirection_m8C99256812C74890B4F54BBCA5BE424A7D608E15 (void);
extern void BaseInputModule_DetermineMoveDirection_m6461EE20A0418E30EFA13CD293A2B0E7A95DBA54 (void);
extern void BaseInputModule_FindCommonRoot_mBCC0541CA6E2BCFF051B90FE34F4F00C28CDFA10 (void);
extern void BaseInputModule_HandlePointerExitAndEnter_m0815F06EAF8F937916E0656D66A69844CB211298 (void);
extern void BaseInputModule_GetAxisEventData_m99FD46006BB2D8FD6D1E10F606886FE017955293 (void);
extern void BaseInputModule_GetBaseEventData_mF750E3A63EC1080B933A2FA2CC21D683A68ED433 (void);
extern void BaseInputModule_IsPointerOverGameObject_m1406F19FE6A3CAEECB2238427345E4CA32E1AD6F (void);
extern void BaseInputModule_ShouldActivateModule_m51B70F9097EF7FEB20B62D4D775F7FEA853185A1 (void);
extern void BaseInputModule_DeactivateModule_mAE698307DADBE4DE8A26BD3DE5F3F7E3D75B385D (void);
extern void BaseInputModule_ActivateModule_m2C693E95727E13FDF06D54FA8762A040175AC3BA (void);
extern void BaseInputModule_UpdateModule_m201C2C266D80D7451D42E929A90EFC8C4B7358BE (void);
extern void BaseInputModule_IsModuleSupported_m60644A4C84A8B0FA66E204E20D149A0BCFAD27A2 (void);
extern void BaseInputModule_ConvertUIToolkitPointerId_m067C6EDDF29815FE295111E95A38F66860D1E441 (void);
extern void BaseInputModule_ConvertPointerEventScrollDeltaToTicks_m8C079F385CC514242F0713126C048819B7FC2782 (void);
extern void BaseInputModule__ctor_m88DDBBE7BC4BB7170F5F8F00A0C9E2EC6328B819 (void);
extern void PointerInputModule_GetPointerData_m8D1C52EE44136560312932072786A2B5AA2C8BE5 (void);
extern void PointerInputModule_RemovePointerData_m012713A1B4511855549793D6BA2B7998134B1BE9 (void);
extern void PointerInputModule_GetTouchPointerEventData_m55EBA8BD04214AAD8E98B9109D44610496A5B2E1 (void);
extern void PointerInputModule_CopyFromTo_m4302FE47F12B3B8C59A3790BD0ADF2BFAAEA9BFD (void);
extern void PointerInputModule_StateForMouseButton_mED5B48F98F706160F97A26511FB82BD84DBAB0CF (void);
extern void PointerInputModule_GetMousePointerEventData_m77052AB014196BA4E66C8BBE27EC9AF739031EFE (void);
extern void PointerInputModule_GetMousePointerEventData_m8D8111399CF7077AEBE4836AC701DDDF3F5ADFC5 (void);
extern void PointerInputModule_GetLastPointerEventData_m6355023718EB2DCF6D9C226A57B63B70CCEECAB4 (void);
extern void PointerInputModule_ShouldStartDrag_m6260055DEAD5E28183E338BDA53C7F8A0521EC6B (void);
extern void PointerInputModule_ProcessMove_m3555F333D82A446C2354D8855034323BF7C9208A (void);
extern void PointerInputModule_ProcessDrag_m73FE39BFACC950DCA7FDD7BDC67F45484DC01207 (void);
extern void PointerInputModule_IsPointerOverGameObject_mBDCC057426289D69D4C6E1EF7F6849C112171883 (void);
extern void PointerInputModule_ClearSelection_mC5852667E5B9CA97C2A4CAB3D7C907344511C1D2 (void);
extern void PointerInputModule_ToString_m9C5DB37AC45C9F27B017E3B52C5CFE22F91CAF9D (void);
extern void PointerInputModule_DeselectIfSelectionChanged_m8F111DD2317E33C4F0498F651BC52BD5C984A95B (void);
extern void PointerInputModule__ctor_mBF074492478BFC24F87EF2C941D6C11A8ACDAF11 (void);
extern void ButtonState_get_eventData_m4767730784143F1DCE06B6138658A31CBC5E155F (void);
extern void ButtonState_set_eventData_mA9D59CB9A1565A7D99569E87D88B90738FEF4E1F (void);
extern void ButtonState_get_button_m2210A465432D0F990F2380B6357AD2FBA4A7540D (void);
extern void ButtonState_set_button_mFAE3F16E2B027BD6B854F18E7C7C2D6CDAB023DE (void);
extern void ButtonState__ctor_m4D7C25C0E1FC598646FFBD436B9A2042DB41AC9E (void);
extern void MouseState_AnyPressesThisFrame_m4887FF61D58D641496B95C83710C8A4E841970F3 (void);
extern void MouseState_AnyReleasesThisFrame_m4FBA37A12735418AD0970F11BC44850517B05E93 (void);
extern void MouseState_GetButtonState_mD25E7D214B0499DBBE3B3E532CD7085C1A021E51 (void);
extern void MouseState_SetButtonState_m72DA468C8D10E76923FA5F993BBDBCFFF57E4326 (void);
extern void MouseState__ctor_mF4A8041A86E50D91202770E73CE0DAF12BB207D9 (void);
extern void MouseButtonEventData_PressedThisFrame_mEE5DC95537AAEB346C57DCA85917E0701A44388D (void);
extern void MouseButtonEventData_ReleasedThisFrame_m5AD4F06D1CA6E0ACD6E84EEFAD4FB112098AFD51 (void);
extern void MouseButtonEventData__ctor_m9EDAC7F39F1D3CFBB93403DDE620A5147C4469A2 (void);
extern void StandaloneInputModule__ctor_m77BAC1DB71B81FFCD2791DE706BD4FE239F47C27 (void);
extern void StandaloneInputModule_get_inputMode_m38D63EDD9DE39E7AFE1821BDE201625C292C66D9 (void);
extern void StandaloneInputModule_get_allowActivationOnMobileDevice_m03E7DC8FCBE7B43A223EADABB454445C91664A1B (void);
extern void StandaloneInputModule_set_allowActivationOnMobileDevice_mFFFF3E19FBD199ED9BFAEC535E5BD11F5027FF25 (void);
extern void StandaloneInputModule_get_forceModuleActive_m381A5525E48FD280EB91ECEEEF138E7603C004B8 (void);
extern void StandaloneInputModule_set_forceModuleActive_m4C3FD8550258266795D24863D8B531F2402500DD (void);
extern void StandaloneInputModule_get_inputActionsPerSecond_m584ABC794A3864BF91EEB27E62ED6E8081DEE0A5 (void);
extern void StandaloneInputModule_set_inputActionsPerSecond_mF367AFA55FF576533999F2DFB60514D7247228FF (void);
extern void StandaloneInputModule_get_repeatDelay_mDB85393BD9AA45BF8C5B94F5E3A523F5480D1F6F (void);
extern void StandaloneInputModule_set_repeatDelay_m236DB6414CFAE01609187B97E955D95A32F0CB40 (void);
extern void StandaloneInputModule_get_horizontalAxis_mDB47CA6F06F26837BBC4853877F69817590161F0 (void);
extern void StandaloneInputModule_set_horizontalAxis_mF71F2B0B425BD0455AF54F39EEEE43DD80DE27EC (void);
extern void StandaloneInputModule_get_verticalAxis_m5F00ECDA3B18F48BCBD6F9E7B4AD67A1F56CFAC2 (void);
extern void StandaloneInputModule_set_verticalAxis_mB0FE6DC9517F0ABF0107F72FC04A322FD91C2AC0 (void);
extern void StandaloneInputModule_get_submitButton_mAF097B352341EB53A42F71052F3469F205243D40 (void);
extern void StandaloneInputModule_set_submitButton_m571CB829C6D76AD062BA105D0903F08CEA0BCCC7 (void);
extern void StandaloneInputModule_get_cancelButton_mE6F80897FDAA6D931803BF6C3A9E4A45877E5585 (void);
extern void StandaloneInputModule_set_cancelButton_m63A06532F16A21785B3BD5AD3B79509B4441B7EF (void);
extern void StandaloneInputModule_ShouldIgnoreEventsOnNoFocus_m423AA5752E528E3B32F105EE2B341FCF5E9F8285 (void);
extern void StandaloneInputModule_UpdateModule_m8B42D289F6AB3EBE6071FB5B904A7449118EAA6B (void);
extern void StandaloneInputModule_ReleaseMouse_mC5C3083C356ACD5CD420A58662D99A6CACC5FAF5 (void);
extern void StandaloneInputModule_ShouldActivateModule_m6E9E205A37636246BFE65CAC33E1EF703A4D99AF (void);
extern void StandaloneInputModule_ActivateModule_m1F28B8DB0FC20082694221BE5B1B275E9B7224BB (void);
extern void StandaloneInputModule_DeactivateModule_m064C1EB615C0E0B0EDFC6F6F5079E55270DF1468 (void);
extern void StandaloneInputModule_Process_mBD949CC45BBCAB5A0FAF5E24F3BB4C3B22FF3E81 (void);
extern void StandaloneInputModule_ProcessTouchEvents_m042FC6B13874B1EE6699BBB51F02FE3A435A25F0 (void);
extern void StandaloneInputModule_ProcessTouchPress_mD72A0807626DA04E47313F9553249DD4A32625E3 (void);
extern void StandaloneInputModule_SendSubmitEventToSelectedObject_m7121ACC09ABA46FF5CDDEAE7B26507BE06A2953F (void);
extern void StandaloneInputModule_GetRawMoveVector_mFF583B1720780B7D8D2DD3248F947ED8D855610B (void);
extern void StandaloneInputModule_SendMoveEventToSelectedObject_mC71D1E623B1DB82DC4E035277AC5FFA7CD64E981 (void);
extern void StandaloneInputModule_ProcessMouseEvent_mCE1BA96E47D9A4448614CB9DAF5A406754F655DD (void);
extern void StandaloneInputModule_ForceAutoSelect_m8A0FD9795D64CAF49AED184D30FA5C7AB6439C75 (void);
extern void StandaloneInputModule_ProcessMouseEvent_m8A8214EB9286BA31C18F515BCC3349DF740B2BBA (void);
extern void StandaloneInputModule_SendUpdateEventToSelectedObject_mE1C4AEB5B19378C2BF97DD3EAF4AA9C0EC5E7520 (void);
extern void StandaloneInputModule_ProcessMousePress_m24665E707FEF5C80719847D62A8A8AEABE27C6C5 (void);
extern void StandaloneInputModule_GetCurrentFocusedGameObject_m6E67A53E66DE554CCD13A943638815631A3E8E87 (void);
extern void TouchInputModule__ctor_mCBE23B5A3A8CE1CD042A061D7E070B04A45E3075 (void);
extern void TouchInputModule_get_allowActivationOnStandalone_m2F9A65E10F7BE98D250CC3D6C9CABA3BCCE95995 (void);
extern void TouchInputModule_set_allowActivationOnStandalone_m5AB9798D9B6071B37FC67E0D1A81A58A0A59D7AD (void);
extern void TouchInputModule_get_forceModuleActive_m7200F7B6C80EDD69987615B8356B6B2497461FCA (void);
extern void TouchInputModule_set_forceModuleActive_m32676A04010774CA55055EE93A05ED6C6388D2C2 (void);
extern void TouchInputModule_UpdateModule_mE6F4F74D770ACFEF3C46611C71CBE705342EA608 (void);
extern void TouchInputModule_IsModuleSupported_m795B5298EC90DC063A96147CF3B3287EC27C9799 (void);
extern void TouchInputModule_ShouldActivateModule_m4B17231DE579430D27088D6592378FB7547ADBDC (void);
extern void TouchInputModule_UseFakeInput_m66A84384ADA044187F239B1CFC143C42E5E56774 (void);
extern void TouchInputModule_Process_m3C0CB50AB7D9E92B519787F7742AE0976FB36841 (void);
extern void TouchInputModule_FakeTouches_m264BF2C70234CD04B0162FE4CED673B1A77A84F9 (void);
extern void TouchInputModule_ProcessTouchEvents_mBB28D85996F6280141E39A462BC35EF01373E514 (void);
extern void TouchInputModule_ProcessTouchPress_m1DC51E52E6B419F02626EB567A60411A0FCFA517 (void);
extern void TouchInputModule_DeactivateModule_m7CF377DBC376C3EC560523E76514E9F47CCC9DEE (void);
extern void TouchInputModule_ToString_m1AD08DB012D85A33FC0EA3322D5AA5EB98CD1956 (void);
extern void RaycasterManager_AddRaycaster_mD2EE52F55FBDB3BEAAF583C4445D4D16B09B6350 (void);
extern void RaycasterManager_GetRaycasters_m876BA9CA8DB8E6D351DC4A97473753503B7017DE (void);
extern void RaycasterManager_RemoveRaycasters_m0800E0ACE007AD43B62D10C1029EC85E7DE28999 (void);
extern void RaycasterManager__cctor_m06146026A557346F1469D15E855918E746125F90 (void);
extern void BaseRaycaster_get_priority_m79C109ECC138B84A60F9CFA40242628A8B29C838 (void);
extern void BaseRaycaster_get_sortOrderPriority_m4E0BEBF85F720AE4B7C78E99CCD786779C3E7226 (void);
extern void BaseRaycaster_get_renderOrderPriority_m03C407856FF76393AB6EE26FA173131B8F36CA66 (void);
extern void BaseRaycaster_get_rootRaycaster_m63E20D85A8B9867AC196768924F8BE579668BF28 (void);
extern void BaseRaycaster_ToString_m12811CE16AB7E07C949B78CDE309C4B2E44B5377 (void);
extern void BaseRaycaster_OnEnable_m87CCF1ACD4116BB8BC0D9DB427F5B07C6FDE3D96 (void);
extern void BaseRaycaster_OnDisable_mC90A700D5F78DDAD0DD926983C2A8D7C50A5D880 (void);
extern void BaseRaycaster_OnCanvasHierarchyChanged_m20A82CFED659012D1F052C5026B8294B44D99BD7 (void);
extern void BaseRaycaster_OnTransformParentChanged_m121068CDDBC97032FF51C4ED944D4C126CB58F7F (void);
extern void BaseRaycaster__ctor_m1B6A963368E54C1E450BE15FAF1AE082142A1683 (void);
extern void Physics2DRaycaster__ctor_mF2F12F2AF9DDCA74EB09349D038A67F3D3F88CF9 (void);
extern void Physics2DRaycaster_Raycast_m9F6AA9C9DC7A34C01959F2053446D3FFCE567630 (void);
extern void PhysicsRaycaster__ctor_mB7D4BAC26DC219A10060B35498EE9D5F05AD0E80 (void);
extern void PhysicsRaycaster_get_eventCamera_m95026618116D1781A906DDE4AF9C415F2374013C (void);
extern void PhysicsRaycaster_get_depth_mCC2E924588088DB1DCA160765C09734D3C4F7F60 (void);
extern void PhysicsRaycaster_get_finalEventMask_m20AD2327FE81B38A5853B23970A587EAA2ECCB1B (void);
extern void PhysicsRaycaster_get_eventMask_mA8FE3884CD425BD59BD22784F4D5219159426DB9 (void);
extern void PhysicsRaycaster_set_eventMask_mD5110BE565C7E3F1738369519D44587452CA056D (void);
extern void PhysicsRaycaster_get_maxRayIntersections_mA06D0B5E291BCF94AE1EF4ED7B68890F39395911 (void);
extern void PhysicsRaycaster_set_maxRayIntersections_mECCF07932870A3B5C8875AE6204FC1ECB2CE01F7 (void);
extern void PhysicsRaycaster_ComputeRayAndDistance_mCFEFA9D83EC1E63393454E383FFFEF89E14C173B (void);
extern void PhysicsRaycaster_Raycast_mB29925EB33102E9BEAA76658F8A59CA666C78B1A (void);
extern void RaycastHitComparer_Compare_mB5B88FE52375A12B781682C712FE58193F417A03 (void);
extern void RaycastHitComparer__ctor_mAB0536EE515BF2BD1B29BE3B39E8BA9E9CFE97C3 (void);
extern void RaycastHitComparer__cctor_m772D29066D594105261C78CDDB5724BE28A773FA (void);
extern void RaycastResult_get_gameObject_m77014B442B9E2D10F2CC3AEEDC07AA95CDE1E2F1 (void);
extern void RaycastResult_set_gameObject_mCFEB66C0E3F01AC5E55040FE8BEB16E40427BD9E (void);
extern void RaycastResult_get_isValid_m69957B97C041A9E3FAF4ECA82BB8099C9FA171CE (void);
extern void RaycastResult_Clear_m0E9DA70AC69CF143CEA8428AFC5BA552F99643AE (void);
extern void RaycastResult_ToString_m0267000494B09783ABD507B9329ADB01FBBC5428 (void);
extern void UIBehaviour_Awake_mDF9D1A4867C8E730C59A7CAE97709CA9B8F3A0F2 (void);
extern void UIBehaviour_OnEnable_m8989ABF5C038905A68E5536BED2E6FFAF8767FFC (void);
extern void UIBehaviour_Start_mB12643ED6D859CD3682B4BF5B9CA7F72E8A72B45 (void);
extern void UIBehaviour_OnDisable_m18D5A0B93F65FB50F4D6CE8197EC07F3452C5DDE (void);
extern void UIBehaviour_OnDestroy_mF225CF9163823F19BE5E2B2735D35898A20D608B (void);
extern void UIBehaviour_IsActive_m9E79A0650C81204AF9A861BBBA8685D9563AE9C4 (void);
extern void UIBehaviour_OnRectTransformDimensionsChange_m86A6D20E0EBF41CDB89DD1E87F23624263B68159 (void);
extern void UIBehaviour_OnBeforeTransformParentChanged_m980EF41E33A7541BD6C65AEC305B25A19C9CA30F (void);
extern void UIBehaviour_OnTransformParentChanged_mAD56D3C6049A1746F00DC2643D1666F7DE921384 (void);
extern void UIBehaviour_OnDidApplyAnimationProperties_mE011A7C92134E28AE2AF3A0EBFB2E4AB88ABE748 (void);
extern void UIBehaviour_OnCanvasGroupChanged_m592FA8BF5238E712E73E2D99258EE0DB6D88D84B (void);
extern void UIBehaviour_OnCanvasHierarchyChanged_mCAC018CB33FA00E857288B64E3722226638A1229 (void);
extern void UIBehaviour_IsDestroyed_m2E9FFA28686D1C82697FB467002541CC58A06BCA (void);
extern void UIBehaviour__ctor_m24C66A65DDD996E779871C6655CF11B871F11337 (void);
static Il2CppMethodPointer s_methodPointers[1903] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mB1996B7A6E79AE47EC82CFA7894FAD8235AA70D0,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m7B536875EDB6C17E12CAC10ADC0C0D4E57F9112A,
	AnimationTriggers_get_normalTrigger_mDB8A7C5C4B69515C514BB138724E25F16FCED43F,
	AnimationTriggers_set_normalTrigger_m43C30CD216A826221653CF8648B1C1823EBBA8E6,
	AnimationTriggers_get_highlightedTrigger_m492093112EC52907A3B5ED518BAD366B0052EDCA,
	AnimationTriggers_set_highlightedTrigger_mCAD700865DD27254AFCA56FE85E2457ECF4CE6B7,
	AnimationTriggers_get_pressedTrigger_mFB9A652E90CCCB38FCF47EE464D20FACE41399B9,
	AnimationTriggers_set_pressedTrigger_m1AEC4AC3C6CF43AE7EFE2B8E9A7561A5C2F7A5A0,
	AnimationTriggers_get_selectedTrigger_m631840BD0D4ED252C62E11405B29826F9BC419E8,
	AnimationTriggers_set_selectedTrigger_m3AC876A19515E1901E0986781756F339A13E45A9,
	AnimationTriggers_get_disabledTrigger_mC91BD8165E64C82F9DCB7E0549ACB2D0537CE376,
	AnimationTriggers_set_disabledTrigger_m20289A9FDAF524362F32320D57EF4E5A323299C1,
	AnimationTriggers__ctor_mDF3C8571BACB06DACEE75D9E5899B53C1D429A02,
	Button__ctor_m0A1FC265F589989085C1909BC8D93E33A698D8E5,
	Button_get_onClick_m701712A7F7F000CC80D517C4510697E15722C35C,
	Button_set_onClick_m4CD77BD99635400BA18692D591BEA79A7ECC66C3,
	Button_Press_mEF76F32CD5C01C1D8B00B80BDFC0C6CEEEF2C993,
	Button_OnPointerClick_mB76B80D7374811C7BBE11DA188E2656904AE5422,
	Button_OnSubmit_m700E3D529DEE2FB6214BD698C61A3C7CDA08F0A8,
	Button_OnFinishSubmit_m72771C6FF84DA31AE09E936D2D7FB6771FE5A7D9,
	ButtonClickedEvent__ctor_m2B38CD66BDA4E63A19DB233BFA32C828A3D5290D,
	U3COnFinishSubmitU3Ed__9__ctor_m52988E24A7D4CE00B0F8A0F60A6EC20166027553,
	U3COnFinishSubmitU3Ed__9_System_IDisposable_Dispose_m15AFD67ECC7FD7739798F4F063F3978AA19D41EA,
	U3COnFinishSubmitU3Ed__9_MoveNext_m1D344B644399C92B28851F8630337135752BDE2A,
	U3COnFinishSubmitU3Ed__9_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m82819377766A051FA756675E7EDB47088418FD96,
	U3COnFinishSubmitU3Ed__9_System_Collections_IEnumerator_Reset_m3C1D75C11B17DE106053B97EC52822B74591061F,
	U3COnFinishSubmitU3Ed__9_System_Collections_IEnumerator_get_Current_m26114B1DB5CA45B24C00C1B5596D29F76A4AED57,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	CanvasUpdateRegistry__ctor_m2265E2D6AE72DCF25E4A8E52A03A390012CDB01C,
	CanvasUpdateRegistry_get_instance_m57BA2006B09FA35EA4CE228078E7EAF0E01509DE,
	CanvasUpdateRegistry_ObjectValidForUpdate_mFF8ACAA818FA7F73C5A6447C8E1E61631690660A,
	CanvasUpdateRegistry_CleanInvalidItems_mFDBE5D212F6B9649B6EB619AA8860DB72F3AA80E,
	CanvasUpdateRegistry_PerformUpdate_mA5CAEDD4452E5C8B58B688C08BC491AC2DAFD51A,
	CanvasUpdateRegistry_ParentCount_mA10504532DE021BBA642052EDB70F46BAD4A55D2,
	CanvasUpdateRegistry_SortLayoutList_m28074D05490A0F8B9D4E5699BEB357F92212141F,
	CanvasUpdateRegistry_RegisterCanvasElementForLayoutRebuild_mB9571A1C6F0E32E1A0B07C46A1E68366E2A598AB,
	CanvasUpdateRegistry_TryRegisterCanvasElementForLayoutRebuild_mCF214BCE7C81643D9684D93C2CC40FEC599E72EB,
	CanvasUpdateRegistry_InternalRegisterCanvasElementForLayoutRebuild_m609303A2651E3AC6B866A9ACDEAE0773F4CCAAC0,
	CanvasUpdateRegistry_RegisterCanvasElementForGraphicRebuild_mEBBD04C3B001E80801966E3347E70A35FCEBE8B1,
	CanvasUpdateRegistry_TryRegisterCanvasElementForGraphicRebuild_mFA63F8841FECC69E9EC84B9F4D7EAF4B0EBFE375,
	CanvasUpdateRegistry_InternalRegisterCanvasElementForGraphicRebuild_m6CA79E8E3D6217779BF91B50C8D4C9FCF7492B60,
	CanvasUpdateRegistry_UnRegisterCanvasElementForRebuild_m61F9979AB8AFBA924430757FE09967D7A335D916,
	CanvasUpdateRegistry_DisableCanvasElementForRebuild_mC1A68AC220C3755789E3CB51E8DBAC81CC61D62F,
	CanvasUpdateRegistry_InternalUnRegisterCanvasElementForLayoutRebuild_m2DA109CB4DE672A779EB3531D2E727D683E3A00A,
	CanvasUpdateRegistry_InternalUnRegisterCanvasElementForGraphicRebuild_m2A2FAEE11B508953630961D186E379ED890DA589,
	CanvasUpdateRegistry_InternalDisableCanvasElementForLayoutRebuild_m64780D6E94F424BEF771EC84E3F2A8D328B9CB6C,
	CanvasUpdateRegistry_InternalDisableCanvasElementForGraphicRebuild_m6AC0A40EDD8462EEA49F355BFA793AD565DB5D57,
	CanvasUpdateRegistry_IsRebuildingLayout_m3C037968252136E38CF1AF8716DC671CE13917EA,
	CanvasUpdateRegistry_IsRebuildingGraphics_m424672693DCCC18C324436EDD483753B8137A482,
	CanvasUpdateRegistry__cctor_m19922681B4E153B487D8E81BE3A583CACBF32858,
	ColorBlock_get_normalColor_m08A07A74ED743B4B0C1B5A5C35774F2D78F1F20E,
	ColorBlock_set_normalColor_m3EBF594F6FA2C6494ACA9FCB9B458807D85B96F8,
	ColorBlock_get_highlightedColor_m4D1A3D268CB00B351F56934F7F244DBC68855301,
	ColorBlock_set_highlightedColor_m04E97DF2CCE7CAC47120D8F486E18BF62F16FF86,
	ColorBlock_get_pressedColor_m5EDADBA10824D08BFB67D02099A9FC6A4235AC62,
	ColorBlock_set_pressedColor_m644C938090857AB07C57B25FE53F6DC2BB0DD5A8,
	ColorBlock_get_selectedColor_m41CD59090E997A5982EE5AB9D23811FEB35C82CF,
	ColorBlock_set_selectedColor_m76FEFB1148798B7A356C974CDEA3BA2E2E3C1D21,
	ColorBlock_get_disabledColor_m2E20FC772B592ADD71CE1336D29B3C3C1523669E,
	ColorBlock_set_disabledColor_m4D10D1F8525CCC7E8E200E3994AFB28ADABB1D8E,
	ColorBlock_get_colorMultiplier_m54C8F6B7E5ACF45D70F9EAAED78AB4606999C187,
	ColorBlock_set_colorMultiplier_m920A048B95541DB0E92AF4AF3894BE7CD2D37102,
	ColorBlock_get_fadeDuration_m506A0FCC2819DA313BE033640C8FEDC5331D1C39,
	ColorBlock_set_fadeDuration_m8519A304808384CE873377EC104969A6B9FBB6C5,
	ColorBlock__cctor_mE6D6008EFBF7B20ECDFC69AD0FBAAF745BBFEB7A,
	ColorBlock_Equals_m20D958BB28F6FDC12D612279AF6B50679C0C1E67,
	ColorBlock_Equals_m52DCE246EA23904A3EF0FCD3ADAB81BC20DC1BE5,
	ColorBlock_op_Equality_m5925207B6FDD0CE013BEB0269B4407B9C3A54276,
	ColorBlock_op_Inequality_m73B2B54AA18CB45290F99B1A870BC43E08209AC7,
	ColorBlock_GetHashCode_m3CCB4E1E5EE93B905650E24BD4753096082270D7,
	ClipperRegistry__ctor_m664370B6F6A28646681B08A723C4EEE7737599A4,
	ClipperRegistry_get_instance_m709E4407231F3C616FCE693389AE2BC0121FCE40,
	ClipperRegistry_Cull_mE2BBF0B75900B6780EDE22699476542FC5B62730,
	ClipperRegistry_Register_m4C47388806CA8A75538144365809137FB61C965B,
	ClipperRegistry_Unregister_mEEF92721B30BDF97F454512C32D1DF8E24834F42,
	ClipperRegistry_Disable_m4541BB1A762E730709A65D7CDA917CF0D56CA687,
	Clipping_FindCullAndClipWorldRect_mE367B99A2BEBA67F6B394B7E95346C9F6416C4B5,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	RectangularVertexClipper_GetCanvasRect_m9C9A5CAF396D20925E1394FA188E71D3B825B383,
	RectangularVertexClipper__ctor_m159190C771C2A7F406769365B902A228DE585C7A,
	DefaultControls_get_factory_m5E94C746BC7C05A928A9886519FC4FB32C2EB57D,
	DefaultControls_CreateUIElementRoot_m29E6F31D5BEA9AA602BFDFA423DB403DFE429683,
	DefaultControls_CreateUIObject_mBBA2C8A5C8BB80104251200395B2947A5CB45071,
	DefaultControls_SetDefaultTextValues_mE667D7E0A8BB03B6EBA7E3A5917A5F0B5DF1D0BB,
	DefaultControls_SetDefaultColorTransitionValues_mA11DBF31257A877F2C3CFE2FC555F77DE4AB27B5,
	DefaultControls_SetParentAndAlign_mA6632B61C5C5C33B4901129DC16919E8C5158952,
	DefaultControls_SetLayerRecursively_mE0BFC051766E26BC0118555582A6579179281CBA,
	DefaultControls_CreatePanel_m69F2B388CE41646797A582D4AF52CC91B45BB7C0,
	DefaultControls_CreateButton_m3551B134B8B79ADCE1DB85D5A77B4C9F32E43619,
	DefaultControls_CreateText_mA23D7B0D56561FA174752601AEFFEB04F26E7C3E,
	DefaultControls_CreateImage_m0D5C35201D8D12B1A42685CE23772F5C39864331,
	DefaultControls_CreateRawImage_m1CF70C988DE4276C35037BB56479E805EAAB567F,
	DefaultControls_CreateSlider_m46C7D78861271433489674E7B9A09D018E33911C,
	DefaultControls_CreateScrollbar_m78FC29513D3DAF700CB9205882F58F59B3F5620E,
	DefaultControls_CreateToggle_mE5D3F00385DD0F468F8218D520C0C0D300BE58F4,
	DefaultControls_CreateInputField_m0381BFDF0D84EC0A896C639EAB732F39A36B8ED2,
	DefaultControls_CreateDropdown_m94CE7639D609D6341FBCF0D49D6494A5901FFDBD,
	DefaultControls_CreateScrollView_m091D81394C468627D85DDAB8236665E837C89AA7,
	DefaultControls__cctor_m712ED7CCB7CEEE815F424F553E03BC3BA4F6E80B,
	NULL,
	DefaultRuntimeFactory_CreateGameObject_m0D5F91ACE140C39C3139BAF1437792D42CC0C389,
	DefaultRuntimeFactory__ctor_m5467830A8AA017398C392E147A4582857EFD0710,
	DefaultRuntimeFactory__cctor_m09225594AC1F1C4EE5EBF6E5FBC7C8760C34AF2D,
	Dropdown_get_template_m6714116D7DA3F457F184B004785B4F017D50987A,
	Dropdown_set_template_m13FE93E0ED2414A5D8D4595D3123DDFD726DB619,
	Dropdown_get_captionText_m0A8DEACA15F0DDFEE339462E03DF511B87389EF4,
	Dropdown_set_captionText_mD314CF798D1B85726553E124496A7EE226BB1830,
	Dropdown_get_captionImage_mA4C6EF8312F06B6190FC4E78583975145274168B,
	Dropdown_set_captionImage_mF6B9BCAF2C8C0018E2F94600F9C8DE2412F5F184,
	Dropdown_get_itemText_m8E98EB1B2B2F8D5C14F0D4A02E620E9240966681,
	Dropdown_set_itemText_m901981335866C0A856E31D7D1C87C7D8E76FBFAA,
	Dropdown_get_itemImage_mA30A3B51B8C9B6E364B57A3FB277B364B6E0EF1B,
	Dropdown_set_itemImage_m6F4BBC06449E2EAF073D495871BB29F4B35BD7FE,
	Dropdown_get_options_m30F757DBA22980CB77DADB8315207D5B87307816,
	Dropdown_set_options_mEC30A0E3E0819485B1EACF8624D0C1974857D368,
	Dropdown_get_onValueChanged_mAC49CE9A83E258FEC024662127057567275CAC12,
	Dropdown_set_onValueChanged_m59337E2E2975F5F4338C5B03C50347A23343C0E0,
	Dropdown_get_alphaFadeSpeed_m17C37664CEDBA2950ACDA7FCB1DFCBAD1A9C82E9,
	Dropdown_set_alphaFadeSpeed_m67E1A7B551D3592380C6EA34355B94C461790984,
	Dropdown_get_value_m386913162D5E273B762657FE5156DC567602BC3C,
	Dropdown_set_value_m0764A5E2023E34705ADD422689BF6C0074449FEE,
	Dropdown_SetValueWithoutNotify_m3D2B40BC16D305309D68D9FF093BF25FF66E4ABA,
	Dropdown_Set_m2F7DFBF09261A4C4CB1AFCF939907716191D8F07,
	Dropdown__ctor_m1AF791E4615DB8F00045A3713730CD45E66A7CD4,
	Dropdown_Awake_m1A9102FB62C5393F695E5A0FB44A0CFEC5B947FF,
	Dropdown_Start_m93BFFE8C88FF09265315FE8B145FE165467CBB35,
	Dropdown_OnDisable_mB9CBBE366F5F5EDA29C064DB5D7A6EA7C711C70E,
	Dropdown_RefreshShownValue_mA112A95E8653859FC2B6C2D0CC89660D36E8970E,
	Dropdown_AddOptions_mE535B30A30D77024D10AB2AB71CE3FD280CD0327,
	Dropdown_AddOptions_mCFB763400FA1BCA695C168E7FBCDE20C9B8E7839,
	Dropdown_AddOptions_mD4460EE082AB7BE36CB54DBB67BFEB4BC172707E,
	Dropdown_ClearOptions_m3EE71BFE47AB96BC7F731C4EE6BC728ED0E6EE56,
	Dropdown_SetupTemplate_m6F68B1CAC7C39B2C3415B46FD2CF8F91DCF48901,
	NULL,
	Dropdown_OnPointerClick_m3AE64082F83B272B4880935125784442E107939C,
	Dropdown_OnSubmit_m3535A89BE2130B54653DB2A6BA850F2055DA7F6D,
	Dropdown_OnCancel_m50A25AA76B6A92E72820E97C4C9DF2295B45FC2A,
	Dropdown_Show_m103EDF14CFC2B5284C92037B097F015DAB1340DC,
	Dropdown_CreateBlocker_mA27CE256509155DAC14FBB8549074ACFF5976DDB,
	Dropdown_DestroyBlocker_mE0B298F69E3343D0551E7B42B28312EEE28C553B,
	Dropdown_CreateDropdownList_mD6A55CE0786F7A418C6FC001798F660D1D2CFF95,
	Dropdown_DestroyDropdownList_mB8F81B723F9C08AF3D303D8CDB395B4474B1846C,
	Dropdown_CreateItem_m2E8E7B65A324DF3CB783D219F7ADA70E28CD8FAA,
	Dropdown_DestroyItem_mD48C6E656F3CE04FE1C26E1F92F599B1F0EAD778,
	Dropdown_AddItem_m16017A91D142FECFE69FB38FAA311636348B499C,
	Dropdown_AlphaFadeList_mF73F53EB84546666A4DB382173BEFEA23DFD9D64,
	Dropdown_AlphaFadeList_m5727C00B9A1FF385C5A4B65799E1CFAE49F29F86,
	Dropdown_SetAlpha_mE367D2B2798F4F7FC0281D772AB4DC7417A2077C,
	Dropdown_Hide_m49F29E7BC614DB6E04512F762399A9AACCDAFCB7,
	Dropdown_DelayedDestroyDropdownList_m5840A3EACBCDA1F7EB89E36A44EA502243E87F8F,
	Dropdown_ImmediateDestroyDropdownList_mAC289C54114CD256FE7F34B8D62EFDA947C00272,
	Dropdown_OnSelectItem_m17D380C68C04FE4125D32EA8494D8F98442150F9,
	Dropdown__cctor_m4A014C9379610C7598BED3E900FD22040E2F9C2C,
	DropdownItem_get_text_m29C926466BC0BE39A7EA282A627E1F8459C53E0D,
	DropdownItem_set_text_mE5F27F83326429B6056B686682BBC9911546DAA0,
	DropdownItem_get_image_m415346A4FB0E83932E4043D41B0AE837F2C3EE75,
	DropdownItem_set_image_mED01F92D19AA3B5C0CACBCE2D1C9A70AFC7049EA,
	DropdownItem_get_rectTransform_mAFF594D5FE8280F8E4CF8D246654C1EC04C892EB,
	DropdownItem_set_rectTransform_m62744FF037D3E7044EDA139CA6BB6FBB11E1061E,
	DropdownItem_get_toggle_m9E93C07903AF29C0D66C48B217575A65CD4CB471,
	DropdownItem_set_toggle_mD58F68764A433037C8F42483BE4F95973EBA3535,
	DropdownItem_OnPointerEnter_mB9464C1CE0EBF0A4F3A7979B37AEF2283E738A34,
	DropdownItem_OnCancel_mFEA3928E939D387662E21AD7496DD64FF40B9FC7,
	DropdownItem__ctor_mB55660FE9B66C2A5E7E8587450729BB691EDAC03,
	OptionData_get_text_m147C3EFE4B7D157914D2C6CF653B32CE2D987AF1,
	OptionData_set_text_mA6022A455FC38025B0CA97B4E3629DA10FDE259E,
	OptionData_get_image_m4E10E9C1338C69EF43C240AB6866AD99CA63451F,
	OptionData_set_image_mE503B098325797C5AA91F3BD71A182CAFF878C9D,
	OptionData__ctor_m6321993E5D83F3A7E52ADC14C9276508D1129166,
	OptionData__ctor_m0BB22D3B9A2443D8D51CE88AD6B4DAEAF11B59E6,
	OptionData__ctor_m59495D34418035A84F4985F134B7557294689252,
	OptionData__ctor_mFF7263F2503D3F2D1E395450A62CAAB48CA9AFDE,
	OptionDataList_get_options_m0400B4F545E0EF3D00D50B701720B5D2F732A00E,
	OptionDataList_set_options_mE730DD2A6EB4DEE150450E52C0C2869CE4573E1C,
	OptionDataList__ctor_mEDE3FBBEC8C69BAB71DC8A4EEBA4DD92A19D2E6E,
	DropdownEvent__ctor_m40067CAE88519F3B3B9991621A3EA5DC89682145,
	U3CU3Ec__DisplayClass63_0__ctor_mA6669AA99E56F2DEE6C1E1ECB173C7BE4DE1CD64,
	U3CU3Ec__DisplayClass63_0_U3CShowU3Eb__0_m2D40C4419DA54F2340E2A0BE7E7E6BD57113B71C,
	U3CDelayedDestroyDropdownListU3Ed__75__ctor_m80FA88C604962EB1BCF0453E39809E4AD856564F,
	U3CDelayedDestroyDropdownListU3Ed__75_System_IDisposable_Dispose_m86610E303C691865AD8CA51A944E0DD22CD76646,
	U3CDelayedDestroyDropdownListU3Ed__75_MoveNext_m4635FEBE76913C9F4A0D60DF2DEFBABE071481D4,
	U3CDelayedDestroyDropdownListU3Ed__75_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD7843A1E586805C8BA4165718774F1579F775077,
	U3CDelayedDestroyDropdownListU3Ed__75_System_Collections_IEnumerator_Reset_mD5684D5117ECD0043294091F4CBB9EEC17957CC2,
	U3CDelayedDestroyDropdownListU3Ed__75_System_Collections_IEnumerator_get_Current_m746DC8D79704E5D1B78C1DC62CDECC7274409D13,
	FontData_get_defaultFontData_mE91EA0AE923A4988ECEF06F608BA8DE764541B6F,
	FontData_get_font_m449DE7A18F42B85D427608E88BC17B528D974D93,
	FontData_set_font_mDF16F5058F749DA9A80B7203BE1E007A21258089,
	FontData_get_fontSize_m6695DDD7FECD4BAC1147A15D26B7F16B78E2B2D3,
	FontData_set_fontSize_m00594E7340206777E0CF1F038943724B8DA9FD53,
	FontData_get_fontStyle_m7671598F11D5C2AE55AA46D28794C78D0D690EC3,
	FontData_set_fontStyle_m90E8DF52C663489F43FB185780D38A3E99A30C29,
	FontData_get_bestFit_m230FD8F27172E1A020BFDDC2D89932DFD01788FC,
	FontData_set_bestFit_m15B4E1EC2E3AA912718466F0C098BF0C22E7B46B,
	FontData_get_minSize_mD8AD04F4CF85C79BEA14710F3AD85228E3DC2D97,
	FontData_set_minSize_mAAC06D3C29D5210054B3DC3FDE58358648460F91,
	FontData_get_maxSize_mA8FDA877D8D459C0C97F1AE7FD8D5F7C27391872,
	FontData_set_maxSize_m3EC43E7AB5A4C022DE729371D8AACFC7D702B527,
	FontData_get_alignment_mC3C237BFE74D104BE4502D0D6BEF6D400AC509F4,
	FontData_set_alignment_m25795B35CBF298D966B5C9A73A4A58F075C17563,
	FontData_get_alignByGeometry_m193ADE84986D74A91F46B31C1F961BC9D688CDFF,
	FontData_set_alignByGeometry_m580D8D1B9D4396C648C9180BB891DAF561E37A2F,
	FontData_get_richText_m76956F1C2063841C77172F1CB404F3C6C81052A1,
	FontData_set_richText_mB37DCE83CBD25C93A3AA4AA9C0C3A7AE332753DC,
	FontData_get_horizontalOverflow_mEF56759973C6722FDE71032861BC0713628E5EA8,
	FontData_set_horizontalOverflow_m8B75EB2EB0241423F50E31C023729BDBAAA019E1,
	FontData_get_verticalOverflow_m306AE42FED4B302C133CC899B55D92FB86C1ED8F,
	FontData_set_verticalOverflow_m857D9882EC486696EE3898EB5BFFFE04053C9D17,
	FontData_get_lineSpacing_mE9627A4D01D54115F8AE42EC1F12CFBB86FAC5E0,
	FontData_set_lineSpacing_m034F2A307093DCAACE71D610550C3306C1389FB5,
	FontData_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_mCA5C2ADF6B05942D58C400752E8D175DAC008399,
	FontData_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_mAB5158604FD53556402CD7297F9797747088EC6F,
	FontData__ctor_m90225BC9FF97C82F911B775CD3EB54B0C95839C8,
	FontUpdateTracker_TrackText_mE52366E2C5DF0BA4E24F35D7DA246FBF32332007,
	FontUpdateTracker_RebuildForFont_mC9A828F534DBBCE1E70BFF4A5034C7B37F7D65EE,
	FontUpdateTracker_UntrackText_m813D712F66E05727FE0CEFAB4438EE7DF5483738,
	FontUpdateTracker__cctor_m82550106869CBCE1C5D8D7AC9F211AD71DBEE5C7,
	Graphic_get_defaultGraphicMaterial_mC3D98DC8F6E8826633B17BB4AC6E38DF20A74E78,
	Graphic_get_color_mA6639AC2B77A8F1B7F27656B69320E7A0FD4F315,
	Graphic_set_color_mC9B90A387A37946AF295D7BCDA1FBC1DBD2D4865,
	Graphic_get_raycastTarget_mA3E3A3A0C7A12EB550D0BCD5DC68F5A40C6D7844,
	Graphic_set_raycastTarget_mE3D3CBB94E605C13362A592F17420AEAAC771448,
	Graphic_get_raycastPadding_m44CC4DC7030C46D15519AAFA7523E9AD4DC462B7,
	Graphic_set_raycastPadding_m5EBFEDD522BD4E1EC0202FEA1D7A0273E25FD5E5,
	Graphic_get_useLegacyMeshGeneration_m2057231F53432FC95BA40EA485E85F5DAF21F423,
	Graphic_set_useLegacyMeshGeneration_m8069890AE2F389C73D944941BB8462C44EB32EC9,
	Graphic__ctor_m61FAEBEC21F22FE00B8CF39A8498AD31F62C0D6D,
	Graphic_SetAllDirty_mE93D6326AF09CED62858980A38F571F01A567E17,
	Graphic_SetLayoutDirty_m707188E6F05B8977FBA14C6269420EAE045A728B,
	Graphic_SetVerticesDirty_m8DBAF14DE97CB50DC54E768A2C120F8F4B3C647E,
	Graphic_SetMaterialDirty_m19E23BAD2FAF23CEF776F467AA8A453C3320473E,
	Graphic_SetRaycastDirty_m07F00097DD9C6278923A1CC204770A4141F4B400,
	Graphic_OnRectTransformDimensionsChange_m2A42F124936B2F377BE4A07BC9586C38CF15EB74,
	Graphic_OnBeforeTransformParentChanged_mFEE7DB7653CD70C7279F397DFF1A5C9B702B36BE,
	Graphic_OnTransformParentChanged_m5FAC5BEDE05D6969B7F7AD15C0A8C5715129EED7,
	Graphic_get_depth_m16A82C751AE0497941048A3715D48A1066939460,
	Graphic_get_rectTransform_mF4752E8934267D630810E84CE02CDFB81EB1FD6D,
	Graphic_get_canvas_mEA2161DF3BD736541DE41F9B814C4860FEB76419,
	Graphic_CacheCanvas_m3F8A1EE9BE3F17297B5E5B9EA02CCA8AF53E34DD,
	Graphic_get_canvasRenderer_m62AB727277A28728264860232642DA6EC20DEAB1,
	Graphic_get_defaultMaterial_m1F258214F9C1F431922BAA0028374FF6F3F81661,
	Graphic_get_material_m7E92B4A77B5454BCC2507952561F12EA88AB9240,
	Graphic_set_material_m49252B02E3CB2C0A17C1A74783F615E50C8801B5,
	Graphic_get_materialForRendering_m4B0017B2B59D2EF578D32ABFCF84A97A835B6B22,
	Graphic_get_mainTexture_mC38AAAD7BB2E9ED5CD1606FB0BB076CCB5F4B70D,
	Graphic_OnEnable_mD7544FBC0068D0C74181E6E66C7EC167B7C6309E,
	Graphic_OnDisable_m2DF81EFCEA05C2E6605C027DA3ABD79945C94F16,
	Graphic_OnDestroy_mDA1CEBC665EEC946C60519596C396477F2E348D9,
	Graphic_OnCanvasHierarchyChanged_m7062158DC1477AF8A5BC2B07755314ED7A184C5C,
	Graphic_OnCullingChanged_m50D153DBBF9C9F17AE177E6ED6D157D847120621,
	Graphic_Rebuild_mEA8B7052D925874A5C8A3F3733F5027CA946EFAD,
	Graphic_LayoutComplete_m42E63C813BCE631365012B856473439ABD49A726,
	Graphic_GraphicUpdateComplete_m02387ED9D65BF3C90D58BD5D2A9614736ABD7D5F,
	Graphic_UpdateMaterial_m0FE63FE57725F78FA05D9C85F8457B6CA06EF665,
	Graphic_UpdateGeometry_m29DD64EA8C3600E9B5A50DAAA8A79D63B9FC1BE5,
	Graphic_DoMeshGeneration_m9A226AB1660C68B8C2ED56845686600741CF7BB9,
	Graphic_DoLegacyMeshGeneration_m6B3FFD836E8904FE9ED48633DED556CB8CEC0156,
	Graphic_get_workerMesh_m31789D0370C0CCCFC9A160714835CAD44CEEB877,
	Graphic_OnFillVBO_m327876DCE662B10A36B5DD71A891F75599186FE4,
	Graphic_OnPopulateMesh_mA35BBAE4555A20A302AABD4EF1AB4F4C9D565160,
	Graphic_OnPopulateMesh_mDD8F1B97C1AB94FB2C61D82080DE06DBAE2C0EEF,
	Graphic_OnDidApplyAnimationProperties_m75AB831FC70C61BF140CFA69D337C48E8762B1CF,
	Graphic_SetNativeSize_m9D5D0610B602745DA5BED808B20A07214CC18991,
	Graphic_Raycast_mEEE1690786A5894545C42BF6143936237BFE61A0,
	Graphic_PixelAdjustPoint_mBC4AFC26628D498B9872314726561D72F6DD2F28,
	Graphic_GetPixelAdjustedRect_m70D7B527D04C0B88C23E7C6661A8FF1ECC4B4BA1,
	Graphic_CrossFadeColor_m6BF11EA2B9F62DF8D9421292EF974D7D548829C5,
	Graphic_CrossFadeColor_m0D1181CC2BF5CE521C14C85BE9CEB22EC0129D43,
	Graphic_CreateColorFromAlpha_mFFAB1C85CFC981F357FCBFE84DDCFC623E2C804A,
	Graphic_CrossFadeAlpha_mB3D045B48E9DDE6CE23F4368B875F1307765B192,
	Graphic_RegisterDirtyLayoutCallback_m870D9C225888AF117EAB7DCFBC5E629797D22B7E,
	Graphic_UnregisterDirtyLayoutCallback_m2284BC352FE69018BB15978CB3218C673F29AD9B,
	Graphic_RegisterDirtyVerticesCallback_m46034B2100B5D28BDBCCB34C1283B1B9B2DB9A9E,
	Graphic_UnregisterDirtyVerticesCallback_mA36A388BF7DDB2D71596D6F13CEFCA79B4199B5C,
	Graphic_RegisterDirtyMaterialCallback_m5EDBA1E08656A49997538A1C7DE29201FDE0A013,
	Graphic_UnregisterDirtyMaterialCallback_m62B9DB9B9021EC647E1B3D5122FE3693F8FC9BD2,
	Graphic__cctor_mF2A854B88E328E94B0091B2E9ACC67559BFD3514,
	Graphic_UnityEngine_UI_ICanvasElement_get_transform_m171A3F16EAE82D42EF768C3B091DC87174D5E768,
	GraphicRaycaster_get_sortOrderPriority_m0F064AFD3551ABC89DE649D406B032AFA6E3D83F,
	GraphicRaycaster_get_renderOrderPriority_m1E6278AF3B98742F9F5A293DAF89F75B06E7441D,
	GraphicRaycaster_get_ignoreReversedGraphics_mC501DBD2D4BD9594F4A5591AFD76AE307EA6BACE,
	GraphicRaycaster_set_ignoreReversedGraphics_m5CFA68408D296EDCC9230AF7CFB53589BE9F1CCB,
	GraphicRaycaster_get_blockingObjects_m54343002F72E2C27919DDF5F4088934891AC13FF,
	GraphicRaycaster_set_blockingObjects_m0CB3F62ABC27BDB348B09B6CF0E6AB4D42A6FBC7,
	GraphicRaycaster_get_blockingMask_mDD3BC80288E6B12D2480B40788BA3B69D6F863C5,
	GraphicRaycaster_set_blockingMask_mCE08DF88D4D4BFD17358C75DE9F0A8F68DB3BB00,
	GraphicRaycaster__ctor_m863917ADCD9732623EBDF53A0CEDDEEB6EA4C42A,
	GraphicRaycaster_get_canvas_mD4D82F397DA3E82EBA7052E93A20562C2263339F,
	GraphicRaycaster_Raycast_mCBF5513CAA3AB70569DA3BE50DCF8980819A6D7F,
	GraphicRaycaster_get_eventCamera_m2EF53324CF216839FA622884418FA77EFB9B3879,
	GraphicRaycaster_Raycast_m06B8EF9AC17F7B4FBDB687E3A2C748EF575CCFCC,
	GraphicRaycaster__cctor_mCE7CB78EE668443FB78303E46D3D62EE92814FBD,
	U3CU3Ec__cctor_m6A476FFBD4558E7BA60882D6696252685DD826F5,
	U3CU3Ec__ctor_mA1FCF997C2A1BC3278AFD9072B0CA4C4273F8F39,
	U3CU3Ec_U3CRaycastU3Eb__27_0_m81E2CE6D45AE93300AF014EA75EF4A4B2E4C059A,
	GraphicRegistry__ctor_m26893FC7AC6ED439CDD999168C66667E27C0B48F,
	GraphicRegistry_get_instance_mB6879C75347DA916BAECEF49280C8A32375BAC60,
	GraphicRegistry_RegisterGraphicForCanvas_m0C0DEF1D00EE4D074927B2592AA0E39EBBC5C935,
	GraphicRegistry_RegisterRaycastGraphicForCanvas_m10218EBBB9EBD098CB0E4954902E94C1862222A9,
	GraphicRegistry_UnregisterGraphicForCanvas_m31671D141DBAF5B11D8F005E90D6E826362FDC3B,
	GraphicRegistry_UnregisterRaycastGraphicForCanvas_mAB5A50A86219AE4AE5DD135C93AADC22989B5CD4,
	GraphicRegistry_DisableGraphicForCanvas_m9AFAD2245A25194017FDDF31DE9D6F6DD9B7A506,
	GraphicRegistry_DisableRaycastGraphicForCanvas_mA4F6606E0E337C952C61773DD6C6109BE27D2115,
	GraphicRegistry_GetGraphicsForCanvas_m72A429EAD15F1CFA7F84BE394A3ECA1A00BE7087,
	GraphicRegistry_GetRaycastableGraphicsForCanvas_mF0EABC1F1DDCAB05BA144A1C37F1EC0EB606E668,
	GraphicRegistry__cctor_m63428B4F697EE7B38C6A4F6C6C724E3A2B4FEC45,
	NULL,
	Image_get_sprite_mB2AA377708722E100574F6F75BC102513BB3BCB1,
	Image_set_sprite_mC0C248340BA27AAEE56855A3FAFA0D8CA12956DE,
	Image_DisableSpriteOptimizations_m94966D77FEEF830B1B97C44EAF74843EB94E7C25,
	Image_get_overrideSprite_mE3FDFDD768A99DA4F19356E1D3F158A29E7A3C65,
	Image_set_overrideSprite_m05036DA9D0E7A173E3A5D2A2156E8E0A50A7983E,
	Image_get_activeSprite_m0F639A03B26FD25CA1D8EEA006D0B0C322037034,
	Image_get_type_m7CE3AA14B38E1C50AC8362176AE842992DA8C639,
	Image_set_type_mECB8D34772AA393FFBC867B03D18EA0F1A8546BF,
	Image_get_preserveAspect_mCF10199F127659628F58CDC7C91E686816B34B5F,
	Image_set_preserveAspect_mF465AFD1313C0F002B37C8B86C75F98CB72A4098,
	Image_get_fillCenter_m4951647922C5C7B1A0243C9536F8CF5A8FDDDC6E,
	Image_set_fillCenter_m3A5E856A3F877649590F678ED6DDE38B64B14FE4,
	Image_get_fillMethod_mAFB1FAAFA913DB0EE050C4053DBBA6FAAD68A5F1,
	Image_set_fillMethod_m5361D29BA950BEFE72E7270AC3BFA0B00AE7E294,
	Image_get_fillAmount_mDEE52490D07124E21E7CB36718A5E3714D8B9788,
	Image_set_fillAmount_m8A9B55F47F966A3214EAC4ACBFE198776A98FAA7,
	Image_get_fillClockwise_mD18612EBF815BC5C238D1591039BF9F1D28DF2C0,
	Image_set_fillClockwise_mB5DBAFC66370F906EA2CC1D49D49FCC366B64646,
	Image_get_fillOrigin_mC9778E141C67C15EC865F6648E5B2545BCC30389,
	Image_set_fillOrigin_m2D89BA820DABB26123A33059CA266212E7970B4E,
	Image_get_eventAlphaThreshold_m19B026C80DB547E702E22A1053FBD0A1BFF2F51A,
	Image_set_eventAlphaThreshold_m999376263E8A9914F5D69E71B4650D76F283AB6D,
	Image_get_alphaHitTestMinimumThreshold_m5F6F90EEC3D06F719E9C360A6813A49CDD7EC4BA,
	Image_set_alphaHitTestMinimumThreshold_m007F9F1C5FD0331E1EDADF4EEE3CB16F6B43F843,
	Image_get_useSpriteMesh_m3157E0D7DB2F54EA7B13284F53FA9013F316F7F8,
	Image_set_useSpriteMesh_mFA81C2E108CEB33E5F92A9142B2C83B871C3A81B,
	Image__ctor_m8F922348981CDB74700D89D833FE39611FA4BC37,
	Image_get_defaultETC1GraphicMaterial_mCEFD3237CA0090EBED29A81983DC3FE78BAFBAB3,
	Image_get_mainTexture_m16CAAF3A2CBF5B3BBB19AC8BD99CE9187C47D3FD,
	Image_get_hasBorder_m9B09E5452FE8CF13958D7301B01A3A8124ADDDC0,
	Image_get_pixelsPerUnitMultiplier_m2B008CF7C16C195A24FDBC5CC7B34531E18F1A18,
	Image_set_pixelsPerUnitMultiplier_m05DA43C7FD5B7B162FCB1ED6FCA850FD41AF7DC1,
	Image_get_pixelsPerUnit_m319197FFB69E9E8661F46B0DF652F3B3F25D16D5,
	Image_get_multipliedPixelsPerUnit_m6F99237811BE288035A4133833611A446BEE6A8A,
	Image_get_material_m62CEA51BA237569FDB47573CDC125CC3E643A3E7,
	Image_set_material_mC1B5D07666D4CF7C4531F2E8424EB2B62A445D19,
	Image_OnBeforeSerialize_mF6D870DBB1C6826A6AFBD2F23D5181A2BE47994A,
	Image_OnAfterDeserialize_mAD5F5C236B40A266EED00C838164502E253957DD,
	Image_PreserveSpriteAspectRatio_mF56B000B224C2EF11A2EAB4BF465EEA158C5BE1D,
	Image_GetDrawingDimensions_mE33EF5C86703080A13063FAD318B6C114B80CB1B,
	Image_SetNativeSize_mC769A2A62A1F5ED648FC64918182CA40D5518817,
	Image_OnPopulateMesh_m5B662B655BB6DD663AFBF9DF440DF6C6C2EEF9EB,
	Image_TrackSprite_m77BFAC0425F494ED236E393B60E6BD26D5B6A5AA,
	Image_OnEnable_m35B953599A5E65EFEA059E93772D73ACA91BD073,
	Image_OnDisable_m453B2333D529FD5359E1F687BFE2D949207AA58C,
	Image_UpdateMaterial_m3EF2E1AA8D38FAA067FB5AF887B88855EBF1AE9C,
	Image_OnCanvasHierarchyChanged_m3B34FE2B1BDEE8A04854E9C1ADAC49934FC7EDA8,
	Image_GenerateSimpleSprite_m32C9150574E952AE9F5B846AD11A5F0BC8521CC9,
	Image_GenerateSprite_mE58FCD6A8B78A30794664E9DEA81A51C5CF6FD03,
	Image_GenerateSlicedSprite_mE27E793AAF0D0E30BD1B02A12C7FF08566132EF1,
	Image_GenerateTiledSprite_mD6AD2832573EB7AFDDDAC9D31C243AABEA7489B5,
	Image_AddQuad_m53D28C1CA949F7C8B1214D15298BDA5E21AFFD21,
	Image_AddQuad_m39CF7AAE0605E563F3D0C6CE62639E44BCAACA42,
	Image_GetAdjustedBorders_mF3AEDCD9810B2DE6038FF269245899F325366CF6,
	Image_GenerateFilledSprite_m3C13BE8BEBBF021D40B2A6AF6A4170055E621915,
	Image_RadialCut_m0D5FED1F2A3FFE1985A19E8C8AE990EDFA42C2E4,
	Image_RadialCut_m9F8E2FE769EE906D4327A856B6DE9ED73B1AE338,
	Image_CalculateLayoutInputHorizontal_m2B3C913A12F299D2ADBC79DCBC2FD533B24E1EC8,
	Image_CalculateLayoutInputVertical_mA3259ED5830198EF68B2FE1490491D6761C9CAF4,
	Image_get_minWidth_m55A550B01D2E2AA928D77B836B6DDD159EF8B9EA,
	Image_get_preferredWidth_m8AB595CC948924C3C0014873E4C32FC60CA7F27E,
	Image_get_flexibleWidth_m76B50FB439854C2E3850E4D1988029BFCD85EEB5,
	Image_get_minHeight_m40CDD49A5304B1E96FBA3325A9865F16C782CA4F,
	Image_get_preferredHeight_m3A6C0CA2FF3F09FD072ABA13D0553783DD5B0A5E,
	Image_get_flexibleHeight_mF47948629BAA50EC3FC818AD668411A0061EEE6C,
	Image_get_layoutPriority_m1D4FFA04DF71939657E16CDFFC81A5453ECE0C67,
	Image_IsRaycastLocationValid_mB71CF2A446BE3F4C6CF896E8BCA9A36BDF676D21,
	Image_MapCoordinate_m11D428E63DF2AEB1A5866A0AE778E5287F4776FF,
	Image_RebuildImage_m5BDCACEE109C4EF96B8F783BCB71FEA9A72E0E45,
	Image_TrackImage_m24AE9D703DB406780DA6975F648C587CA1F62EDC,
	Image_UnTrackImage_m59DCA4A9F6ABE55046D24006FCC7373FC0717A0C,
	Image_OnDidApplyAnimationProperties_mA079140EDEA8341023066DC950E94F38C61EEE27,
	Image__cctor_m67595BC3057DCFD5A6593929CA673CE415F5803C,
	Image_U3Cset_spriteU3Eg__ResetAlphaHitThresholdIfNeededU7C11_0_m0ECCFF2F9B90F50D42D720113D585420A1AB661D,
	Image_U3Cset_spriteU3Eg__SpriteSupportsAlphaHitTestU7C11_1_mF1454043FC6ED87CE73456C4A45CE2070D6099D3,
	NULL,
	NULL,
	NULL,
	InputField_get_input_m23129FACBD4CDCEA3FC9A977D7DA5BBD4BBB0B2B,
	InputField_get_compositionString_m5E9F323DE7B62EBB69AFC569C05ABC00F619FC4A,
	InputField__ctor_m06B9629E3C878D578A8B43C1A8835B526629D6E5,
	InputField_get_mesh_m89CB1A4155FF8E7C42D5D97178DD00A3A7D8888E,
	InputField_get_cachedInputTextGenerator_m42F16837E9BC49BB43F58163B827C4260303E48E,
	InputField_set_shouldHideMobileInput_mC3759A3E3DED19B9EC01E30CB810922772894C76,
	InputField_get_shouldHideMobileInput_mA752B065435F4062EFB931119C34FDB5B35157E2,
	InputField_set_shouldActivateOnSelect_m5F21C9511D040820CFF661E56145C25D147D17A5,
	InputField_get_shouldActivateOnSelect_m4DA84FAEB2FFB6F036A3821675730842FF86245F,
	InputField_get_text_m6E0796350FF559505E4DF17311803962699D6704,
	InputField_set_text_m28B1C806BBCAC44F3ACCDC3B550509CA0C7D257F,
	InputField_SetTextWithoutNotify_m2CD8DAC2A298CBABFCEC654A17294427DDD238A3,
	InputField_SetText_m66574324D7550D728E41F71DD704CDCDEADF9E66,
	InputField_get_isFocused_m19BD51E842077CA087824025F294C4078B2DAC50,
	InputField_get_caretBlinkRate_m5D6172BA3B84F25897444A1A469AA53FC5CE5613,
	InputField_set_caretBlinkRate_mCE440AA4049C7A1EDEDB63E5B0AE4005563C5226,
	InputField_get_caretWidth_m6D85BF105006F28ABF2940033BEED2E595C89E55,
	InputField_set_caretWidth_mD71B00146099D90D920F4F63A7032E8AEDD39915,
	InputField_get_textComponent_m319EF4B9B24056AF25327874A2455362FF7B7A85,
	InputField_set_textComponent_m09DF6BBF8544028D98D68D3F905AAAE17486D272,
	InputField_get_placeholder_m84C2F2E414B8A03B372C7CEB3C97A2AE72F3A39F,
	InputField_set_placeholder_m64F47B180F584EB1049CF8B501DAC3FCA9029F25,
	InputField_get_caretColor_m92C8BB7D9BD4B4DAE361494F85418F834EE87832,
	InputField_set_caretColor_mF9C606AA2F9F123CB6AD078DF616DE35061FF830,
	InputField_get_customCaretColor_mB1D8A9DE8CD1787B3614BAF3E50E27B2428C7215,
	InputField_set_customCaretColor_m7CA0470187246247EEC354FEB7053E4B4911DC13,
	InputField_get_selectionColor_m988C5ACE38195B9B6397352B5A226FF3867A6E54,
	InputField_set_selectionColor_m2B7800A90FCE0840800CC01EC2C17059634B015E,
	InputField_get_onEndEdit_m92C86FF7CA6108C4B14392CED20C9ED9D39AD9A3,
	InputField_set_onEndEdit_m0AA121171524CB10C4BE4692117839A97E6AAD08,
	InputField_get_onSubmit_m66A3BFEC3D3D5C261558043FD606D4FBCC7D478D,
	InputField_set_onSubmit_m1763F344243E5E3CF28F07872A80AAF809FC1988,
	InputField_get_onValueChange_mF6915B4F33F4B24A91D8E56DE20EFFAE25C59756,
	InputField_set_onValueChange_mA1AEDDDB12CEC499949DB0605A83D8F383212CEA,
	InputField_get_onValueChanged_mA9ABE178FE3EB05AEF3DC20C11349427C59916AE,
	InputField_set_onValueChanged_m2B2F8D1E8F5FE418CE0797F2534B61A1A45B8A85,
	InputField_get_onValidateInput_m370D93274B6040422092981DD3A34E4B88E96EBC,
	InputField_set_onValidateInput_m3A3FA74285B9BBA68325A91AA862201AF3A18CE4,
	InputField_get_characterLimit_m7FE26FC66741545B89BFFDCAD8E8B34EB1274403,
	InputField_set_characterLimit_m98A2187FF493DB170821C39A6D069731F3AFFF2B,
	InputField_get_contentType_m8C589B15987EB8852D5F4948A79084186935B19B,
	InputField_set_contentType_m5C3DDD7C14781E963BFFC88F7A8A537919F34C59,
	InputField_get_lineType_m6CEA63D8FCACAEC05D3499577ED0771EFFF33377,
	InputField_set_lineType_m06BE148366DF8F17E0F91C3CF094628C201B5FD8,
	InputField_get_inputType_mC324081499638BC8AAA45CC110536C016C707BD0,
	InputField_set_inputType_mB2A3B667DC710AD1F9E1C046659AC35720AB0313,
	InputField_get_touchScreenKeyboard_m99338FA7655276193EE1BA8FCB821C7F1928B3D8,
	InputField_get_keyboardType_mCF9432AC88C35E77546235909346C5689682E620,
	InputField_set_keyboardType_m9DD165B20CF12F93BD85140D8D1F54371FF4E9F3,
	InputField_get_characterValidation_m02AD706E70817147BAADD487DAC73D79547BCBBF,
	InputField_set_characterValidation_m9DE08B33714B9D97F570853ADB56C070C2DD4072,
	InputField_get_readOnly_m37800B8623CB744D99E5F5607C80AEBE6C7043B3,
	InputField_set_readOnly_mD70582D7F885929AD7CF28BF083623991C5F543F,
	InputField_get_multiLine_m4AF37C1E2560778A214C50E91C472430D8F777B6,
	InputField_get_asteriskChar_m2556CE9FA8ABF5C00552BA665299F71EAC7D55C5,
	InputField_set_asteriskChar_m26FC4CE6C8637E49ADE854769F6C777A6BEF5CB6,
	InputField_get_wasCanceled_m75E09A773352839E08B04B33F966ED3E849436E9,
	InputField_ClampPos_m8939841884C3CD51A6169F5DA05A85CC3C16A371,
	InputField_get_caretPositionInternal_mF01180C72008CCDD2A5371EE45B84D7745CB6BC0,
	InputField_set_caretPositionInternal_mA35B05D5FF035A060967C6E456610D659367C3EA,
	InputField_get_caretSelectPositionInternal_mBAE2F71F18603A0C4A6AA08F0BFE5831CBBBA461,
	InputField_set_caretSelectPositionInternal_mCA096AAD610587421E739BDD195A1680FD93A75A,
	InputField_get_hasSelection_m3E8EF152E7A7238C8F0631FFC16727800CF16B24,
	InputField_get_caretPosition_mC43674CCFF5BF7D047C2D4682B2CD7DE8A179EA7,
	InputField_set_caretPosition_mF502AA3301C39D4397C7BF809D1F3A18D0603BD7,
	InputField_get_selectionAnchorPosition_mF5CB19025C29DECEA0EBA8C6EC3D6D5687A1D65E,
	InputField_set_selectionAnchorPosition_mE57B85DBF03991E694729ED36283B44A8D7D1E68,
	InputField_get_selectionFocusPosition_m14D662A0A20FF6952E73CFAB7C1F21FD7CF4298A,
	InputField_set_selectionFocusPosition_mE9E0E491C5AC1B89B4F9272EC3B67617A4F7DFEB,
	InputField_Awake_m7253E5687FD0D44982BA34EA523894C0CBE927A6,
	InputField_OnEnable_m00FE61194E553F736B0C1AABC73A79EEDE81D9AF,
	InputField_OnDisable_mA79B9B02E48BE7F1AA6C94C6CECB7A6AB323AB8B,
	InputField_OnDestroy_m551000531722FAD0D2DEB4CA9A76EF25A7067EAA,
	InputField_CaretBlink_m030EE72571B48D2CD7E346D68B0F236C9BB25CB5,
	InputField_SetCaretVisible_m9DB05703AF6B427F53FB4948BB592CF061AA37AB,
	InputField_SetCaretActive_mC91972AACD936D757447E3F7967CE2DAD4B46D0E,
	InputField_UpdateCaretMaterial_mA2C86C0AFC38D35509A3BD66A10411AF7D13FFD4,
	InputField_OnFocus_m5EC2CB19FBDAA84FB317F5ADA86548D78B550F37,
	InputField_SelectAll_mC3A2CAB32B290BC43782A61452760BD127E729EA,
	InputField_MoveTextEnd_m1C20AF9DB90F79CD85C4DAB179DA4EDB4D971810,
	InputField_MoveTextStart_mE56A94C2D4AE751A3BE1035250880D9B592BF130,
	InputField_get_clipboard_m4ACB240747BB6AF77A3FEF28A63A5C2B2A049543,
	InputField_set_clipboard_mA8C4BC1DA5B1C12F8A7E7880E0F74185E2D8BCDB,
	InputField_TouchScreenKeyboardShouldBeUsed_m56104E5B7C58A89C552D4CF8FD7A1B1D93D7340A,
	InputField_InPlaceEditing_m1F71173373CC2A21034D23ECA0060FA4E5A89F11,
	InputField_InPlaceEditingChanged_mE02AC706260B93670AF1380BE4060F3AA4063C47,
	InputField_GetInternalSelection_m35675AC10C34AD29A54A6E980CDA925CB1BFC6F3,
	InputField_UpdateKeyboardCaret_mF290D51F06E5AC063A8FE6FD20F81C5C70792680,
	InputField_UpdateCaretFromKeyboard_mCFB186696BE23B347D7AA94DF50A13555C31F8B4,
	InputField_LateUpdate_mA1C1B81011E3D2F3D6F0769C0FE0D4B0A8E71020,
	InputField_ScreenToLocal_m3ABFAAAC443370A1621926D80EA665CF421CAF9E,
	InputField_GetUnclampedCharacterLineFromPosition_mDD25BDEA1097899537A5D7E8881F23D3D49327DC,
	InputField_GetCharacterIndexFromPosition_m9C0D9CBB43A1CCC47F7B4234379668E46AE3EB32,
	InputField_MayDrag_m72ED9A80A46F59B07697E415E1D691084BC133E6,
	InputField_OnBeginDrag_m3A945C4E07937EDA5E99447572F5F167F1143691,
	InputField_OnDrag_mEF28C06EFB5024C1E236C5A21E715B62CA87BE84,
	InputField_MouseDragOutsideRect_m464392D721204B540DC92E449B48BCB04BCFDABC,
	InputField_OnEndDrag_m0BAA34E5BDBC9A3E241F8BC7DBA8172DD5D9651B,
	InputField_OnPointerDown_m4A3A77DDBA95CB4E50A4BFDF0EDD59B5A9191BF2,
	InputField_KeyPressed_mD6FAC314D8211F43C4C041AE87B3290665A05D28,
	InputField_IsValidChar_mDFF88F1042D52286FDCD5D7302706C837265876D,
	InputField_ProcessEvent_mF905BEF5A4CFF9144159FA40DE2F9DFD4A967358,
	InputField_OnUpdateSelected_m36FFEE16138CDDCA30643962A4C5A41763FE2E55,
	InputField_GetSelectedString_mDF15471A4398D6D7B391105A8549F09DC03DA283,
	InputField_FindtNextWordBegin_m1152E725F12932E30E304F4F10A42B0733201A18,
	InputField_MoveRight_m0D51E23BE4EF55EA54DED277573263BB2A5B1D38,
	InputField_FindtPrevWordBegin_m54E76FA4BF8AE95109D2F78EA0814751837F5AF7,
	InputField_MoveLeft_mD7E3870F7E54009522CF9412764FD5FD9212BBAA,
	InputField_DetermineCharacterLine_mD80BD8A0F49EE45FA6E512796D3A4A15462D97BC,
	InputField_LineUpCharacterPosition_m6E5C0F57795B5CF3D588EFF099A65D90E60848A0,
	InputField_LineDownCharacterPosition_m3212B8EC92092E97AC60D072EFBD385FE72CA829,
	InputField_MoveDown_m365DDF603B2D68FD98B0240F3302886FF7CFF16E,
	InputField_MoveDown_m13622D37FC022939623A9DBC447E49F5D9F43C80,
	InputField_MoveUp_m4703516BEB5B1A3C4020895BABD0558427BE7895,
	InputField_MoveUp_m7F41FF9D5EA2BF64B36C1ACABB67169722C668DD,
	InputField_Delete_m12AD40195316F01879910401E6E0DCEC7F5A8132,
	InputField_ForwardSpace_m4CF251F5CE00CF4918EA0C2D322770A4B556D4E7,
	InputField_Backspace_m4BDCF533ECD04258884076830CB4F0907FCED3E6,
	InputField_Insert_m925B9FADD75785B8FDD886477F0B0CC1E0B4C718,
	InputField_UpdateTouchKeyboardFromEditChanges_m68C429349526101B885D038FFD0C2935151E0772,
	InputField_SendOnValueChangedAndUpdateLabel_mEB064D57921681BB49F55AA796E046A951DAA7BA,
	InputField_SendOnValueChanged_m52131907987E99A872F6007B599345A2ADD244AC,
	InputField_SendOnEndEdit_m79E2689DD75F72FDA8157EECD3F17391D187094B,
	InputField_SendOnSubmit_m933C160291FD9118A9EC7FD7AED5E805B998BA27,
	InputField_Append_m78F45E67DDB94E034940730969D199A971C7D1F1,
	InputField_Append_m22A6348E74FB83932286AC1CDD73322C05BBC63F,
	InputField_UpdateLabel_mDBE25D21A1021AE4563539586438B5EA89511D58,
	InputField_IsSelectionVisible_m2A7FD156812466D2D2397B57959BF91BACC52EB0,
	InputField_GetLineStartPosition_m6ABF6AFB8A9495D7A5446B577EB2ECA8770A9660,
	InputField_GetLineEndPosition_m690864C08F9250D76D718D5D54611C886AAA0A79,
	InputField_SetDrawRangeToContainCaretPosition_m0F3F4E0179627915136B2B2927CD234304E8432C,
	InputField_ForceLabelUpdate_m49441594294B33C5DC10D717198A476B523EE1C8,
	InputField_MarkGeometryAsDirty_m71DCE40033F96C4A842885A7601E3882FF0BD4F4,
	InputField_Rebuild_m4974AB56B494186177AB4BA6C2626BCB0EF93304,
	InputField_LayoutComplete_m7953946E63BF48E14CE1FF13D76FCAA832735C7F,
	InputField_GraphicUpdateComplete_m25B7375B32DC3384EF8684ADDAB6996359668DBF,
	InputField_UpdateGeometry_mABF2E288AF71AF5C8E608F30745D6BAE40A9CB4D,
	InputField_AssignPositioningIfNeeded_m114957547C208AD107279D1B6E8A855D18915E36,
	InputField_OnFillVBO_m84E1576406EFFC37D6EFDDD4604B393E281C5BA2,
	InputField_GenerateCaret_m401461627986E86804E31BE16332003BDCD9EF98,
	InputField_CreateCursorVerts_m2170881250E5F316805946E87EA1F1A794E6AB23,
	InputField_GenerateHighlight_mD1A67441901D78AE29E17A655791754A92EEC072,
	InputField_Validate_mBB63D4E37F8CD96C0F57270259DDE69E3BCB7656,
	InputField_ActivateInputField_m4986DE5488FE44D93DE1D906C140D6500134DF05,
	InputField_ActivateInputFieldInternal_m5B89A6BBCE9D7DD6F0A3DF4B6296533507170119,
	InputField_OnSelect_m723C2F0E81FAFF8264CFE4596CA2AF30B7D9E307,
	InputField_OnPointerClick_mCADA1FE2E0B6EA1F6A9B69DB3790E752243BA4F3,
	InputField_DeactivateInputField_m58D0B3BF095094A0963A9CE8BABF1979F7D1254D,
	InputField_OnDeselect_mA31D1383106BAF91CB638C04E508322FBEB2EFDC,
	InputField_OnSubmit_mFEBD3EF3B76741F19E84A12FBBF9B5BB60E5952C,
	InputField_EnforceContentType_mB8A7743C77E3EAE952426EF14BB5BE5B80E7488A,
	InputField_EnforceTextHOverflow_m7F0E61391D942F47B4AD128C0C8B9B204BBE14B8,
	InputField_SetToCustomIfContentTypeIsNot_m8E1B8AF7133B6B42F9E6BA3951AE2AA4D2AF1071,
	InputField_SetToCustom_m1D8B546B458993E86A24A05B868C57286E8C6BF4,
	InputField_DoStateTransition_m51CFACBDB11404C6F10D0BA3AACB394036CB35A8,
	InputField_CalculateLayoutInputHorizontal_m291256FA87BF5E7F0D7CD64205B58E6B7E88809B,
	InputField_CalculateLayoutInputVertical_m4102477D8FA249BA49FDF9C0CE5F45A42752B083,
	InputField_get_minWidth_mE316201A4474E22FA455CFD381C0A73B76CF5B06,
	InputField_get_preferredWidth_m13ACB831ECB400033C936A46342FF10E8A96D05B,
	InputField_get_flexibleWidth_mCCBC75043CD2BF11B0E38D71A00A5CE790DD9E8C,
	InputField_get_minHeight_mC742ED6E8E46602EE8C085F724AD5442A24DB1D7,
	InputField_get_preferredHeight_m7C3EAA7E8DC12397B9C83A72582C8FC219BA63DA,
	InputField_get_flexibleHeight_mE4CA2B68F90E91C6B884D87FF98D3CA062332A6D,
	InputField_get_layoutPriority_m88277B59E761DA55E6DF1AA803B0DC629ECDFE3C,
	InputField__cctor_m963ABF5968D8C97B8286CD633B0B0B4691ACEBBD,
	InputField_UnityEngine_UI_ICanvasElement_get_transform_m68143981855D6B92BF815F3058EA2F063A63D59A,
	OnValidateInput__ctor_mDC0454BF264F87154EF8694821905B5A6A587A29,
	OnValidateInput_Invoke_m6A7776E0E91552E39F207A90C7E33A4D4479F076,
	OnValidateInput_BeginInvoke_m2A003B257BC355C137B83FB37B3D9DD34821F9D2,
	OnValidateInput_EndInvoke_mE13A5F2C1260AD59F20BFBD7CCC9BE42F84EF6DA,
	SubmitEvent__ctor_mE8908589516FD77AA786BDACC7BEBC2182A87EE3,
	EndEditEvent__ctor_mEAA90FD69A3F6F34EF977AF11A424CEEFF441953,
	OnChangeEvent__ctor_m3D387EF9F415EC6E177649A23DAA137AB98F3E05,
	U3CCaretBlinkU3Ed__172__ctor_mD71554D61758324CCBD8F37F5CE8249169AA88F6,
	U3CCaretBlinkU3Ed__172_System_IDisposable_Dispose_m4B3174F229D803FBEC9FE749FE1A76E0A17A7AF1,
	U3CCaretBlinkU3Ed__172_MoveNext_m725171803230FB9AB7A1FD06EA915CE483335D82,
	U3CCaretBlinkU3Ed__172_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m6F0710FF54BBA7F57D031169B56C02C9D0503926,
	U3CCaretBlinkU3Ed__172_System_Collections_IEnumerator_Reset_m880C7F0BD8A9138228E0D9C61A53D0C7FF616BCA,
	U3CCaretBlinkU3Ed__172_System_Collections_IEnumerator_get_Current_m047B5A2A48DE0ADB8ADA21A1B9C70A81F7ADD9CE,
	U3CMouseDragOutsideRectU3Ed__196__ctor_m4D872B98EC92F270BC3DBA1CAC11BB23B4E3A701,
	U3CMouseDragOutsideRectU3Ed__196_System_IDisposable_Dispose_mCC11E5EB4E68249708984A0913B46AB4CB922E61,
	U3CMouseDragOutsideRectU3Ed__196_MoveNext_m612C8A3B05E3FBF76D294B1B25E4BBFFA5CEF388,
	U3CMouseDragOutsideRectU3Ed__196_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA1214C3CE058B3933969D9AB9FBE5C6F90D6C4A7,
	U3CMouseDragOutsideRectU3Ed__196_System_Collections_IEnumerator_Reset_m7D896FDF203405E7EAADF1F2C6FC180531BA98D6,
	U3CMouseDragOutsideRectU3Ed__196_System_Collections_IEnumerator_get_Current_m0EDF9E4AFA373A221F99E8A9C102A72D8D82D5FC,
	AspectRatioFitter_get_aspectMode_m530AE9878F26D7C1166C2AC3C2B2547CED122B27,
	AspectRatioFitter_set_aspectMode_m1CDA777FF728BD01AB939C074D03F9C18675FB65,
	AspectRatioFitter_get_aspectRatio_m72A1972D15B7435EF895562EEF0AE8C689ED120E,
	AspectRatioFitter_set_aspectRatio_m4192E203648BE0ACA39D9C0540C982331CEA91D9,
	AspectRatioFitter_get_rectTransform_m20FD6C51C01C8BBC2C8223C255F9C28E00689496,
	AspectRatioFitter__ctor_m277805022C03480F2EDA97E7AA48D33839EBD102,
	AspectRatioFitter_OnEnable_m5B6FCCB531F87ABBAEFEB38AEDA1340E10EDEFDD,
	AspectRatioFitter_Start_mB88F08EC9C3453EAB41607D91825BD90C6391F64,
	AspectRatioFitter_OnDisable_m43DAB6B9ADAE9683A99395FCD7B769879A75477F,
	AspectRatioFitter_OnTransformParentChanged_mD5F9B727921A4416EB663B4152CCB499EFB8B111,
	AspectRatioFitter_Update_mE1F6FB785AB83C3416B67EEC58CDB74144583230,
	AspectRatioFitter_OnRectTransformDimensionsChange_mE94570689C2A5CCB2C2EB33F9C5F240E780A2784,
	AspectRatioFitter_UpdateRect_mFAB08DEB6F064E439934183C92038428ADA0F235,
	AspectRatioFitter_GetSizeDeltaToProduceSize_m467FB832F233366AC4B795E932F05740E6429A4D,
	AspectRatioFitter_GetParentSize_mA3CFC47B4F43532BCA0267F99911DC2CA1FFE9B0,
	AspectRatioFitter_SetLayoutHorizontal_m49115A35A48158B48CD198028034214F95793FAE,
	AspectRatioFitter_SetLayoutVertical_mAC5B9A89C0E8659BB590BD62608D22349B90D612,
	AspectRatioFitter_SetDirty_mCCB04E2ECBD43C874822C58BEEAC00AB7EA8A58A,
	AspectRatioFitter_IsComponentValidOnObject_m163BC9DE4258B1C308B850BC259704A3D285A3B0,
	AspectRatioFitter_IsAspectModeValid_m0311FF067288EAD9D2A81ED3A4151C406F0A30B7,
	AspectRatioFitter_DoesParentExists_m9475339C8EE94000A65B9F39AFB08A867940D925,
	CanvasScaler_get_uiScaleMode_m8E92609E011796E8CC23B1739F95CE7BE2631525,
	CanvasScaler_set_uiScaleMode_m064C83FFA35E2AED4E9FA7D5EC1AD19630D8FC2A,
	CanvasScaler_get_referencePixelsPerUnit_mE0A7FECC27003A4A2BE6AE6E70747FAC8C19A008,
	CanvasScaler_set_referencePixelsPerUnit_m8817BAEB73BE78DD7C87EAB7D2FE2983B2300628,
	CanvasScaler_get_scaleFactor_mB2BFA22B99AEC96F09886F490DA9EE2F825D3431,
	CanvasScaler_set_scaleFactor_mD53E8CAE41E8C1B0DF53CCF14D5941FF8EA3488B,
	CanvasScaler_get_referenceResolution_m79C03DD8CE6759B045928C5339A3C5E6220276B5,
	CanvasScaler_set_referenceResolution_m793679B8505AF9BBF64F45D80AFE39F3F99FAB8D,
	CanvasScaler_get_screenMatchMode_mA07ABCCF6AFE98C16651EBD5AB24BFF08B10F768,
	CanvasScaler_set_screenMatchMode_m926C437B408D2F2CA4900723BEEEE09504A6768F,
	CanvasScaler_get_matchWidthOrHeight_m9C40FBA943172874FD27F3F7B880E2D5D5862C9B,
	CanvasScaler_set_matchWidthOrHeight_m44635DC3E4424255C312814C325A48E37E6B6E30,
	CanvasScaler_get_physicalUnit_mD4B04FD2D68F8C3CA39550C056A7AFC836DEB6EA,
	CanvasScaler_set_physicalUnit_m6A759A32FFBEBC43A51C98621A3F505289670C5C,
	CanvasScaler_get_fallbackScreenDPI_m966C603918C0420EAB4C3048591DE408190FFAA2,
	CanvasScaler_set_fallbackScreenDPI_m01E7CB32B519FBC9F5A77F060EE0B2DF7D6895AC,
	CanvasScaler_get_defaultSpriteDPI_m2F1CDF6DE4F2B2E3DED10D50D6E674699120C50A,
	CanvasScaler_set_defaultSpriteDPI_m742DFE7A3315C0B33763D2E3FB2424BCFF35D3DE,
	CanvasScaler_get_dynamicPixelsPerUnit_m6DFC581EFFD626F6815BA8C9579DD736514626AB,
	CanvasScaler_set_dynamicPixelsPerUnit_m7A081D5FD963F751140DCF1E5190ED4E51308CA2,
	CanvasScaler__ctor_m0D60150B065E8CFBCB4BC324F364A0FF08762493,
	CanvasScaler_OnEnable_m9F50E6AF109CE6227FD9E523B0698925B89D29F8,
	CanvasScaler_Canvas_preWillRenderCanvases_mDBBF36EADD3DFBE62E1E5F14D0DC9BB86FC21E6A,
	CanvasScaler_OnDisable_mE0CE97F651B806DD2B2565203A00E97A6A781B2E,
	CanvasScaler_Handle_m0EF8A30C92B8A90A54D2B0BB06E7698E74AD5967,
	CanvasScaler_HandleWorldCanvas_m3E325EB0AC3221EA44B3D81360DFE63C36C13190,
	CanvasScaler_HandleConstantPixelSize_m7C504A9281A98E3473F0113CD74A9305AE4C5CD0,
	CanvasScaler_HandleScaleWithScreenSize_m3F436166B074013EDBEE38B7009C338650CF942C,
	CanvasScaler_HandleConstantPhysicalSize_m44CEBEFEE2AAD54993DA3A43047E86AE07B32DD7,
	CanvasScaler_SetScaleFactor_m195FFD8019696523653CA6CB1B8531ECE4020636,
	CanvasScaler_SetReferencePixelsPerUnit_m77B9E51B468EC9750355687AA6E25564D60BE9B5,
	ContentSizeFitter_get_horizontalFit_mA5FBF6AB42F551272B94A7B89A372B1AA1ADBC0D,
	ContentSizeFitter_set_horizontalFit_m7B0DB223B08B8D578F749DEC381349E7D66DCDE4,
	ContentSizeFitter_get_verticalFit_m3F2848F19A5F8F30F55E0B5D930EFEF4E5EFAFF5,
	ContentSizeFitter_set_verticalFit_m8F61CFD01D4C3D3DC253F30BA8FC2F44F8F927CF,
	ContentSizeFitter_get_rectTransform_m757AAC9852D5C462C083FDA80390813E4FF06467,
	ContentSizeFitter__ctor_m60693679801693DCDEC5BF0FD45590BD66F2434A,
	ContentSizeFitter_OnEnable_m31DA9C05A1B5FAB9BD1BE05C43192B427C156CD3,
	ContentSizeFitter_OnDisable_mA11B1667210796F7DEE199F2B78844A6CA0C720F,
	ContentSizeFitter_OnRectTransformDimensionsChange_m427809780F5D59796CDB386A8CD5B4DB985D7691,
	ContentSizeFitter_HandleSelfFittingAlongAxis_mA050224EA492DF6C8B339DC36FC3BB8ED5D09A85,
	ContentSizeFitter_SetLayoutHorizontal_m694E40D536D88366735B3838FA040EB2D2144320,
	ContentSizeFitter_SetLayoutVertical_mB58DDF80917329DFAE202DA73472AD39BF37E561,
	ContentSizeFitter_SetDirty_m5A4C67937A3C77E467881648D5B9D7AB4E8C5C59,
	GridLayoutGroup_get_startCorner_m0796B782C9F3981B6E97F83A6815102A5176657D,
	GridLayoutGroup_set_startCorner_mCE5A1E957B06BF34173119A5C62B832E279DA78A,
	GridLayoutGroup_get_startAxis_mADFB75A761550B3141256B0130655A6703FF3FF5,
	GridLayoutGroup_set_startAxis_m2C9BCD2A1CD3ECFDDF3B0A8B7EE28C48179A7739,
	GridLayoutGroup_get_cellSize_m30D8A051F44C8EE0C87B6D6CDDC00C2592A78B6D,
	GridLayoutGroup_set_cellSize_m0A3FF07694BDBF52D973597978FC87B0941BE5F9,
	GridLayoutGroup_get_spacing_m19BC15652BF18D051B0998C14F13DB83191F3E58,
	GridLayoutGroup_set_spacing_mA5550A683F7B4A7A1510B267B5D4CACEB8981306,
	GridLayoutGroup_get_constraint_mAEC0A95B4DF9F48E07B5403CC5F954AFDE503029,
	GridLayoutGroup_set_constraint_m632CB37D0D79A12DE81372EE819348CD1226B84A,
	GridLayoutGroup_get_constraintCount_m63AE4B7889A27D8CAA8EB04A40B1FE53D80CC318,
	GridLayoutGroup_set_constraintCount_m685F6D5254B6D77AF8BE070EF3DCA5F049B3D043,
	GridLayoutGroup__ctor_mBC2ADB7B7F092C83138425C82DEDBB6701F73F7D,
	GridLayoutGroup_CalculateLayoutInputHorizontal_mFDEDFB79ECF5C03713EE1C128362D3AC0D48ED8E,
	GridLayoutGroup_CalculateLayoutInputVertical_m41E33CD0EBF75155C0B842E9EDA2C66EB68AA9EA,
	GridLayoutGroup_SetLayoutHorizontal_m16F35F3DA5B7AED47787C0EBEC723723DC9034F0,
	GridLayoutGroup_SetLayoutVertical_mAF83C49C8BBA29EC4465B1BC2A8A39B0321FB038,
	GridLayoutGroup_SetCellsAlongAxis_m815D9BF1B794A46C96CFE3E069C49274FCB66739,
	HorizontalLayoutGroup__ctor_m811D870AB5F67030CD9A3C1FC02FFE69298131BC,
	HorizontalLayoutGroup_CalculateLayoutInputHorizontal_mB2C54B2F51CB18A490867DE302D6444C93ADC537,
	HorizontalLayoutGroup_CalculateLayoutInputVertical_m8739924AF17AA7FD9061BBDEBECFC3E2C946D27E,
	HorizontalLayoutGroup_SetLayoutHorizontal_mA4203F549D73128EB605594C74DA47CA07278A25,
	HorizontalLayoutGroup_SetLayoutVertical_m6B8A658837C88E6A29A9850725734F9C5CA67B82,
	HorizontalOrVerticalLayoutGroup_get_spacing_m916C9BF57D4AB0EF76E6BC4EC5E1EA54B7918782,
	HorizontalOrVerticalLayoutGroup_set_spacing_m90373F54D37DA8DFA90E102DC60EC33E542FD859,
	HorizontalOrVerticalLayoutGroup_get_childForceExpandWidth_m07A6B6378938DA69E365DCFB2794EEE7D71CC510,
	HorizontalOrVerticalLayoutGroup_set_childForceExpandWidth_m351827AA1A453ACD17C2EAC7B4DAB9C5DB1760E5,
	HorizontalOrVerticalLayoutGroup_get_childForceExpandHeight_mFCBB20057EDC1E7B2DFD56FB6ABFE9A462560741,
	HorizontalOrVerticalLayoutGroup_set_childForceExpandHeight_mA144CF421614F41813DE346AA9D1C64621C6C2E5,
	HorizontalOrVerticalLayoutGroup_get_childControlWidth_mBA38BDC393C180CFC30DA02478B493D6CCD92AB1,
	HorizontalOrVerticalLayoutGroup_set_childControlWidth_m0B9A78B8284E17C438645684984796AC0E2D1BD8,
	HorizontalOrVerticalLayoutGroup_get_childControlHeight_m867F7E1D52F29ED8F9E5F060089800295E186AA4,
	HorizontalOrVerticalLayoutGroup_set_childControlHeight_m8DD189C9B1F926641F4A2FD41F41F2097E4D7751,
	HorizontalOrVerticalLayoutGroup_get_childScaleWidth_mF5057406C963AB6CB70DC1B2B213A1F5F7C97E91,
	HorizontalOrVerticalLayoutGroup_set_childScaleWidth_m96A12D7E1C6BCDD510EC08FC470FA5F69B90922D,
	HorizontalOrVerticalLayoutGroup_get_childScaleHeight_mA5AD05DFD31E25C5C014C24B5B11DC5492A2E893,
	HorizontalOrVerticalLayoutGroup_set_childScaleHeight_m519A990CF97DE1C974DD1F48466763E4AEC648BC,
	HorizontalOrVerticalLayoutGroup_get_reverseArrangement_m245D8EC788EDA70DCB831FE62DAB8DB806BE7EA3,
	HorizontalOrVerticalLayoutGroup_set_reverseArrangement_m2AF5AC83D6FE8AE364C626C0518B2ECCEE9C0477,
	HorizontalOrVerticalLayoutGroup_CalcAlongAxis_m12CA995AB887ED06762B07E97953D456B316647A,
	HorizontalOrVerticalLayoutGroup_SetChildrenAlongAxis_m4D7B06435A66102659B2372B48D49B2117D57F09,
	HorizontalOrVerticalLayoutGroup_GetChildSizes_mE555CFCDBD0CD9913829BB56457F939A166BA383,
	HorizontalOrVerticalLayoutGroup__ctor_m778C23DD9F3973AFACD3C6CCEDABF81902665D3F,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	LayoutElement_get_ignoreLayout_m32A9F0BACBC8E6BAE46F35E570DF71E937924412,
	LayoutElement_set_ignoreLayout_mF3D4AF6214FD719979E4BA6A120494E7226FF18C,
	LayoutElement_CalculateLayoutInputHorizontal_mD6645A83B5E234C1EA5C764E48CAD4F3C135C4D7,
	LayoutElement_CalculateLayoutInputVertical_m1D25D380F32BD322135C80C41D407BD81C5D88F6,
	LayoutElement_get_minWidth_m6943ECF36A67019A485C4A7AFFC0BF7FD94480CE,
	LayoutElement_set_minWidth_mC140AB11DDA8F8FD299A5A7E3A9674FB21E827E4,
	LayoutElement_get_minHeight_mC1951830B9F43C57AC4A287E9AF3A62A0871E9C3,
	LayoutElement_set_minHeight_m8B794B9E92B440D9B88FEACD95492DC5257D628F,
	LayoutElement_get_preferredWidth_m214B11641CBD652E174F42133EF7CDC413CF6CE0,
	LayoutElement_set_preferredWidth_m9D8F8097227D2EBAC03BB0E2E6B0E0A6C8887BA6,
	LayoutElement_get_preferredHeight_mE630312564CC2A3E459C9C3E5FFDC2138D35EC88,
	LayoutElement_set_preferredHeight_m0F5874AD74B74F2A8F1CE86ED0477FEA9555433F,
	LayoutElement_get_flexibleWidth_m2E51EA4DC58A4740702314E253FCA8816A1B98A8,
	LayoutElement_set_flexibleWidth_m29E6E303E19AE180FD805D6E5481A00FC49E2983,
	LayoutElement_get_flexibleHeight_m8A7B16E85F304CAA03BF6417BE1D0F6C0212E2E4,
	LayoutElement_set_flexibleHeight_m39C426C07583BE074F9B71DA9ECA1216860A43D2,
	LayoutElement_get_layoutPriority_m20D5C7FC2019146C2FFD09CF1A3D908703763510,
	LayoutElement_set_layoutPriority_m8EAEC716134A0536F1E96F8C3AB0980D5416E2BD,
	LayoutElement__ctor_m31C173AFE1B1749B6957B578C9463044BA22624A,
	LayoutElement_OnEnable_mCD4984C5E35B4658AAB3224795209A92DAD65C6B,
	LayoutElement_OnTransformParentChanged_m7495A830D24B032BBCE6FC2F540CDCE8B713C330,
	LayoutElement_OnDisable_m5DBCC5762DB101EA70B19A24F8A41BCDE450AB87,
	LayoutElement_OnDidApplyAnimationProperties_m3D225CF42A2339702431CEB9F43DC769567E1535,
	LayoutElement_OnBeforeTransformParentChanged_mC3BA3EA166CF4AE74B9A00799DE1C2869A9261D6,
	LayoutElement_SetDirty_m9ECC494A5A6C3764AAB0D3E2C61C6050FC517879,
	LayoutGroup_get_padding_m91ABA3C588704717EDC82E72BA6D1B82711FE83C,
	LayoutGroup_set_padding_m9F415F3402E5E4AE684FD153493CE3E8D64D3EB7,
	LayoutGroup_get_childAlignment_m45C0D32DB91FD92852CA50278904034A26ADEFC1,
	LayoutGroup_set_childAlignment_mA97DF1F2CF43C0CD1B83CFE7883626AA86ABB0AF,
	LayoutGroup_get_rectTransform_mE9AD2CFD78229C631BF21260FDB40C2D0D895974,
	LayoutGroup_get_rectChildren_mEB00A4F0B86326AA9BE3D5E5DD7E4C9E3A032391,
	LayoutGroup_CalculateLayoutInputHorizontal_mAB313A3646FC94E9FA98E5C4EA19DBAA7F3754FD,
	NULL,
	LayoutGroup_get_minWidth_m3EFD1527249470CC4F71588466BFB17D4A632229,
	LayoutGroup_get_preferredWidth_mDE70B887487494986C9A5621C9F19488154EE2CA,
	LayoutGroup_get_flexibleWidth_mB4DCC3B208370CF2A2FE276A56D011922BC08609,
	LayoutGroup_get_minHeight_mE2FA1D3B4B40AAD5CD4493087C5B63C7BCAE9B3C,
	LayoutGroup_get_preferredHeight_m055B2270ECB1C9C0FCCCA396FD7E9F8EFBDBDBA8,
	LayoutGroup_get_flexibleHeight_m5F911708AAE2DDEF9ABF8EC7894F2B7A7264EB0A,
	LayoutGroup_get_layoutPriority_mC86CB36BF49A18F09F6577A9B298CB639F1FEC4A,
	NULL,
	NULL,
	LayoutGroup__ctor_m3F10CB94B64D503325A8EE097A94261C08AA2337,
	LayoutGroup_OnEnable_m49EF8F43626DCBD10EB37D7F95BDEF2817DECC72,
	LayoutGroup_OnDisable_mC10A4F2B949F44688E26D0F1499BE39B0655DB42,
	LayoutGroup_OnDidApplyAnimationProperties_m7E426AAB3C937005BF074ABCF5A1C9FB2D67BB95,
	LayoutGroup_GetTotalMinSize_mFBD1A44880D3390EFC7AF2441D556C9FAD49059A,
	LayoutGroup_GetTotalPreferredSize_mEFFC79C79FC70A3BDD06E46C6188827E0F7EABC3,
	LayoutGroup_GetTotalFlexibleSize_m0750BE35A8B466C0CB82460B0A490139B8BE1E2A,
	LayoutGroup_GetStartOffset_m3748EE96F01312488AD6B764B01171AB2F5E309B,
	LayoutGroup_GetAlignmentOnAxis_m14E9D80D22AFAE88909D806F5439BCB9EF194A45,
	LayoutGroup_SetLayoutInputForAxis_m3704D7673470CF7CF1F2B145F226C9C30C25E660,
	LayoutGroup_SetChildAlongAxis_m25F11D4F93E0D31E68F7227D74000FFB067A8FDC,
	LayoutGroup_SetChildAlongAxisWithScale_mDCF850DCCD115F9B2ED8AC9D5D7EF8EA0B42EA94,
	LayoutGroup_SetChildAlongAxis_mBE88585F9D066C2997499871D934C0A4E9AE871F,
	LayoutGroup_SetChildAlongAxisWithScale_mC1910181779269C2656D954DE36F384D19F11C22,
	LayoutGroup_get_isRootLayoutGroup_mFB0EC6A489F3847C38599F1187755B6E04301B04,
	LayoutGroup_OnRectTransformDimensionsChange_m32A8C9D736F6096B93235870A9623D63C2CBCA74,
	LayoutGroup_OnTransformChildrenChanged_mF55AB48380641070CF0F92AC633357266D14A04A,
	NULL,
	LayoutGroup_SetDirty_m32F20D8BB5C4B4DF350AF5F35A5917660FF9CE60,
	LayoutGroup_DelayedSetDirty_m67C0D880E25888F274BE8AE9D3F4C28EA4A22D0C,
	U3CDelayedSetDirtyU3Ed__56__ctor_mF6AE811754CADB1402BABF82639E38DB56C9AFCB,
	U3CDelayedSetDirtyU3Ed__56_System_IDisposable_Dispose_m347957DDD38BB5EFEE268E592550D258E5189F75,
	U3CDelayedSetDirtyU3Ed__56_MoveNext_mE69F0D45357F390412D5833F35A0C4B4F3E47420,
	U3CDelayedSetDirtyU3Ed__56_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m581E5D52D9508F8B755213FC2BFFC15412352F79,
	U3CDelayedSetDirtyU3Ed__56_System_Collections_IEnumerator_Reset_m7C5E44C5235E8C18D0066EF7464A6165F6D4B1C0,
	U3CDelayedSetDirtyU3Ed__56_System_Collections_IEnumerator_get_Current_m83BB521EA7AF720756B95AA6D637557DFCDCA2A3,
	LayoutRebuilder_Initialize_m3186A381CF387FC04A9D52BF6ED9982B0150E562,
	LayoutRebuilder_Clear_m2BCF887531F7BA60FB962F7153564A055A136AA1,
	LayoutRebuilder__cctor_m5AE721B6C2738FA3FD23CF5103CEF96E6AEA578B,
	LayoutRebuilder_ReapplyDrivenProperties_m2FAE70C6B03F93BDF9484BC4674FA80D956BE45F,
	LayoutRebuilder_get_transform_m885E49969AFEF977B384D517212E68ABFDDB6555,
	LayoutRebuilder_IsDestroyed_mEB8D2E6A0E61BD035965F28D30FB5BB41AAB2149,
	LayoutRebuilder_StripDisabledBehavioursFromList_mB6E476924D6DDDA8050F40300A58696303F0753A,
	LayoutRebuilder_ForceRebuildLayoutImmediate_mCCA094579654469919EFA4B5AA5D9AF93CD67B4A,
	LayoutRebuilder_Rebuild_mE0477A991681D208BF504CCAABFE01D7FCD8E137,
	LayoutRebuilder_PerformLayoutControl_mA6EB813FBAC300966A6357D248FAADD947C92D4B,
	LayoutRebuilder_PerformLayoutCalculation_m0733192A11C335EEF72298A2321937CDA97A1C34,
	LayoutRebuilder_MarkLayoutForRebuild_m37F415D59609E9D18D49423D9C33E7EA6D859EBD,
	LayoutRebuilder_ValidController_m28AC31CE8158B1D2D9656A99ACE3F259F5212C70,
	LayoutRebuilder_MarkLayoutRootForRebuild_m72542D06667BE02C021D13ADC4C77094614552FF,
	LayoutRebuilder_LayoutComplete_mB93ADFB170DD29029D271710D946FE0B08665380,
	LayoutRebuilder_GraphicUpdateComplete_m177178144B034919E062EB9C205AB65CB437BC6D,
	LayoutRebuilder_GetHashCode_m408B8FD9884FA8F7F967F1E8C7015055B8F780D3,
	LayoutRebuilder_Equals_mD6E988174B451D9E832E3FC8B1EBBA0DD1FFB92E,
	LayoutRebuilder_ToString_m7F8066428103BE4A1E0A71F45B1D0E725E377A58,
	LayoutRebuilder__ctor_m685B1B7449046E3467525550996AFB6A4565219E,
	U3CU3Ec__cctor_m0D6AD1DB52B49A72650F253F02B465D02C18BE04,
	U3CU3Ec__ctor_mD28F0F8B5399F1C60A8E4575F9DCDC847D2CAA23,
	U3CU3Ec_U3C_cctorU3Eb__5_0_mD2D37BAA0BED9121AC22FDFC48D7CA35BF400E14,
	U3CU3Ec_U3C_cctorU3Eb__5_1_mE8F9670037E944B0EB22487F1640407057B8A290,
	U3CU3Ec_U3CStripDisabledBehavioursFromListU3Eb__10_0_m03CF501571C52837064AFBD196238DB52B64BC5E,
	U3CU3Ec_U3CRebuildU3Eb__12_0_mDB44431ACB66295B4B09C0132E9EF267DB0090F1,
	U3CU3Ec_U3CRebuildU3Eb__12_1_m08B6A90656111D084CCF7AF3B07D50C03B95BF9F,
	U3CU3Ec_U3CRebuildU3Eb__12_2_m516829AF584D4F446B08587F777088C704548C40,
	U3CU3Ec_U3CRebuildU3Eb__12_3_mDEE0C1EB02D33044B945FF479F81E7A3DD85684B,
	LayoutUtility_GetMinSize_mE0421687F579243D5252F065A737268EF736374C,
	LayoutUtility_GetPreferredSize_mCAFD360B8490CD02AF5F99E93E09D8625BD85F52,
	LayoutUtility_GetFlexibleSize_m2D6400EB342AE2E811DAFFF0CD2AA2F8AA5DD655,
	LayoutUtility_GetMinWidth_m920875D5D911EF3FAC615C045D9178630697A0F3,
	LayoutUtility_GetPreferredWidth_mFF51E72881BE14E8C59521A71188E458475D4052,
	LayoutUtility_GetFlexibleWidth_mFE4684C5AC223E1D8E4369AA3460461A3964D9BE,
	LayoutUtility_GetMinHeight_mB428914EB41682FC709CDBB27A0CB42D42EC4D9E,
	LayoutUtility_GetPreferredHeight_m3E8CDE02CC980080BBD4BBA1D6BFDFD42F7CF706,
	LayoutUtility_GetFlexibleHeight_m13C0A91CEB1B4DE827C4B9E5230D4AA73FFC23DC,
	LayoutUtility_GetLayoutProperty_m3A5BD03879B0B223BE78DDD6AB7595BDF14CF7A7,
	LayoutUtility_GetLayoutProperty_mEE37FED419E8D6C3B799296DC6712D312AA5261F,
	U3CU3Ec__cctor_m0CA720C96E971F17E2E861D8AFE6982FD1F59F44,
	U3CU3Ec__ctor_m5E4858F8F2E57AAC48D706CFDE697DB16DE163D3,
	U3CU3Ec_U3CGetMinWidthU3Eb__3_0_mB230985725E48B130593DCE9618576ABD38544E9,
	U3CU3Ec_U3CGetPreferredWidthU3Eb__4_0_mB8A573FB7B4D32CBC78F6AF30C42FDD204D18862,
	U3CU3Ec_U3CGetPreferredWidthU3Eb__4_1_m21A4B6CC6110B2FE507ECD670D1C701BC9E1C9F9,
	U3CU3Ec_U3CGetFlexibleWidthU3Eb__5_0_mDAFF6CD37904716095CE8E674BB7D2586ED17A95,
	U3CU3Ec_U3CGetMinHeightU3Eb__6_0_m92D133D332D83DAECA59B06DD94CE6390C617162,
	U3CU3Ec_U3CGetPreferredHeightU3Eb__7_0_m869A0C6EBD3699CA96970C944D80A94779D58FEA,
	U3CU3Ec_U3CGetPreferredHeightU3Eb__7_1_m6851C73E5D55417BF6A9A29D3DD22EA033D40EEB,
	U3CU3Ec_U3CGetFlexibleHeightU3Eb__8_0_m55A0D46B9D7CD1002AE7F803E496E6F1A98F3E24,
	VerticalLayoutGroup__ctor_m2EF3851AD1D83E3ADBB5053D1FAEF84A773E1D5B,
	VerticalLayoutGroup_CalculateLayoutInputHorizontal_mF9483E90B39BDCE71C90F9B27CBF76F2A10E4D28,
	VerticalLayoutGroup_CalculateLayoutInputVertical_m360446A9D17C1B798BBB0B777EA1BEEE297807BA,
	VerticalLayoutGroup_SetLayoutHorizontal_mD074FE8C9B3187AA23AF6578E8C81B23404D58B2,
	VerticalLayoutGroup_SetLayoutVertical_m27D87A8FEBBBD9226B95F250A076B3AB7BAA155F,
	Mask_get_rectTransform_m4D1933DACBE7B0F93B1B83F1B3B0A09F65B24209,
	Mask_get_showMaskGraphic_m87FD20C72F514AB305E05A5B104B180D9B35601B,
	Mask_set_showMaskGraphic_m9F288D22259CFD781D5A4D9B9747C2A2895E7D67,
	Mask_get_graphic_mDC288968F569C492F1E18F82229ECB7AA3804AD2,
	Mask__ctor_mB4AF8A6FD9496A1E8EEB7631D43F8A0548134DB9,
	Mask_MaskEnabled_m330C9AE8B8A642ECF3C4E88687E9F297969B6351,
	Mask_OnSiblingGraphicEnabledDisabled_m38D50E90213834C6E06D449F364C7A802964FC74,
	Mask_OnEnable_m928342074FD21B3A58E1370F681DC762BD64B095,
	Mask_OnDisable_m7B533EC440BB28CB80AB8AE914BFA501FAB3ADA5,
	Mask_IsRaycastLocationValid_mE12C460DF4AF0C65082DBBA6F46A2259687A2534,
	Mask_GetModifiedMaterial_m5D7DE1884428D7EBC6A7AA6376650E4FB966B1F4,
	MaskableGraphic_get_onCullStateChanged_m8452945E93AF20B975D85E61999B51039CAF6538,
	MaskableGraphic_set_onCullStateChanged_m4284F81D75D8F8293FE2FB5FC236FDF63579BBF7,
	MaskableGraphic_get_maskable_m34B87CD87CFF73FF4E09D892ADB316E412F22660,
	MaskableGraphic_set_maskable_mC2486FDC0636C83AC3BDBFF11E6E85CC27F15689,
	MaskableGraphic_get_isMaskingGraphic_m8C4270841AF6071FD5AC4EB7225AF259053DF55E,
	MaskableGraphic_set_isMaskingGraphic_m350EDFCF390CF594B939BBEE3A0D634F2EA48A78,
	MaskableGraphic_GetModifiedMaterial_mBE4C5B18ED4221E0A6C026C750B6A04E9B35312A,
	MaskableGraphic_Cull_mF6948476960E33BD174FD3723101650E3C344CC7,
	MaskableGraphic_UpdateCull_mAC0798E6376F7B103BB36929AC4DD69729E30E86,
	MaskableGraphic_SetClipRect_m19317C49A4CC99A991A3F0135756DB94020930C2,
	MaskableGraphic_SetClipSoftness_mF11957AB91E1BA19B6008ACEF95C5F9AD930CAE4,
	MaskableGraphic_OnEnable_m4BF46ECE5E57E2EE11ED4CE41AD50DADF141C9BC,
	MaskableGraphic_OnDisable_m9123E729FA7BE001037CDE14E8A75B69AD68E16C,
	MaskableGraphic_OnTransformParentChanged_mE5ABE137F670FBA7E6FCD2A67616E4A8097AD876,
	MaskableGraphic_ParentMaskStateChanged_m1353B87D25271925B6ED342FDC06B05F7EAD3992,
	MaskableGraphic_OnCanvasHierarchyChanged_mB30092A7276A921F711E466E9CE85C04ED982E77,
	MaskableGraphic_get_rootCanvasRect_mB7F5E772A53CBCCF920CD924E84634CD8155F6D8,
	MaskableGraphic_UpdateClipParent_mEFEEC27574B12503C1D8B694BA61C7166828F6A2,
	MaskableGraphic_RecalculateClipping_mFDD980F0A3AC1BEFF0BC9EDE95EF063AA9C282F7,
	MaskableGraphic_RecalculateMasking_m76F4A84B87AD4938F8A68B022A5A2BB4B5F343AF,
	MaskableGraphic__ctor_mD2E256F950AAAE0E2445971361B5C54D2066E4C2,
	MaskableGraphic_UnityEngine_UI_IClippable_get_gameObject_m17FD7D774DA4D9D0F2E23240D9E17FF5C7DC4A44,
	CullStateChangedEvent__ctor_m885AD59B4D0D6075AB6DFA71AD69A7BB48640CE4,
	MaskUtilities_Notify2DMaskStateChanged_mBD5C9FCE2AC1327C599BF0D7390BFD86FAE06937,
	MaskUtilities_NotifyStencilStateChanged_m112CACEF914385BC2F96F4D66D4038AF1E0FCD6B,
	MaskUtilities_FindRootSortOverrideCanvas_mCB7DABA799F6C5BDF659D4CA60BA2FE8141A65AA,
	MaskUtilities_GetStencilDepth_m782D2795F76F569F4FB261C5BFB6D9EF241C0EE9,
	MaskUtilities_IsDescendantOrSelf_mBCC8B1428F599BAF1EAFA16E9586639A11B87C23,
	MaskUtilities_GetRectMaskForClippable_m7AEB8F89DFD994A487EED33DDD8C59E5A784245C,
	MaskUtilities_GetRectMasksForClip_mDEC4D04BA24F5C4C5828A4E8A677BE4F3CC6FAAF,
	MaskUtilities__ctor_m32B6A8721369418CAA95A8EF5D65E0B8CD89DA82,
	NULL,
	Misc_Destroy_mA812AD936D10BCABA81E04C6C4C190034995214F,
	Misc_DestroyImmediate_m7E53D180A6459C9577D115A345D59C26FC05F919,
	MultipleDisplayUtilities_GetRelativeMousePositionForDrag_m3C283E331437CB72CF86C5C98B9E61D2317B8F4A,
	MultipleDisplayUtilities_GetRelativeMousePositionForRaycast_mBD9CBF4855B536FF62D12DA8D774B196C2E7EC1C,
	MultipleDisplayUtilities_RelativeMouseAtScaled_m80E65BE9255F7D65D9341FDFDB0CA11470703E71,
	Navigation_get_mode_m3B574F1549B3806753EC33228EB3FF3031F4E809,
	Navigation_set_mode_m0BEF999F733332AD994CF3CA4AC17B2A47531207,
	Navigation_get_wrapAround_mA24021791B1C67F665065B5A415434837CEA86DD,
	Navigation_set_wrapAround_m9D808EC49EE5F3AFA7F0D13E86FF9F72AA20A081,
	Navigation_get_selectOnUp_mD24FC0BAB97E5DBB28C9C7209BAC2ACC9419B183,
	Navigation_set_selectOnUp_mCB04000FDFC05D3BAC497602E4BA346A536152E5,
	Navigation_get_selectOnDown_m1D36E990CDB38C4BB78745587668F94BBE8A1285,
	Navigation_set_selectOnDown_m0EBBAB8C51107F75F63FFBC3DF88D9010E6A44BB,
	Navigation_get_selectOnLeft_mA4F7DA341D7C660A7E15520B34847B0757C65F81,
	Navigation_set_selectOnLeft_mA4E7480D7CBDA9A5ECA93BAFCD1CF1976A994FCB,
	Navigation_get_selectOnRight_m7A781F4050AE064DC0473E68AA6D07CFFF0A8FF9,
	Navigation_set_selectOnRight_mD0B38024BB628CDC801EA93E9FF7C438ECE2055B,
	Navigation_get_defaultNavigation_m142FA3A8F52EE3DD355FFE30061771FB9A86671E,
	Navigation_Equals_mE25B4E3D0AB85C1469B99971E6AB16E2039E6B4D,
	RawImage__ctor_mB9515043B2286A9012B98913D023EA0ACEF57401,
	RawImage_get_mainTexture_mDA4701244E31799D8897FBB6E0A1FA41EF5B81E9,
	RawImage_get_texture_m84CCFDF78F6886F73EBE5A7C78D6E9C3CA903813,
	RawImage_set_texture_mC016318C95CC17A826D57DD219DBCB6DFD295C02,
	RawImage_get_uvRect_m83D2C4632C6AE437D1DC775904AC2FA8CB83D823,
	RawImage_set_uvRect_m9DF6BBBC6AC46F7F3290A220ED6F076CAB4BC52F,
	RawImage_SetNativeSize_m02ACAE096422EE2D5E17FCF89CC7BBB74A64FD6A,
	RawImage_OnPopulateMesh_mA85FE8B6123F6B3D013E8DEFB9DE3CAAF0C08F6D,
	RawImage_OnDidApplyAnimationProperties_m571F0CB106D9060554503E87FCA700A6B6C997A6,
	RectMask2D_get_padding_m37CA7BF6DA7386AB4D9A6449CAC48ED6BC4B7777,
	RectMask2D_set_padding_m2E8CADD2DC7A40E78586118453CFE2D8795C997A,
	RectMask2D_get_softness_m2638D596B2600278FF2D3225B14038624DA19E34,
	RectMask2D_set_softness_m2857F567959455CA644277BC644A2EE0984089D4,
	RectMask2D_get_Canvas_m689A6760F58FD683B7A5EA6A92691AAA521D4634,
	RectMask2D_get_canvasRect_m81DEFAC3250A9F3FE4B97981335E406B43CFF4F4,
	RectMask2D_get_rectTransform_m6EF34408BB7A5763A590F36D65DE7974E6C996DD,
	RectMask2D__ctor_mC7257CF022267C2E98E8F04CFC28CA37CF8C64FD,
	RectMask2D_OnEnable_m2C52D2F840A9E7462488AB028C21803D3BE14A51,
	RectMask2D_OnDisable_m2CF7F93D68B6ADC28322024E7A9AD4102832F4CC,
	RectMask2D_OnDestroy_m950120AD49BDAFF20E783C22AF863741897015BF,
	RectMask2D_IsRaycastLocationValid_m9ADA1029D511D9A62CFC1B576F396EDD0A31E4FF,
	RectMask2D_get_rootCanvasRect_mC644AE792D28558B8260E23A87C8E6645D33224A,
	RectMask2D_PerformClipping_mD89C9AEAC139EA7AFBB189608D02ABB87F3D7AB0,
	RectMask2D_UpdateClipSoftness_m84A9BCB92DEB1654703D0084C5A3F0BCD2E1BFF2,
	RectMask2D_AddClippable_m90A9698CD91A2A08EBE86AB60B05E76AFA38EAA4,
	RectMask2D_RemoveClippable_m2247DBCAD9B09980191AB791A7CB83FF9C355C2D,
	RectMask2D_OnTransformParentChanged_m593E595A4C1293CEFB17764B55C96E2EC41E4648,
	RectMask2D_OnCanvasHierarchyChanged_m232F0056ED310EAB18C3BA314A666ABF13B4353B,
	Scrollbar_get_handleRect_mEC95A981B744C4DB961D8B5DF6D2B81132CBB238,
	Scrollbar_set_handleRect_m2B621325A0EEA1EDCB71402FCBC7DBEB9C2BD4B0,
	Scrollbar_get_direction_m1950D7EE42DDD0E3DBEABCDD59DD7E0FEC164C4C,
	Scrollbar_set_direction_m1C307CE73857CD7D3FBB160FE66875CA6BA6A3C6,
	Scrollbar__ctor_m65C96C26AB7CBC074ACDC19557E1982155CA30A4,
	Scrollbar_get_value_mC2F43475C89766DA596FFAA019CA59F94CC89A35,
	Scrollbar_set_value_m8F7815DB02D4A69B33B091FC5F674609F070D804,
	Scrollbar_SetValueWithoutNotify_m6E2A4BE4DA16EBA596D2E6E40E4AC2DAC8B6C162,
	Scrollbar_get_size_mD88FDA836274F40EC8A97237C72B7E3C4906DB5F,
	Scrollbar_set_size_m5376982465D6013425FAB0CA8EFC620C3E1458FB,
	Scrollbar_get_numberOfSteps_mC3CEFF66E82BEF0473A82581CA7ACE08AA93B999,
	Scrollbar_set_numberOfSteps_m59EA2D1FDFB3D5E91CC5630254E319605B67E095,
	Scrollbar_get_onValueChanged_m14356CECC1A2BA96576EB73279AF2ECF28B26D6A,
	Scrollbar_set_onValueChanged_m4167C1B411C38C2BCF9967840102723367B35AAF,
	Scrollbar_get_stepSize_m76926AD1E9F264A61B9BF098BC90F1E1335FA7A5,
	Scrollbar_Rebuild_mB6BEE134B0B018A07FD5DE27A353DC4F8834EE85,
	Scrollbar_LayoutComplete_m62E02A6865F74A44F1301CC085D1D4CA4CC90797,
	Scrollbar_GraphicUpdateComplete_mD1DB8FC7C34AC5454CDF41D39483122DA7118876,
	Scrollbar_OnEnable_m80353998984F644C00DFC51861A9ACE4134D2C86,
	Scrollbar_OnDisable_mB78DB94C4093312BBBE28F78FE21B16F8485D2B5,
	Scrollbar_Update_m758EF18E62B3A8D6F319D5CEC9ACDFB005CD1AC3,
	Scrollbar_UpdateCachedReferences_m63BD63A223E31DF89731186F8204993FE707F0AE,
	Scrollbar_Set_m9A15F05D06D200A038C20B1F1C6A4DFA5B17D0A4,
	Scrollbar_OnRectTransformDimensionsChange_m06E846A58CBE1B1006AA3453784789F1A56B8CC6,
	Scrollbar_get_axis_m7C529809A9A4246CAA1F7417AC3418270B7D7ADB,
	Scrollbar_get_reverseValue_mDEEB7F6EC4FD16FD6B1F6806335463FDBC417571,
	Scrollbar_UpdateVisuals_m262B64133E8C98F2B1FF1A075AEACF0F8CBFF72C,
	Scrollbar_UpdateDrag_mD7B02B0A326AF4BB20B66423F3EAEC8FD4BCC787,
	Scrollbar_UpdateDrag_mF99B7B888CC0AE9DF0AF9A72563C5C4DB114D7CE,
	Scrollbar_DoUpdateDrag_mC0C9D56DA7F9AAF3E8941206448DEF1FF2E4BC3E,
	Scrollbar_MayDrag_m19259CC2C45110C1951E59E7E0F8CB207DD69430,
	Scrollbar_OnBeginDrag_m9B628433953BE38D64DB2AE5A3A14A82CDD789CE,
	Scrollbar_OnDrag_m79EAA59922BB2ED61C042ACCCCF9EE14B0990675,
	Scrollbar_OnPointerDown_m8A4C9EDFECF2503F92F57D70C8D71842A3165A27,
	Scrollbar_ClickRepeat_mB3CD100CB06D4687F163B47B1BE806F5519FD8C8,
	Scrollbar_ClickRepeat_m9805A27D61BE928E0A8CC8B6CF6D7DD0A2256830,
	Scrollbar_OnPointerUp_m957C480C8DE9E46E381A800B4B60B07FF12F64B7,
	Scrollbar_OnMove_m17725BD4A3BB30209D66B1938BDF15172F05AD51,
	Scrollbar_FindSelectableOnLeft_m4D775883935EA4A06A67C452C47971BDA90FEFE9,
	Scrollbar_FindSelectableOnRight_mD77EA6CD469357D8E014C5075301A5752A0CA052,
	Scrollbar_FindSelectableOnUp_m44369416317D6AF92FC5CD29CF3B4D4CB44D247D,
	Scrollbar_FindSelectableOnDown_mA0C3C3970272025DE78D382CCDB96721B4EBDD6D,
	Scrollbar_OnInitializePotentialDrag_m7B2840ACB1D2A6D3DA0F03DF9677D2DCF790E065,
	Scrollbar_SetDirection_mA62DC964AA698D058BC84FA1DCAFA46BCA6A8182,
	Scrollbar_UnityEngine_UI_ICanvasElement_get_transform_m402A9522ECD918080DBBE778E8BEF58415E41B44,
	ScrollEvent__ctor_m8875FD9430D9657557F83634E0BDAC8A4C280C10,
	U3CClickRepeatU3Ed__59__ctor_m8345ADC433BB6228E8D107936D6B88E0F658E0E6,
	U3CClickRepeatU3Ed__59_System_IDisposable_Dispose_m94D37A486F75C0142FE08E84A273AEF4BCA7B104,
	U3CClickRepeatU3Ed__59_MoveNext_m1E013AEC306260D4360A63440D9C073D276F4816,
	U3CClickRepeatU3Ed__59_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m781C1E7B3BA449470506588C16DAED846A1F3230,
	U3CClickRepeatU3Ed__59_System_Collections_IEnumerator_Reset_m20693B941CFFE66C958912A917AD64638D196651,
	U3CClickRepeatU3Ed__59_System_Collections_IEnumerator_get_Current_m4AEB23A992754C7BE244B0A9FFAFAB8A75B2EF86,
	ScrollRect_get_content_m7878BCA28A96B7FBA02DC466A1ED2C9E191C6996,
	ScrollRect_set_content_m01BF6FE0205985CBD16C6D3BB4B6F345B3AF484E,
	ScrollRect_get_horizontal_mDA4358EF29CE64E6B346D6CC5D70E08F00D3D05B,
	ScrollRect_set_horizontal_m99C076AF2B2B596C87435E1465EF0B104281B150,
	ScrollRect_get_vertical_m43F2C650302CB71D53A0A373934CA9F9921CC38B,
	ScrollRect_set_vertical_m972088E788E72690AAE139E7C0F8F634C325E7CE,
	ScrollRect_get_movementType_m0672A0BA382BC5479398DE95C551530FE5B38621,
	ScrollRect_set_movementType_m2A900C10E6C005FD6866EFF1DA2DF78AA957534A,
	ScrollRect_get_elasticity_mF0DE000D57AA94F2A5D9E1C48EC6F6514C1F4565,
	ScrollRect_set_elasticity_mCA1500D31E9A8DE62FA03EA3E1276BFFB7F6094B,
	ScrollRect_get_inertia_m10C8837B3E43787E1FA94C71683D19638FCEFFBF,
	ScrollRect_set_inertia_m8A17589561A5E7A2F5F543B8F2F6149458C68AC2,
	ScrollRect_get_decelerationRate_mDE7178B7D5AEA48B258A328ED352C7A8AF9065AF,
	ScrollRect_set_decelerationRate_m7DB02F71AC6E7C519ADB3FA88F9B46EF187FCD61,
	ScrollRect_get_scrollSensitivity_m36A71A35CCAE99F83DE336A51520BB2657686E4C,
	ScrollRect_set_scrollSensitivity_m07A6D8B94625BC52775BED72633CCBEA41E27E1D,
	ScrollRect_get_viewport_m85092216DD476F77E78F5CE50F9C4E70063ECCF9,
	ScrollRect_set_viewport_m53D91C0869950B18953E163E9A3CE5E7AFB0A262,
	ScrollRect_get_horizontalScrollbar_mDE0EC3FD5C1AC8FDB4D8E8EF4B093A77218DF534,
	ScrollRect_set_horizontalScrollbar_m38777B9083CABE5B05EE674DF59867247613F6CA,
	ScrollRect_get_verticalScrollbar_mCEB62CC858B43CE7FB07D287CAFC1363668E78C6,
	ScrollRect_set_verticalScrollbar_m3A3503567D1ED44E21A452FE51B12691E084426C,
	ScrollRect_get_horizontalScrollbarVisibility_m3BB3586EBE511EEB0946353153D4818D5207A91C,
	ScrollRect_set_horizontalScrollbarVisibility_mA00C9BDAC3704BEEE76986BCD1D2DFB7F2E2D818,
	ScrollRect_get_verticalScrollbarVisibility_m8F8691067DFB8070BDB2A15D40C6E98E858B1E77,
	ScrollRect_set_verticalScrollbarVisibility_m40A791E57B3FD37CEB97D2FD29639C4EC5B49ABF,
	ScrollRect_get_horizontalScrollbarSpacing_mA61BE48D8F60FA41696D3854501BD6931297DFB6,
	ScrollRect_set_horizontalScrollbarSpacing_mF3FDBF169F96C109BCC75EE62AAC265D23E30D63,
	ScrollRect_get_verticalScrollbarSpacing_mB3FB9008708D488CCC4EE2753B4EE74953CBEB7C,
	ScrollRect_set_verticalScrollbarSpacing_m27BECB09BC4EE6BC91EAABEF50657182A637C1E7,
	ScrollRect_get_onValueChanged_mA6AF3832A97E82D31BB8C20BCD6E87A300E56C05,
	ScrollRect_set_onValueChanged_mB3D669EB2351EDDEBEF2D0F85FBE6279BE905288,
	ScrollRect_get_viewRect_m3E97A12D75F8D1CBE409EFD5D550141B0DA326C3,
	ScrollRect_get_velocity_m8F7DDB02F52BFF2503F079C216FC5C89AA4875DC,
	ScrollRect_set_velocity_mBC8D4BC0A0184FCC3AEB359AE68E9130E811AFC2,
	ScrollRect_get_rectTransform_mB34A69B7E6E21FFF066786508974D89B5A6D4E4C,
	ScrollRect__ctor_m71A7660A30496E9D4937AE250FBAB722BF0747C7,
	ScrollRect_Rebuild_mC15C5A090517F09F981F12DFD46BCCAE96FF9660,
	ScrollRect_LayoutComplete_mA3AB518DD92641DF7F01CE8108EBFC4C0424A115,
	ScrollRect_GraphicUpdateComplete_mF50A0A85D39C499126C7305CCCF055360091EE22,
	ScrollRect_UpdateCachedData_m5E25EF1E36AB04D01FEE66C8E0CD30C0E6CCA933,
	ScrollRect_OnEnable_m5A4AE9FF349A1F5C9780F2DC17CEF3304B795AE9,
	ScrollRect_OnDisable_m0C287FAF83174051A941BA2F90F4D0E38B3ECFDC,
	ScrollRect_IsActive_mBACF2D3F35080C325C5D6A54CF86D17C19FF9A70,
	ScrollRect_EnsureLayoutHasRebuilt_mDEA99980960C5429B17B200EFB3B2EB13B01956A,
	ScrollRect_StopMovement_mA278F4EBDE715F61F9D38F88E71E364E82870851,
	ScrollRect_OnScroll_m86BA4041DE7B1B13101BCC01D90752143A5A28F6,
	ScrollRect_OnInitializePotentialDrag_m35BB18E5EB6B50B7CC4B44171433E1493A5F8A10,
	ScrollRect_OnBeginDrag_m6B0948CCD12A89B43E4F2596E3C7220A6D426868,
	ScrollRect_OnEndDrag_m7CB3145874E1930FEBD50874DF31280FC35B480B,
	ScrollRect_OnDrag_m1BA80F29441E3761A294E32C7CCE52C35F1B6E5C,
	ScrollRect_SetContentAnchoredPosition_m4C8EC3F85A2B1011985E7583AFDC15A69FF90ACE,
	ScrollRect_LateUpdate_m7E003F1E2C34057F6B802003E77AABF54526C0EE,
	ScrollRect_UpdatePrevData_m4BF4AF6ACB7DC3E4A3F7DA8F468B784D1320ED8D,
	ScrollRect_UpdateScrollbars_m9D6268FD19434213F7BCE166722A9B36346C755B,
	ScrollRect_get_normalizedPosition_m4B05A9E790891D503C2B65953728278C7FF8CB58,
	ScrollRect_set_normalizedPosition_m8CFC50007450856E3B1FEB9E61A6311FBC0E709E,
	ScrollRect_get_horizontalNormalizedPosition_mC2C3A7F67E27AA7470A81042AD2B0AD0B5F1AF93,
	ScrollRect_set_horizontalNormalizedPosition_m9B268C9AE7891FC73623DC7BE6B9900640C029B6,
	ScrollRect_get_verticalNormalizedPosition_m4FE766F04272C1805FDE2A4B72D80F6190841FA1,
	ScrollRect_set_verticalNormalizedPosition_m4AF461113925E6710BF04F46A49CF1F856F7738C,
	ScrollRect_SetHorizontalNormalizedPosition_m3F43FC307A146E534DC3F73F4DE38386AAC10405,
	ScrollRect_SetVerticalNormalizedPosition_m4E9F3559FA6369389C1B70D3E94AA35AEC7903E5,
	ScrollRect_SetNormalizedPosition_m99C3731F06EEEF281E68D5D448914B1A3C5636FB,
	ScrollRect_RubberDelta_m5A4BE5FAAA0C39B318A422F236C898D1008AE248,
	ScrollRect_OnRectTransformDimensionsChange_mD41D649A067BFD8DC067FC612C04E48518D691BF,
	ScrollRect_get_hScrollingNeeded_m426A4490F146A56FF76349CBBA4B587EDA5F78DB,
	ScrollRect_get_vScrollingNeeded_m96BA5B252797DF209A1784D1DE3C09AAFEFB25B2,
	ScrollRect_CalculateLayoutInputHorizontal_mEC706200EAB973A2333279BA6C2EE7F6DAA884A6,
	ScrollRect_CalculateLayoutInputVertical_mF708C890C569C942921A2ED809FC0294E13CC9A4,
	ScrollRect_get_minWidth_m3824272990612610DDDCA8D35C23EDC0E97A6751,
	ScrollRect_get_preferredWidth_m16914F16D3F8F1102428267D62CCBF5E8B1EF131,
	ScrollRect_get_flexibleWidth_m6C7F8AC0595D6B5179BF02EAFEF3126731B162D6,
	ScrollRect_get_minHeight_m3D973E3759C8D35899E2F62CFA7677834E6050B4,
	ScrollRect_get_preferredHeight_m90993A52773D1214E648E8DC937D89317F6D4F72,
	ScrollRect_get_flexibleHeight_m91767E81456CA1069B6BBEFCD140BE65962C421F,
	ScrollRect_get_layoutPriority_m19C83DF0ACE68769627C6FB8E09F92FDF63E80E9,
	ScrollRect_SetLayoutHorizontal_m26167C6091ECF4AFB6A4747575592C2923CA4EE5,
	ScrollRect_SetLayoutVertical_mAC8DF5F2CEB21C69D993846A3AF307C6217B83C8,
	ScrollRect_UpdateScrollbarVisibility_mC4E22621A76C4FED36EFA5421BA4006DCB4E5140,
	ScrollRect_UpdateOneScrollbarVisibility_mB2A129E7AE74E39D6080389679DFDB99D1A65FD7,
	ScrollRect_UpdateScrollbarLayout_m41BFD2C6E126A96E99A6892EB88249D2F44530D2,
	ScrollRect_UpdateBounds_m71C0450FC4E45F3A60CAEC0D3ABE21702364BA92,
	ScrollRect_AdjustBounds_mF4ADDB84F572E72668E1FA1E699F84A4A89E9F96,
	ScrollRect_GetBounds_m867D453097CBE1F32BF2F9D74F88255542F692A2,
	ScrollRect_InternalGetBounds_m678E51D17A614402FEA0D24741A37EBE45B31817,
	ScrollRect_CalculateOffset_mAFCC1C71DF0F848130BBF11C914E2333B8E5155D,
	ScrollRect_InternalCalculateOffset_m47D8A586D3069AA701718AB516A5F2FECC8AE1C0,
	ScrollRect_SetDirty_mAE263F4AB8A126B60FECCB4A20A6DE1C0A7EB8FE,
	ScrollRect_SetDirtyCaching_m8E5F2F8A20AE671802C2ABA400E9125CF60FF19F,
	ScrollRect_UnityEngine_UI_ICanvasElement_get_transform_m92CB3091979234EDC51D449A75CC22C2F9223AD8,
	ScrollRectEvent__ctor_m1A1148AF5CFAEA289C3F017565F6B1261CDB95AC,
	Selectable_get_allSelectablesArray_m1071647E8ED4DDE7162EE56B3D730468D09454B3,
	Selectable_get_allSelectableCount_m2C8D64447141260C734038679940C8D9DB39A6CA,
	Selectable_get_allSelectables_m0B3507A121322D32AC9E8EE45424F84B3653D8AF,
	Selectable_AllSelectablesNoAlloc_m1583EDE9D566FA98A92F1AFC543519E3A8BE56BC,
	Selectable_get_navigation_mA0E5FC6B1D19C2DCABA5C82EC33C49CF7F17103E,
	Selectable_set_navigation_m706D254813B084B60F07980607D7AE43AC44AFEF,
	Selectable_get_transition_mBDC7F9FCA36E707B6D77E2F33FCEFA344A3E5005,
	Selectable_set_transition_m67F9584736EB6891A314C9804489368C430F0F59,
	Selectable_get_colors_mB53E365D02351D4B64084295C4B2A7AF2DEC4750,
	Selectable_set_colors_m0A49ED3ACD6647B7E5A2DA10B3D417E8FE1BE55A,
	Selectable_get_spriteState_m7388F8F08AB8A03CB56516A7C9713733A737629A,
	Selectable_set_spriteState_mE0E2CDA8757045FE0D35BC4D9E827857F64E19ED,
	Selectable_get_animationTriggers_m58213BBD3E4D5B7C8A25F1DAC51F2B06176A08DA,
	Selectable_set_animationTriggers_m564A90FBE85D0F3A5055AEA255E753EF58C2B1D8,
	Selectable_get_targetGraphic_m659A2940226EC644AAFC2D5CCC326ABEE6384388,
	Selectable_set_targetGraphic_m23DB0DF4E5F2DABD50C662C708B4555162171FB9,
	Selectable_get_interactable_m17DD0484DC62DCB4467109488D7A599BC85EC112,
	Selectable_set_interactable_m8DD581C1AD99B2EFA8B3EE9AF69EDDF26688B492,
	Selectable_get_isPointerInside_mB31AB05760CDC4A72B7E5D7B86061C9829BE5DF0,
	Selectable_set_isPointerInside_mF82515A016E440225E31092AC6CB63EA09D71D4D,
	Selectable_get_isPointerDown_m61C9ECC7F52547B6638CD046CD7FF61A7FA1F778,
	Selectable_set_isPointerDown_m02FB181F4C59A8477243C9971AA17CD77A86A70C,
	Selectable_get_hasSelection_m7F81F2A77E32862AE18BB0459A0732275EFFA11A,
	Selectable_set_hasSelection_m9EBB907C29E5BB0DAB3066EFCC728595B125D235,
	Selectable__ctor_m340EDFEA07F025166175C3ECB1BD2EEDD81C8638,
	Selectable_get_image_m88664022F6BC90E7B8D4BFCBA7FE24B48E90C639,
	Selectable_set_image_mE9DDDBE46C5A435F9788E88EEF0187B5E09A30A8,
	Selectable_get_animator_mE0AB180AF3936F681535220F4344FF3016C96C34,
	Selectable_Awake_m55439376D9E09A622C61C4BD7DA413E1E0EFD469,
	Selectable_OnCanvasGroupChanged_mC30124BB26D8462F8E163F33A57388B443A1BBA0,
	Selectable_ParentGroupAllowsInteraction_m1625A67D30524BAEE9D5D6F39C131712268928AE,
	Selectable_IsInteractable_mEF8BE44216120C4200B619E9BEE7ABF608D5246D,
	Selectable_OnDidApplyAnimationProperties_m62471EC7970DF938373D7E63BB1D4DFB74EA7330,
	Selectable_OnEnable_mBE48F9440061AFFCEA53B103F7C7A059AC115FA7,
	Selectable_OnTransformParentChanged_mC802FD6123F88D70845E1FDE93FD38D38315EB27,
	Selectable_OnSetProperty_m9070CBEB5C95931EFC0DA7BCA038461CDF835010,
	Selectable_OnDisable_m293DB718E1101FC77E655E4A2C4F2DE1DBD4663C,
	Selectable_OnApplicationFocus_mE3ADCB53E6FD825F59B51BD0390F0C81AAD8E8F4,
	Selectable_get_currentSelectionState_mD8AC0B7BF3C5AFB574C57BDC81274F621978FABC,
	Selectable_InstantClearState_m8D5BD204B502945CC1AB73A0C45CF8DE199A041B,
	Selectable_DoStateTransition_mE74A03CC2A2DBCA9C07559B168FA6A77FFE57942,
	Selectable_FindSelectable_m332211FC94618A05817C0C62A9C36C60F787179E,
	Selectable_GetPointOnRectEdge_m3E2D149816CB643503988036FEA4E25F914788C2,
	Selectable_Navigate_mF67643CAEFF8AFFB80A1EC743CF6B2C1121556C5,
	Selectable_FindSelectableOnLeft_m1DB05BA9AB4FBED7AAD646526926BCC9BC99E134,
	Selectable_FindSelectableOnRight_m9F76D3B04DD85E9A2C6DC3F1041DE0C9200F307E,
	Selectable_FindSelectableOnUp_m3B25FCB3C7EBEA5A777325A7ECB7985A4B7345CC,
	Selectable_FindSelectableOnDown_mF1715CEA701C504DA775E4A22373881031F851B3,
	Selectable_OnMove_m0801D5433615BD3163659A17B1DB2B23886AF05A,
	Selectable_StartColorTween_m13B3BCF55A09B8C4CD56C25018C93E97F2B51097,
	Selectable_DoSpriteSwap_mDE447BE74A0240AE366AA9C0D9701B2E4689ECD5,
	Selectable_TriggerAnimation_mDA4462FAF2B2DE28945F4AD30E9F8904F4AC4D8E,
	Selectable_IsHighlighted_m889908FCD27411E02267021F8A1B0C72525EF96F,
	Selectable_IsPressed_m2B38EC61FF57A3C4161EDAA8DB4CA5757A3196FA,
	Selectable_EvaluateAndTransitionToSelectionState_mD0648A10DDF70A60B8B707507CC1DBF6A148F9B2,
	Selectable_OnPointerDown_m4425D3C7641AAD2430A7E666F35047E2F3B623D3,
	Selectable_OnPointerUp_mF7B6987EE86DD7079DDA835339A17BCFC6E7A4C9,
	Selectable_OnPointerEnter_m4AEEEAFB92045B8D8794C65890965E9CC8870860,
	Selectable_OnPointerExit_mA288BF802AD6844F51CE19C120DF5CCEBF487929,
	Selectable_OnSelect_m50BA6D8F185CEA3211F9DEFE68AB6439AF685242,
	Selectable_OnDeselect_m43A2F451FC100ACAFA88D67331CD4537994B8262,
	Selectable_Select_mE52B485BB3714E96666F913358BAB9D57588F9A7,
	Selectable__cctor_m6D5E8673B9C3F2CE492ECCF88960CBC03C9D8E51,
	SetPropertyUtility_SetColor_m05C71F5D62DDB2E51E35EDACC0060205B2BA7237,
	NULL,
	NULL,
	Slider_get_fillRect_m35EE2868F52084F9543158A2EAD99476E5C13D9A,
	Slider_set_fillRect_m2CB86D7C94EA17486DACA010B643F9FE308B6AA3,
	Slider_get_handleRect_mF6564572F3D7074E01D17661BD012F5987D328D9,
	Slider_set_handleRect_m572400ADF8F03B8931302F709BB638745CAD5111,
	Slider_get_direction_mEB650B873607C3D4E49EC4AB25EE9CE2554B33D5,
	Slider_set_direction_mD219E6B22DA729C74E1594C8571B926C4A96871D,
	Slider_get_minValue_m4443221B443E357866F07B062CE39944134C794C,
	Slider_set_minValue_mC4D1F7709276A9A418F9284A04799FF767DEDC4F,
	Slider_get_maxValue_mB34C0C9337F5D00ECB2915E8008BCAEB8E7C5FB6,
	Slider_set_maxValue_m43F3BF47C6D7063D80C578FD9B95AD88494203BE,
	Slider_get_wholeNumbers_mF1A52AF2845985E1FC462236783B3E5BE83F9928,
	Slider_set_wholeNumbers_m8A76CC011B30B0281F47F8ED085DDE62EACA0EC5,
	Slider_get_value_m92843A0FEE9FBF1FC5228C7F677E74642D860010,
	Slider_set_value_mA6DC34301E7F76E7FD9C964D61A7B06C95A05D0C,
	Slider_SetValueWithoutNotify_mD60D03926D6D1CBA6F162DE01296B239CF9A03BA,
	Slider_get_normalizedValue_mC839197322275EF1318B6E49B7573FDB30F74D83,
	Slider_set_normalizedValue_mD8E0F3B3EC5CA862BCD1B2AB42DC1CDFCC381A1C,
	Slider_get_onValueChanged_m4DA3FD0F8D7BB838F442C07F7796EEE584D0D4F6,
	Slider_set_onValueChanged_mBFB4D238D7D0B4C686BF47E944A4F873A82939C9,
	Slider_get_stepSize_m55FF421B05B5995454F93AF1D3A5313A6C58977D,
	Slider__ctor_m205FDE3F1A59F98FE8E0E138FA96C107F79BDDB0,
	Slider_Rebuild_mD7FD3AE545912A90D82A84164779FAD30448BDE0,
	Slider_LayoutComplete_mD2E60699C63B82C9164232EA8A2669B5436A2DA5,
	Slider_GraphicUpdateComplete_m2BFE85324EE12F00974D6034CCEBD058911DBC6D,
	Slider_OnEnable_m7B8D2AD29196D07E85830E4D17E01EB7D2151E8A,
	Slider_OnDisable_m3FCB26AAD286DAC967219251AF81A676436A3D39,
	Slider_Update_m522C74C2E16CB8923FFBB2F67A39DC92812A2283,
	Slider_OnDidApplyAnimationProperties_m09393E7CF5C36A5C28AFDF3B767BB8F7B8B75FBC,
	Slider_UpdateCachedReferences_m377F41A1442EBDA6661A3E7E40E92B6D4CD4F5AE,
	Slider_ClampValue_m78647872AACF7C1DADF80CE1355C4FA72E17F91E,
	Slider_Set_m8407F7245321EAA46ED3E5F6CC9B2F04B9A2FDD5,
	Slider_OnRectTransformDimensionsChange_mAD826A7F943BB26DA36F11310D286422ADA5E69A,
	Slider_get_axis_m1EBD05C9A3C34B1859FEA0192B4569AD45EC7DED,
	Slider_get_reverseValue_mE0B463C7174C203F870866456E0EF2AD39D8E834,
	Slider_UpdateVisuals_mF0D5A86EE4352DBFE092CA49479F1AAD9212B00E,
	Slider_UpdateDrag_m704D10BF17D39858193BDD4E946558C520DAB304,
	Slider_MayDrag_mBC1025F6079EF65A36594E8BC2C0647A7F809576,
	Slider_OnPointerDown_m37122B2271F5C26BA2A36ABB70D07B8620A0961F,
	Slider_OnDrag_mC19612CC7EA3D02F3D338ECD6C534B7E402A3856,
	Slider_OnMove_m3FAB8435346C2D8DBA59AB5CC59D569B06CC1500,
	Slider_FindSelectableOnLeft_mB3F6A127C5B758ED1CB9E850FEE3BBE0D4CF3F85,
	Slider_FindSelectableOnRight_m109193687A0ADDDF1BA1AEA2E6B49A1399AF18D4,
	Slider_FindSelectableOnUp_m3523976BA934C07123A7E28CCE8302CAF321115A,
	Slider_FindSelectableOnDown_mC50638D5F0EE1A5D2E9322F0C7F88ED33F6161F1,
	Slider_OnInitializePotentialDrag_m509A0273020AF65D2DA0DB25EC35FDB748F75DC9,
	Slider_SetDirection_m84FCDE9EB319203D0C5C4069A1BFC40447760101,
	Slider_UnityEngine_UI_ICanvasElement_get_transform_mA7875ACE1B6F89DDBF76529E25C1E122F027C33E,
	SliderEvent__ctor_m5FD31BB6BB3FAF583C0A555FCF3733EAD6A6C319,
	SpriteState_get_highlightedSprite_m5D24B628AB2E4DEBF67E094CCA059BDADAB952BB,
	SpriteState_set_highlightedSprite_mEECDB7C62DE0C6A0B2A7D5D7ADF54EB8CDDB20B0,
	SpriteState_get_pressedSprite_m89052B1818D1659DA7E594F218485F1DEB8128BD,
	SpriteState_set_pressedSprite_mD01568B87B1BC1374CCFB5CD190D7CD62A6FDAA3,
	SpriteState_get_selectedSprite_m5316836E91F7EB454E953CADD439FF69AA198BA5,
	SpriteState_set_selectedSprite_m902ACABEC203C0A2408B4ECD7B74C10DFE7BB340,
	SpriteState_get_disabledSprite_m6BE5A2231E20BE1600328082B4EFE53EE7F3E12C,
	SpriteState_set_disabledSprite_m624499C245DC34D314FF0326FE5ADCF35DA28E27,
	SpriteState_Equals_mAF58D9F36662F5A8196071690175AAFCC4506653,
	StencilMaterial_Add_m84AA68A912ABDADC5187B9E9BC0E4FE6DB4E2220,
	StencilMaterial_Add_m4FBC1C2732C3B161EE38767ABE2020105E0BF7F4,
	StencilMaterial_LogWarningWhenNotInBatchmode_mDA6F8CA72D4AAD9C9B480AD2AB4FEFE0C73CD8E3,
	StencilMaterial_Add_m7BF719F0507970D16D11F47019761391ACE55766,
	StencilMaterial_Remove_m828D3D85F213AD5B3E4FE6A230981E9115007412,
	StencilMaterial_ClearAll_mB1977688C5675CB7C32AD21537795223742B7084,
	StencilMaterial__cctor_m18A5D42EF758C1549A3DA3C6871A47042E5E547B,
	MatEntry__ctor_mEB63E7AA0A179AF5EE93EE6DCAC4E91BFAEF2CBA,
	Text__ctor_mE28BC6E42B4715F23401A9379C9681867A0631C1,
	Text_get_cachedTextGenerator_mFC242539F7380F54696D431B126B69DC4EFC821E,
	Text_get_cachedTextGeneratorForLayout_m409B96DB358F900C531F543CE351B02B0974A077,
	Text_get_mainTexture_m8EF8E897193467EF8B839C99B5F388AA3241315D,
	Text_FontTextureChanged_mD716EBECCAFA43F8D01D90FF9F869C69E484A763,
	Text_get_font_mBF98ED39D5C5081AF14A64170EC3391D206CCAFD,
	Text_set_font_mA0D2999281A72029A5BC7294A886C5674F07DC5F,
	Text_get_text_mE71474D219ECCE472FD9A08679168E859577A3D1,
	Text_set_text_m6872BDD62D0904C075F06A19CF5AD96A2B2FE23F,
	Text_get_supportRichText_mE5B61670099BB2611BB60D84ADB72C9A54BAC68B,
	Text_set_supportRichText_mB4DB141150AEBCCADEFFF4EC7A799F85FD075265,
	Text_get_resizeTextForBestFit_mA4EEC57C4C188C1598187D1E11A83B950883B011,
	Text_set_resizeTextForBestFit_m1376BB9FDBAC5571E0F24564E22391AC8EB89A35,
	Text_get_resizeTextMinSize_mAB17F2DA673C7A3860E6EA0746BFC0C919D5A659,
	Text_set_resizeTextMinSize_m1DC5160514ED872A8C572024A94D7EA9D6357655,
	Text_get_resizeTextMaxSize_m7B61DCEEA4D801C4B8149674B27DBE99098A38E3,
	Text_set_resizeTextMaxSize_m25EB2C9302AA9354237A2F56BB3E019192C6015B,
	Text_get_alignment_m01C4D0437DF8A2E05BE4489779A8BEF231A2F2CC,
	Text_set_alignment_m9FAD6C1C270FA28C610AB1E07414FBF96403157A,
	Text_get_alignByGeometry_m68F41E942D6BC7AF8F134B3CCDF039A8D3D49DC3,
	Text_set_alignByGeometry_mB427C41097943370E11579A3DA822A3295836CE2,
	Text_get_fontSize_m837C0618E78D0FDA972D11DDE3015DC888E93993,
	Text_set_fontSize_m426338B0A2CDA58609028FFD471EF5F2C9F364D4,
	Text_get_horizontalOverflow_mC909085F76EF49D62A70A8E503C5BC14C30176F1,
	Text_set_horizontalOverflow_m10AAFBA65FD7F4B1934B5D628B3E70D75D02FFD6,
	Text_get_verticalOverflow_mEC72BD123A8B12278F6F7B89D29EB9D93D0A97FD,
	Text_set_verticalOverflow_m72A544DEAE0EBFCCBDE8174DF4C10C903DA8444F,
	Text_get_lineSpacing_m124405CE023E0E23D9040BAA84318408248DF9CF,
	Text_set_lineSpacing_m36CE565189BAF89DB1DA1E0DE5786521D4763D0E,
	Text_get_fontStyle_m7684B5FFE1DC8237FB573A012B482DDB04E3BA47,
	Text_set_fontStyle_m5ABEF66BFC88E7E0A950E2817E4978FF472F6C1D,
	Text_get_pixelsPerUnit_mC48AE94D40662DE114A72B870DF77BF7B418925E,
	Text_OnEnable_m183EE6D534BE840F16D23DD36A0C1619AFC905F8,
	Text_OnDisable_m94F10EBC54572DCD1D3DB7B6C7CBEC8CBE8AF60E,
	Text_UpdateGeometry_mEAEFCA5F05F983DC984FA1497A905A4B2DCF132F,
	Text_AssignDefaultFont_m475A3C848C9F8ADFBD5438E936E81B618FB4B398,
	Text_AssignDefaultFontIfNecessary_mF5167C211C87E6DD62978C938F6521B287F371CF,
	Text_GetGenerationSettings_m620E0E5AFB30E3331A0371EB2361F587BB0A1E0F,
	Text_GetTextAnchorPivot_mD0734509B028EC6E42800FD73A6CB8476EDF0150,
	Text_OnPopulateMesh_m6505569424B120C338EAF6840893E38530185ECE,
	Text_CalculateLayoutInputHorizontal_m42B464C2F7C1AE5A0B1311FC5E6BECE1C6EEAC5C,
	Text_CalculateLayoutInputVertical_m7A76D597BFFF1C68C3FEA03E26EE440B9A67E532,
	Text_get_minWidth_m8C6D60E991BAABD25859D3C04AAB6107BAD4F139,
	Text_get_preferredWidth_m1624ADC4EB2193885E4EA35D44E6A80C12A436BC,
	Text_get_flexibleWidth_m97C2D396445B82CD45260F21CD15CF6F6B279A4A,
	Text_get_minHeight_mB6B7E5A4426313C18A7F2CD28F3A7B5CE2DAA6F9,
	Text_get_preferredHeight_mF95F444557BFB576AB0E1E876146E03174DA058C,
	Text_get_flexibleHeight_mC82DCE442BB670E3AC683BA1C660810FB4936FC8,
	Text_get_layoutPriority_m99DD053FCD9F30FFCA40937B2DFBF796B67CBD12,
	Toggle_get_group_mE182279EECC97BECAFFA919AA08E4D5B6E9C83FF,
	Toggle_set_group_mEE85FE3AB2ACFF9056DA613239DBACECA588507B,
	Toggle__ctor_mA84E212B567F21B617B480F90BC335B602523400,
	Toggle_Rebuild_m101F36A2CD0C4ABD7BAF41262493A0D6ED0B0D3E,
	Toggle_LayoutComplete_mD0D33BD5078F2E190A61E306A4CF88E45F80C473,
	Toggle_GraphicUpdateComplete_mE1636C76DF59E2B1483BB62FF0982EAAEE1185EA,
	Toggle_OnDestroy_mCC155BA7A5FE311B49536F9C904AC74EC6282E68,
	Toggle_OnEnable_mA9EF315CBA63011213BBB13A9CA1EB84147DAF0D,
	Toggle_OnDisable_mB32FFD1AAE48A56205E782BC041A5EC86B66B536,
	Toggle_OnDidApplyAnimationProperties_m1D3922CE86EA2AFB38F3204935BC15DCD534BFB3,
	Toggle_SetToggleGroup_mDD819C46310559ADC2346EAEE7BE3BEEF51BB1B1,
	Toggle_get_isOn_m89A609E936CD67F460E336CA8E03C4047BFB6619,
	Toggle_set_isOn_m61D6AB073668E87530A9F49D990A3B3631D2061F,
	Toggle_SetIsOnWithoutNotify_mF5B19F1767B9EFF02335E41D3D2DC678642170C2,
	Toggle_Set_mA2CCB1FBC23519004E2F47CA0F53CA6E1B368DDE,
	Toggle_PlayEffect_m728310FF62E7251958CC8D4016C2435AAC9DF0A2,
	Toggle_Start_m3E085820286E51F69BD848C1EA1FCA7DFD07E3E7,
	Toggle_InternalToggle_mD36F2575F4B2E26641C0E24A73B277E0C8BF25A1,
	Toggle_OnPointerClick_m2D0D693EE40BDC56482DA6982C3CB42DBACD98E3,
	Toggle_OnSubmit_mCD303693EDD107D676D55CABA07BA43F9328C9B2,
	Toggle_UnityEngine_UI_ICanvasElement_get_transform_mD7C00596C9A48C3A7C40E285DFFF9C2B255221C2,
	ToggleEvent__ctor_m8983544B67193810F8BAA820B2C408251CBEF145,
	ToggleGroup_get_allowSwitchOff_mA6724BF0B3965330FE892FB9E144D761ACFA7364,
	ToggleGroup_set_allowSwitchOff_m30C71C353E740F9B3F9641689A4ABA4AB8BAC9C3,
	ToggleGroup__ctor_mED87CABB1682380A925DCC8FA41C739ED6ADF3EE,
	ToggleGroup_Start_mA5D0C8F6437723E98C53149716EA28C7984BDDA7,
	ToggleGroup_OnEnable_m5679531D85D1CAC371A71AC5B1E980248A01F038,
	ToggleGroup_ValidateToggleIsInGroup_mA9CAD4C4345BE7AE18351AB4F31D1574A900280C,
	ToggleGroup_NotifyToggleOn_m0676292BE46CBE477DAF139D9DABCE5DB72F7F45,
	ToggleGroup_UnregisterToggle_m6A07803166E901CCDE4F23FCED1BD76CBB002307,
	ToggleGroup_RegisterToggle_mADE82548BE13A13E9FDD00F488471D3416A97214,
	ToggleGroup_EnsureValidState_m5A8B88CF91EEB8F7E7DECB87261EAFF5A556778B,
	ToggleGroup_AnyTogglesOn_mCE714D4DBDD9CF56D41C830719FCFC24008C1700,
	ToggleGroup_ActiveToggles_m04CAF25D2C9DE5F310090D63B9841963954BF2BF,
	ToggleGroup_GetFirstActiveToggle_m07251AA447A7F06B082B685CD44F5C0465A323BA,
	ToggleGroup_SetAllTogglesOff_m770745001C6B4553805F3E333084F5DFCB08B78F,
	U3CU3Ec__cctor_m878A0225FF0D038AD88F4E89CB4DACC057132A26,
	U3CU3Ec__ctor_m71A650578043739A4182051BA48B2792FA8C287F,
	U3CU3Ec_U3CAnyTogglesOnU3Eb__13_0_m060E207CF61E772B8A8C9B5262EA8867E086B397,
	U3CU3Ec_U3CActiveTogglesU3Eb__14_0_m9567251B779C881881B16EB6ADEF9D7E71CD3B0D,
	ReflectionMethodsCache__ctor_m2BBCC7AC457DD7CB3836B01B5307F8B6285CADB3,
	ReflectionMethodsCache_get_Singleton_m77EFD7AC0043333E09987BD601113B810453C42B,
	Raycast3DCallback__ctor_m8F06E2216CD1CCB88B9B94197226CE3F1EC67310,
	Raycast3DCallback_Invoke_m9CDA4EB17B5A46853DEBC894E610B5C8FCE23415,
	Raycast3DCallback_BeginInvoke_m2FED0375CEC6BC3E6545ED6A19E88D7913AB2E6F,
	Raycast3DCallback_EndInvoke_m4DB350E572F75C4D0C92DE38EC9B8B0368464871,
	RaycastAllCallback__ctor_m971EC206409480AB580913F8E1E7E9850DC0DB03,
	RaycastAllCallback_Invoke_mB30319766AF5F0D5145A6C0AFFEA829A31A5D4B7,
	RaycastAllCallback_BeginInvoke_mFD486859C455F0E5198BC58E3505CD12ACDAC0AA,
	RaycastAllCallback_EndInvoke_mBF07366FEF9002D0A44A4E8982BA201699C864CE,
	GetRaycastNonAllocCallback__ctor_mBF3B05A56B530A13FFE0C8F66A12A94230598787,
	GetRaycastNonAllocCallback_Invoke_m36411365E9622819DAB93D0BB0F169FEE99F07D9,
	GetRaycastNonAllocCallback_BeginInvoke_m381F75A2BBE062F5DFB3B910D3B3066CA512D024,
	GetRaycastNonAllocCallback_EndInvoke_m0A8CB6C1DBFAD95A2E3AC7686E161E1D6057F52D,
	Raycast2DCallback__ctor_m4C15F69C429322DFC4BDA9A9E99A500D4C9718BB,
	Raycast2DCallback_Invoke_m36A82ED39B674FC0D8D80D62948B729CC244C366,
	Raycast2DCallback_BeginInvoke_m2135AC8F9004783AB5F6E92DFA11B187CBBC3DE8,
	Raycast2DCallback_EndInvoke_mC51CF38067DEC9BBB94782FF1BB129A19EC602C4,
	GetRayIntersectionAllCallback__ctor_mF6F153CE75C4728D934A766700346C408088ECFF,
	GetRayIntersectionAllCallback_Invoke_m917AA4108EBDC724AFEF39BFD06A586B7461F497,
	GetRayIntersectionAllCallback_BeginInvoke_m9C824E08C6261803AAE7A1B39D7525A4B748679E,
	GetRayIntersectionAllCallback_EndInvoke_mE9CE6394D3F2C7C56DE578882666ADBFFC729965,
	GetRayIntersectionAllNonAllocCallback__ctor_mAF9F997ABE4C3EECD5A402BAB7CB30B19CC50F9C,
	GetRayIntersectionAllNonAllocCallback_Invoke_mFAA36E9AF362DC72204EEF53B28DBFC3367D09A7,
	GetRayIntersectionAllNonAllocCallback_BeginInvoke_mE93D1099CC919A75041D25C692B4BA8FB1F66061,
	GetRayIntersectionAllNonAllocCallback_EndInvoke_m0C4A807380DA32E16F797AF910C2A69C06D1352C,
	VertexHelper__ctor_mE8DE438637116EA7AF8180E10E7641FD00DB64A5,
	VertexHelper__ctor_mE42FAE63F4A3200C38ACFDD9C3F601FDC7E258F8,
	VertexHelper_InitializeListIfRequired_mC7180B010A6DCC7C1115A33D29B8E00B92DB2542,
	VertexHelper_Dispose_mAA41704ED960A368DA8BFB8D1506A3969A033653,
	VertexHelper_Clear_mB19E51AD5AF1C04CB2C6E6A272D032D651EC40F5,
	VertexHelper_get_currentVertCount_m45BFEBD6FCB7DF3BF9F76946D6002BDC58B173A4,
	VertexHelper_get_currentIndexCount_mF409C3D4A6786E64AC4E8EC0D6D97E27597A900C,
	VertexHelper_PopulateUIVertex_m48FF05C38D56529E18A360D629F4842BE5D050BE,
	VertexHelper_SetUIVertex_m539A518867E7872E0893715AD372DC9A06334FD9,
	VertexHelper_FillMesh_m524F00287F0A0C7683E2CC7768A77B5755544A0E,
	VertexHelper_AddVert_mC7596EEC59384AB7BFD12CA6F8350ACDC5BF5E56,
	VertexHelper_AddVert_m5765AC8F13C86709B2EFEC613D492963BE1E1198,
	VertexHelper_AddVert_m2187D76DC2CE7E9AF69280424660739858901287,
	VertexHelper_AddVert_mB65D778E8E3C6916CDFF5382208890882C3031BA,
	VertexHelper_AddTriangle_mBA2504734E550C672A33168BE119D76D92C788A4,
	VertexHelper_AddUIVertexQuad_m6AC21081F2A5A48D22BC3497E527D0A9AB8278B0,
	VertexHelper_AddUIVertexStream_m213E27491ADDA2C603D40730E34F3AA6C5E7757D,
	VertexHelper_AddUIVertexTriangleStream_m29A217271BF2B3D3D60B7CBDA4114C7BB40C2841,
	VertexHelper_GetUIVertexStream_m87D56EB5559CCCA150F68B1DD660FF4154CACBCE,
	VertexHelper__cctor_mEED5FDE0E235482F4F4A551E114358266128E0CC,
	NULL,
	BaseVertexEffect__ctor_m9458015A3EDD3D42F821F8608D0E595B85A70B6C,
	BaseMeshEffect_get_graphic_mE8226BAC46FDB49681BEAD2DE8A4EE3CEC18FF04,
	BaseMeshEffect_OnEnable_m74592558CD0F70DC35EEFCBC1E23F84493CD77F7,
	BaseMeshEffect_OnDisable_mE005F7A15BFE7127D274717C3C482561481D3603,
	BaseMeshEffect_OnDidApplyAnimationProperties_m981407A626B008820D9554F3778DAA8E7959451E,
	BaseMeshEffect_ModifyMesh_mD98C4CF227E0EF63BD031824AABC5F3C1AB7BDEC,
	NULL,
	BaseMeshEffect__ctor_mFFF23FD89B32150DAC512C556A1CCF563D062427,
	NULL,
	NULL,
	NULL,
	Outline__ctor_m1E8EF7C85B52E0DE3D67506C8F7C118A1E2B3552,
	Outline_ModifyMesh_mC6D402BD2D65E27A163B68676F3102AF03BFC4C9,
	PositionAsUV1__ctor_mE24D2DE5032BCAE8B065B8D4CAA90BA3256EB382,
	PositionAsUV1_ModifyMesh_mFE5C4211D991E7A292A29DB85BFBAF3C31282F90,
	Shadow__ctor_mDE7F89B477692F7FF0CCE6B8CE01A63D9942291E,
	Shadow_get_effectColor_m6E7751BB8792C85BE9DAD0D133D787317D9CF59B,
	Shadow_set_effectColor_mCCC5DB6B7D09C5DEE0C677DEB3B9B0C578F05AF1,
	Shadow_get_effectDistance_mA87EB50066AFEBC13C69D27376E50033930FA58F,
	Shadow_set_effectDistance_m5E7B565C41CF2A8C84EC98319ACBF5C8E1FE47DA,
	Shadow_get_useGraphicAlpha_mD2A88F78B7B2E25905D1750788B0DFA3082AC616,
	Shadow_set_useGraphicAlpha_m70CCAE5D643B2373A5ADC8BD04031D3CBF0AF722,
	Shadow_ApplyShadowZeroAlloc_m010AE345D731FC53595A62CF8D0B401C2D6F4B58,
	Shadow_ApplyShadow_mB615BBD368431C63B1407CAFD7DD32BE023E543E,
	Shadow_ModifyMesh_m7201FBFE56F97B440215E92064BFC59F00ACA9C6,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ColorTween_get_startColor_m9E33FB5C5F76BCF49A3B20201CD8006DBFB46012,
	ColorTween_set_startColor_mD22349343421BD44F0C31E537718ED53BE4850DA,
	ColorTween_get_targetColor_m240A7018BDC3B44AB44BA674AA16C39960BC23FF,
	ColorTween_set_targetColor_m7D8E74B32AC3A9C17C3192096003B12A1500D749,
	ColorTween_get_tweenMode_m06B83FB6E45A807F83FDD762A8241D478FD13F8B,
	ColorTween_set_tweenMode_m105EEB49F6632D6D105C63DA9919385233A5D4DE,
	ColorTween_get_duration_m40D8F08C13FF2FE7583746934C6A017A93398548,
	ColorTween_set_duration_m1C278AB5A90B5C108CEB4870CAC90A9A9EAC19CB,
	ColorTween_get_ignoreTimeScale_mEDB15A4ADE3A0B9487D240964A7571247F974708,
	ColorTween_set_ignoreTimeScale_m060FF3CED06F73EA1F555A37999D61DC58F99927,
	ColorTween_TweenValue_mF5CBA9BDE7F73E47F9CF26DC4EC2419694049860,
	ColorTween_AddOnChangedCallback_mAC2856A154604B4B6721DAC185B819A98D6F7438,
	ColorTween_GetIgnoreTimescale_m679C83012235779A37DCCD0AA75CD6B0DAE5BCFA,
	ColorTween_GetDuration_mC40D6776769FDB79C7ADC42D59F059A2A9AE2F66,
	ColorTween_ValidTarget_m1D7A682CE00048FAF1A3BDD55EB76F44C9122B4D,
	ColorTweenCallback__ctor_mFEB49A6A1ABACFE2351A63060F786B762E2DC6B9,
	FloatTween_get_startValue_mCA121483CCF4C8F10991BB3306E3F2769EBB3A3C,
	FloatTween_set_startValue_m43B55D74B7B34D9C32439D6004F306BFA18E4A1A,
	FloatTween_get_targetValue_m6EFBD9EAB206F145959832269DC24C4B68FEE6B1,
	FloatTween_set_targetValue_m4AE44CE862797E898CDE00A1B7D6A33CE0AFDCFB,
	FloatTween_get_duration_mB1496D38A618FF8282205FD3AA14CA9C6D76454D,
	FloatTween_set_duration_m40E10A7B796B4B54FFB8DA3889B09557BEC98456,
	FloatTween_get_ignoreTimeScale_m6F6BDCBD59C19E68572370F9FE3D7373B4212B3B,
	FloatTween_set_ignoreTimeScale_m09041A4110040F9C86D24E1B4DED6E6B7FB206A8,
	FloatTween_TweenValue_mE51344369BDDA58E9C3AEC62E1B1C1AC0349278E,
	FloatTween_AddOnChangedCallback_m13B1FFCAD78C7E690E70704311B20D5BB67D8224,
	FloatTween_GetIgnoreTimescale_mA2463285D4524B70A46776FC60C4F939B3BCD045,
	FloatTween_GetDuration_m3E981D91F15C36ED6F241117665E703F2BD2A6D4,
	FloatTween_ValidTarget_m36EABC84C8FEFF79EBAC8E9C3C7A394F1377E311,
	FloatTweenCallback__ctor_m3BD06E1999E88B4BAC7627A04B37300331CA210A,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	PanelEventHandler_get_panel_mA34094B6004584B051A7E9554DCF7CE3C18E2642,
	PanelEventHandler_set_panel_m30302AED739E083827B25651029CB2F1563D2988,
	PanelEventHandler_get_selectableGameObject_m327CAA6DDDE5191CF001B5FED18EC8857E6915FC,
	PanelEventHandler_get_eventSystem_m4AC951AFE51C13E82DF39BD27DDD6BE9258535A2,
	PanelEventHandler_get_isCurrentFocusedPanel_mE9616A8F71808D5507809F0A6A5521DB25564587,
	PanelEventHandler_get_currentFocusedElement_m7D1860148CB99DE9BE6923146289E97A6DD02C18,
	PanelEventHandler_OnEnable_m75610BA601D59B6BCB212DB140580859B2C7B777,
	PanelEventHandler_OnDisable_m7174BFEA2A756C36F810433F9D285D9D0B464CE4,
	PanelEventHandler_RegisterCallbacks_mDA52FF8A14161DC03A7B09826079F60AEDBAC565,
	PanelEventHandler_UnregisterCallbacks_m93894C7D3D238507B771066CC0025C98816D309C,
	PanelEventHandler_OnPanelDestroyed_m1E7871C24E171C75D64BA6F6FB8F70EF7B345366,
	PanelEventHandler_OnElementFocus_m5EF528DB20E06FC6EBECE22E71F90B47660041F1,
	PanelEventHandler_OnElementBlur_m7FB60FF1D38D571180F0FB9B740DE531E9F43A24,
	PanelEventHandler_OnSelect_m01E0EBF7AF99013F9D7B0EE96F52CEA3B2C6FB68,
	PanelEventHandler_OnDeselect_m6AF499D5E0F1F162B5BCEA063D4D285E086AF663,
	PanelEventHandler_OnPointerMove_m753962E17CA7F9176FF96F765527BB093ED1F058,
	PanelEventHandler_OnPointerUp_mC03E88905E10E8730E8211810EC98927A3B89F96,
	PanelEventHandler_OnPointerDown_mB46E8626C4F1D143AA495ABAF6B5D57301D3C303,
	PanelEventHandler_OnPointerExit_m2A57890B6822CBB0D51D61FEBA91B32FE269B1B4,
	PanelEventHandler_OnPointerEnter_m0627B36F32B2C7D59783CF07C7781AA66F202C70,
	PanelEventHandler_OnPointerClick_mD65AC5B586A941C8D235326E29A1DDE0B07B5D14,
	PanelEventHandler_OnSubmit_m56C7D96593E7DC7B561AE24B741647431C75E84D,
	PanelEventHandler_OnCancel_mAC960731F19FB4522FD960CD51790361A9F26C8A,
	PanelEventHandler_OnMove_m3103CB2983C10B1E721004FDE9EAAF9E8C598DF4,
	PanelEventHandler_OnScroll_mE4812293B72E54A268D49C31845DF17687E68DA4,
	PanelEventHandler_SendEvent_m380CCD38E3E7949B65EE3001067AA9548B19B9F5,
	PanelEventHandler_SendEvent_m8A731185591EB81DC398B72D4C081970A89D421B,
	PanelEventHandler_Update_mB47B3B1C74E2FE6EC56B7A6861D71DACD2FFC733,
	PanelEventHandler_LateUpdate_mF537C32BA3237501B31627DEFCED28C934DDAF14,
	PanelEventHandler_ProcessImguiEvents_m2C97483500700868379D4AF34C85D9CFE1EEA63A,
	PanelEventHandler_ProcessKeyboardEvent_m665D51CC6E7CDE6F1C130A49B89CD67C29A6F450,
	PanelEventHandler_ProcessTabEvent_m57753FE73E0530973D9A9EF0D606B773CE16A337,
	PanelEventHandler_SendTabEvent_m00C849500EAEFC5F544C811A1F6D6496EDDD3B8B,
	PanelEventHandler_SendKeyUpEvent_mE718C7FBA4F4936F9D3B0C3FB3E3629288AD0F44,
	PanelEventHandler_SendKeyDownEvent_m41EE3C5B80C20F66A3056EC839FC60BCBACCE6B5,
	PanelEventHandler_ReadPointerData_m655D52851C00124DBA14106CDBE322B7AE2F9372,
	PanelEventHandler__ctor_mF90AF37F849E48687B1A1D3730E952A379A62C5B,
	PointerEvent_get_pointerId_mD4E22379BC076C3D75E103BC55ACFBA81BEF59BE,
	PointerEvent_set_pointerId_m1BFCE40A5AF978254069B94292CADC4B39CB4E6B,
	PointerEvent_get_pointerType_m6AB451260BF46DEFEFF3607498093DE56F8CF537,
	PointerEvent_set_pointerType_m1BD8CE6C878A3FFB6441A60302634515525E1050,
	PointerEvent_get_isPrimary_m5AF6EA62872F5E02DF4E88BCB078CAEDDB0813A6,
	PointerEvent_set_isPrimary_m90DD30E4F4B1641C8F800C4EF04DF078F7F37D2E,
	PointerEvent_get_button_m72275A3B433F9433FAAC939B5776E908CBAC488C,
	PointerEvent_set_button_mBD5A4ADBC9FB28B3D78019091D0279C18AC5F248,
	PointerEvent_get_pressedButtons_m809CC87D0F8B424423079C5DA3E4EFC87E829F02,
	PointerEvent_set_pressedButtons_m0B5199481431978AC07CFEAE090907BA5E70FA68,
	PointerEvent_get_position_m6CAC16F2273B6222BB18583B11B85BE3ECA8BB45,
	PointerEvent_set_position_m24E0958379E88BDD173E563CC00B2523E77EE051,
	PointerEvent_get_localPosition_m06C5B58432C1E806B885F67BA0FB8C90EAD71793,
	PointerEvent_set_localPosition_m759E75018F6729AA6744C93A57907ACEB390727C,
	PointerEvent_get_deltaPosition_m185F5E48BD0879D48BADC520D64DDD68183B83D2,
	PointerEvent_set_deltaPosition_m12E7B298A9EEDC1D623E72EA99758204F16B4A11,
	PointerEvent_get_deltaTime_mD95C5D61E308ACD9D91FD202D274C2DF94780940,
	PointerEvent_set_deltaTime_mEECBC843D749F429B1F72559BB488BE17BBFC3E0,
	PointerEvent_get_clickCount_m1E9AE0EF81D8BC131012E0DEE2C4E169C8B1EE06,
	PointerEvent_set_clickCount_m87C44B61E5E2154178CD4D4CD931C2C463971B89,
	PointerEvent_get_pressure_mA7FA9AFBE607289D1A785889E0E2C8CEB705EDB2,
	PointerEvent_set_pressure_m1D036CF601B6EEB97DBDB8DB75F0923D39303FD9,
	PointerEvent_get_tangentialPressure_m585887790F2A05742888E412B19E0331C4402320,
	PointerEvent_set_tangentialPressure_m7ADB233CDA686FCB10A995F2A6826EE5F54AB36D,
	PointerEvent_get_altitudeAngle_mFDE6773840B002EC90E34041817D96BB8F27A3C4,
	PointerEvent_set_altitudeAngle_m8B963C51BB5DB8A14A943F4B1BEC39B175ABABEB,
	PointerEvent_get_azimuthAngle_mDB91EA27BE4126C4582A66DF75CB8012DE16254B,
	PointerEvent_set_azimuthAngle_m404D73D4BAF6C658A52B29DD2C1D5FBEDC174139,
	PointerEvent_get_twist_mDE5D41083F1E9237B3B852B4E8EA778E2C5D2AE7,
	PointerEvent_set_twist_mCA0ECFFE48E1771A1540212CABB34326C7AD6B5D,
	PointerEvent_get_tilt_m424372381952C6375821AE5652E0B61EA26EFB32,
	PointerEvent_set_tilt_m48167AF2DE4D07C779878FBB802ED4995D2176D9,
	PointerEvent_get_penStatus_mEF440062B56450BE46F12A8CA949CE0C919F7594,
	PointerEvent_set_penStatus_m239C34E387BA4214C671F7F5E521E9840CBF9A89,
	PointerEvent_get_radius_m2C6907BE1B20DE289E3C166F45FBBCEEAB095F32,
	PointerEvent_set_radius_m387840E4830548F1B1DA865A5A062062D86590EC,
	PointerEvent_get_radiusVariance_mFED4A22BC0C0667DDC74F6046046A5DA315F4CA2,
	PointerEvent_set_radiusVariance_m2627A414E6EFAE8132E2B4FBAC008D830CF0458D,
	PointerEvent_get_modifiers_m31E21D875E7EF1A47DB29878AA76698B0047BD6D,
	PointerEvent_set_modifiers_mB339D7800998DB09F5D8B47B7DDD365897FD61C5,
	PointerEvent_get_shiftKey_mB459C1F6FA17DA9FF904A67473A19A1B22970631,
	PointerEvent_get_ctrlKey_m6EEB9C3A61C998C00946B424121C7BB32CDA6BED,
	PointerEvent_get_commandKey_m750005DB9507733FAEE22D4DE58F28C11FD15DB3,
	PointerEvent_get_altKey_m6306F34C315A6BF2B2B95448657A812817AE2B4E,
	PointerEvent_get_actionKey_mED4C6D96CBEE2F84F52354EFB3540A5759A47CA0,
	PointerEvent_Read_mE5A2B332E857E3264562FF872625FB5ACE2E3248,
	PointerEvent_SetPosition_mBFFE588EBFBDB353A634B4D6D544291073916B88,
	PointerEvent__ctor_mE689E41BEA012A2914C799FECBCE48F7A58FCF73,
	PointerEvent_U3CReadU3Eg__InRangeU7C90_0_m46008DBA5EB0F3B7C13C191A22B393A0046E2F7A,
	PanelRaycaster_get_panel_m9D8D3E52B0D7A2E4F71B997CC95FB8C808395B85,
	PanelRaycaster_set_panel_m840C66DD38B96603B01E8FAA09C74CA1A67E602C,
	PanelRaycaster_RegisterCallbacks_m840C71BFC5351078CB6BE82C8510F596DC55616D,
	PanelRaycaster_UnregisterCallbacks_mD26ACD360F0C27CFB33A824ADE371742853D66F6,
	PanelRaycaster_OnPanelDestroyed_mF761BC7FD349DB95EBBD8C8D55B404300E5D8AF2,
	PanelRaycaster_get_selectableGameObject_m26B496BDA7A92AD0C66B4209171B56321308A628,
	PanelRaycaster_get_sortOrderPriority_mBFB2EAC6C4F13CAA1E1723216A0B624B7652EA54,
	PanelRaycaster_get_renderOrderPriority_m5E21F65FA1954268DBE3862EB754B20A8B48BE8F,
	PanelRaycaster_Raycast_mFD63FF3E65B14E412D6CD21A3A9455416CE5F895,
	PanelRaycaster_get_eventCamera_m5CDBA1FA81F8BB62020925C81617830897793A2B,
	PanelRaycaster__ctor_m1F8C36B6C6A4A92394FFB160E6A084F8FA833F6C,
	AxisEventData_get_moveVector_m7979B5CF62B6B3B0C5F2DA8B328C499ED80ECC41,
	AxisEventData_set_moveVector_mC744F8B3519A6EE5E60482E8FB39641181C62914,
	AxisEventData_get_moveDir_mC8E219BB19708AC67C202C860DF2E6D08C29B8B9,
	AxisEventData_set_moveDir_mD82A8AEB52FEFAC48CA064BB77A381B9A3E1B24B,
	AxisEventData__ctor_mD9AFBD293F84F7032BAC2BDCB47FF5A780418CC5,
	AbstractEventData_Reset_mC3FF13B6FB1012E8FAB00250AE8CD2E1975EF6AC,
	AbstractEventData_Use_m5DBA1B649A757E09ACB14C3632998231C03795B8,
	AbstractEventData_get_used_m0C95B1F392BD74E99F3AD87963647AA060EE5DDF,
	AbstractEventData__ctor_m3D5B26D1C8BC7ACDDF16F505CF7AE273B54584FC,
	BaseEventData__ctor_mE51C4DB618D8661AB2527EC5DE4D563D2284F558,
	BaseEventData_get_currentInputModule_mA46B583FC6DAA697F2DAA91A73D14B3E914AF1A5,
	BaseEventData_get_selectedObject_m0642DE5E08D7CCC49C67D66B296EEE060E357CE1,
	BaseEventData_set_selectedObject_mF3EE53D700B0EA9444D1D7FAF0FB234B4D88A884,
	PointerEventData_get_pointerEnter_m6CE76D5C0C36C4666CDDE348B57885C52D495A4B,
	PointerEventData_set_pointerEnter_m2DA660C24CBDE9B83DF2B2D09D9AF0E94A770D17,
	PointerEventData_get_lastPress_m46720C62503214A44EE947679A8BA307BC2AEEDC,
	PointerEventData_set_lastPress_m0B9EDFBA95B410FBD8CA2A82306ED3EA6696AE64,
	PointerEventData_get_rawPointerPress_m8B7A6235A116E26EDDBBDB24473BE0F9634C7B71,
	PointerEventData_set_rawPointerPress_mEEC4E3C7CD00F1DDCD3DA98DA5837E71BB8455E3,
	PointerEventData_get_pointerDrag_m36BF08A32216845A8095C5F74DFE6C9959A11E96,
	PointerEventData_set_pointerDrag_m0E8D72362B07962843671C39ADC8F4D5E4915010,
	PointerEventData_get_pointerClick_m2AFE23543BC381EC734E85ADB16DD63BA2017FEB,
	PointerEventData_set_pointerClick_m8FA5D91C9556A722BAE8ADBBB5353C79854D74C0,
	PointerEventData_get_pointerCurrentRaycast_m1C6B7D707CEE9C6574DD443289D90102EDC7A2C4,
	PointerEventData_set_pointerCurrentRaycast_m52E1E9E89BACACFA6E8F105191654C7E24A98667,
	PointerEventData_get_pointerPressRaycast_mEB1B974F5543F78162984E2924EF908E18CE3B5D,
	PointerEventData_set_pointerPressRaycast_m55CA127474B4CBCA795A9C872B7630AAF766F852,
	PointerEventData_get_eligibleForClick_m4B01A1640C694FD7421BDFB19CF763BC85672C8E,
	PointerEventData_set_eligibleForClick_m360125CB3E348F3CF64C39F163467A842E479C21,
	PointerEventData_get_displayIndex_m8B2088561EA850F1CAD124797F0F6E3756F584FA,
	PointerEventData_set_displayIndex_mCEBCECBC08CCF4D1770189B0992B8A1847A8137D,
	PointerEventData_get_pointerId_m81DDB468147FE75C1474C9C6C35753BB53A21275,
	PointerEventData_set_pointerId_m5B5FF54AB1DE7BD4454022A7C0535C618049BD9B,
	PointerEventData_get_position_m5BE71C28EB72EFB8435749E4E6E839213AEF458C,
	PointerEventData_set_position_m66E8DFE693F550372E6B085C6E2F887FDB092FAA,
	PointerEventData_get_delta_m7DC87C01EAE1D10282C37842ED215FDBFE2C1C5B,
	PointerEventData_set_delta_mD200AF7CCAEAD92D947091902AF864CB4ACE0F1D,
	PointerEventData_get_pressPosition_m8A6788DA6BF81481E4EBCBA2ED1838F786EBAE63,
	PointerEventData_set_pressPosition_m85544FBAB798DABE70067508294A6C4841A95379,
	PointerEventData_get_worldPosition_m296F53ACF7665D00DE12A18E1A91E3FFDEB42101,
	PointerEventData_set_worldPosition_m917BBBF297B2D89BB6836985D466A93B863899FA,
	PointerEventData_get_worldNormal_m5C5939C06E4AAC48C134A59A9C8F03A6D6CD8884,
	PointerEventData_set_worldNormal_mA342C6737631C9C902EFDF1F816AF5C6BE6B0EC7,
	PointerEventData_get_clickTime_m5ABE0298E8CEF28B6BD7E750E940756CD78AB96E,
	PointerEventData_set_clickTime_m93D27EB35F490AC9100369A23002F09148F85996,
	PointerEventData_get_clickCount_m3977011C09DB9F904B1AAC3708B821B8D6AC0F9F,
	PointerEventData_set_clickCount_m0A87C2C367987492F310786DC9C3DF1616EA4D49,
	PointerEventData_get_scrollDelta_m38C419C3E84811D17D1A42973AF7B3A457B316EA,
	PointerEventData_set_scrollDelta_m58007CAE9A9B333B82C36B9E5431FBD926CB556C,
	PointerEventData_get_useDragThreshold_m3ED1F39E71630C9AB1F340C92F8FA39AA489E1C5,
	PointerEventData_set_useDragThreshold_m63FE2034E4B240F1A0A902B1EB893B3DBA2D848B,
	PointerEventData_get_dragging_mE0AD837F228E3830D4A74657AD3D47F53F6C87E9,
	PointerEventData_set_dragging_m43982B3F95F05986F40A736914CFBC45D2A9BB8E,
	PointerEventData_get_button_mA8CBDAF2E16927E6952BC60040D56630BCC95B0B,
	PointerEventData_set_button_m77DA0291BA43CB813FE83752D826AF3982C81601,
	PointerEventData_get_pressure_m0745482FB0BD942F9615009C647765E3000F12C3,
	PointerEventData_set_pressure_m4471D0EEC22789490EA12FE6521A620CF60A37CA,
	PointerEventData_get_tangentialPressure_m76ED73E8545F01660D6196DCEBAA6C63DDDE374C,
	PointerEventData_set_tangentialPressure_m66792087B044033F0FF0FA4B2BA316233755EEF4,
	PointerEventData_get_altitudeAngle_m3D72F9EF9FF2238B1FE2E6B5870F8B0DD14B90FE,
	PointerEventData_set_altitudeAngle_m20F2AF2ADB0A20BF20C4B9A6AFE2566A0F4C8BD1,
	PointerEventData_get_azimuthAngle_mBFF5F23355EEAB911D8FF55965CCFF9CB3DD3F42,
	PointerEventData_set_azimuthAngle_mBE64BAD91A9A47E9D9163E25E9E0D1E677B0FC1B,
	PointerEventData_get_twist_m15A76D34614115A290B8FA90799752FBE00580B7,
	PointerEventData_set_twist_mE49469F4F730BA43906F2167E7ADDB9CB2F946E4,
	PointerEventData_get_tilt_m9F3341B8386EF98ECB4AA1F104DE90387DE25AF9,
	PointerEventData_set_tilt_m51D7B90D6C480D7C39217BCBADBBE544D722B034,
	PointerEventData_get_penStatus_mFDF49B3339E3F3A01407BE25CA2B3DF2F0E10996,
	PointerEventData_set_penStatus_m875854DF53437651CADFA190BCE3ED14FF4D65BD,
	PointerEventData_get_radius_mA89C671E5F8CA0D0684113CF05E7FAF2961BF7D0,
	PointerEventData_set_radius_mB2F29A6E8A14D1DE1162ECAB3398B539FEF83ABE,
	PointerEventData_get_radiusVariance_m5A3BC7FD6B455570A6535911E0F72F88B0F598BB,
	PointerEventData_set_radiusVariance_m62367BD7EE689AFF5BB5394D984E4AF026A2D15E,
	PointerEventData_get_fullyExited_m8A648782FBCC4F948B2D6DEC3B35AFF59A7C794C,
	PointerEventData_set_fullyExited_mDC23BED1E8A933E25E955A25109494A5D9F25C74,
	PointerEventData_get_reentered_m8B88B2F3A8C9FBBE878B458560F5BFF2D7DD142B,
	PointerEventData_set_reentered_mE363C3D307806C3FF87DF730C14E82AF68A96D8A,
	PointerEventData__ctor_m63837790B68893F0022CCEFEF26ADD55A975F71C,
	PointerEventData_IsPointerMoving_m281B3698E618D116F3D1E7473BADFAE5B67C834E,
	PointerEventData_IsScrolling_mFB78E050A248CDF5221482334808B82500D0A564,
	PointerEventData_get_enterEventCamera_m2EBF9CB2E5C1B169F6B6BB066C9CF5B99A7476CF,
	PointerEventData_get_pressEventCamera_m8D6A377D5CA730307D9F8ABB8656FFB8FCD56AE3,
	PointerEventData_get_pointerPress_mEE815DDB67E40AA587090BCCE0E3CABA6405C50A,
	PointerEventData_set_pointerPress_m51241AAA6E5F87ADEBBB8DB7D4452CE45200BEE8,
	PointerEventData_ToString_m49B5681669B6866A981884B774BC48E87D64B48D,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	EventSystem_get_current_mC87C69FB418563DC2A571A10E2F9DB59A6785016,
	EventSystem_set_current_mCAAA4D0C90542AF31D363CC4ACE4D615D5D28233,
	EventSystem_get_sendNavigationEvents_m8BA21E58E633B2C5B477E49DAABAD3C97A8158AF,
	EventSystem_set_sendNavigationEvents_m9309FBEDCBAA85162A202AADF3FDBB7A47D52D30,
	EventSystem_get_pixelDragThreshold_m2F7B0D1B5ACC63EB507FD7CCFE74F2B2804FF2E3,
	EventSystem_set_pixelDragThreshold_m2D2A087B9A9992D7B624CDB98A6E30BE9D10EF63,
	EventSystem_get_currentInputModule_m30559FCECCCE1AAD97D801968B8BD1C483FBF7AC,
	EventSystem_get_firstSelectedGameObject_m15FB056EE7A99D8DD5891D40A6E3EF18719F0553,
	EventSystem_set_firstSelectedGameObject_m626D151EC4AC93DE63E18689FDC13A03DCFB5AAE,
	EventSystem_get_currentSelectedGameObject_mD606FFACF3E72755298A523CBB709535CF08C98A,
	EventSystem_get_lastSelectedGameObject_m494BAB623DA90318F7B37C2FFEAD1D8E17FBE735,
	EventSystem_get_isFocused_mB0BB5BE03F7203A06D2F351ACD28BA177079104A,
	EventSystem__ctor_mEEF6F5A0BCA90CC9AD827AA3F2522783B71C6E50,
	EventSystem_UpdateModules_m2D91F02D546D50094DDB25BF0228A987E2EAFF91,
	EventSystem_get_alreadySelecting_m3DB9F620A5E2976EBF1362F95C05C12031BACCC4,
	EventSystem_SetSelectedGameObject_m9675415B7B3FE13B35E2CCB220F0C8AF04ECA173,
	EventSystem_get_baseEventDataCache_mF9AFC01C9D2B055F0816F6EEA2CC0011F1D82B7F,
	EventSystem_SetSelectedGameObject_m91382EAC4D552C672CC07BE7EB1481F156045280,
	EventSystem_RaycastComparer_mBF2582FBEDA9A1B604EE4281C61CB5E3DF676795,
	EventSystem_RaycastAll_mE93CC75909438D20D17A0EF98348A064FBFEA528,
	EventSystem_IsPointerOverGameObject_mC89BFEA46B0DA67F914B9B90356E63BFBE11EB38,
	EventSystem_IsPointerOverGameObject_m238732B4FDEA343976D798FF04DB34C3221243C2,
	EventSystem_get_isUIToolkitActiveEventSystem_m6FF1DA7E38D73742C5AEBF93C611723B9CC93FDE,
	EventSystem_get_sendUIToolkitEvents_m7E11CCC27DFE797BC4DFAEAE2D1C94BF845B08C9,
	EventSystem_get_createUIToolkitPanelGameObjectsOnStart_mD617E1B0EA52D750421DE03A8F131CF2F5831712,
	EventSystem_SetUITookitEventSystemOverride_m31B7776BD35EFB75371E2B860CF6E34FCDCD6A59,
	EventSystem_StartTrackingUIToolkitPanels_m4207099C99B738BE1B513CCB34ABA08AE57BD232,
	EventSystem_StopTrackingUIToolkitPanels_mB4F11691245874E390D2F76F72502904F08070E5,
	EventSystem_CreateUIToolkitPanelGameObject_mFE582264FE41E29CA6BBCCA384E1B238671D3B4B,
	EventSystem_Start_m392BF40F247855AA4D87C74F2CB5F9AC175F5556,
	EventSystem_OnEnable_m4A1E4BD3E26E6DD1150AF17B8A4E14DA9FDA2D9C,
	EventSystem_OnDisable_m7667186DBAD79874E4B7CE04A5F0291C35FBE240,
	EventSystem_TickModules_mD3F159C0C33396BEB5789B633065005DE771028C,
	EventSystem_OnApplicationFocus_m85C0A5CBBCEC8D900365BDD4F3E3188ED0EE8DC9,
	EventSystem_Update_m9D0AC1A7236F0DA1CCA0A8FFE0D8D33D960D433C,
	EventSystem_ChangeEventModule_m18F27ADCD2CF6656D771CB0413B7B4D768D38181,
	EventSystem_ToString_m0C3906BF8A1C2D7BCC31B09224890BC89B2AF35B,
	EventSystem__cctor_mE933C88969E443D3DEE106C6E747F97F40D3B48F,
	U3CU3Ec__DisplayClass56_0__ctor_m180E6793CA9B0603F1D4ED924CDC89823C6D5ECD,
	U3CU3Ec__DisplayClass56_0_U3CCreateUIToolkitPanelGameObjectU3Eb__0_mF0E9A748CB11B0F45553197CA9F1F6FCA0B0E439,
	EventTrigger_get_delegates_m0EF6EB8D0AB4964C9AB563D74387B1D5366B9004,
	EventTrigger_set_delegates_m47AE262A9A8E4F2F2824F2C877597DC4CE2A979A,
	EventTrigger__ctor_m2A471D4099280D37183A1B668FF092B9517BA294,
	EventTrigger_get_triggers_m2361511923086BCD40339097448A70AFB22C4647,
	EventTrigger_set_triggers_m5F861F79BBA48C26CFB83BEA7E25580B21BDA815,
	EventTrigger_Execute_m8F637065284AB93B0D2C1090C63830AFD9CE25BE,
	EventTrigger_OnPointerEnter_m78A0620B719E345A02F2A628EBC1D08ADAA5FD89,
	EventTrigger_OnPointerExit_mF15D24467BCC9686CD9DC11C728632F7ED098BF4,
	EventTrigger_OnDrag_mD4E2457101987E2E96C251EDBBAD8960BED20874,
	EventTrigger_OnDrop_mF111804E0134C1C873156D4B22E8479CDDEC0C1B,
	EventTrigger_OnPointerDown_m91957FC65D1AE1C5FD6B0548682DEE1B4283ECC0,
	EventTrigger_OnPointerUp_m63A37DEC73942B6C5863F79DED7A2BCDEF8B8DB6,
	EventTrigger_OnPointerClick_m6006A8F9138007DF16AEA63968E865D8A2AF128E,
	EventTrigger_OnSelect_m191AD9E9C686FABEEF036AAC0D89F27D7BACC8E4,
	EventTrigger_OnDeselect_m647B65049C6C332711233F0B2F72C99E4AE2DE46,
	EventTrigger_OnScroll_m45ED61FD7F6FAD70C71502A38D2479DEE50B1370,
	EventTrigger_OnMove_mEE11502B46693D5F5C8E23380E0DF0D4B75EE9CF,
	EventTrigger_OnUpdateSelected_mF5C79E9494D1F1F3D032FFB17B1D5B3701FB5BD7,
	EventTrigger_OnInitializePotentialDrag_mF57EC5149D92811696046CACFA2CD4422890AE78,
	EventTrigger_OnBeginDrag_m370891394066DA8891BFA458D335A4B878988E7B,
	EventTrigger_OnEndDrag_mFC87A35C4060855401A4C0C28612829D0894A8A8,
	EventTrigger_OnSubmit_m2EC7EAB0AAD5AD0528511C8184A430FD91E95E0D,
	EventTrigger_OnCancel_m17724FAA28975B06DDDE55D06716DE33A1788144,
	TriggerEvent__ctor_mBC60D36344FFB96FBE826D229CE25D4C25E08440,
	Entry__ctor_m7325965EB4BD264BE16F837B6AA2693ECEDBB5E8,
	NULL,
	ExecuteEvents_Execute_m316D0EE5A1936BFFD9999F4C145722DC6C121FF7,
	ExecuteEvents_Execute_m554281680E2DBC534055073ECCE46230E488A3E6,
	ExecuteEvents_Execute_m3DD6C7687A440E55EEF8B7D115DEF950728295B6,
	ExecuteEvents_Execute_m36FF8B992CDB75A825077B7A52AA7BE72318B37F,
	ExecuteEvents_Execute_mD0811E5B0A4F7D5A88E7ACF0A845CA107485F579,
	ExecuteEvents_Execute_m512ACDD06180A73819570FED3C2BEE0F0E2DA3F2,
	ExecuteEvents_Execute_mCD88FE48772FC6DA5E9FE9CAF910402F63090C35,
	ExecuteEvents_Execute_m3F3FEE80AD62CF4207EDA55D6998B98DFF8FFB64,
	ExecuteEvents_Execute_mBEB42D218E11F4B9834CAC70894631C305E6AF18,
	ExecuteEvents_Execute_m1A35D0185316601E2CE063420F4953C8D3D62D3A,
	ExecuteEvents_Execute_m6DD01624C34CF22057ECF1B0C7E561006DA6D2F3,
	ExecuteEvents_Execute_m08AB6D464ED66E7D539C957D84076F79D8ED5563,
	ExecuteEvents_Execute_mF1F3202132B706B56AE43B19577758BCA4EAEB88,
	ExecuteEvents_Execute_mBDDBAF4DEB956C013CD19E514088B6AC086783B2,
	ExecuteEvents_Execute_m070554B8CD391A49E8A51A4FC10C8CB8827E5627,
	ExecuteEvents_Execute_mBC94A654B65C6B14834E3CD0FF0472DB5445E2F2,
	ExecuteEvents_Execute_m6CE7DBF76F4858C3014295BB2EBBAD768EF5992E,
	ExecuteEvents_Execute_m30F76D861B01F5DE4671B93C23B57989889EC8AC,
	ExecuteEvents_get_pointerMoveHandler_m996E37A7026F03F8791EFBB69B72DE1FC4FA3A60,
	ExecuteEvents_get_pointerEnterHandler_m9F921E3357CE38A925DF20E9CD94B4C3AEE9AE48,
	ExecuteEvents_get_pointerExitHandler_m03735363884BC967C1B04246B51FE98886C9C6DE,
	ExecuteEvents_get_pointerDownHandler_mA67CE33D32540939A273DB88D6456C7FE43C13EF,
	ExecuteEvents_get_pointerUpHandler_m51B83B4AD7498D6F7A2CBD5F2331E91A37AE4CF2,
	ExecuteEvents_get_pointerClickHandler_m0017F9D1EAF7C02CDDB4C148C92D6685D88EA8D5,
	ExecuteEvents_get_initializePotentialDrag_m16F4CD40448FB1B78F6683AA26A9CC574F48AFBD,
	ExecuteEvents_get_beginDragHandler_mB0BEAC09E4A8F31438B07FE68A5BF7267FF8C2FC,
	ExecuteEvents_get_dragHandler_m9193926B9685C80C0560C2E105FF6150C4EAE507,
	ExecuteEvents_get_endDragHandler_mE78FEEEAE114635E416FF1FE33C851E62B60555B,
	ExecuteEvents_get_dropHandler_m8E00FA7361DE68780ACEB365E6B14206A2180D03,
	ExecuteEvents_get_scrollHandler_m51E902070611D3F81AD5F1F5762AE2C48E84AFE2,
	ExecuteEvents_get_updateSelectedHandler_m8AF7543437082AD4F5690AAA77F2623AE28C3D09,
	ExecuteEvents_get_selectHandler_mCF588328D11715E24BC54DF464EF4F3D694B3939,
	ExecuteEvents_get_deselectHandler_m9CDD5F8B5269067CA38247DDD41B521A8FCEDFEF,
	ExecuteEvents_get_moveHandler_mF717824AB0018BBED3C2DF3C67486E3B2B3836D2,
	ExecuteEvents_get_submitHandler_mDCAAA40F0F6AEA385B375C1839B4DC37E5FC4A7A,
	ExecuteEvents_get_cancelHandler_mBCDFD10C95FC2BBC5DC5A512FEA1BBEECC2DAE12,
	ExecuteEvents_GetEventChain_m4FAD69B97B2D0AADFCA3AE06CA80E680B60632ED,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ExecuteEvents__cctor_mFF9D727E7E8EEEE34D6861D20720411F1CACE9C5,
	NULL,
	NULL,
	NULL,
	NULL,
	BaseInput_get_compositionString_m2F5F37C4A3E1CBFB779113D038802AC2BA3E556E,
	BaseInput_get_imeCompositionMode_m61F7F9CF12191DE6328F900458CB1886AEDA2B08,
	BaseInput_set_imeCompositionMode_m10C9A03CA5BE2DD16C356603A1D03EE197B29085,
	BaseInput_get_compositionCursorPos_m40F062B6C5FB3667DE689AA06AA0CA5DD52DBF7C,
	BaseInput_set_compositionCursorPos_m186F1B453469AC2FEE13F9B2144B1A59D4D7519E,
	BaseInput_get_mousePresent_mCB00324CF55402907B52C63213CF397E53D03E71,
	BaseInput_GetMouseButtonDown_m6276D605B7C48000F3D61BF56BE9E3B5F86398AF,
	BaseInput_GetMouseButtonUp_mBB470F97111BB7BCC1B543CD282F898C9033DAE4,
	BaseInput_GetMouseButton_mD6EAA726BE691C40052FEDBFF51AEAA1DACAAB57,
	BaseInput_get_mousePosition_mD75C96534C8B4EFF48A732F4826E6C9E09CB4540,
	BaseInput_get_mouseScrollDelta_mD764FCD7B05C6505AAB3161C3011A5EA51DEDC39,
	BaseInput_get_mouseScrollDeltaPerTick_m0FB0F8CF77BB9FAB531E0174690A74416DA20C4B,
	BaseInput_get_touchSupported_mA46B82A5BCB1A1BE47FB15BD4ACD522694DBDC1C,
	BaseInput_get_touchCount_mCD103B50B46B7938389AE38F81C34F173B9F94FD,
	BaseInput_GetTouch_m1FA092A2BD2276B8F94A3058B3D8A9E301DDCBE6,
	BaseInput_GetAxisRaw_m817F925099FC5D22086D616249C0CB5C7F21445B,
	BaseInput_GetButtonDown_m3B34561DB7A1E129F880B1E09CE8B844B1FF6FBC,
	BaseInput__ctor_m7E497239A0A78CCAC04BE6EE9AA81D49D887787C,
	BaseInputModule_get_sendPointerHoverToParent_m3C22EF19BA6E95672ACD15F7E3FCD11277EBBF47,
	BaseInputModule_set_sendPointerHoverToParent_m1E97FE3C9AEECB53AFAB89429D6ABCFE7A9F1882,
	BaseInputModule_get_input_mCB3F78528AA14A7AD7E957870DBB0152B4BF13FB,
	BaseInputModule_get_inputOverride_m92D66E309898C180BF887F6043CCE7AE63C6C44C,
	BaseInputModule_set_inputOverride_m876BAC421A4BC40FB5873FC386E361C4CFA53987,
	BaseInputModule_get_eventSystem_m341B2378F61A58D5432906B9EE1E12265E2FAB33,
	BaseInputModule_OnEnable_m2F440F226F94D4D79905CD403F08C3AEEE99D965,
	BaseInputModule_OnDisable_m370643AD83FFAD10B9E67301355F63B4FF7FB389,
	NULL,
	BaseInputModule_FindFirstRaycast_mE07BDA14A7C9A8E3DFBFDAF449E5896597C9F6F5,
	BaseInputModule_DetermineMoveDirection_m8C99256812C74890B4F54BBCA5BE424A7D608E15,
	BaseInputModule_DetermineMoveDirection_m6461EE20A0418E30EFA13CD293A2B0E7A95DBA54,
	BaseInputModule_FindCommonRoot_mBCC0541CA6E2BCFF051B90FE34F4F00C28CDFA10,
	BaseInputModule_HandlePointerExitAndEnter_m0815F06EAF8F937916E0656D66A69844CB211298,
	BaseInputModule_GetAxisEventData_m99FD46006BB2D8FD6D1E10F606886FE017955293,
	BaseInputModule_GetBaseEventData_mF750E3A63EC1080B933A2FA2CC21D683A68ED433,
	BaseInputModule_IsPointerOverGameObject_m1406F19FE6A3CAEECB2238427345E4CA32E1AD6F,
	BaseInputModule_ShouldActivateModule_m51B70F9097EF7FEB20B62D4D775F7FEA853185A1,
	BaseInputModule_DeactivateModule_mAE698307DADBE4DE8A26BD3DE5F3F7E3D75B385D,
	BaseInputModule_ActivateModule_m2C693E95727E13FDF06D54FA8762A040175AC3BA,
	BaseInputModule_UpdateModule_m201C2C266D80D7451D42E929A90EFC8C4B7358BE,
	BaseInputModule_IsModuleSupported_m60644A4C84A8B0FA66E204E20D149A0BCFAD27A2,
	BaseInputModule_ConvertUIToolkitPointerId_m067C6EDDF29815FE295111E95A38F66860D1E441,
	BaseInputModule_ConvertPointerEventScrollDeltaToTicks_m8C079F385CC514242F0713126C048819B7FC2782,
	BaseInputModule__ctor_m88DDBBE7BC4BB7170F5F8F00A0C9E2EC6328B819,
	PointerInputModule_GetPointerData_m8D1C52EE44136560312932072786A2B5AA2C8BE5,
	PointerInputModule_RemovePointerData_m012713A1B4511855549793D6BA2B7998134B1BE9,
	PointerInputModule_GetTouchPointerEventData_m55EBA8BD04214AAD8E98B9109D44610496A5B2E1,
	PointerInputModule_CopyFromTo_m4302FE47F12B3B8C59A3790BD0ADF2BFAAEA9BFD,
	PointerInputModule_StateForMouseButton_mED5B48F98F706160F97A26511FB82BD84DBAB0CF,
	PointerInputModule_GetMousePointerEventData_m77052AB014196BA4E66C8BBE27EC9AF739031EFE,
	PointerInputModule_GetMousePointerEventData_m8D8111399CF7077AEBE4836AC701DDDF3F5ADFC5,
	PointerInputModule_GetLastPointerEventData_m6355023718EB2DCF6D9C226A57B63B70CCEECAB4,
	PointerInputModule_ShouldStartDrag_m6260055DEAD5E28183E338BDA53C7F8A0521EC6B,
	PointerInputModule_ProcessMove_m3555F333D82A446C2354D8855034323BF7C9208A,
	PointerInputModule_ProcessDrag_m73FE39BFACC950DCA7FDD7BDC67F45484DC01207,
	PointerInputModule_IsPointerOverGameObject_mBDCC057426289D69D4C6E1EF7F6849C112171883,
	PointerInputModule_ClearSelection_mC5852667E5B9CA97C2A4CAB3D7C907344511C1D2,
	PointerInputModule_ToString_m9C5DB37AC45C9F27B017E3B52C5CFE22F91CAF9D,
	PointerInputModule_DeselectIfSelectionChanged_m8F111DD2317E33C4F0498F651BC52BD5C984A95B,
	PointerInputModule__ctor_mBF074492478BFC24F87EF2C941D6C11A8ACDAF11,
	ButtonState_get_eventData_m4767730784143F1DCE06B6138658A31CBC5E155F,
	ButtonState_set_eventData_mA9D59CB9A1565A7D99569E87D88B90738FEF4E1F,
	ButtonState_get_button_m2210A465432D0F990F2380B6357AD2FBA4A7540D,
	ButtonState_set_button_mFAE3F16E2B027BD6B854F18E7C7C2D6CDAB023DE,
	ButtonState__ctor_m4D7C25C0E1FC598646FFBD436B9A2042DB41AC9E,
	MouseState_AnyPressesThisFrame_m4887FF61D58D641496B95C83710C8A4E841970F3,
	MouseState_AnyReleasesThisFrame_m4FBA37A12735418AD0970F11BC44850517B05E93,
	MouseState_GetButtonState_mD25E7D214B0499DBBE3B3E532CD7085C1A021E51,
	MouseState_SetButtonState_m72DA468C8D10E76923FA5F993BBDBCFFF57E4326,
	MouseState__ctor_mF4A8041A86E50D91202770E73CE0DAF12BB207D9,
	MouseButtonEventData_PressedThisFrame_mEE5DC95537AAEB346C57DCA85917E0701A44388D,
	MouseButtonEventData_ReleasedThisFrame_m5AD4F06D1CA6E0ACD6E84EEFAD4FB112098AFD51,
	MouseButtonEventData__ctor_m9EDAC7F39F1D3CFBB93403DDE620A5147C4469A2,
	StandaloneInputModule__ctor_m77BAC1DB71B81FFCD2791DE706BD4FE239F47C27,
	StandaloneInputModule_get_inputMode_m38D63EDD9DE39E7AFE1821BDE201625C292C66D9,
	StandaloneInputModule_get_allowActivationOnMobileDevice_m03E7DC8FCBE7B43A223EADABB454445C91664A1B,
	StandaloneInputModule_set_allowActivationOnMobileDevice_mFFFF3E19FBD199ED9BFAEC535E5BD11F5027FF25,
	StandaloneInputModule_get_forceModuleActive_m381A5525E48FD280EB91ECEEEF138E7603C004B8,
	StandaloneInputModule_set_forceModuleActive_m4C3FD8550258266795D24863D8B531F2402500DD,
	StandaloneInputModule_get_inputActionsPerSecond_m584ABC794A3864BF91EEB27E62ED6E8081DEE0A5,
	StandaloneInputModule_set_inputActionsPerSecond_mF367AFA55FF576533999F2DFB60514D7247228FF,
	StandaloneInputModule_get_repeatDelay_mDB85393BD9AA45BF8C5B94F5E3A523F5480D1F6F,
	StandaloneInputModule_set_repeatDelay_m236DB6414CFAE01609187B97E955D95A32F0CB40,
	StandaloneInputModule_get_horizontalAxis_mDB47CA6F06F26837BBC4853877F69817590161F0,
	StandaloneInputModule_set_horizontalAxis_mF71F2B0B425BD0455AF54F39EEEE43DD80DE27EC,
	StandaloneInputModule_get_verticalAxis_m5F00ECDA3B18F48BCBD6F9E7B4AD67A1F56CFAC2,
	StandaloneInputModule_set_verticalAxis_mB0FE6DC9517F0ABF0107F72FC04A322FD91C2AC0,
	StandaloneInputModule_get_submitButton_mAF097B352341EB53A42F71052F3469F205243D40,
	StandaloneInputModule_set_submitButton_m571CB829C6D76AD062BA105D0903F08CEA0BCCC7,
	StandaloneInputModule_get_cancelButton_mE6F80897FDAA6D931803BF6C3A9E4A45877E5585,
	StandaloneInputModule_set_cancelButton_m63A06532F16A21785B3BD5AD3B79509B4441B7EF,
	StandaloneInputModule_ShouldIgnoreEventsOnNoFocus_m423AA5752E528E3B32F105EE2B341FCF5E9F8285,
	StandaloneInputModule_UpdateModule_m8B42D289F6AB3EBE6071FB5B904A7449118EAA6B,
	StandaloneInputModule_ReleaseMouse_mC5C3083C356ACD5CD420A58662D99A6CACC5FAF5,
	StandaloneInputModule_ShouldActivateModule_m6E9E205A37636246BFE65CAC33E1EF703A4D99AF,
	StandaloneInputModule_ActivateModule_m1F28B8DB0FC20082694221BE5B1B275E9B7224BB,
	StandaloneInputModule_DeactivateModule_m064C1EB615C0E0B0EDFC6F6F5079E55270DF1468,
	StandaloneInputModule_Process_mBD949CC45BBCAB5A0FAF5E24F3BB4C3B22FF3E81,
	StandaloneInputModule_ProcessTouchEvents_m042FC6B13874B1EE6699BBB51F02FE3A435A25F0,
	StandaloneInputModule_ProcessTouchPress_mD72A0807626DA04E47313F9553249DD4A32625E3,
	StandaloneInputModule_SendSubmitEventToSelectedObject_m7121ACC09ABA46FF5CDDEAE7B26507BE06A2953F,
	StandaloneInputModule_GetRawMoveVector_mFF583B1720780B7D8D2DD3248F947ED8D855610B,
	StandaloneInputModule_SendMoveEventToSelectedObject_mC71D1E623B1DB82DC4E035277AC5FFA7CD64E981,
	StandaloneInputModule_ProcessMouseEvent_mCE1BA96E47D9A4448614CB9DAF5A406754F655DD,
	StandaloneInputModule_ForceAutoSelect_m8A0FD9795D64CAF49AED184D30FA5C7AB6439C75,
	StandaloneInputModule_ProcessMouseEvent_m8A8214EB9286BA31C18F515BCC3349DF740B2BBA,
	StandaloneInputModule_SendUpdateEventToSelectedObject_mE1C4AEB5B19378C2BF97DD3EAF4AA9C0EC5E7520,
	StandaloneInputModule_ProcessMousePress_m24665E707FEF5C80719847D62A8A8AEABE27C6C5,
	StandaloneInputModule_GetCurrentFocusedGameObject_m6E67A53E66DE554CCD13A943638815631A3E8E87,
	TouchInputModule__ctor_mCBE23B5A3A8CE1CD042A061D7E070B04A45E3075,
	TouchInputModule_get_allowActivationOnStandalone_m2F9A65E10F7BE98D250CC3D6C9CABA3BCCE95995,
	TouchInputModule_set_allowActivationOnStandalone_m5AB9798D9B6071B37FC67E0D1A81A58A0A59D7AD,
	TouchInputModule_get_forceModuleActive_m7200F7B6C80EDD69987615B8356B6B2497461FCA,
	TouchInputModule_set_forceModuleActive_m32676A04010774CA55055EE93A05ED6C6388D2C2,
	TouchInputModule_UpdateModule_mE6F4F74D770ACFEF3C46611C71CBE705342EA608,
	TouchInputModule_IsModuleSupported_m795B5298EC90DC063A96147CF3B3287EC27C9799,
	TouchInputModule_ShouldActivateModule_m4B17231DE579430D27088D6592378FB7547ADBDC,
	TouchInputModule_UseFakeInput_m66A84384ADA044187F239B1CFC143C42E5E56774,
	TouchInputModule_Process_m3C0CB50AB7D9E92B519787F7742AE0976FB36841,
	TouchInputModule_FakeTouches_m264BF2C70234CD04B0162FE4CED673B1A77A84F9,
	TouchInputModule_ProcessTouchEvents_mBB28D85996F6280141E39A462BC35EF01373E514,
	TouchInputModule_ProcessTouchPress_m1DC51E52E6B419F02626EB567A60411A0FCFA517,
	TouchInputModule_DeactivateModule_m7CF377DBC376C3EC560523E76514E9F47CCC9DEE,
	TouchInputModule_ToString_m1AD08DB012D85A33FC0EA3322D5AA5EB98CD1956,
	RaycasterManager_AddRaycaster_mD2EE52F55FBDB3BEAAF583C4445D4D16B09B6350,
	RaycasterManager_GetRaycasters_m876BA9CA8DB8E6D351DC4A97473753503B7017DE,
	RaycasterManager_RemoveRaycasters_m0800E0ACE007AD43B62D10C1029EC85E7DE28999,
	RaycasterManager__cctor_m06146026A557346F1469D15E855918E746125F90,
	NULL,
	NULL,
	BaseRaycaster_get_priority_m79C109ECC138B84A60F9CFA40242628A8B29C838,
	BaseRaycaster_get_sortOrderPriority_m4E0BEBF85F720AE4B7C78E99CCD786779C3E7226,
	BaseRaycaster_get_renderOrderPriority_m03C407856FF76393AB6EE26FA173131B8F36CA66,
	BaseRaycaster_get_rootRaycaster_m63E20D85A8B9867AC196768924F8BE579668BF28,
	BaseRaycaster_ToString_m12811CE16AB7E07C949B78CDE309C4B2E44B5377,
	BaseRaycaster_OnEnable_m87CCF1ACD4116BB8BC0D9DB427F5B07C6FDE3D96,
	BaseRaycaster_OnDisable_mC90A700D5F78DDAD0DD926983C2A8D7C50A5D880,
	BaseRaycaster_OnCanvasHierarchyChanged_m20A82CFED659012D1F052C5026B8294B44D99BD7,
	BaseRaycaster_OnTransformParentChanged_m121068CDDBC97032FF51C4ED944D4C126CB58F7F,
	BaseRaycaster__ctor_m1B6A963368E54C1E450BE15FAF1AE082142A1683,
	Physics2DRaycaster__ctor_mF2F12F2AF9DDCA74EB09349D038A67F3D3F88CF9,
	Physics2DRaycaster_Raycast_m9F6AA9C9DC7A34C01959F2053446D3FFCE567630,
	PhysicsRaycaster__ctor_mB7D4BAC26DC219A10060B35498EE9D5F05AD0E80,
	PhysicsRaycaster_get_eventCamera_m95026618116D1781A906DDE4AF9C415F2374013C,
	PhysicsRaycaster_get_depth_mCC2E924588088DB1DCA160765C09734D3C4F7F60,
	PhysicsRaycaster_get_finalEventMask_m20AD2327FE81B38A5853B23970A587EAA2ECCB1B,
	PhysicsRaycaster_get_eventMask_mA8FE3884CD425BD59BD22784F4D5219159426DB9,
	PhysicsRaycaster_set_eventMask_mD5110BE565C7E3F1738369519D44587452CA056D,
	PhysicsRaycaster_get_maxRayIntersections_mA06D0B5E291BCF94AE1EF4ED7B68890F39395911,
	PhysicsRaycaster_set_maxRayIntersections_mECCF07932870A3B5C8875AE6204FC1ECB2CE01F7,
	PhysicsRaycaster_ComputeRayAndDistance_mCFEFA9D83EC1E63393454E383FFFEF89E14C173B,
	PhysicsRaycaster_Raycast_mB29925EB33102E9BEAA76658F8A59CA666C78B1A,
	RaycastHitComparer_Compare_mB5B88FE52375A12B781682C712FE58193F417A03,
	RaycastHitComparer__ctor_mAB0536EE515BF2BD1B29BE3B39E8BA9E9CFE97C3,
	RaycastHitComparer__cctor_m772D29066D594105261C78CDDB5724BE28A773FA,
	RaycastResult_get_gameObject_m77014B442B9E2D10F2CC3AEEDC07AA95CDE1E2F1,
	RaycastResult_set_gameObject_mCFEB66C0E3F01AC5E55040FE8BEB16E40427BD9E,
	RaycastResult_get_isValid_m69957B97C041A9E3FAF4ECA82BB8099C9FA171CE,
	RaycastResult_Clear_m0E9DA70AC69CF143CEA8428AFC5BA552F99643AE,
	RaycastResult_ToString_m0267000494B09783ABD507B9329ADB01FBBC5428,
	UIBehaviour_Awake_mDF9D1A4867C8E730C59A7CAE97709CA9B8F3A0F2,
	UIBehaviour_OnEnable_m8989ABF5C038905A68E5536BED2E6FFAF8767FFC,
	UIBehaviour_Start_mB12643ED6D859CD3682B4BF5B9CA7F72E8A72B45,
	UIBehaviour_OnDisable_m18D5A0B93F65FB50F4D6CE8197EC07F3452C5DDE,
	UIBehaviour_OnDestroy_mF225CF9163823F19BE5E2B2735D35898A20D608B,
	UIBehaviour_IsActive_m9E79A0650C81204AF9A861BBBA8685D9563AE9C4,
	UIBehaviour_OnRectTransformDimensionsChange_m86A6D20E0EBF41CDB89DD1E87F23624263B68159,
	UIBehaviour_OnBeforeTransformParentChanged_m980EF41E33A7541BD6C65AEC305B25A19C9CA30F,
	UIBehaviour_OnTransformParentChanged_mAD56D3C6049A1746F00DC2643D1666F7DE921384,
	UIBehaviour_OnDidApplyAnimationProperties_mE011A7C92134E28AE2AF3A0EBFB2E4AB88ABE748,
	UIBehaviour_OnCanvasGroupChanged_m592FA8BF5238E712E73E2D99258EE0DB6D88D84B,
	UIBehaviour_OnCanvasHierarchyChanged_mCAC018CB33FA00E857288B64E3722226638A1229,
	UIBehaviour_IsDestroyed_m2E9FFA28686D1C82697FB467002541CC58A06BCA,
	UIBehaviour__ctor_m24C66A65DDD996E779871C6655CF11B871F11337,
};
extern void ColorBlock_get_normalColor_m08A07A74ED743B4B0C1B5A5C35774F2D78F1F20E_AdjustorThunk (void);
extern void ColorBlock_set_normalColor_m3EBF594F6FA2C6494ACA9FCB9B458807D85B96F8_AdjustorThunk (void);
extern void ColorBlock_get_highlightedColor_m4D1A3D268CB00B351F56934F7F244DBC68855301_AdjustorThunk (void);
extern void ColorBlock_set_highlightedColor_m04E97DF2CCE7CAC47120D8F486E18BF62F16FF86_AdjustorThunk (void);
extern void ColorBlock_get_pressedColor_m5EDADBA10824D08BFB67D02099A9FC6A4235AC62_AdjustorThunk (void);
extern void ColorBlock_set_pressedColor_m644C938090857AB07C57B25FE53F6DC2BB0DD5A8_AdjustorThunk (void);
extern void ColorBlock_get_selectedColor_m41CD59090E997A5982EE5AB9D23811FEB35C82CF_AdjustorThunk (void);
extern void ColorBlock_set_selectedColor_m76FEFB1148798B7A356C974CDEA3BA2E2E3C1D21_AdjustorThunk (void);
extern void ColorBlock_get_disabledColor_m2E20FC772B592ADD71CE1336D29B3C3C1523669E_AdjustorThunk (void);
extern void ColorBlock_set_disabledColor_m4D10D1F8525CCC7E8E200E3994AFB28ADABB1D8E_AdjustorThunk (void);
extern void ColorBlock_get_colorMultiplier_m54C8F6B7E5ACF45D70F9EAAED78AB4606999C187_AdjustorThunk (void);
extern void ColorBlock_set_colorMultiplier_m920A048B95541DB0E92AF4AF3894BE7CD2D37102_AdjustorThunk (void);
extern void ColorBlock_get_fadeDuration_m506A0FCC2819DA313BE033640C8FEDC5331D1C39_AdjustorThunk (void);
extern void ColorBlock_set_fadeDuration_m8519A304808384CE873377EC104969A6B9FBB6C5_AdjustorThunk (void);
extern void ColorBlock_Equals_m20D958BB28F6FDC12D612279AF6B50679C0C1E67_AdjustorThunk (void);
extern void ColorBlock_Equals_m52DCE246EA23904A3EF0FCD3ADAB81BC20DC1BE5_AdjustorThunk (void);
extern void ColorBlock_GetHashCode_m3CCB4E1E5EE93B905650E24BD4753096082270D7_AdjustorThunk (void);
extern void Navigation_get_mode_m3B574F1549B3806753EC33228EB3FF3031F4E809_AdjustorThunk (void);
extern void Navigation_set_mode_m0BEF999F733332AD994CF3CA4AC17B2A47531207_AdjustorThunk (void);
extern void Navigation_get_wrapAround_mA24021791B1C67F665065B5A415434837CEA86DD_AdjustorThunk (void);
extern void Navigation_set_wrapAround_m9D808EC49EE5F3AFA7F0D13E86FF9F72AA20A081_AdjustorThunk (void);
extern void Navigation_get_selectOnUp_mD24FC0BAB97E5DBB28C9C7209BAC2ACC9419B183_AdjustorThunk (void);
extern void Navigation_set_selectOnUp_mCB04000FDFC05D3BAC497602E4BA346A536152E5_AdjustorThunk (void);
extern void Navigation_get_selectOnDown_m1D36E990CDB38C4BB78745587668F94BBE8A1285_AdjustorThunk (void);
extern void Navigation_set_selectOnDown_m0EBBAB8C51107F75F63FFBC3DF88D9010E6A44BB_AdjustorThunk (void);
extern void Navigation_get_selectOnLeft_mA4F7DA341D7C660A7E15520B34847B0757C65F81_AdjustorThunk (void);
extern void Navigation_set_selectOnLeft_mA4E7480D7CBDA9A5ECA93BAFCD1CF1976A994FCB_AdjustorThunk (void);
extern void Navigation_get_selectOnRight_m7A781F4050AE064DC0473E68AA6D07CFFF0A8FF9_AdjustorThunk (void);
extern void Navigation_set_selectOnRight_mD0B38024BB628CDC801EA93E9FF7C438ECE2055B_AdjustorThunk (void);
extern void Navigation_Equals_mE25B4E3D0AB85C1469B99971E6AB16E2039E6B4D_AdjustorThunk (void);
extern void SpriteState_get_highlightedSprite_m5D24B628AB2E4DEBF67E094CCA059BDADAB952BB_AdjustorThunk (void);
extern void SpriteState_set_highlightedSprite_mEECDB7C62DE0C6A0B2A7D5D7ADF54EB8CDDB20B0_AdjustorThunk (void);
extern void SpriteState_get_pressedSprite_m89052B1818D1659DA7E594F218485F1DEB8128BD_AdjustorThunk (void);
extern void SpriteState_set_pressedSprite_mD01568B87B1BC1374CCFB5CD190D7CD62A6FDAA3_AdjustorThunk (void);
extern void SpriteState_get_selectedSprite_m5316836E91F7EB454E953CADD439FF69AA198BA5_AdjustorThunk (void);
extern void SpriteState_set_selectedSprite_m902ACABEC203C0A2408B4ECD7B74C10DFE7BB340_AdjustorThunk (void);
extern void SpriteState_get_disabledSprite_m6BE5A2231E20BE1600328082B4EFE53EE7F3E12C_AdjustorThunk (void);
extern void SpriteState_set_disabledSprite_m624499C245DC34D314FF0326FE5ADCF35DA28E27_AdjustorThunk (void);
extern void SpriteState_Equals_mAF58D9F36662F5A8196071690175AAFCC4506653_AdjustorThunk (void);
extern void ColorTween_get_startColor_m9E33FB5C5F76BCF49A3B20201CD8006DBFB46012_AdjustorThunk (void);
extern void ColorTween_set_startColor_mD22349343421BD44F0C31E537718ED53BE4850DA_AdjustorThunk (void);
extern void ColorTween_get_targetColor_m240A7018BDC3B44AB44BA674AA16C39960BC23FF_AdjustorThunk (void);
extern void ColorTween_set_targetColor_m7D8E74B32AC3A9C17C3192096003B12A1500D749_AdjustorThunk (void);
extern void ColorTween_get_tweenMode_m06B83FB6E45A807F83FDD762A8241D478FD13F8B_AdjustorThunk (void);
extern void ColorTween_set_tweenMode_m105EEB49F6632D6D105C63DA9919385233A5D4DE_AdjustorThunk (void);
extern void ColorTween_get_duration_m40D8F08C13FF2FE7583746934C6A017A93398548_AdjustorThunk (void);
extern void ColorTween_set_duration_m1C278AB5A90B5C108CEB4870CAC90A9A9EAC19CB_AdjustorThunk (void);
extern void ColorTween_get_ignoreTimeScale_mEDB15A4ADE3A0B9487D240964A7571247F974708_AdjustorThunk (void);
extern void ColorTween_set_ignoreTimeScale_m060FF3CED06F73EA1F555A37999D61DC58F99927_AdjustorThunk (void);
extern void ColorTween_TweenValue_mF5CBA9BDE7F73E47F9CF26DC4EC2419694049860_AdjustorThunk (void);
extern void ColorTween_AddOnChangedCallback_mAC2856A154604B4B6721DAC185B819A98D6F7438_AdjustorThunk (void);
extern void ColorTween_GetIgnoreTimescale_m679C83012235779A37DCCD0AA75CD6B0DAE5BCFA_AdjustorThunk (void);
extern void ColorTween_GetDuration_mC40D6776769FDB79C7ADC42D59F059A2A9AE2F66_AdjustorThunk (void);
extern void ColorTween_ValidTarget_m1D7A682CE00048FAF1A3BDD55EB76F44C9122B4D_AdjustorThunk (void);
extern void FloatTween_get_startValue_mCA121483CCF4C8F10991BB3306E3F2769EBB3A3C_AdjustorThunk (void);
extern void FloatTween_set_startValue_m43B55D74B7B34D9C32439D6004F306BFA18E4A1A_AdjustorThunk (void);
extern void FloatTween_get_targetValue_m6EFBD9EAB206F145959832269DC24C4B68FEE6B1_AdjustorThunk (void);
extern void FloatTween_set_targetValue_m4AE44CE862797E898CDE00A1B7D6A33CE0AFDCFB_AdjustorThunk (void);
extern void FloatTween_get_duration_mB1496D38A618FF8282205FD3AA14CA9C6D76454D_AdjustorThunk (void);
extern void FloatTween_set_duration_m40E10A7B796B4B54FFB8DA3889B09557BEC98456_AdjustorThunk (void);
extern void FloatTween_get_ignoreTimeScale_m6F6BDCBD59C19E68572370F9FE3D7373B4212B3B_AdjustorThunk (void);
extern void FloatTween_set_ignoreTimeScale_m09041A4110040F9C86D24E1B4DED6E6B7FB206A8_AdjustorThunk (void);
extern void FloatTween_TweenValue_mE51344369BDDA58E9C3AEC62E1B1C1AC0349278E_AdjustorThunk (void);
extern void FloatTween_AddOnChangedCallback_m13B1FFCAD78C7E690E70704311B20D5BB67D8224_AdjustorThunk (void);
extern void FloatTween_GetIgnoreTimescale_mA2463285D4524B70A46776FC60C4F939B3BCD045_AdjustorThunk (void);
extern void FloatTween_GetDuration_m3E981D91F15C36ED6F241117665E703F2BD2A6D4_AdjustorThunk (void);
extern void FloatTween_ValidTarget_m36EABC84C8FEFF79EBAC8E9C3C7A394F1377E311_AdjustorThunk (void);
extern void RaycastResult_get_gameObject_m77014B442B9E2D10F2CC3AEEDC07AA95CDE1E2F1_AdjustorThunk (void);
extern void RaycastResult_set_gameObject_mCFEB66C0E3F01AC5E55040FE8BEB16E40427BD9E_AdjustorThunk (void);
extern void RaycastResult_get_isValid_m69957B97C041A9E3FAF4ECA82BB8099C9FA171CE_AdjustorThunk (void);
extern void RaycastResult_Clear_m0E9DA70AC69CF143CEA8428AFC5BA552F99643AE_AdjustorThunk (void);
extern void RaycastResult_ToString_m0267000494B09783ABD507B9329ADB01FBBC5428_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[72] = 
{
	{ 0x06000037, ColorBlock_get_normalColor_m08A07A74ED743B4B0C1B5A5C35774F2D78F1F20E_AdjustorThunk },
	{ 0x06000038, ColorBlock_set_normalColor_m3EBF594F6FA2C6494ACA9FCB9B458807D85B96F8_AdjustorThunk },
	{ 0x06000039, ColorBlock_get_highlightedColor_m4D1A3D268CB00B351F56934F7F244DBC68855301_AdjustorThunk },
	{ 0x0600003A, ColorBlock_set_highlightedColor_m04E97DF2CCE7CAC47120D8F486E18BF62F16FF86_AdjustorThunk },
	{ 0x0600003B, ColorBlock_get_pressedColor_m5EDADBA10824D08BFB67D02099A9FC6A4235AC62_AdjustorThunk },
	{ 0x0600003C, ColorBlock_set_pressedColor_m644C938090857AB07C57B25FE53F6DC2BB0DD5A8_AdjustorThunk },
	{ 0x0600003D, ColorBlock_get_selectedColor_m41CD59090E997A5982EE5AB9D23811FEB35C82CF_AdjustorThunk },
	{ 0x0600003E, ColorBlock_set_selectedColor_m76FEFB1148798B7A356C974CDEA3BA2E2E3C1D21_AdjustorThunk },
	{ 0x0600003F, ColorBlock_get_disabledColor_m2E20FC772B592ADD71CE1336D29B3C3C1523669E_AdjustorThunk },
	{ 0x06000040, ColorBlock_set_disabledColor_m4D10D1F8525CCC7E8E200E3994AFB28ADABB1D8E_AdjustorThunk },
	{ 0x06000041, ColorBlock_get_colorMultiplier_m54C8F6B7E5ACF45D70F9EAAED78AB4606999C187_AdjustorThunk },
	{ 0x06000042, ColorBlock_set_colorMultiplier_m920A048B95541DB0E92AF4AF3894BE7CD2D37102_AdjustorThunk },
	{ 0x06000043, ColorBlock_get_fadeDuration_m506A0FCC2819DA313BE033640C8FEDC5331D1C39_AdjustorThunk },
	{ 0x06000044, ColorBlock_set_fadeDuration_m8519A304808384CE873377EC104969A6B9FBB6C5_AdjustorThunk },
	{ 0x06000046, ColorBlock_Equals_m20D958BB28F6FDC12D612279AF6B50679C0C1E67_AdjustorThunk },
	{ 0x06000047, ColorBlock_Equals_m52DCE246EA23904A3EF0FCD3ADAB81BC20DC1BE5_AdjustorThunk },
	{ 0x0600004A, ColorBlock_GetHashCode_m3CCB4E1E5EE93B905650E24BD4753096082270D7_AdjustorThunk },
	{ 0x06000367, Navigation_get_mode_m3B574F1549B3806753EC33228EB3FF3031F4E809_AdjustorThunk },
	{ 0x06000368, Navigation_set_mode_m0BEF999F733332AD994CF3CA4AC17B2A47531207_AdjustorThunk },
	{ 0x06000369, Navigation_get_wrapAround_mA24021791B1C67F665065B5A415434837CEA86DD_AdjustorThunk },
	{ 0x0600036A, Navigation_set_wrapAround_m9D808EC49EE5F3AFA7F0D13E86FF9F72AA20A081_AdjustorThunk },
	{ 0x0600036B, Navigation_get_selectOnUp_mD24FC0BAB97E5DBB28C9C7209BAC2ACC9419B183_AdjustorThunk },
	{ 0x0600036C, Navigation_set_selectOnUp_mCB04000FDFC05D3BAC497602E4BA346A536152E5_AdjustorThunk },
	{ 0x0600036D, Navigation_get_selectOnDown_m1D36E990CDB38C4BB78745587668F94BBE8A1285_AdjustorThunk },
	{ 0x0600036E, Navigation_set_selectOnDown_m0EBBAB8C51107F75F63FFBC3DF88D9010E6A44BB_AdjustorThunk },
	{ 0x0600036F, Navigation_get_selectOnLeft_mA4F7DA341D7C660A7E15520B34847B0757C65F81_AdjustorThunk },
	{ 0x06000370, Navigation_set_selectOnLeft_mA4E7480D7CBDA9A5ECA93BAFCD1CF1976A994FCB_AdjustorThunk },
	{ 0x06000371, Navigation_get_selectOnRight_m7A781F4050AE064DC0473E68AA6D07CFFF0A8FF9_AdjustorThunk },
	{ 0x06000372, Navigation_set_selectOnRight_mD0B38024BB628CDC801EA93E9FF7C438ECE2055B_AdjustorThunk },
	{ 0x06000374, Navigation_Equals_mE25B4E3D0AB85C1469B99971E6AB16E2039E6B4D_AdjustorThunk },
	{ 0x06000493, SpriteState_get_highlightedSprite_m5D24B628AB2E4DEBF67E094CCA059BDADAB952BB_AdjustorThunk },
	{ 0x06000494, SpriteState_set_highlightedSprite_mEECDB7C62DE0C6A0B2A7D5D7ADF54EB8CDDB20B0_AdjustorThunk },
	{ 0x06000495, SpriteState_get_pressedSprite_m89052B1818D1659DA7E594F218485F1DEB8128BD_AdjustorThunk },
	{ 0x06000496, SpriteState_set_pressedSprite_mD01568B87B1BC1374CCFB5CD190D7CD62A6FDAA3_AdjustorThunk },
	{ 0x06000497, SpriteState_get_selectedSprite_m5316836E91F7EB454E953CADD439FF69AA198BA5_AdjustorThunk },
	{ 0x06000498, SpriteState_set_selectedSprite_m902ACABEC203C0A2408B4ECD7B74C10DFE7BB340_AdjustorThunk },
	{ 0x06000499, SpriteState_get_disabledSprite_m6BE5A2231E20BE1600328082B4EFE53EE7F3E12C_AdjustorThunk },
	{ 0x0600049A, SpriteState_set_disabledSprite_m624499C245DC34D314FF0326FE5ADCF35DA28E27_AdjustorThunk },
	{ 0x0600049B, SpriteState_Equals_mAF58D9F36662F5A8196071690175AAFCC4506653_AdjustorThunk },
	{ 0x06000560, ColorTween_get_startColor_m9E33FB5C5F76BCF49A3B20201CD8006DBFB46012_AdjustorThunk },
	{ 0x06000561, ColorTween_set_startColor_mD22349343421BD44F0C31E537718ED53BE4850DA_AdjustorThunk },
	{ 0x06000562, ColorTween_get_targetColor_m240A7018BDC3B44AB44BA674AA16C39960BC23FF_AdjustorThunk },
	{ 0x06000563, ColorTween_set_targetColor_m7D8E74B32AC3A9C17C3192096003B12A1500D749_AdjustorThunk },
	{ 0x06000564, ColorTween_get_tweenMode_m06B83FB6E45A807F83FDD762A8241D478FD13F8B_AdjustorThunk },
	{ 0x06000565, ColorTween_set_tweenMode_m105EEB49F6632D6D105C63DA9919385233A5D4DE_AdjustorThunk },
	{ 0x06000566, ColorTween_get_duration_m40D8F08C13FF2FE7583746934C6A017A93398548_AdjustorThunk },
	{ 0x06000567, ColorTween_set_duration_m1C278AB5A90B5C108CEB4870CAC90A9A9EAC19CB_AdjustorThunk },
	{ 0x06000568, ColorTween_get_ignoreTimeScale_mEDB15A4ADE3A0B9487D240964A7571247F974708_AdjustorThunk },
	{ 0x06000569, ColorTween_set_ignoreTimeScale_m060FF3CED06F73EA1F555A37999D61DC58F99927_AdjustorThunk },
	{ 0x0600056A, ColorTween_TweenValue_mF5CBA9BDE7F73E47F9CF26DC4EC2419694049860_AdjustorThunk },
	{ 0x0600056B, ColorTween_AddOnChangedCallback_mAC2856A154604B4B6721DAC185B819A98D6F7438_AdjustorThunk },
	{ 0x0600056C, ColorTween_GetIgnoreTimescale_m679C83012235779A37DCCD0AA75CD6B0DAE5BCFA_AdjustorThunk },
	{ 0x0600056D, ColorTween_GetDuration_mC40D6776769FDB79C7ADC42D59F059A2A9AE2F66_AdjustorThunk },
	{ 0x0600056E, ColorTween_ValidTarget_m1D7A682CE00048FAF1A3BDD55EB76F44C9122B4D_AdjustorThunk },
	{ 0x06000570, FloatTween_get_startValue_mCA121483CCF4C8F10991BB3306E3F2769EBB3A3C_AdjustorThunk },
	{ 0x06000571, FloatTween_set_startValue_m43B55D74B7B34D9C32439D6004F306BFA18E4A1A_AdjustorThunk },
	{ 0x06000572, FloatTween_get_targetValue_m6EFBD9EAB206F145959832269DC24C4B68FEE6B1_AdjustorThunk },
	{ 0x06000573, FloatTween_set_targetValue_m4AE44CE862797E898CDE00A1B7D6A33CE0AFDCFB_AdjustorThunk },
	{ 0x06000574, FloatTween_get_duration_mB1496D38A618FF8282205FD3AA14CA9C6D76454D_AdjustorThunk },
	{ 0x06000575, FloatTween_set_duration_m40E10A7B796B4B54FFB8DA3889B09557BEC98456_AdjustorThunk },
	{ 0x06000576, FloatTween_get_ignoreTimeScale_m6F6BDCBD59C19E68572370F9FE3D7373B4212B3B_AdjustorThunk },
	{ 0x06000577, FloatTween_set_ignoreTimeScale_m09041A4110040F9C86D24E1B4DED6E6B7FB206A8_AdjustorThunk },
	{ 0x06000578, FloatTween_TweenValue_mE51344369BDDA58E9C3AEC62E1B1C1AC0349278E_AdjustorThunk },
	{ 0x06000579, FloatTween_AddOnChangedCallback_m13B1FFCAD78C7E690E70704311B20D5BB67D8224_AdjustorThunk },
	{ 0x0600057A, FloatTween_GetIgnoreTimescale_mA2463285D4524B70A46776FC60C4F939B3BCD045_AdjustorThunk },
	{ 0x0600057B, FloatTween_GetDuration_m3E981D91F15C36ED6F241117665E703F2BD2A6D4_AdjustorThunk },
	{ 0x0600057C, FloatTween_ValidTarget_m36EABC84C8FEFF79EBAC8E9C3C7A394F1377E311_AdjustorThunk },
	{ 0x0600075D, RaycastResult_get_gameObject_m77014B442B9E2D10F2CC3AEEDC07AA95CDE1E2F1_AdjustorThunk },
	{ 0x0600075E, RaycastResult_set_gameObject_mCFEB66C0E3F01AC5E55040FE8BEB16E40427BD9E_AdjustorThunk },
	{ 0x0600075F, RaycastResult_get_isValid_m69957B97C041A9E3FAF4ECA82BB8099C9FA171CE_AdjustorThunk },
	{ 0x06000760, RaycastResult_Clear_m0E9DA70AC69CF143CEA8428AFC5BA552F99643AE_AdjustorThunk },
	{ 0x06000761, RaycastResult_ToString_m0267000494B09783ABD507B9329ADB01FBBC5428_AdjustorThunk },
};
static const int32_t s_InvokerIndices[1903] = 
{
	21892,
	12563,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12563,
	12563,
	12380,
	9510,
	12563,
	9510,
	9510,
	12380,
	12563,
	9453,
	12563,
	12205,
	12380,
	12563,
	12380,
	-1,
	-1,
	-1,
	-1,
	-1,
	12563,
	21775,
	6668,
	12563,
	12563,
	20200,
	17398,
	20677,
	20037,
	6668,
	20677,
	20037,
	6668,
	20677,
	20677,
	9510,
	9510,
	9510,
	9510,
	21725,
	21725,
	21856,
	12210,
	9340,
	12210,
	9340,
	12210,
	9340,
	12210,
	9340,
	12210,
	9340,
	12471,
	9591,
	12471,
	9591,
	21856,
	6668,
	6512,
	17078,
	17078,
	12323,
	12563,
	21775,
	12563,
	20677,
	20677,
	20677,
	17640,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	3591,
	12563,
	21775,
	15846,
	15840,
	20677,
	20677,
	18305,
	18298,
	20360,
	20360,
	20360,
	20360,
	20360,
	20360,
	20360,
	20360,
	20360,
	20360,
	20360,
	21856,
	-1,
	3570,
	12563,
	21856,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12471,
	9591,
	12323,
	9453,
	9453,
	4244,
	12563,
	12563,
	12563,
	12563,
	12563,
	9510,
	9510,
	9510,
	12563,
	9510,
	-1,
	9510,
	9510,
	9510,
	12563,
	8126,
	9510,
	8126,
	9510,
	8126,
	9510,
	1250,
	4857,
	2148,
	9591,
	12563,
	8130,
	12563,
	9510,
	21856,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	9510,
	9510,
	12563,
	12380,
	9510,
	12380,
	9510,
	12563,
	9510,
	9510,
	4756,
	12380,
	9510,
	12563,
	12563,
	12563,
	9335,
	9453,
	12563,
	12205,
	12380,
	12563,
	12380,
	21775,
	12380,
	9510,
	12323,
	9453,
	12323,
	9453,
	12205,
	9335,
	12323,
	9453,
	12323,
	9453,
	12323,
	9453,
	12205,
	9335,
	12205,
	9335,
	12323,
	9453,
	12323,
	9453,
	12471,
	9591,
	12563,
	12563,
	12563,
	20677,
	20677,
	20677,
	21856,
	21775,
	12210,
	9340,
	12205,
	9335,
	12554,
	9675,
	12205,
	9335,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	12323,
	12380,
	12380,
	12563,
	12380,
	12380,
	12380,
	9510,
	12380,
	12380,
	12563,
	12563,
	12563,
	12563,
	12563,
	9453,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	21775,
	9510,
	9510,
	9510,
	12563,
	12563,
	3014,
	8423,
	12424,
	1345,
	640,
	20075,
	2146,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	21856,
	12380,
	12323,
	12323,
	12205,
	9335,
	12323,
	9453,
	12335,
	9465,
	12563,
	12380,
	4756,
	12380,
	14302,
	21856,
	21856,
	12563,
	3301,
	12563,
	21775,
	18305,
	18305,
	18305,
	18305,
	18305,
	18305,
	20342,
	20342,
	21856,
	-1,
	12380,
	9510,
	12563,
	12380,
	9510,
	12380,
	12323,
	9453,
	12205,
	9335,
	12205,
	9335,
	12323,
	9453,
	12471,
	9591,
	12205,
	9335,
	12323,
	9453,
	12471,
	9591,
	12471,
	9591,
	12205,
	9335,
	12563,
	21775,
	12380,
	12205,
	12471,
	9591,
	12471,
	12471,
	12380,
	9510,
	12563,
	12563,
	3874,
	8436,
	12563,
	9510,
	12563,
	12563,
	12563,
	12563,
	12563,
	4732,
	4732,
	9510,
	9510,
	15055,
	13252,
	3665,
	4732,
	13340,
	14311,
	12563,
	12563,
	12471,
	12471,
	12471,
	12471,
	12471,
	12471,
	12323,
	3014,
	3660,
	20677,
	20677,
	20677,
	12563,
	21856,
	12563,
	12205,
	-1,
	-1,
	-1,
	12380,
	12380,
	12563,
	12380,
	12380,
	9335,
	12205,
	9335,
	12205,
	12380,
	9510,
	9510,
	4732,
	12205,
	12471,
	9591,
	12323,
	9453,
	12380,
	9510,
	12380,
	9510,
	12210,
	9340,
	12205,
	9335,
	12210,
	9340,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12323,
	9453,
	12323,
	9453,
	12323,
	9453,
	12323,
	9453,
	12380,
	12323,
	9453,
	12323,
	9453,
	12205,
	9335,
	12205,
	12543,
	9663,
	12205,
	9307,
	12323,
	9453,
	12323,
	9453,
	12205,
	12323,
	9453,
	12323,
	9453,
	12323,
	9453,
	12563,
	12563,
	12563,
	12563,
	12380,
	12563,
	12563,
	12563,
	12563,
	12563,
	9335,
	9335,
	21775,
	20677,
	12205,
	12205,
	12205,
	12416,
	12563,
	12563,
	12563,
	8423,
	3386,
	7823,
	6668,
	9510,
	9510,
	8126,
	9510,
	9510,
	7692,
	6820,
	9510,
	9510,
	12380,
	12323,
	3894,
	12323,
	3894,
	3259,
	3256,
	3256,
	9335,
	3894,
	9335,
	3894,
	12563,
	12563,
	12563,
	9663,
	12563,
	12563,
	12563,
	12563,
	12563,
	9510,
	9663,
	12563,
	12205,
	17397,
	17397,
	9453,
	12563,
	12563,
	9453,
	12563,
	12563,
	12563,
	12563,
	9510,
	4803,
	12563,
	4803,
	1849,
	12563,
	12563,
	9510,
	9510,
	12563,
	9510,
	9510,
	12563,
	12563,
	9510,
	12563,
	4244,
	12563,
	12563,
	12471,
	12471,
	12471,
	12471,
	12471,
	12471,
	12323,
	21856,
	12380,
	4749,
	1849,
	599,
	8394,
	12563,
	12563,
	12563,
	9453,
	12563,
	12205,
	12380,
	12563,
	12380,
	9453,
	12563,
	12205,
	12380,
	12563,
	12380,
	12323,
	9453,
	12471,
	9591,
	12380,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	3612,
	12550,
	12563,
	12563,
	12563,
	12205,
	12205,
	12205,
	12323,
	9453,
	12471,
	9591,
	12471,
	9591,
	12550,
	9671,
	12323,
	9453,
	12471,
	9591,
	12323,
	9453,
	12471,
	9591,
	12471,
	9591,
	12471,
	9591,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	9591,
	9591,
	12323,
	9453,
	12323,
	9453,
	12380,
	12563,
	12563,
	12563,
	12563,
	9453,
	12563,
	12563,
	12563,
	12323,
	9453,
	12323,
	9453,
	12550,
	9671,
	12550,
	9671,
	12323,
	9453,
	12323,
	9453,
	12563,
	12563,
	12563,
	12563,
	12563,
	9453,
	12563,
	12563,
	12563,
	12563,
	12563,
	12471,
	9591,
	12205,
	9335,
	12205,
	9335,
	12205,
	9335,
	12205,
	9335,
	12205,
	9335,
	12205,
	9335,
	12205,
	9335,
	4244,
	4244,
	208,
	12563,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	12205,
	9335,
	12563,
	12563,
	12471,
	9591,
	12471,
	9591,
	12471,
	9591,
	12471,
	9591,
	12471,
	9591,
	12471,
	9591,
	12323,
	9453,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	12380,
	9510,
	12323,
	9453,
	12380,
	12380,
	12563,
	-1,
	12471,
	12471,
	12471,
	12471,
	12471,
	12471,
	12323,
	-1,
	-1,
	12563,
	12563,
	12563,
	12563,
	8268,
	8268,
	8268,
	3602,
	8268,
	1515,
	2072,
	1447,
	1447,
	697,
	12205,
	12563,
	12563,
	-1,
	12563,
	8126,
	9453,
	12563,
	12205,
	12380,
	12563,
	12380,
	9510,
	12563,
	21856,
	20677,
	12380,
	12205,
	20677,
	20677,
	9453,
	4756,
	4756,
	20677,
	17136,
	20677,
	12563,
	12563,
	12323,
	6668,
	12380,
	12563,
	21856,
	12563,
	12380,
	9510,
	6668,
	9510,
	9510,
	9510,
	9510,
	17682,
	17682,
	17682,
	20450,
	20450,
	20450,
	20450,
	20450,
	20450,
	15890,
	14890,
	21856,
	12563,
	8269,
	8269,
	8269,
	8269,
	8269,
	8269,
	8269,
	8269,
	12563,
	12563,
	12563,
	12563,
	12563,
	12380,
	12205,
	9335,
	12380,
	12563,
	12205,
	12563,
	12563,
	12563,
	3014,
	8126,
	12380,
	9510,
	12205,
	9335,
	12205,
	9335,
	8126,
	4832,
	9335,
	4832,
	9671,
	12563,
	12563,
	12563,
	12563,
	12563,
	12424,
	12563,
	12563,
	12563,
	12563,
	12380,
	12563,
	20677,
	20677,
	20342,
	17398,
	17136,
	20342,
	18305,
	12563,
	-1,
	20677,
	20677,
	17128,
	20640,
	17806,
	12323,
	9453,
	12205,
	9335,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	21774,
	6666,
	12563,
	12380,
	12380,
	9510,
	12424,
	9550,
	12563,
	9510,
	12563,
	12554,
	9675,
	12551,
	9672,
	12380,
	12424,
	12380,
	12563,
	12563,
	12563,
	12563,
	3014,
	12424,
	12563,
	12563,
	9510,
	9510,
	12563,
	12563,
	12380,
	9510,
	12323,
	9453,
	12563,
	12471,
	9591,
	9591,
	12471,
	9591,
	12323,
	9453,
	12380,
	9510,
	12471,
	9453,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	4854,
	12563,
	12323,
	12205,
	12563,
	9510,
	2128,
	4907,
	6668,
	9510,
	9510,
	9510,
	8126,
	3578,
	9510,
	9510,
	12380,
	12380,
	12380,
	12380,
	9510,
	4244,
	12380,
	12563,
	9453,
	12563,
	12205,
	12380,
	12563,
	12380,
	12380,
	9510,
	12205,
	9335,
	12205,
	9335,
	12323,
	9453,
	12471,
	9591,
	12205,
	9335,
	12471,
	9591,
	12471,
	9591,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12323,
	9453,
	12323,
	9453,
	12471,
	9591,
	12471,
	9591,
	12380,
	9510,
	12380,
	12550,
	9671,
	12380,
	12563,
	9453,
	12563,
	12563,
	12563,
	12563,
	12563,
	12205,
	12563,
	12563,
	9510,
	9510,
	9510,
	9510,
	9510,
	9671,
	12563,
	12563,
	9671,
	12550,
	9671,
	12471,
	9591,
	12471,
	9591,
	9591,
	9591,
	4856,
	17689,
	12563,
	12205,
	12205,
	12563,
	12563,
	12471,
	12471,
	12471,
	12471,
	12471,
	12471,
	12323,
	12563,
	12563,
	12563,
	14979,
	12563,
	12563,
	14922,
	12202,
	16733,
	8423,
	13179,
	12563,
	12563,
	12380,
	12563,
	21775,
	21757,
	21775,
	20200,
	12377,
	9507,
	12323,
	9453,
	12212,
	9342,
	12474,
	9594,
	12380,
	9510,
	12380,
	9510,
	12205,
	9335,
	12205,
	9335,
	12205,
	9335,
	12205,
	9335,
	12563,
	12380,
	9510,
	12380,
	12563,
	12563,
	12205,
	12205,
	12563,
	12563,
	12563,
	12563,
	12563,
	9335,
	12323,
	12563,
	4244,
	8141,
	17800,
	4756,
	12380,
	12380,
	12380,
	12380,
	9510,
	3904,
	9510,
	9510,
	12205,
	12205,
	12563,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	12563,
	21856,
	17043,
	-1,
	-1,
	12380,
	9510,
	12380,
	9510,
	12323,
	9453,
	12471,
	9591,
	12471,
	9591,
	12205,
	9335,
	12471,
	9591,
	9591,
	12471,
	9591,
	12380,
	9510,
	12471,
	12563,
	9453,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	8271,
	4854,
	12563,
	12323,
	12205,
	12563,
	4756,
	6668,
	9510,
	9510,
	9510,
	12380,
	12380,
	12380,
	12380,
	9510,
	4244,
	12380,
	12563,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	6755,
	17560,
	13811,
	18305,
	13038,
	20677,
	21856,
	21856,
	12563,
	12563,
	12380,
	12380,
	12380,
	12563,
	12380,
	9510,
	12380,
	9510,
	12205,
	9335,
	12205,
	9335,
	12323,
	9453,
	12323,
	9453,
	12323,
	9453,
	12205,
	9335,
	12323,
	9453,
	12323,
	9453,
	12323,
	9453,
	12471,
	9591,
	12323,
	9453,
	12471,
	12563,
	12563,
	12563,
	12563,
	12563,
	8349,
	20629,
	9510,
	12563,
	12563,
	12471,
	12471,
	12471,
	12471,
	12471,
	12471,
	12323,
	12380,
	9510,
	12563,
	9453,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	4732,
	12205,
	9335,
	9335,
	3894,
	9335,
	12563,
	12563,
	9510,
	9510,
	12380,
	12563,
	12205,
	9335,
	12563,
	12563,
	12563,
	9510,
	4732,
	9510,
	9510,
	12563,
	12205,
	12380,
	12380,
	9335,
	21856,
	12563,
	6668,
	6668,
	12563,
	21775,
	4749,
	804,
	286,
	2751,
	4749,
	1795,
	605,
	8126,
	4749,
	1224,
	287,
	7692,
	4749,
	1273,
	288,
	8202,
	4749,
	1795,
	605,
	8126,
	4749,
	1224,
	287,
	7692,
	12563,
	9510,
	12563,
	12563,
	12563,
	12323,
	12323,
	3844,
	4878,
	9510,
	147,
	397,
	2170,
	9662,
	1993,
	9510,
	4756,
	9510,
	9510,
	21856,
	-1,
	12563,
	12380,
	12563,
	12563,
	12563,
	9510,
	-1,
	12563,
	-1,
	-1,
	-1,
	12563,
	9510,
	12563,
	9510,
	12563,
	12210,
	9340,
	12550,
	9671,
	12205,
	9335,
	336,
	336,
	9510,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	12210,
	9340,
	12210,
	9340,
	12323,
	9453,
	12471,
	9591,
	12205,
	9335,
	9591,
	9510,
	12205,
	12471,
	12205,
	12563,
	12471,
	9591,
	12471,
	9591,
	12471,
	9591,
	12205,
	9335,
	9591,
	9510,
	12205,
	12471,
	12205,
	12563,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	12380,
	9510,
	12380,
	12380,
	12205,
	12380,
	12563,
	12563,
	12563,
	12563,
	12563,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	4756,
	4756,
	12563,
	12563,
	9510,
	4756,
	4756,
	2068,
	4756,
	4756,
	1664,
	12563,
	12323,
	9453,
	12380,
	9510,
	12205,
	9335,
	12323,
	9453,
	12323,
	9453,
	12552,
	9673,
	12552,
	9673,
	12552,
	9673,
	12471,
	9591,
	12323,
	9453,
	12471,
	9591,
	12471,
	9591,
	12471,
	9591,
	12471,
	9591,
	12471,
	9591,
	12550,
	9671,
	12323,
	9453,
	12550,
	9671,
	12550,
	9671,
	12323,
	9453,
	12205,
	12205,
	12205,
	12205,
	12205,
	2093,
	4913,
	12563,
	15526,
	12380,
	9510,
	12563,
	12563,
	12563,
	12380,
	12323,
	12323,
	4756,
	12380,
	12563,
	12550,
	9671,
	12323,
	9453,
	9510,
	12563,
	12563,
	12205,
	12563,
	9510,
	12380,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12422,
	9548,
	12422,
	9548,
	12205,
	9335,
	12323,
	9453,
	12323,
	9453,
	12550,
	9671,
	12550,
	9671,
	12550,
	9671,
	12552,
	9673,
	12552,
	9673,
	12471,
	9591,
	12323,
	9453,
	12550,
	9671,
	12205,
	9335,
	12205,
	9335,
	12323,
	9453,
	12471,
	9591,
	12471,
	9591,
	12471,
	9591,
	12471,
	9591,
	12471,
	9591,
	12550,
	9671,
	12323,
	9453,
	12550,
	9671,
	12550,
	9671,
	12205,
	9335,
	12205,
	9335,
	9510,
	12205,
	12205,
	12380,
	12380,
	12380,
	9510,
	12380,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	21775,
	20677,
	12205,
	9335,
	12323,
	9453,
	12380,
	12380,
	9510,
	12380,
	12380,
	12205,
	12563,
	12563,
	12205,
	4756,
	12380,
	9510,
	17407,
	4756,
	12205,
	6613,
	12205,
	12205,
	12205,
	16419,
	12563,
	12563,
	9510,
	12563,
	12563,
	12563,
	12563,
	9335,
	12563,
	9510,
	12380,
	21856,
	12563,
	12563,
	12380,
	9510,
	12563,
	12380,
	9510,
	4365,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	9510,
	12563,
	12563,
	-1,
	18305,
	18305,
	18305,
	18305,
	18305,
	18305,
	18305,
	18305,
	18305,
	18305,
	18305,
	18305,
	18305,
	18305,
	18305,
	18305,
	18305,
	18305,
	21775,
	21775,
	21775,
	21775,
	21775,
	21775,
	21775,
	21775,
	21775,
	21775,
	21775,
	21775,
	21775,
	21775,
	21775,
	21775,
	21775,
	21775,
	18305,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	21856,
	-1,
	-1,
	-1,
	-1,
	12380,
	12323,
	9453,
	12550,
	9671,
	12205,
	6613,
	6613,
	6613,
	12550,
	12550,
	12471,
	12205,
	12323,
	8374,
	8269,
	6668,
	12563,
	12205,
	9335,
	12380,
	12380,
	9510,
	12380,
	12563,
	12563,
	-1,
	20395,
	17411,
	15736,
	17564,
	4756,
	1796,
	12380,
	6613,
	12205,
	12563,
	12563,
	12563,
	12205,
	7692,
	8423,
	12563,
	1605,
	9510,
	1798,
	4756,
	7646,
	12380,
	8118,
	8118,
	14447,
	9510,
	9510,
	6613,
	12563,
	12380,
	4756,
	12563,
	12380,
	9510,
	12323,
	9453,
	12563,
	12205,
	12205,
	8118,
	1995,
	12563,
	12205,
	12205,
	12563,
	12563,
	12323,
	12205,
	9335,
	12205,
	9335,
	12471,
	9591,
	12471,
	9591,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12380,
	9510,
	12205,
	12563,
	4756,
	12205,
	12563,
	12563,
	12563,
	12205,
	2045,
	12205,
	12550,
	12205,
	12563,
	12205,
	9453,
	12205,
	9510,
	12380,
	12563,
	12205,
	9335,
	12205,
	9335,
	12563,
	12205,
	12205,
	12205,
	12563,
	12563,
	12563,
	2045,
	12563,
	12380,
	20677,
	21775,
	20677,
	21856,
	-1,
	-1,
	12323,
	12323,
	12323,
	12380,
	12380,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	4756,
	12563,
	12380,
	12323,
	12323,
	12335,
	9465,
	12323,
	9453,
	787,
	4756,
	3318,
	12563,
	21856,
	12380,
	9510,
	12205,
	12563,
	12380,
	12563,
	12563,
	12563,
	12563,
	12563,
	12205,
	12563,
	12563,
	12563,
	12563,
	12563,
	12563,
	12205,
	12563,
};
static const Il2CppTokenRangePair s_rgctxIndices[14] = 
{
	{ 0x02000089, { 15, 32 } },
	{ 0x02000090, { 47, 8 } },
	{ 0x02000091, { 55, 6 } },
	{ 0x06000090, { 0, 3 } },
	{ 0x060002F7, { 3, 3 } },
	{ 0x06000461, { 6, 6 } },
	{ 0x06000462, { 12, 3 } },
	{ 0x06000692, { 61, 2 } },
	{ 0x060006B8, { 63, 5 } },
	{ 0x060006B9, { 68, 2 } },
	{ 0x060006BA, { 70, 1 } },
	{ 0x060006BB, { 71, 1 } },
	{ 0x060006BC, { 72, 1 } },
	{ 0x060006BD, { 73, 1 } },
};
extern const uint32_t g_rgctx_GameObject_GetComponent_TisT_tDCDB222928733797E8A881B5E6860AECE6D2C87B_m4A9D4F24F342D6ED79FC709ACEEA1F5DB66ECE68;
extern const uint32_t g_rgctx_T_tDCDB222928733797E8A881B5E6860AECE6D2C87B;
extern const uint32_t g_rgctx_GameObject_AddComponent_TisT_tDCDB222928733797E8A881B5E6860AECE6D2C87B_mA3F5345F1762DBB8509064C972E1634189F75AB6;
extern const uint32_t g_rgctx_TU26_tC96C8640C4E3D1BDA7A5710344EF27455ECAC7A4;
extern const uint32_t g_rgctx_T_t4CE5249EE868EADA2EF337B748A959CE36EDC68C;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t4CE5249EE868EADA2EF337B748A959CE36EDC68C_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B;
extern const uint32_t g_rgctx_EqualityComparer_1_get_Default_m3E4622851CCB9DA5DC1CEB3289DDBE10F5424603;
extern const uint32_t g_rgctx_EqualityComparer_1_tBBA6C41BC096CA6655CB0E0ECD1E295787CD837C;
extern const uint32_t g_rgctx_EqualityComparer_1_tBBA6C41BC096CA6655CB0E0ECD1E295787CD837C;
extern const uint32_t g_rgctx_TU26_t38035F51E4BA978B12A0828B3F61D3C4CCD74ECA;
extern const uint32_t g_rgctx_T_t2B35672C0DFF2AE016E1355CBCBFFADAEBE7E740;
extern const uint32_t g_rgctx_EqualityComparer_1_Equals_m6F4573F4EA78FD68AFF236B0A106A95AA0BB7BBA;
extern const uint32_t g_rgctx_TU26_t961BCE8CED5491E386706ADF07A6D3F5A60CA208;
extern const uint32_t g_rgctx_T_tAD0C781C6CA5D4906775F4DB23C0757A642BE3CC;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tAD0C781C6CA5D4906775F4DB23C0757A642BE3CC_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B;
extern const uint32_t g_rgctx_T_tF32BE10FE67E96738E5F6FB28BD3C8C747C643D1;
extern const uint32_t g_rgctx_IndexedSet_1_Add_mD20EC572DE14037FD7A9FE5DD175094403539AA1;
extern const uint32_t g_rgctx_IndexedSet_1_tA3883B427A205C0E87FEF8C904A5B81A28E21919;
extern const uint32_t g_rgctx_List_1_t2D02B439A60142305D30BD294433449D8922C8C6;
extern const uint32_t g_rgctx_List_1_Add_m8D4D0140F1E49FA04DA28F07F993A26C5EDE7C88;
extern const uint32_t g_rgctx_Dictionary_2_t47F9FA6433A13FDC91FE9527F27DE9E61ED763C6;
extern const uint32_t g_rgctx_List_1_get_Count_m0DC4CD1127534421D09FEA705DE80F6694752412;
extern const uint32_t g_rgctx_Dictionary_2_Add_m3A38D2FC6C96C849FC10E687D72097305ABFF096;
extern const uint32_t g_rgctx_IndexedSet_1_EnableItem_mC8D13DF12750C15FBCEF80C2B1D03466FA4502C2;
extern const uint32_t g_rgctx_Dictionary_2_ContainsKey_m5DC4DD0C23CCA8B57FCFB320720E5DD6C285E362;
extern const uint32_t g_rgctx_IndexedSet_1_DisableItem_m9040FC7F6E07351B87855B597A94D8EEF7452170;
extern const uint32_t g_rgctx_Dictionary_2_TryGetValue_m61E88BDD99D44D8ED0D0005AC6BD763FD4F6AD3A;
extern const uint32_t g_rgctx_IndexedSet_1_Swap_mD3B9A95183A1DCC547EB9470165257DA74A23E1B;
extern const uint32_t g_rgctx_IndexedSet_1_RemoveAt_m93460241D6FE2C5A76BCF5ED7E138C6A3A2098F6;
extern const uint32_t g_rgctx_IEnumerator_1_tFF83E67A73976F90FAEACDBF90E8610001610A94;
extern const uint32_t g_rgctx_IndexedSet_1_GetEnumerator_m4F29E52D3658BED5203041BAEB69CC34711A4C2D;
extern const uint32_t g_rgctx_List_1_Clear_mAB6EF3ED221BAADA6E88D0E217FE2A41541E4B34;
extern const uint32_t g_rgctx_Dictionary_2_Clear_m4FF43671D9D1BD0E0CD0B70F8B1D6D857B0FF449;
extern const uint32_t g_rgctx_TU5BU5D_t2B2021832352ED07933F5A8ECDF7D7E8AD3599D0;
extern const uint32_t g_rgctx_List_1_CopyTo_m2C75509C4BFDE2430579C027C9999F0870B2B59D;
extern const uint32_t g_rgctx_List_1_get_Item_mC6651E26E5A9883DB3DD16C72E206325C717F93E;
extern const uint32_t g_rgctx_List_1_RemoveAt_m97C070EC37AF2B92EBC68E0EF8B2F0E4D556F1F3;
extern const uint32_t g_rgctx_Dictionary_2_Remove_m1D7F2A69EDBA5BFAD35F9E7C416B0073A98516CC;
extern const uint32_t g_rgctx_List_1_set_Item_m140B60FAA886CF2400F87C32F9A7D045AAB98B32;
extern const uint32_t g_rgctx_Dictionary_2_set_Item_m33F1EE8B7595E01E9CE2E6D041640A01B2B50988;
extern const uint32_t g_rgctx_Predicate_1_tA2EDE53213BCD07C8FE4C3A0110189780F365E88;
extern const uint32_t g_rgctx_Predicate_1_Invoke_mD18C22C930F67CBD3C1DA61D885594BD3B2F561C;
extern const uint32_t g_rgctx_IndexedSet_1_Remove_mA0884D2F7B5BF67DDEC2434A3C6950A9B7CDF784;
extern const uint32_t g_rgctx_Comparison_1_t09F478A0AD03BD796140922FD28198B2872A38FC;
extern const uint32_t g_rgctx_List_1_Sort_mA6609F95F5F8AE75DCE485B9059573B94E3BB6A9;
extern const uint32_t g_rgctx_List_1__ctor_mD37D3EB3894802797A15134D2B5A16F58D3ACFD8;
extern const uint32_t g_rgctx_Dictionary_2__ctor_mCF27B5F0486A62F3B2C2F515946616955DF00FE6;
extern const uint32_t g_rgctx_U3CStartU3Ed__2_t34E76D7E9AFCC508842E5758DE2CAFEE5E3B0DB4;
extern const uint32_t g_rgctx_U3CStartU3Ed__2__ctor_m66854530A2219382341C781A18D794C811A02705;
extern const uint32_t g_rgctx_T_tDD41B39C2629C5EB98444C2FDDAFD12366A424ED;
extern const uint32_t g_rgctx_TweenRunner_1_t93A997920D7FA94D4C40C3554D50391D6CA04EBB;
extern const uint32_t g_rgctx_TweenRunner_1_StopTween_m9B63AE5A4006FC35334F581AE9D2B31345B40C1B;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tDD41B39C2629C5EB98444C2FDDAFD12366A424ED_ITweenValue_TweenValue_m2C736D23475DD9895C07A345BD3B41CD0A96486D;
extern const uint32_t g_rgctx_TweenRunner_1_Start_m3C1186007278CF3B2081B3E7B0636E780DCBAF07;
extern const uint32_t g_rgctx_TweenRunner_1_t93A997920D7FA94D4C40C3554D50391D6CA04EBB;
extern const uint32_t g_rgctx_U3CStartU3Ed__2_tE66C782D751AB27356DD757092861689732B7112;
extern const uint32_t g_rgctx_T_tD5818FCC55D7393F6B75BFE81A5252965161FCDF;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tD5818FCC55D7393F6B75BFE81A5252965161FCDF_ITweenValue_ValidTarget_m740605403325DF395B81749DB73275C2CA9B2DA9;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tD5818FCC55D7393F6B75BFE81A5252965161FCDF_ITweenValue_get_ignoreTimeScale_m13D8C5499B95314B248A92C936CE08E1580F9332;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tD5818FCC55D7393F6B75BFE81A5252965161FCDF_ITweenValue_get_duration_mB900628EB167B01368D3F5E1B6790AA5F8E0501A;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tD5818FCC55D7393F6B75BFE81A5252965161FCDF_ITweenValue_TweenValue_m2C736D23475DD9895C07A345BD3B41CD0A96486D;
extern const uint32_t g_rgctx_T_t58E37477D59675354F05DF74854CDE6C83341AC0;
extern const uint32_t g_rgctx_T_t58E37477D59675354F05DF74854CDE6C83341AC0;
extern const uint32_t g_rgctx_ExecuteEvents_GetEventList_TisT_tD2D65B1E49BADA45153C291D66E3D03F695D0EF3_mCA6E5D0B1E3D1D3F9FE2E9EB346D1DE8E20D2C2D;
extern const uint32_t g_rgctx_T_tD2D65B1E49BADA45153C291D66E3D03F695D0EF3;
extern const uint32_t g_rgctx_T_tD2D65B1E49BADA45153C291D66E3D03F695D0EF3;
extern const uint32_t g_rgctx_EventFunction_1_t22CB52A9851B399DC428B842E24E65952F3CEE08;
extern const uint32_t g_rgctx_EventFunction_1_Invoke_m06CD6B5F492CB3D17CF986E50B042AC8423D6566;
extern const uint32_t g_rgctx_EventFunction_1_tBAFF9AC9F1ADA4EDF1250F025E6A46F511B14259;
extern const uint32_t g_rgctx_ExecuteEvents_Execute_TisT_tA38065E18CDAB29F8D4D45B3D81509DFFE58A55D_m9AD6EDD5C3C4681CFF2D5481545F40CCEF708CB6;
extern const uint32_t g_rgctx_T_t3D7CB97D1CD271643D41497DBCD44565829B30BB;
extern const uint32_t g_rgctx_ExecuteEvents_ShouldSendToComponent_TisT_t02FD379FA294C29488ED497ADADDDE5C474B8875_m3FF7985681EFF3C08C818438428725D0D421BE46;
extern const uint32_t g_rgctx_ExecuteEvents_GetEventList_TisT_tA0E8C0350401F48FF3B7EBBEB7545D889BC028F8_mB695606F30CF787AC875F65414D59FA63865709E;
extern const uint32_t g_rgctx_ExecuteEvents_CanHandleEvent_TisT_t972CBEB03C4BD4F4E3B581D83A5C518ACCA216ED_mA0E3EB65CFD909872401B0296F3393AF7269BD0F;
static const Il2CppRGCTXDefinition s_rgctxValues[74] = 
{
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponent_TisT_tDCDB222928733797E8A881B5E6860AECE6D2C87B_m4A9D4F24F342D6ED79FC709ACEEA1F5DB66ECE68 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tDCDB222928733797E8A881B5E6860AECE6D2C87B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_AddComponent_TisT_tDCDB222928733797E8A881B5E6860AECE6D2C87B_mA3F5345F1762DBB8509064C972E1634189F75AB6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tC96C8640C4E3D1BDA7A5710344EF27455ECAC7A4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t4CE5249EE868EADA2EF337B748A959CE36EDC68C },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t4CE5249EE868EADA2EF337B748A959CE36EDC68C_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_get_Default_m3E4622851CCB9DA5DC1CEB3289DDBE10F5424603 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_tBBA6C41BC096CA6655CB0E0ECD1E295787CD837C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_tBBA6C41BC096CA6655CB0E0ECD1E295787CD837C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t38035F51E4BA978B12A0828B3F61D3C4CCD74ECA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2B35672C0DFF2AE016E1355CBCBFFADAEBE7E740 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_Equals_m6F4573F4EA78FD68AFF236B0A106A95AA0BB7BBA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t961BCE8CED5491E386706ADF07A6D3F5A60CA208 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tAD0C781C6CA5D4906775F4DB23C0757A642BE3CC },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tAD0C781C6CA5D4906775F4DB23C0757A642BE3CC_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF32BE10FE67E96738E5F6FB28BD3C8C747C643D1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IndexedSet_1_Add_mD20EC572DE14037FD7A9FE5DD175094403539AA1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IndexedSet_1_tA3883B427A205C0E87FEF8C904A5B81A28E21919 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t2D02B439A60142305D30BD294433449D8922C8C6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m8D4D0140F1E49FA04DA28F07F993A26C5EDE7C88 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_t47F9FA6433A13FDC91FE9527F27DE9E61ED763C6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_m0DC4CD1127534421D09FEA705DE80F6694752412 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Add_m3A38D2FC6C96C849FC10E687D72097305ABFF096 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IndexedSet_1_EnableItem_mC8D13DF12750C15FBCEF80C2B1D03466FA4502C2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_ContainsKey_m5DC4DD0C23CCA8B57FCFB320720E5DD6C285E362 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IndexedSet_1_DisableItem_m9040FC7F6E07351B87855B597A94D8EEF7452170 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_TryGetValue_m61E88BDD99D44D8ED0D0005AC6BD763FD4F6AD3A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IndexedSet_1_Swap_mD3B9A95183A1DCC547EB9470165257DA74A23E1B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IndexedSet_1_RemoveAt_m93460241D6FE2C5A76BCF5ED7E138C6A3A2098F6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tFF83E67A73976F90FAEACDBF90E8610001610A94 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IndexedSet_1_GetEnumerator_m4F29E52D3658BED5203041BAEB69CC34711A4C2D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_mAB6EF3ED221BAADA6E88D0E217FE2A41541E4B34 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Clear_m4FF43671D9D1BD0E0CD0B70F8B1D6D857B0FF449 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t2B2021832352ED07933F5A8ECDF7D7E8AD3599D0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_CopyTo_m2C75509C4BFDE2430579C027C9999F0870B2B59D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_mC6651E26E5A9883DB3DD16C72E206325C717F93E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_RemoveAt_m97C070EC37AF2B92EBC68E0EF8B2F0E4D556F1F3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Remove_m1D7F2A69EDBA5BFAD35F9E7C416B0073A98516CC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_set_Item_m140B60FAA886CF2400F87C32F9A7D045AAB98B32 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_set_Item_m33F1EE8B7595E01E9CE2E6D041640A01B2B50988 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Predicate_1_tA2EDE53213BCD07C8FE4C3A0110189780F365E88 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Predicate_1_Invoke_mD18C22C930F67CBD3C1DA61D885594BD3B2F561C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IndexedSet_1_Remove_mA0884D2F7B5BF67DDEC2434A3C6950A9B7CDF784 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Comparison_1_t09F478A0AD03BD796140922FD28198B2872A38FC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Sort_mA6609F95F5F8AE75DCE485B9059573B94E3BB6A9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_mD37D3EB3894802797A15134D2B5A16F58D3ACFD8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_mCF27B5F0486A62F3B2C2F515946616955DF00FE6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CStartU3Ed__2_t34E76D7E9AFCC508842E5758DE2CAFEE5E3B0DB4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CStartU3Ed__2__ctor_m66854530A2219382341C781A18D794C811A02705 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tDD41B39C2629C5EB98444C2FDDAFD12366A424ED },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenRunner_1_t93A997920D7FA94D4C40C3554D50391D6CA04EBB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TweenRunner_1_StopTween_m9B63AE5A4006FC35334F581AE9D2B31345B40C1B },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tDD41B39C2629C5EB98444C2FDDAFD12366A424ED_ITweenValue_TweenValue_m2C736D23475DD9895C07A345BD3B41CD0A96486D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TweenRunner_1_Start_m3C1186007278CF3B2081B3E7B0636E780DCBAF07 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenRunner_1_t93A997920D7FA94D4C40C3554D50391D6CA04EBB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CStartU3Ed__2_tE66C782D751AB27356DD757092861689732B7112 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD5818FCC55D7393F6B75BFE81A5252965161FCDF },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tD5818FCC55D7393F6B75BFE81A5252965161FCDF_ITweenValue_ValidTarget_m740605403325DF395B81749DB73275C2CA9B2DA9 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tD5818FCC55D7393F6B75BFE81A5252965161FCDF_ITweenValue_get_ignoreTimeScale_m13D8C5499B95314B248A92C936CE08E1580F9332 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tD5818FCC55D7393F6B75BFE81A5252965161FCDF_ITweenValue_get_duration_mB900628EB167B01368D3F5E1B6790AA5F8E0501A },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tD5818FCC55D7393F6B75BFE81A5252965161FCDF_ITweenValue_TweenValue_m2C736D23475DD9895C07A345BD3B41CD0A96486D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t58E37477D59675354F05DF74854CDE6C83341AC0 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t58E37477D59675354F05DF74854CDE6C83341AC0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExecuteEvents_GetEventList_TisT_tD2D65B1E49BADA45153C291D66E3D03F695D0EF3_mCA6E5D0B1E3D1D3F9FE2E9EB346D1DE8E20D2C2D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD2D65B1E49BADA45153C291D66E3D03F695D0EF3 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tD2D65B1E49BADA45153C291D66E3D03F695D0EF3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EventFunction_1_t22CB52A9851B399DC428B842E24E65952F3CEE08 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EventFunction_1_Invoke_m06CD6B5F492CB3D17CF986E50B042AC8423D6566 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EventFunction_1_tBAFF9AC9F1ADA4EDF1250F025E6A46F511B14259 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExecuteEvents_Execute_TisT_tA38065E18CDAB29F8D4D45B3D81509DFFE58A55D_m9AD6EDD5C3C4681CFF2D5481545F40CCEF708CB6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3D7CB97D1CD271643D41497DBCD44565829B30BB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExecuteEvents_ShouldSendToComponent_TisT_t02FD379FA294C29488ED497ADADDDE5C474B8875_m3FF7985681EFF3C08C818438428725D0D421BE46 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExecuteEvents_GetEventList_TisT_tA0E8C0350401F48FF3B7EBBEB7545D889BC028F8_mB695606F30CF787AC875F65414D59FA63865709E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExecuteEvents_CanHandleEvent_TisT_t972CBEB03C4BD4F4E3B581D83A5C518ACCA216ED_mA0E3EB65CFD909872401B0296F3393AF7269BD0F },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_UI_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_UI_CodeGenModule = 
{
	"UnityEngine.UI.dll",
	1903,
	s_methodPointers,
	72,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	14,
	s_rgctxIndices,
	74,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
