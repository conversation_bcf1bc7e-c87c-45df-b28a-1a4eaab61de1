﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void FaceInfo_get_faceIndex_m3C9FB6429035CD34ACD201FB5951AF06E9C2A641 (void);
extern void FaceInfo_get_familyName_m62DAF5DE45EA9F3B300B927603D101D350D27241 (void);
extern void FaceInfo_get_styleName_mACBAA6529BCE3BC14987645691350A4FB1CB0FC6 (void);
extern void FaceInfo_get_pointSize_mDCB57F74233408806DF4F658751ECEE9A66C003E (void);
extern void FaceInfo_get_scale_mC475A572AD4956B47D8B9F8D90DC69BBBB102FCD (void);
extern void FaceInfo_set_scale_m379253929403DA08480CAD127C908A0E8329A5D4 (void);
extern void FaceInfo_get_unitsPerEM_m92CD79B71FC504DE1C87923716E999D85A35AC0E (void);
extern void FaceInfo_set_unitsPerEM_m3E48D789D666984A4EA8D221BED22833761E9A90 (void);
extern void FaceInfo_get_lineHeight_m528B4A822181FCECF3D4FF1045DF288E5872AB9D (void);
extern void FaceInfo_get_ascentLine_m193755D649428EC24A7E433A1728F11DA7547ABD (void);
extern void FaceInfo_get_capLine_m0D95B5D5CEC5CFB12091F5EB5965DE6E38588C88 (void);
extern void FaceInfo_set_capLine_m4716D75CE87EC018E5EA3DE87DA703DBE7A9FEEF (void);
extern void FaceInfo_get_meanLine_m5FF396E0E32A046C1D3CBD3AC1FA279984394D96 (void);
extern void FaceInfo_set_meanLine_mE957CD43CB778B093331CC4B10A5330E8FF1CB99 (void);
extern void FaceInfo_get_baseline_m934B597D3E0080FEF98CBDD091C457B497179C3A (void);
extern void FaceInfo_get_descentLine_m811A243C9B328B0C546BF9927A010A05DF172BD3 (void);
extern void FaceInfo_get_superscriptOffset_m8D462DB86414D8507C7D1CC6881DA9EC896FB80A (void);
extern void FaceInfo_get_superscriptSize_mC3ABE7C70559A8214294CDA598B17FD62BDC2EE0 (void);
extern void FaceInfo_get_subscriptOffset_mF1D3E68AC3D449CBC73AA0CBF5B8A187C6C5285A (void);
extern void FaceInfo_get_subscriptSize_mF6264BFB215FDE6C94A45D2F8FC946ADFCDD2E31 (void);
extern void FaceInfo_get_underlineOffset_mB1CBB29ECFFE69047F35E654E7F90755F95DD251 (void);
extern void FaceInfo_get_underlineThickness_mC032F8C026994AF3FD49E6AB12E113F61EFA98E2 (void);
extern void FaceInfo_get_strikethroughOffset_m7997E4A1512FE358331B3A6543C62C92A0AA5CA5 (void);
extern void FaceInfo_set_strikethroughOffset_m57B05848BDA21F7AF6563394D29C8AE7107FEFF0 (void);
extern void FaceInfo_get_tabWidth_mC6D9F42C40EDD767DE22050E4FBE3878AC96B161 (void);
extern void GlyphRect_get_x_m453EECC6C6F08602B1F74C5E1D8EE1163236A898 (void);
extern void GlyphRect_get_y_mE31390BB3185EEA82DD16EA41E208F6A0397E3EA (void);
extern void GlyphRect_get_width_mD291C7644BBF18D6A213427F6C9C28840F233F12 (void);
extern void GlyphRect_get_height_m7F4D04452994E0D18762BB44352608E484DAAC1A (void);
extern void GlyphRect_get_zero_m359121752EE1A46C51118D84F03204F3285FF3FA (void);
extern void GlyphRect__ctor_m2B11A6C6C70735CB77FE2176E3D55D922D772A95 (void);
extern void GlyphRect_GetHashCode_mC012C2627F2A0C7EB7B47522085764441D47014F (void);
extern void GlyphRect_Equals_mF3BA7FD405AFCEA9E2A6ED2423C27CC023A1289B (void);
extern void GlyphRect_Equals_m29BCDCCDB99C88355A61EDDA65F6A51762BF9C87 (void);
extern void GlyphRect__cctor_m74BDAD5150F67B623F7D02238DD25D30C133BBDA (void);
extern void GlyphMetrics_get_width_m0F9F391E3A98984167E8001D4101BE1CE9354D13 (void);
extern void GlyphMetrics_get_height_mE0872B23CE1A20BF78DEACDBD53BAF789D84AD5C (void);
extern void GlyphMetrics_get_horizontalBearingX_m9C39B5E6D27FF34B706649AE47EE9390B5D76D6F (void);
extern void GlyphMetrics_get_horizontalBearingY_mD316BDD38A32258256994D6A2BCF0FC051D9B223 (void);
extern void GlyphMetrics_get_horizontalAdvance_m110E66C340A19E672FB1C26DFB875AB6900AFFF1 (void);
extern void GlyphMetrics__ctor_m9CD09465685783A596A7F9112EF7D6A7E1A2792D (void);
extern void GlyphMetrics_GetHashCode_mB1988F3C7DA77518D4DE0F116995F63C99A073E6 (void);
extern void GlyphMetrics_Equals_mB8EE2CF8E9D9D51AF2C7B4F13209AFC66456C970 (void);
extern void GlyphMetrics_Equals_mA8F8587C1725FA86DD6E87CFDFF0DDB9996112CB (void);
extern void Glyph_get_index_mCFBBCF85E7F3434B7A595EEE3411EFFB78E5675B (void);
extern void Glyph_set_index_mD033C966D79B910424B985F9D81C01D4E056B72C (void);
extern void Glyph_get_metrics_mB6E9D3D1899E35BA257638F6F58B7D260170B6FA (void);
extern void Glyph_set_metrics_m3350984977FC50061481B1EC563DE59147428BC2 (void);
extern void Glyph_get_glyphRect_m94E7C5FE682695CDC096248EF027079F33768EE5 (void);
extern void Glyph_set_glyphRect_mC21EB362D6EC56E0D110B0A08505CAD2DF26A6A8 (void);
extern void Glyph_get_scale_m3ED738CBB032247526DB38161E180759B2D06F29 (void);
extern void Glyph_set_scale_m44247C5948E32562931FA8C44799A3E1E4F0562A (void);
extern void Glyph_get_atlasIndex_m575332307F2C182655EE9AD352E92F1B5F4D26DF (void);
extern void Glyph_set_atlasIndex_m622CB24F3110B65CADB0C9F0223133B0DA926ABE (void);
extern void Glyph__ctor_m9FB83C6B166AC59E03B585F76C09C5FC1720281F (void);
extern void Glyph__ctor_m2E3C296A46BE48B869634BBE3F97B30F3442CC94 (void);
extern void Glyph__ctor_m71D55A8FBEA48ECCD78D65CAC9D008905E56FBF1 (void);
extern void FontEngine_LoadFontFace_mF3AD8D250D32D8B7CB347F91D409E63431CF4F9F (void);
extern void FontEngine_LoadFontFace_Internal_mAF51FEDD7DE13341FB210DC0743D877AD34416C8 (void);
extern void FontEngine_LoadFontFace_m311EA2DAC1A8B1CEEC455F9DCA683B8C4FE85F49 (void);
extern void FontEngine_LoadFontFace_With_Size_And_FaceIndex_Internal_mD89D8C9D6A2B8E7D29BAE669C15781DBCC63B8E4 (void);
extern void FontEngine_LoadFontFace_mE510EBEADA675798D81BCEF3E45819CFD43548D6 (void);
extern void FontEngine_LoadFontFace_With_Size_and_FaceIndex_FromFont_Internal_m7C4E8F4F88CDDE6B838D890FDC1E0722EBD40497 (void);
extern void FontEngine_LoadFontFace_m29DC8E2D25FCE54E91790A018416D30AA5813225 (void);
extern void FontEngine_LoadFontFace_With_Size_by_FamilyName_and_StyleName_Internal_m0856180C0F5FFA09E964986A78153D021F810F30 (void);
extern void FontEngine_UnloadFontFace_m09E0B255BF0B5CEDA90D1C38E1371B3440DD2E51 (void);
extern void FontEngine_UnloadFontFace_Internal_mC25601FC5B89A8F169771813EF33C2B97FCCFDDF (void);
extern void FontEngine_TryGetSystemFontReference_mA32D1513035E9B58417092500DDC3A7C939367A1 (void);
extern void FontEngine_TryGetSystemFontReference_Internal_m61AAB78124D19B1BC88C22D520287893E7280E1F (void);
extern void FontEngine_GetFaceInfo_mF371B75CDEEDA91FF910BED8DEE8FEB2A493BE37 (void);
extern void FontEngine_GetFaceInfo_Internal_m34E21653DF4724C3FCE289DA20AD5AB1B2F24B90 (void);
extern void FontEngine_GetFontFaces_m7EC5E883BE91B715F363A6D5FFA83888144D3BA1 (void);
extern void FontEngine_GetFontFaces_Internal_m130B4EBF35348F810B30BF42208886281F2B04FF (void);
extern void FontEngine_GetVariantGlyphIndex_m4C3A4AA47058F12996EC35715AF52F06E198B9DA (void);
extern void FontEngine_GetGlyphIndex_mEAE36421D92783413286344213D6EFD52E90CC00 (void);
extern void FontEngine_TryGetGlyphWithUnicodeValue_m58889809E3D65A0F1C5AEFF4DF6D319EBD139159 (void);
extern void FontEngine_TryGetGlyphWithUnicodeValue_Internal_mD799CD2B5CEA61ED5ABA16EDAA428385FD906BD8 (void);
extern void FontEngine_TryGetGlyphWithIndexValue_mD922A7EB95949E95D96C222D2CA1ED56BA2E81C3 (void);
extern void FontEngine_TryGetGlyphWithIndexValue_Internal_m2C5359B3C62139D20F52C1D232938CFAC9C3459D (void);
extern void FontEngine_SetTextureUploadMode_m04D13DFE627B79D5EB574EC74556E31DF42A83F3 (void);
extern void FontEngine_TryAddGlyphToTexture_m45A94FA06ADDCE2FA6B139B29E942496B760A090 (void);
extern void FontEngine_TryAddGlyphToTexture_Internal_mD6ED15BDDDC4F874C98D514D8DCE699EADB8C708 (void);
extern void FontEngine_TryAddGlyphsToTexture_m18740AD9F7264F54C397916268C0AB0738879801 (void);
extern void FontEngine_TryAddGlyphsToTexture_Internal_m43D4D242873C647DF5A20F7579FD90E373999EA8 (void);
extern void FontEngine_GetAllLigatureSubstitutionRecords_m362CD420864108B994FCA46FCE362CC8E7CE05B6 (void);
extern void FontEngine_GetLigatureSubstitutionRecords_m4189533C549B466BC84F22F5B8F6A24E18258134 (void);
extern void FontEngine_GetLigatureSubstitutionRecords_m65ADF14EB9A148D4BCB7DF107D99A83BEDF36009 (void);
extern void FontEngine_GetLigatureSubstitutionRecords_mAE1873C8AED5673CBF3789CB2E4C1E1FFA0E272F (void);
extern void FontEngine_PopulateLigatureSubstitutionRecordMarshallingArray_mFF6E6E4EF766AFDCE5E94ACD22704995EA1774A7 (void);
extern void FontEngine_GetLigatureSubstitutionRecordsFromMarshallingArray_mB3F24B46CFCB60CFCC8C054A71F2B3E534A70B72 (void);
extern void FontEngine_GetGlyphPairAdjustmentTable_m67DAC7C0029384FC814621F85F8B35D0D3327BC5 (void);
extern void FontEngine_PopulatePairAdjustmentRecordMarshallingArray_from_KernTable_mE3B5D21C5D72CEADB2BEA5AA8022E466356386E7 (void);
extern void FontEngine_GetAllPairAdjustmentRecords_m1994497F95AB84FFB5982B00E585C4559B8191AC (void);
extern void FontEngine_GetPairAdjustmentRecords_m763EA14AA400F46B391CD6B81AE7CD897A66565B (void);
extern void FontEngine_GetPairAdjustmentRecords_mE8570C2BF546FDD3B9EFEA60C211B0F204ED4416 (void);
extern void FontEngine_PopulatePairAdjustmentRecordMarshallingArray_m31A262416D56194C32CCE05C12DBEB31B22A36CD (void);
extern void FontEngine_GetPairAdjustmentRecordsFromMarshallingArray_m9D170234E7A068915120D0CC6EEFDC130E1F1659 (void);
extern void FontEngine_GetAllMarkToBaseAdjustmentRecords_m046FCE9EBE1B7011A6B52531AB694C45041B42F6 (void);
extern void FontEngine_GetMarkToBaseAdjustmentRecords_mBBA5CD78F1F95CA8198EA76AFBDC7D0BD2C26265 (void);
extern void FontEngine_GetMarkToBaseAdjustmentRecords_m408B8B0363B6F923D696140EFC62370A4928686A (void);
extern void FontEngine_PopulateMarkToBaseAdjustmentRecordMarshallingArray_m20E04208FC0890DF48163AC3ABBF33F23E57237D (void);
extern void FontEngine_GetMarkToBaseAdjustmentRecordsFromMarshallingArray_mBE93B0CDCCC0E021EA24251F7AF85696E353F491 (void);
extern void FontEngine_GetAllMarkToMarkAdjustmentRecords_mA11C88084182EB94F428AA97E40448C93B5A64A2 (void);
extern void FontEngine_GetMarkToMarkAdjustmentRecords_m53AC560161E74D387D08ECFCA1255CC1763AA10A (void);
extern void FontEngine_GetMarkToMarkAdjustmentRecords_m83EC8E58C4706BA4B4CC3C045DAC9D3F057F120E (void);
extern void FontEngine_PopulateMarkToMarkAdjustmentRecordMarshallingArray_m9C7BC1E19B7F3ED79079E1308CBDB5790287F9D2 (void);
extern void FontEngine_GetMarkToMarkAdjustmentRecordsFromMarshallingArray_m47A4D8635517F3934BE84723BD84A9E8842B096A (void);
extern void FontEngine_GlyphIndexToMarshallingArray_mF3A3CD803687D55F90DED04F3AE52284EFB89831 (void);
extern void FontEngine_ResetAtlasTexture_m15BBE67DFDD8A1E740BC0C4B29612A8C866860DC (void);
extern void FontEngine__cctor_mD8EC115E258FD225726CBC71B249C1F411447D79 (void);
extern void FontEngine_LoadFontFace_Internal_Injected_m8C9A26C7431365233D9F0B9942B3AA072FED857E (void);
extern void FontEngine_LoadFontFace_With_Size_And_FaceIndex_Internal_Injected_m03606EF4E63F5E7D1E82798F08EE87ED63FCCBA8 (void);
extern void FontEngine_LoadFontFace_With_Size_and_FaceIndex_FromFont_Internal_Injected_m0848C498D465DF63E8A5DA8FFD2B9CB50BF297BC (void);
extern void FontEngine_LoadFontFace_With_Size_by_FamilyName_and_StyleName_Internal_Injected_m412A9CEEE4497B18A7A166BE136E1E5199519086 (void);
extern void FontEngine_TryGetSystemFontReference_Internal_Injected_mA56F0B65B6962E736DBA14BD2E7E4053B9C9BE7E (void);
extern void FontEngine_TryAddGlyphToTexture_Internal_Injected_mB8645E5CECA2353771B00DB239D26E8FA0219EA7 (void);
extern void FontEngine_TryAddGlyphsToTexture_Internal_Injected_mB91F233ED4B3D03BA0B4DBD5C6F4F026E18AC095 (void);
extern void FontEngine_PopulateLigatureSubstitutionRecordMarshallingArray_Injected_m37F521168E0C22399E768BDF7CA9F02698F35CA2 (void);
extern void FontEngine_PopulatePairAdjustmentRecordMarshallingArray_from_KernTable_Injected_mB71A683FAAF5209C008578FCF6A02DF55BDE43E3 (void);
extern void FontEngine_GetAllPairAdjustmentRecords_Injected_m2D011C7BD4493115C3CF227C0D3F78F313C43998 (void);
extern void FontEngine_PopulatePairAdjustmentRecordMarshallingArray_Injected_m990AD3D771CDD8A2813FE4FCCD85DD7BC5AD18FF (void);
extern void FontEngine_GetPairAdjustmentRecordsFromMarshallingArray_Injected_m1A98CBFA81F94C924A22DDB57E0D5A92AEA759F3 (void);
extern void FontEngine_GetAllMarkToBaseAdjustmentRecords_Injected_m87E42B9692CCF10DD3F1E474D3C9AB2EF0D3F967 (void);
extern void FontEngine_PopulateMarkToBaseAdjustmentRecordMarshallingArray_Injected_mF6823651D5556F79D2C3DE9297499A1418701981 (void);
extern void FontEngine_GetMarkToBaseAdjustmentRecordsFromMarshallingArray_Injected_mEE9679F9CFC038CD4596D8BAA74799BCA2318DE3 (void);
extern void FontEngine_GetAllMarkToMarkAdjustmentRecords_Injected_mCCF8F4CE41C58B8EB620E4E4AB683E9A86EBE123 (void);
extern void FontEngine_PopulateMarkToMarkAdjustmentRecordMarshallingArray_Injected_mDEEF74C9D0495D3432F6B87B57FB17BCE75039FF (void);
extern void FontEngine_GetMarkToMarkAdjustmentRecordsFromMarshallingArray_Injected_m3E33C2687D81FBC3E86F863083DA6EAF718F5A9C (void);
extern void FontEngine_ResetAtlasTexture_Injected_m9000D6C1602BA954582A5FFE99033B73A8F7642A (void);
extern void FontEngineUtilities_MaxValue_m7E0FBF90FE07F65C9895ACD56FD032A53E417F83 (void);
extern void GlyphValueRecord_get_xPlacement_m5E2B8B05A5DF57B2DC4B3795E71330CDDE1761C8 (void);
extern void GlyphValueRecord_set_xPlacement_m79F92029922BDE50ED63A6A03EBE478869F1CCFC (void);
extern void GlyphValueRecord_get_yPlacement_mB6303F8800305F6F96ECCD0CD9AA70A1A30A15DA (void);
extern void GlyphValueRecord_set_yPlacement_m04DA300FAB827A708CB291DA3B2EA3128279CA2B (void);
extern void GlyphValueRecord_get_xAdvance_m6C392027FA91E0705C1585C5EF40D984AAA0013E (void);
extern void GlyphValueRecord_set_xAdvance_mA8475DBF8792908481A5DA5FFE7C7FAD3E596A35 (void);
extern void GlyphValueRecord_get_yAdvance_m1379AA10FCCFFABEAF43E767F8BFBF32CA76B5B6 (void);
extern void GlyphValueRecord__ctor_m01488AC8714CC329C1A23B034C1ABF4B52F0740A (void);
extern void GlyphValueRecord_op_Addition_mF26165B4CE61A5409AEFF24B0D1727804E13602B (void);
extern void GlyphValueRecord_GetHashCode_m9A2BFC7780FBD61A4B7E0091F8FE87DA15081B60 (void);
extern void GlyphValueRecord_Equals_m5DC74E9C597D8F27754444C057F819ECB24CB8B6 (void);
extern void GlyphValueRecord_Equals_mB5F45CBE745D1C5BAF7944989DF4239FDC78D972 (void);
extern void GlyphAdjustmentRecord_get_glyphIndex_mB1C51945CA4FF019A74BC98C01C8883A396CBFA9 (void);
extern void GlyphAdjustmentRecord_get_glyphValueRecord_m83866DCE07A22F903D4BA417476E64114625BDD7 (void);
extern void GlyphAdjustmentRecord__ctor_m54853163D58AD883E19A7C0D795A4CF73FCAE28C (void);
extern void GlyphAdjustmentRecord_GetHashCode_m8E0D84D4458A732085FCCA04AAE0A0B16CB3226C (void);
extern void GlyphAdjustmentRecord_Equals_mEF9EBAA5A7ED2B136EA25A2AD26353406884F47D (void);
extern void GlyphAdjustmentRecord_Equals_m2F5908F3B2BB2F6596E40E6A74E5F2120BCDA535 (void);
extern void GlyphPairAdjustmentRecord_get_firstAdjustmentRecord_m867469548F17B298F893B78EE2F93D34E4A6C39C (void);
extern void GlyphPairAdjustmentRecord_set_firstAdjustmentRecord_mA19E5BFDFB5B5EF6B82BE195BC407390E3754296 (void);
extern void GlyphPairAdjustmentRecord_get_secondAdjustmentRecord_mFDFECB1F7A38E22BD2388FFE9C71E732F6B44D91 (void);
extern void GlyphPairAdjustmentRecord_get_featureLookupFlags_m08DA76766FDE949068B881DBEA29955C9C43E8A9 (void);
extern void GlyphPairAdjustmentRecord__ctor_m2B1BCCA950399944824CFB79D74C39A7A4F87A6A (void);
extern void GlyphPairAdjustmentRecord_GetHashCode_mC253F24FFD3BCE5EEB44CA6CDE1BE19336E0A5F5 (void);
extern void GlyphPairAdjustmentRecord_Equals_m0F49F5D76C114BB660B7619A93247591AE323CFD (void);
extern void GlyphPairAdjustmentRecord_Equals_m2DADFD15A4DFF37570EA51D9EAEBA30DF0007689 (void);
extern void GlyphAnchorPoint_get_xCoordinate_mCD33464763911ECB78DEB1965970A916FA27DD1C (void);
extern void GlyphAnchorPoint_set_xCoordinate_m6820F49984BD779C6274A656AE013575C2E121FA (void);
extern void GlyphAnchorPoint_get_yCoordinate_m2683C19C6A3D750E4D6C536307313E55589909D6 (void);
extern void GlyphAnchorPoint_set_yCoordinate_m3DC770A8B68515510EC83140E7CC2164880EDA97 (void);
extern void MarkPositionAdjustment_get_xPositionAdjustment_m5ACBB4C515357320C12597CAE5E4D409BA298765 (void);
extern void MarkPositionAdjustment_set_xPositionAdjustment_mBD0304ADF7B91F788755A319868513C1D5C7020C (void);
extern void MarkPositionAdjustment_get_yPositionAdjustment_m1F5F7DBBFEB0B52CCC772F68664D06B11D6A9F2C (void);
extern void MarkPositionAdjustment_set_yPositionAdjustment_m80D07753C658004FD58CD20B4B0D70EFB791836E (void);
extern void MarkToBaseAdjustmentRecord_get_baseGlyphID_mD6A0DD18DBE69E0E68ACA6AABF47B1EA61B633A5 (void);
extern void MarkToBaseAdjustmentRecord_set_baseGlyphID_mC8151EF01F0FCFFA0611EB65EC03099B6562ABAC (void);
extern void MarkToBaseAdjustmentRecord_get_baseGlyphAnchorPoint_mCBF57932B7A89C532B0EF750DFD81F8FE389EE08 (void);
extern void MarkToBaseAdjustmentRecord_set_baseGlyphAnchorPoint_m1C79E0AD92B197313FCD2273FD57CB6CA3B3E379 (void);
extern void MarkToBaseAdjustmentRecord_get_markGlyphID_mB4BB0291733ECFC2433F5C2837F0B28EB05CAF5C (void);
extern void MarkToBaseAdjustmentRecord_set_markGlyphID_mCA5B1438023B89A5FDE7B6F8F1C20F7CEB8A8C09 (void);
extern void MarkToBaseAdjustmentRecord_get_markPositionAdjustment_m570715D1D0F84361A90564D4A958394453E1F9AB (void);
extern void MarkToBaseAdjustmentRecord_set_markPositionAdjustment_m585228E1ADB46FEB33EF9400B3A477FEE6559AFC (void);
extern void MarkToMarkAdjustmentRecord_get_baseMarkGlyphID_mA0EAEA751467A8841F9D20D122C4998905A508CC (void);
extern void MarkToMarkAdjustmentRecord_set_baseMarkGlyphID_m3F60246B56DB78EF3754421041378A08E9FC018F (void);
extern void MarkToMarkAdjustmentRecord_get_baseMarkGlyphAnchorPoint_mB87ADA10491B42650BAD4DB7330771061827ACAB (void);
extern void MarkToMarkAdjustmentRecord_set_baseMarkGlyphAnchorPoint_mF99F7E7CBCB4B3C36D87BC6F6B92232EEE63B17B (void);
extern void MarkToMarkAdjustmentRecord_get_combiningMarkGlyphID_m84613A2A27F87AA21CAEDD08759031302C9A8FBF (void);
extern void MarkToMarkAdjustmentRecord_set_combiningMarkGlyphID_mABD6E44107C367EE21EB258DB5681707C4B34075 (void);
extern void MarkToMarkAdjustmentRecord_get_combiningMarkPositionAdjustment_mC109ECEDB4AD314A25C0EB1F6F6151AE611DE15C (void);
extern void MarkToMarkAdjustmentRecord_set_combiningMarkPositionAdjustment_mFAB0D9C53F43D866DD2DA6F3B85A5E1AE23897EC (void);
extern void LigatureSubstitutionRecord_get_componentGlyphIDs_m3BBDC9421E3A7369B198379F5433FBB13ADCE628 (void);
extern void LigatureSubstitutionRecord_set_componentGlyphIDs_mDD8D76279FF545B516D4CE689568AA513A95EF63 (void);
extern void LigatureSubstitutionRecord_get_ligatureGlyphID_m5FD629E204026FB8B6279498CDE5CAB1D23827EC (void);
extern void LigatureSubstitutionRecord_set_ligatureGlyphID_mDB4F809745EF0536182FAF90AFB276F389C35EB6 (void);
extern void LigatureSubstitutionRecord_Equals_m3CC62722B819D61147F694E4BF1891FF7A752BF1 (void);
extern void LigatureSubstitutionRecord_Equals_mD986B72ED54907EFBDF421099D75E99A6198D814 (void);
extern void LigatureSubstitutionRecord_GetHashCode_m1CBBE1E99AD03EA162ED8AE2C1EC3EAC15A0B238 (void);
extern void LigatureSubstitutionRecord_op_Equality_mFE8553B8629DEC763581A836A7897A0F9DA11A13 (void);
static Il2CppMethodPointer s_methodPointers[190] = 
{
	FaceInfo_get_faceIndex_m3C9FB6429035CD34ACD201FB5951AF06E9C2A641,
	FaceInfo_get_familyName_m62DAF5DE45EA9F3B300B927603D101D350D27241,
	FaceInfo_get_styleName_mACBAA6529BCE3BC14987645691350A4FB1CB0FC6,
	FaceInfo_get_pointSize_mDCB57F74233408806DF4F658751ECEE9A66C003E,
	FaceInfo_get_scale_mC475A572AD4956B47D8B9F8D90DC69BBBB102FCD,
	FaceInfo_set_scale_m379253929403DA08480CAD127C908A0E8329A5D4,
	FaceInfo_get_unitsPerEM_m92CD79B71FC504DE1C87923716E999D85A35AC0E,
	FaceInfo_set_unitsPerEM_m3E48D789D666984A4EA8D221BED22833761E9A90,
	FaceInfo_get_lineHeight_m528B4A822181FCECF3D4FF1045DF288E5872AB9D,
	FaceInfo_get_ascentLine_m193755D649428EC24A7E433A1728F11DA7547ABD,
	FaceInfo_get_capLine_m0D95B5D5CEC5CFB12091F5EB5965DE6E38588C88,
	FaceInfo_set_capLine_m4716D75CE87EC018E5EA3DE87DA703DBE7A9FEEF,
	FaceInfo_get_meanLine_m5FF396E0E32A046C1D3CBD3AC1FA279984394D96,
	FaceInfo_set_meanLine_mE957CD43CB778B093331CC4B10A5330E8FF1CB99,
	FaceInfo_get_baseline_m934B597D3E0080FEF98CBDD091C457B497179C3A,
	FaceInfo_get_descentLine_m811A243C9B328B0C546BF9927A010A05DF172BD3,
	FaceInfo_get_superscriptOffset_m8D462DB86414D8507C7D1CC6881DA9EC896FB80A,
	FaceInfo_get_superscriptSize_mC3ABE7C70559A8214294CDA598B17FD62BDC2EE0,
	FaceInfo_get_subscriptOffset_mF1D3E68AC3D449CBC73AA0CBF5B8A187C6C5285A,
	FaceInfo_get_subscriptSize_mF6264BFB215FDE6C94A45D2F8FC946ADFCDD2E31,
	FaceInfo_get_underlineOffset_mB1CBB29ECFFE69047F35E654E7F90755F95DD251,
	FaceInfo_get_underlineThickness_mC032F8C026994AF3FD49E6AB12E113F61EFA98E2,
	FaceInfo_get_strikethroughOffset_m7997E4A1512FE358331B3A6543C62C92A0AA5CA5,
	FaceInfo_set_strikethroughOffset_m57B05848BDA21F7AF6563394D29C8AE7107FEFF0,
	FaceInfo_get_tabWidth_mC6D9F42C40EDD767DE22050E4FBE3878AC96B161,
	GlyphRect_get_x_m453EECC6C6F08602B1F74C5E1D8EE1163236A898,
	GlyphRect_get_y_mE31390BB3185EEA82DD16EA41E208F6A0397E3EA,
	GlyphRect_get_width_mD291C7644BBF18D6A213427F6C9C28840F233F12,
	GlyphRect_get_height_m7F4D04452994E0D18762BB44352608E484DAAC1A,
	GlyphRect_get_zero_m359121752EE1A46C51118D84F03204F3285FF3FA,
	GlyphRect__ctor_m2B11A6C6C70735CB77FE2176E3D55D922D772A95,
	GlyphRect_GetHashCode_mC012C2627F2A0C7EB7B47522085764441D47014F,
	GlyphRect_Equals_mF3BA7FD405AFCEA9E2A6ED2423C27CC023A1289B,
	GlyphRect_Equals_m29BCDCCDB99C88355A61EDDA65F6A51762BF9C87,
	GlyphRect__cctor_m74BDAD5150F67B623F7D02238DD25D30C133BBDA,
	GlyphMetrics_get_width_m0F9F391E3A98984167E8001D4101BE1CE9354D13,
	GlyphMetrics_get_height_mE0872B23CE1A20BF78DEACDBD53BAF789D84AD5C,
	GlyphMetrics_get_horizontalBearingX_m9C39B5E6D27FF34B706649AE47EE9390B5D76D6F,
	GlyphMetrics_get_horizontalBearingY_mD316BDD38A32258256994D6A2BCF0FC051D9B223,
	GlyphMetrics_get_horizontalAdvance_m110E66C340A19E672FB1C26DFB875AB6900AFFF1,
	GlyphMetrics__ctor_m9CD09465685783A596A7F9112EF7D6A7E1A2792D,
	GlyphMetrics_GetHashCode_mB1988F3C7DA77518D4DE0F116995F63C99A073E6,
	GlyphMetrics_Equals_mB8EE2CF8E9D9D51AF2C7B4F13209AFC66456C970,
	GlyphMetrics_Equals_mA8F8587C1725FA86DD6E87CFDFF0DDB9996112CB,
	Glyph_get_index_mCFBBCF85E7F3434B7A595EEE3411EFFB78E5675B,
	Glyph_set_index_mD033C966D79B910424B985F9D81C01D4E056B72C,
	Glyph_get_metrics_mB6E9D3D1899E35BA257638F6F58B7D260170B6FA,
	Glyph_set_metrics_m3350984977FC50061481B1EC563DE59147428BC2,
	Glyph_get_glyphRect_m94E7C5FE682695CDC096248EF027079F33768EE5,
	Glyph_set_glyphRect_mC21EB362D6EC56E0D110B0A08505CAD2DF26A6A8,
	Glyph_get_scale_m3ED738CBB032247526DB38161E180759B2D06F29,
	Glyph_set_scale_m44247C5948E32562931FA8C44799A3E1E4F0562A,
	Glyph_get_atlasIndex_m575332307F2C182655EE9AD352E92F1B5F4D26DF,
	Glyph_set_atlasIndex_m622CB24F3110B65CADB0C9F0223133B0DA926ABE,
	Glyph__ctor_m9FB83C6B166AC59E03B585F76C09C5FC1720281F,
	Glyph__ctor_m2E3C296A46BE48B869634BBE3F97B30F3442CC94,
	Glyph__ctor_m71D55A8FBEA48ECCD78D65CAC9D008905E56FBF1,
	FontEngine_LoadFontFace_mF3AD8D250D32D8B7CB347F91D409E63431CF4F9F,
	FontEngine_LoadFontFace_Internal_mAF51FEDD7DE13341FB210DC0743D877AD34416C8,
	FontEngine_LoadFontFace_m311EA2DAC1A8B1CEEC455F9DCA683B8C4FE85F49,
	FontEngine_LoadFontFace_With_Size_And_FaceIndex_Internal_mD89D8C9D6A2B8E7D29BAE669C15781DBCC63B8E4,
	FontEngine_LoadFontFace_mE510EBEADA675798D81BCEF3E45819CFD43548D6,
	FontEngine_LoadFontFace_With_Size_and_FaceIndex_FromFont_Internal_m7C4E8F4F88CDDE6B838D890FDC1E0722EBD40497,
	FontEngine_LoadFontFace_m29DC8E2D25FCE54E91790A018416D30AA5813225,
	FontEngine_LoadFontFace_With_Size_by_FamilyName_and_StyleName_Internal_m0856180C0F5FFA09E964986A78153D021F810F30,
	FontEngine_UnloadFontFace_m09E0B255BF0B5CEDA90D1C38E1371B3440DD2E51,
	FontEngine_UnloadFontFace_Internal_mC25601FC5B89A8F169771813EF33C2B97FCCFDDF,
	FontEngine_TryGetSystemFontReference_mA32D1513035E9B58417092500DDC3A7C939367A1,
	FontEngine_TryGetSystemFontReference_Internal_m61AAB78124D19B1BC88C22D520287893E7280E1F,
	FontEngine_GetFaceInfo_mF371B75CDEEDA91FF910BED8DEE8FEB2A493BE37,
	FontEngine_GetFaceInfo_Internal_m34E21653DF4724C3FCE289DA20AD5AB1B2F24B90,
	FontEngine_GetFontFaces_m7EC5E883BE91B715F363A6D5FFA83888144D3BA1,
	FontEngine_GetFontFaces_Internal_m130B4EBF35348F810B30BF42208886281F2B04FF,
	FontEngine_GetVariantGlyphIndex_m4C3A4AA47058F12996EC35715AF52F06E198B9DA,
	FontEngine_GetGlyphIndex_mEAE36421D92783413286344213D6EFD52E90CC00,
	FontEngine_TryGetGlyphWithUnicodeValue_m58889809E3D65A0F1C5AEFF4DF6D319EBD139159,
	FontEngine_TryGetGlyphWithUnicodeValue_Internal_mD799CD2B5CEA61ED5ABA16EDAA428385FD906BD8,
	FontEngine_TryGetGlyphWithIndexValue_mD922A7EB95949E95D96C222D2CA1ED56BA2E81C3,
	FontEngine_TryGetGlyphWithIndexValue_Internal_m2C5359B3C62139D20F52C1D232938CFAC9C3459D,
	FontEngine_SetTextureUploadMode_m04D13DFE627B79D5EB574EC74556E31DF42A83F3,
	FontEngine_TryAddGlyphToTexture_m45A94FA06ADDCE2FA6B139B29E942496B760A090,
	FontEngine_TryAddGlyphToTexture_Internal_mD6ED15BDDDC4F874C98D514D8DCE699EADB8C708,
	FontEngine_TryAddGlyphsToTexture_m18740AD9F7264F54C397916268C0AB0738879801,
	FontEngine_TryAddGlyphsToTexture_Internal_m43D4D242873C647DF5A20F7579FD90E373999EA8,
	FontEngine_GetAllLigatureSubstitutionRecords_m362CD420864108B994FCA46FCE362CC8E7CE05B6,
	FontEngine_GetLigatureSubstitutionRecords_m4189533C549B466BC84F22F5B8F6A24E18258134,
	FontEngine_GetLigatureSubstitutionRecords_m65ADF14EB9A148D4BCB7DF107D99A83BEDF36009,
	FontEngine_GetLigatureSubstitutionRecords_mAE1873C8AED5673CBF3789CB2E4C1E1FFA0E272F,
	FontEngine_PopulateLigatureSubstitutionRecordMarshallingArray_mFF6E6E4EF766AFDCE5E94ACD22704995EA1774A7,
	FontEngine_GetLigatureSubstitutionRecordsFromMarshallingArray_mB3F24B46CFCB60CFCC8C054A71F2B3E534A70B72,
	FontEngine_GetGlyphPairAdjustmentTable_m67DAC7C0029384FC814621F85F8B35D0D3327BC5,
	FontEngine_PopulatePairAdjustmentRecordMarshallingArray_from_KernTable_mE3B5D21C5D72CEADB2BEA5AA8022E466356386E7,
	FontEngine_GetAllPairAdjustmentRecords_m1994497F95AB84FFB5982B00E585C4559B8191AC,
	FontEngine_GetPairAdjustmentRecords_m763EA14AA400F46B391CD6B81AE7CD897A66565B,
	FontEngine_GetPairAdjustmentRecords_mE8570C2BF546FDD3B9EFEA60C211B0F204ED4416,
	FontEngine_PopulatePairAdjustmentRecordMarshallingArray_m31A262416D56194C32CCE05C12DBEB31B22A36CD,
	FontEngine_GetPairAdjustmentRecordsFromMarshallingArray_m9D170234E7A068915120D0CC6EEFDC130E1F1659,
	FontEngine_GetAllMarkToBaseAdjustmentRecords_m046FCE9EBE1B7011A6B52531AB694C45041B42F6,
	FontEngine_GetMarkToBaseAdjustmentRecords_mBBA5CD78F1F95CA8198EA76AFBDC7D0BD2C26265,
	FontEngine_GetMarkToBaseAdjustmentRecords_m408B8B0363B6F923D696140EFC62370A4928686A,
	FontEngine_PopulateMarkToBaseAdjustmentRecordMarshallingArray_m20E04208FC0890DF48163AC3ABBF33F23E57237D,
	FontEngine_GetMarkToBaseAdjustmentRecordsFromMarshallingArray_mBE93B0CDCCC0E021EA24251F7AF85696E353F491,
	FontEngine_GetAllMarkToMarkAdjustmentRecords_mA11C88084182EB94F428AA97E40448C93B5A64A2,
	FontEngine_GetMarkToMarkAdjustmentRecords_m53AC560161E74D387D08ECFCA1255CC1763AA10A,
	FontEngine_GetMarkToMarkAdjustmentRecords_m83EC8E58C4706BA4B4CC3C045DAC9D3F057F120E,
	FontEngine_PopulateMarkToMarkAdjustmentRecordMarshallingArray_m9C7BC1E19B7F3ED79079E1308CBDB5790287F9D2,
	FontEngine_GetMarkToMarkAdjustmentRecordsFromMarshallingArray_m47A4D8635517F3934BE84723BD84A9E8842B096A,
	FontEngine_GlyphIndexToMarshallingArray_mF3A3CD803687D55F90DED04F3AE52284EFB89831,
	NULL,
	NULL,
	FontEngine_ResetAtlasTexture_m15BBE67DFDD8A1E740BC0C4B29612A8C866860DC,
	FontEngine__cctor_mD8EC115E258FD225726CBC71B249C1F411447D79,
	FontEngine_LoadFontFace_Internal_Injected_m8C9A26C7431365233D9F0B9942B3AA072FED857E,
	FontEngine_LoadFontFace_With_Size_And_FaceIndex_Internal_Injected_m03606EF4E63F5E7D1E82798F08EE87ED63FCCBA8,
	FontEngine_LoadFontFace_With_Size_and_FaceIndex_FromFont_Internal_Injected_m0848C498D465DF63E8A5DA8FFD2B9CB50BF297BC,
	FontEngine_LoadFontFace_With_Size_by_FamilyName_and_StyleName_Internal_Injected_m412A9CEEE4497B18A7A166BE136E1E5199519086,
	FontEngine_TryGetSystemFontReference_Internal_Injected_mA56F0B65B6962E736DBA14BD2E7E4053B9C9BE7E,
	FontEngine_TryAddGlyphToTexture_Internal_Injected_mB8645E5CECA2353771B00DB239D26E8FA0219EA7,
	FontEngine_TryAddGlyphsToTexture_Internal_Injected_mB91F233ED4B3D03BA0B4DBD5C6F4F026E18AC095,
	FontEngine_PopulateLigatureSubstitutionRecordMarshallingArray_Injected_m37F521168E0C22399E768BDF7CA9F02698F35CA2,
	FontEngine_PopulatePairAdjustmentRecordMarshallingArray_from_KernTable_Injected_mB71A683FAAF5209C008578FCF6A02DF55BDE43E3,
	FontEngine_GetAllPairAdjustmentRecords_Injected_m2D011C7BD4493115C3CF227C0D3F78F313C43998,
	FontEngine_PopulatePairAdjustmentRecordMarshallingArray_Injected_m990AD3D771CDD8A2813FE4FCCD85DD7BC5AD18FF,
	FontEngine_GetPairAdjustmentRecordsFromMarshallingArray_Injected_m1A98CBFA81F94C924A22DDB57E0D5A92AEA759F3,
	FontEngine_GetAllMarkToBaseAdjustmentRecords_Injected_m87E42B9692CCF10DD3F1E474D3C9AB2EF0D3F967,
	FontEngine_PopulateMarkToBaseAdjustmentRecordMarshallingArray_Injected_mF6823651D5556F79D2C3DE9297499A1418701981,
	FontEngine_GetMarkToBaseAdjustmentRecordsFromMarshallingArray_Injected_mEE9679F9CFC038CD4596D8BAA74799BCA2318DE3,
	FontEngine_GetAllMarkToMarkAdjustmentRecords_Injected_mCCF8F4CE41C58B8EB620E4E4AB683E9A86EBE123,
	FontEngine_PopulateMarkToMarkAdjustmentRecordMarshallingArray_Injected_mDEEF74C9D0495D3432F6B87B57FB17BCE75039FF,
	FontEngine_GetMarkToMarkAdjustmentRecordsFromMarshallingArray_Injected_m3E33C2687D81FBC3E86F863083DA6EAF718F5A9C,
	FontEngine_ResetAtlasTexture_Injected_m9000D6C1602BA954582A5FFE99033B73A8F7642A,
	FontEngineUtilities_MaxValue_m7E0FBF90FE07F65C9895ACD56FD032A53E417F83,
	GlyphValueRecord_get_xPlacement_m5E2B8B05A5DF57B2DC4B3795E71330CDDE1761C8,
	GlyphValueRecord_set_xPlacement_m79F92029922BDE50ED63A6A03EBE478869F1CCFC,
	GlyphValueRecord_get_yPlacement_mB6303F8800305F6F96ECCD0CD9AA70A1A30A15DA,
	GlyphValueRecord_set_yPlacement_m04DA300FAB827A708CB291DA3B2EA3128279CA2B,
	GlyphValueRecord_get_xAdvance_m6C392027FA91E0705C1585C5EF40D984AAA0013E,
	GlyphValueRecord_set_xAdvance_mA8475DBF8792908481A5DA5FFE7C7FAD3E596A35,
	GlyphValueRecord_get_yAdvance_m1379AA10FCCFFABEAF43E767F8BFBF32CA76B5B6,
	GlyphValueRecord__ctor_m01488AC8714CC329C1A23B034C1ABF4B52F0740A,
	GlyphValueRecord_op_Addition_mF26165B4CE61A5409AEFF24B0D1727804E13602B,
	GlyphValueRecord_GetHashCode_m9A2BFC7780FBD61A4B7E0091F8FE87DA15081B60,
	GlyphValueRecord_Equals_m5DC74E9C597D8F27754444C057F819ECB24CB8B6,
	GlyphValueRecord_Equals_mB5F45CBE745D1C5BAF7944989DF4239FDC78D972,
	GlyphAdjustmentRecord_get_glyphIndex_mB1C51945CA4FF019A74BC98C01C8883A396CBFA9,
	GlyphAdjustmentRecord_get_glyphValueRecord_m83866DCE07A22F903D4BA417476E64114625BDD7,
	GlyphAdjustmentRecord__ctor_m54853163D58AD883E19A7C0D795A4CF73FCAE28C,
	GlyphAdjustmentRecord_GetHashCode_m8E0D84D4458A732085FCCA04AAE0A0B16CB3226C,
	GlyphAdjustmentRecord_Equals_mEF9EBAA5A7ED2B136EA25A2AD26353406884F47D,
	GlyphAdjustmentRecord_Equals_m2F5908F3B2BB2F6596E40E6A74E5F2120BCDA535,
	GlyphPairAdjustmentRecord_get_firstAdjustmentRecord_m867469548F17B298F893B78EE2F93D34E4A6C39C,
	GlyphPairAdjustmentRecord_set_firstAdjustmentRecord_mA19E5BFDFB5B5EF6B82BE195BC407390E3754296,
	GlyphPairAdjustmentRecord_get_secondAdjustmentRecord_mFDFECB1F7A38E22BD2388FFE9C71E732F6B44D91,
	GlyphPairAdjustmentRecord_get_featureLookupFlags_m08DA76766FDE949068B881DBEA29955C9C43E8A9,
	GlyphPairAdjustmentRecord__ctor_m2B1BCCA950399944824CFB79D74C39A7A4F87A6A,
	GlyphPairAdjustmentRecord_GetHashCode_mC253F24FFD3BCE5EEB44CA6CDE1BE19336E0A5F5,
	GlyphPairAdjustmentRecord_Equals_m0F49F5D76C114BB660B7619A93247591AE323CFD,
	GlyphPairAdjustmentRecord_Equals_m2DADFD15A4DFF37570EA51D9EAEBA30DF0007689,
	GlyphAnchorPoint_get_xCoordinate_mCD33464763911ECB78DEB1965970A916FA27DD1C,
	GlyphAnchorPoint_set_xCoordinate_m6820F49984BD779C6274A656AE013575C2E121FA,
	GlyphAnchorPoint_get_yCoordinate_m2683C19C6A3D750E4D6C536307313E55589909D6,
	GlyphAnchorPoint_set_yCoordinate_m3DC770A8B68515510EC83140E7CC2164880EDA97,
	MarkPositionAdjustment_get_xPositionAdjustment_m5ACBB4C515357320C12597CAE5E4D409BA298765,
	MarkPositionAdjustment_set_xPositionAdjustment_mBD0304ADF7B91F788755A319868513C1D5C7020C,
	MarkPositionAdjustment_get_yPositionAdjustment_m1F5F7DBBFEB0B52CCC772F68664D06B11D6A9F2C,
	MarkPositionAdjustment_set_yPositionAdjustment_m80D07753C658004FD58CD20B4B0D70EFB791836E,
	MarkToBaseAdjustmentRecord_get_baseGlyphID_mD6A0DD18DBE69E0E68ACA6AABF47B1EA61B633A5,
	MarkToBaseAdjustmentRecord_set_baseGlyphID_mC8151EF01F0FCFFA0611EB65EC03099B6562ABAC,
	MarkToBaseAdjustmentRecord_get_baseGlyphAnchorPoint_mCBF57932B7A89C532B0EF750DFD81F8FE389EE08,
	MarkToBaseAdjustmentRecord_set_baseGlyphAnchorPoint_m1C79E0AD92B197313FCD2273FD57CB6CA3B3E379,
	MarkToBaseAdjustmentRecord_get_markGlyphID_mB4BB0291733ECFC2433F5C2837F0B28EB05CAF5C,
	MarkToBaseAdjustmentRecord_set_markGlyphID_mCA5B1438023B89A5FDE7B6F8F1C20F7CEB8A8C09,
	MarkToBaseAdjustmentRecord_get_markPositionAdjustment_m570715D1D0F84361A90564D4A958394453E1F9AB,
	MarkToBaseAdjustmentRecord_set_markPositionAdjustment_m585228E1ADB46FEB33EF9400B3A477FEE6559AFC,
	MarkToMarkAdjustmentRecord_get_baseMarkGlyphID_mA0EAEA751467A8841F9D20D122C4998905A508CC,
	MarkToMarkAdjustmentRecord_set_baseMarkGlyphID_m3F60246B56DB78EF3754421041378A08E9FC018F,
	MarkToMarkAdjustmentRecord_get_baseMarkGlyphAnchorPoint_mB87ADA10491B42650BAD4DB7330771061827ACAB,
	MarkToMarkAdjustmentRecord_set_baseMarkGlyphAnchorPoint_mF99F7E7CBCB4B3C36D87BC6F6B92232EEE63B17B,
	MarkToMarkAdjustmentRecord_get_combiningMarkGlyphID_m84613A2A27F87AA21CAEDD08759031302C9A8FBF,
	MarkToMarkAdjustmentRecord_set_combiningMarkGlyphID_mABD6E44107C367EE21EB258DB5681707C4B34075,
	MarkToMarkAdjustmentRecord_get_combiningMarkPositionAdjustment_mC109ECEDB4AD314A25C0EB1F6F6151AE611DE15C,
	MarkToMarkAdjustmentRecord_set_combiningMarkPositionAdjustment_mFAB0D9C53F43D866DD2DA6F3B85A5E1AE23897EC,
	LigatureSubstitutionRecord_get_componentGlyphIDs_m3BBDC9421E3A7369B198379F5433FBB13ADCE628,
	LigatureSubstitutionRecord_set_componentGlyphIDs_mDD8D76279FF545B516D4CE689568AA513A95EF63,
	LigatureSubstitutionRecord_get_ligatureGlyphID_m5FD629E204026FB8B6279498CDE5CAB1D23827EC,
	LigatureSubstitutionRecord_set_ligatureGlyphID_mDB4F809745EF0536182FAF90AFB276F389C35EB6,
	LigatureSubstitutionRecord_Equals_m3CC62722B819D61147F694E4BF1891FF7A752BF1,
	LigatureSubstitutionRecord_Equals_mD986B72ED54907EFBDF421099D75E99A6198D814,
	LigatureSubstitutionRecord_GetHashCode_m1CBBE1E99AD03EA162ED8AE2C1EC3EAC15A0B238,
	LigatureSubstitutionRecord_op_Equality_mFE8553B8629DEC763581A836A7897A0F9DA11A13,
};
extern void FaceInfo_get_faceIndex_m3C9FB6429035CD34ACD201FB5951AF06E9C2A641_AdjustorThunk (void);
extern void FaceInfo_get_familyName_m62DAF5DE45EA9F3B300B927603D101D350D27241_AdjustorThunk (void);
extern void FaceInfo_get_styleName_mACBAA6529BCE3BC14987645691350A4FB1CB0FC6_AdjustorThunk (void);
extern void FaceInfo_get_pointSize_mDCB57F74233408806DF4F658751ECEE9A66C003E_AdjustorThunk (void);
extern void FaceInfo_get_scale_mC475A572AD4956B47D8B9F8D90DC69BBBB102FCD_AdjustorThunk (void);
extern void FaceInfo_set_scale_m379253929403DA08480CAD127C908A0E8329A5D4_AdjustorThunk (void);
extern void FaceInfo_get_unitsPerEM_m92CD79B71FC504DE1C87923716E999D85A35AC0E_AdjustorThunk (void);
extern void FaceInfo_set_unitsPerEM_m3E48D789D666984A4EA8D221BED22833761E9A90_AdjustorThunk (void);
extern void FaceInfo_get_lineHeight_m528B4A822181FCECF3D4FF1045DF288E5872AB9D_AdjustorThunk (void);
extern void FaceInfo_get_ascentLine_m193755D649428EC24A7E433A1728F11DA7547ABD_AdjustorThunk (void);
extern void FaceInfo_get_capLine_m0D95B5D5CEC5CFB12091F5EB5965DE6E38588C88_AdjustorThunk (void);
extern void FaceInfo_set_capLine_m4716D75CE87EC018E5EA3DE87DA703DBE7A9FEEF_AdjustorThunk (void);
extern void FaceInfo_get_meanLine_m5FF396E0E32A046C1D3CBD3AC1FA279984394D96_AdjustorThunk (void);
extern void FaceInfo_set_meanLine_mE957CD43CB778B093331CC4B10A5330E8FF1CB99_AdjustorThunk (void);
extern void FaceInfo_get_baseline_m934B597D3E0080FEF98CBDD091C457B497179C3A_AdjustorThunk (void);
extern void FaceInfo_get_descentLine_m811A243C9B328B0C546BF9927A010A05DF172BD3_AdjustorThunk (void);
extern void FaceInfo_get_superscriptOffset_m8D462DB86414D8507C7D1CC6881DA9EC896FB80A_AdjustorThunk (void);
extern void FaceInfo_get_superscriptSize_mC3ABE7C70559A8214294CDA598B17FD62BDC2EE0_AdjustorThunk (void);
extern void FaceInfo_get_subscriptOffset_mF1D3E68AC3D449CBC73AA0CBF5B8A187C6C5285A_AdjustorThunk (void);
extern void FaceInfo_get_subscriptSize_mF6264BFB215FDE6C94A45D2F8FC946ADFCDD2E31_AdjustorThunk (void);
extern void FaceInfo_get_underlineOffset_mB1CBB29ECFFE69047F35E654E7F90755F95DD251_AdjustorThunk (void);
extern void FaceInfo_get_underlineThickness_mC032F8C026994AF3FD49E6AB12E113F61EFA98E2_AdjustorThunk (void);
extern void FaceInfo_get_strikethroughOffset_m7997E4A1512FE358331B3A6543C62C92A0AA5CA5_AdjustorThunk (void);
extern void FaceInfo_set_strikethroughOffset_m57B05848BDA21F7AF6563394D29C8AE7107FEFF0_AdjustorThunk (void);
extern void FaceInfo_get_tabWidth_mC6D9F42C40EDD767DE22050E4FBE3878AC96B161_AdjustorThunk (void);
extern void GlyphRect_get_x_m453EECC6C6F08602B1F74C5E1D8EE1163236A898_AdjustorThunk (void);
extern void GlyphRect_get_y_mE31390BB3185EEA82DD16EA41E208F6A0397E3EA_AdjustorThunk (void);
extern void GlyphRect_get_width_mD291C7644BBF18D6A213427F6C9C28840F233F12_AdjustorThunk (void);
extern void GlyphRect_get_height_m7F4D04452994E0D18762BB44352608E484DAAC1A_AdjustorThunk (void);
extern void GlyphRect__ctor_m2B11A6C6C70735CB77FE2176E3D55D922D772A95_AdjustorThunk (void);
extern void GlyphRect_GetHashCode_mC012C2627F2A0C7EB7B47522085764441D47014F_AdjustorThunk (void);
extern void GlyphRect_Equals_mF3BA7FD405AFCEA9E2A6ED2423C27CC023A1289B_AdjustorThunk (void);
extern void GlyphRect_Equals_m29BCDCCDB99C88355A61EDDA65F6A51762BF9C87_AdjustorThunk (void);
extern void GlyphMetrics_get_width_m0F9F391E3A98984167E8001D4101BE1CE9354D13_AdjustorThunk (void);
extern void GlyphMetrics_get_height_mE0872B23CE1A20BF78DEACDBD53BAF789D84AD5C_AdjustorThunk (void);
extern void GlyphMetrics_get_horizontalBearingX_m9C39B5E6D27FF34B706649AE47EE9390B5D76D6F_AdjustorThunk (void);
extern void GlyphMetrics_get_horizontalBearingY_mD316BDD38A32258256994D6A2BCF0FC051D9B223_AdjustorThunk (void);
extern void GlyphMetrics_get_horizontalAdvance_m110E66C340A19E672FB1C26DFB875AB6900AFFF1_AdjustorThunk (void);
extern void GlyphMetrics__ctor_m9CD09465685783A596A7F9112EF7D6A7E1A2792D_AdjustorThunk (void);
extern void GlyphMetrics_GetHashCode_mB1988F3C7DA77518D4DE0F116995F63C99A073E6_AdjustorThunk (void);
extern void GlyphMetrics_Equals_mB8EE2CF8E9D9D51AF2C7B4F13209AFC66456C970_AdjustorThunk (void);
extern void GlyphMetrics_Equals_mA8F8587C1725FA86DD6E87CFDFF0DDB9996112CB_AdjustorThunk (void);
extern void GlyphValueRecord_get_xPlacement_m5E2B8B05A5DF57B2DC4B3795E71330CDDE1761C8_AdjustorThunk (void);
extern void GlyphValueRecord_set_xPlacement_m79F92029922BDE50ED63A6A03EBE478869F1CCFC_AdjustorThunk (void);
extern void GlyphValueRecord_get_yPlacement_mB6303F8800305F6F96ECCD0CD9AA70A1A30A15DA_AdjustorThunk (void);
extern void GlyphValueRecord_set_yPlacement_m04DA300FAB827A708CB291DA3B2EA3128279CA2B_AdjustorThunk (void);
extern void GlyphValueRecord_get_xAdvance_m6C392027FA91E0705C1585C5EF40D984AAA0013E_AdjustorThunk (void);
extern void GlyphValueRecord_set_xAdvance_mA8475DBF8792908481A5DA5FFE7C7FAD3E596A35_AdjustorThunk (void);
extern void GlyphValueRecord_get_yAdvance_m1379AA10FCCFFABEAF43E767F8BFBF32CA76B5B6_AdjustorThunk (void);
extern void GlyphValueRecord__ctor_m01488AC8714CC329C1A23B034C1ABF4B52F0740A_AdjustorThunk (void);
extern void GlyphValueRecord_GetHashCode_m9A2BFC7780FBD61A4B7E0091F8FE87DA15081B60_AdjustorThunk (void);
extern void GlyphValueRecord_Equals_m5DC74E9C597D8F27754444C057F819ECB24CB8B6_AdjustorThunk (void);
extern void GlyphValueRecord_Equals_mB5F45CBE745D1C5BAF7944989DF4239FDC78D972_AdjustorThunk (void);
extern void GlyphAdjustmentRecord_get_glyphIndex_mB1C51945CA4FF019A74BC98C01C8883A396CBFA9_AdjustorThunk (void);
extern void GlyphAdjustmentRecord_get_glyphValueRecord_m83866DCE07A22F903D4BA417476E64114625BDD7_AdjustorThunk (void);
extern void GlyphAdjustmentRecord__ctor_m54853163D58AD883E19A7C0D795A4CF73FCAE28C_AdjustorThunk (void);
extern void GlyphAdjustmentRecord_GetHashCode_m8E0D84D4458A732085FCCA04AAE0A0B16CB3226C_AdjustorThunk (void);
extern void GlyphAdjustmentRecord_Equals_mEF9EBAA5A7ED2B136EA25A2AD26353406884F47D_AdjustorThunk (void);
extern void GlyphAdjustmentRecord_Equals_m2F5908F3B2BB2F6596E40E6A74E5F2120BCDA535_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_get_firstAdjustmentRecord_m867469548F17B298F893B78EE2F93D34E4A6C39C_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_set_firstAdjustmentRecord_mA19E5BFDFB5B5EF6B82BE195BC407390E3754296_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_get_secondAdjustmentRecord_mFDFECB1F7A38E22BD2388FFE9C71E732F6B44D91_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_get_featureLookupFlags_m08DA76766FDE949068B881DBEA29955C9C43E8A9_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord__ctor_m2B1BCCA950399944824CFB79D74C39A7A4F87A6A_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_GetHashCode_mC253F24FFD3BCE5EEB44CA6CDE1BE19336E0A5F5_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_Equals_m0F49F5D76C114BB660B7619A93247591AE323CFD_AdjustorThunk (void);
extern void GlyphPairAdjustmentRecord_Equals_m2DADFD15A4DFF37570EA51D9EAEBA30DF0007689_AdjustorThunk (void);
extern void GlyphAnchorPoint_get_xCoordinate_mCD33464763911ECB78DEB1965970A916FA27DD1C_AdjustorThunk (void);
extern void GlyphAnchorPoint_set_xCoordinate_m6820F49984BD779C6274A656AE013575C2E121FA_AdjustorThunk (void);
extern void GlyphAnchorPoint_get_yCoordinate_m2683C19C6A3D750E4D6C536307313E55589909D6_AdjustorThunk (void);
extern void GlyphAnchorPoint_set_yCoordinate_m3DC770A8B68515510EC83140E7CC2164880EDA97_AdjustorThunk (void);
extern void MarkPositionAdjustment_get_xPositionAdjustment_m5ACBB4C515357320C12597CAE5E4D409BA298765_AdjustorThunk (void);
extern void MarkPositionAdjustment_set_xPositionAdjustment_mBD0304ADF7B91F788755A319868513C1D5C7020C_AdjustorThunk (void);
extern void MarkPositionAdjustment_get_yPositionAdjustment_m1F5F7DBBFEB0B52CCC772F68664D06B11D6A9F2C_AdjustorThunk (void);
extern void MarkPositionAdjustment_set_yPositionAdjustment_m80D07753C658004FD58CD20B4B0D70EFB791836E_AdjustorThunk (void);
extern void MarkToBaseAdjustmentRecord_get_baseGlyphID_mD6A0DD18DBE69E0E68ACA6AABF47B1EA61B633A5_AdjustorThunk (void);
extern void MarkToBaseAdjustmentRecord_set_baseGlyphID_mC8151EF01F0FCFFA0611EB65EC03099B6562ABAC_AdjustorThunk (void);
extern void MarkToBaseAdjustmentRecord_get_baseGlyphAnchorPoint_mCBF57932B7A89C532B0EF750DFD81F8FE389EE08_AdjustorThunk (void);
extern void MarkToBaseAdjustmentRecord_set_baseGlyphAnchorPoint_m1C79E0AD92B197313FCD2273FD57CB6CA3B3E379_AdjustorThunk (void);
extern void MarkToBaseAdjustmentRecord_get_markGlyphID_mB4BB0291733ECFC2433F5C2837F0B28EB05CAF5C_AdjustorThunk (void);
extern void MarkToBaseAdjustmentRecord_set_markGlyphID_mCA5B1438023B89A5FDE7B6F8F1C20F7CEB8A8C09_AdjustorThunk (void);
extern void MarkToBaseAdjustmentRecord_get_markPositionAdjustment_m570715D1D0F84361A90564D4A958394453E1F9AB_AdjustorThunk (void);
extern void MarkToBaseAdjustmentRecord_set_markPositionAdjustment_m585228E1ADB46FEB33EF9400B3A477FEE6559AFC_AdjustorThunk (void);
extern void MarkToMarkAdjustmentRecord_get_baseMarkGlyphID_mA0EAEA751467A8841F9D20D122C4998905A508CC_AdjustorThunk (void);
extern void MarkToMarkAdjustmentRecord_set_baseMarkGlyphID_m3F60246B56DB78EF3754421041378A08E9FC018F_AdjustorThunk (void);
extern void MarkToMarkAdjustmentRecord_get_baseMarkGlyphAnchorPoint_mB87ADA10491B42650BAD4DB7330771061827ACAB_AdjustorThunk (void);
extern void MarkToMarkAdjustmentRecord_set_baseMarkGlyphAnchorPoint_mF99F7E7CBCB4B3C36D87BC6F6B92232EEE63B17B_AdjustorThunk (void);
extern void MarkToMarkAdjustmentRecord_get_combiningMarkGlyphID_m84613A2A27F87AA21CAEDD08759031302C9A8FBF_AdjustorThunk (void);
extern void MarkToMarkAdjustmentRecord_set_combiningMarkGlyphID_mABD6E44107C367EE21EB258DB5681707C4B34075_AdjustorThunk (void);
extern void MarkToMarkAdjustmentRecord_get_combiningMarkPositionAdjustment_mC109ECEDB4AD314A25C0EB1F6F6151AE611DE15C_AdjustorThunk (void);
extern void MarkToMarkAdjustmentRecord_set_combiningMarkPositionAdjustment_mFAB0D9C53F43D866DD2DA6F3B85A5E1AE23897EC_AdjustorThunk (void);
extern void LigatureSubstitutionRecord_get_componentGlyphIDs_m3BBDC9421E3A7369B198379F5433FBB13ADCE628_AdjustorThunk (void);
extern void LigatureSubstitutionRecord_set_componentGlyphIDs_mDD8D76279FF545B516D4CE689568AA513A95EF63_AdjustorThunk (void);
extern void LigatureSubstitutionRecord_get_ligatureGlyphID_m5FD629E204026FB8B6279498CDE5CAB1D23827EC_AdjustorThunk (void);
extern void LigatureSubstitutionRecord_set_ligatureGlyphID_mDB4F809745EF0536182FAF90AFB276F389C35EB6_AdjustorThunk (void);
extern void LigatureSubstitutionRecord_Equals_m3CC62722B819D61147F694E4BF1891FF7A752BF1_AdjustorThunk (void);
extern void LigatureSubstitutionRecord_Equals_mD986B72ED54907EFBDF421099D75E99A6198D814_AdjustorThunk (void);
extern void LigatureSubstitutionRecord_GetHashCode_m1CBBE1E99AD03EA162ED8AE2C1EC3EAC15A0B238_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[98] = 
{
	{ 0x06000001, FaceInfo_get_faceIndex_m3C9FB6429035CD34ACD201FB5951AF06E9C2A641_AdjustorThunk },
	{ 0x06000002, FaceInfo_get_familyName_m62DAF5DE45EA9F3B300B927603D101D350D27241_AdjustorThunk },
	{ 0x06000003, FaceInfo_get_styleName_mACBAA6529BCE3BC14987645691350A4FB1CB0FC6_AdjustorThunk },
	{ 0x06000004, FaceInfo_get_pointSize_mDCB57F74233408806DF4F658751ECEE9A66C003E_AdjustorThunk },
	{ 0x06000005, FaceInfo_get_scale_mC475A572AD4956B47D8B9F8D90DC69BBBB102FCD_AdjustorThunk },
	{ 0x06000006, FaceInfo_set_scale_m379253929403DA08480CAD127C908A0E8329A5D4_AdjustorThunk },
	{ 0x06000007, FaceInfo_get_unitsPerEM_m92CD79B71FC504DE1C87923716E999D85A35AC0E_AdjustorThunk },
	{ 0x06000008, FaceInfo_set_unitsPerEM_m3E48D789D666984A4EA8D221BED22833761E9A90_AdjustorThunk },
	{ 0x06000009, FaceInfo_get_lineHeight_m528B4A822181FCECF3D4FF1045DF288E5872AB9D_AdjustorThunk },
	{ 0x0600000A, FaceInfo_get_ascentLine_m193755D649428EC24A7E433A1728F11DA7547ABD_AdjustorThunk },
	{ 0x0600000B, FaceInfo_get_capLine_m0D95B5D5CEC5CFB12091F5EB5965DE6E38588C88_AdjustorThunk },
	{ 0x0600000C, FaceInfo_set_capLine_m4716D75CE87EC018E5EA3DE87DA703DBE7A9FEEF_AdjustorThunk },
	{ 0x0600000D, FaceInfo_get_meanLine_m5FF396E0E32A046C1D3CBD3AC1FA279984394D96_AdjustorThunk },
	{ 0x0600000E, FaceInfo_set_meanLine_mE957CD43CB778B093331CC4B10A5330E8FF1CB99_AdjustorThunk },
	{ 0x0600000F, FaceInfo_get_baseline_m934B597D3E0080FEF98CBDD091C457B497179C3A_AdjustorThunk },
	{ 0x06000010, FaceInfo_get_descentLine_m811A243C9B328B0C546BF9927A010A05DF172BD3_AdjustorThunk },
	{ 0x06000011, FaceInfo_get_superscriptOffset_m8D462DB86414D8507C7D1CC6881DA9EC896FB80A_AdjustorThunk },
	{ 0x06000012, FaceInfo_get_superscriptSize_mC3ABE7C70559A8214294CDA598B17FD62BDC2EE0_AdjustorThunk },
	{ 0x06000013, FaceInfo_get_subscriptOffset_mF1D3E68AC3D449CBC73AA0CBF5B8A187C6C5285A_AdjustorThunk },
	{ 0x06000014, FaceInfo_get_subscriptSize_mF6264BFB215FDE6C94A45D2F8FC946ADFCDD2E31_AdjustorThunk },
	{ 0x06000015, FaceInfo_get_underlineOffset_mB1CBB29ECFFE69047F35E654E7F90755F95DD251_AdjustorThunk },
	{ 0x06000016, FaceInfo_get_underlineThickness_mC032F8C026994AF3FD49E6AB12E113F61EFA98E2_AdjustorThunk },
	{ 0x06000017, FaceInfo_get_strikethroughOffset_m7997E4A1512FE358331B3A6543C62C92A0AA5CA5_AdjustorThunk },
	{ 0x06000018, FaceInfo_set_strikethroughOffset_m57B05848BDA21F7AF6563394D29C8AE7107FEFF0_AdjustorThunk },
	{ 0x06000019, FaceInfo_get_tabWidth_mC6D9F42C40EDD767DE22050E4FBE3878AC96B161_AdjustorThunk },
	{ 0x0600001A, GlyphRect_get_x_m453EECC6C6F08602B1F74C5E1D8EE1163236A898_AdjustorThunk },
	{ 0x0600001B, GlyphRect_get_y_mE31390BB3185EEA82DD16EA41E208F6A0397E3EA_AdjustorThunk },
	{ 0x0600001C, GlyphRect_get_width_mD291C7644BBF18D6A213427F6C9C28840F233F12_AdjustorThunk },
	{ 0x0600001D, GlyphRect_get_height_m7F4D04452994E0D18762BB44352608E484DAAC1A_AdjustorThunk },
	{ 0x0600001F, GlyphRect__ctor_m2B11A6C6C70735CB77FE2176E3D55D922D772A95_AdjustorThunk },
	{ 0x06000020, GlyphRect_GetHashCode_mC012C2627F2A0C7EB7B47522085764441D47014F_AdjustorThunk },
	{ 0x06000021, GlyphRect_Equals_mF3BA7FD405AFCEA9E2A6ED2423C27CC023A1289B_AdjustorThunk },
	{ 0x06000022, GlyphRect_Equals_m29BCDCCDB99C88355A61EDDA65F6A51762BF9C87_AdjustorThunk },
	{ 0x06000024, GlyphMetrics_get_width_m0F9F391E3A98984167E8001D4101BE1CE9354D13_AdjustorThunk },
	{ 0x06000025, GlyphMetrics_get_height_mE0872B23CE1A20BF78DEACDBD53BAF789D84AD5C_AdjustorThunk },
	{ 0x06000026, GlyphMetrics_get_horizontalBearingX_m9C39B5E6D27FF34B706649AE47EE9390B5D76D6F_AdjustorThunk },
	{ 0x06000027, GlyphMetrics_get_horizontalBearingY_mD316BDD38A32258256994D6A2BCF0FC051D9B223_AdjustorThunk },
	{ 0x06000028, GlyphMetrics_get_horizontalAdvance_m110E66C340A19E672FB1C26DFB875AB6900AFFF1_AdjustorThunk },
	{ 0x06000029, GlyphMetrics__ctor_m9CD09465685783A596A7F9112EF7D6A7E1A2792D_AdjustorThunk },
	{ 0x0600002A, GlyphMetrics_GetHashCode_mB1988F3C7DA77518D4DE0F116995F63C99A073E6_AdjustorThunk },
	{ 0x0600002B, GlyphMetrics_Equals_mB8EE2CF8E9D9D51AF2C7B4F13209AFC66456C970_AdjustorThunk },
	{ 0x0600002C, GlyphMetrics_Equals_mA8F8587C1725FA86DD6E87CFDFF0DDB9996112CB_AdjustorThunk },
	{ 0x06000085, GlyphValueRecord_get_xPlacement_m5E2B8B05A5DF57B2DC4B3795E71330CDDE1761C8_AdjustorThunk },
	{ 0x06000086, GlyphValueRecord_set_xPlacement_m79F92029922BDE50ED63A6A03EBE478869F1CCFC_AdjustorThunk },
	{ 0x06000087, GlyphValueRecord_get_yPlacement_mB6303F8800305F6F96ECCD0CD9AA70A1A30A15DA_AdjustorThunk },
	{ 0x06000088, GlyphValueRecord_set_yPlacement_m04DA300FAB827A708CB291DA3B2EA3128279CA2B_AdjustorThunk },
	{ 0x06000089, GlyphValueRecord_get_xAdvance_m6C392027FA91E0705C1585C5EF40D984AAA0013E_AdjustorThunk },
	{ 0x0600008A, GlyphValueRecord_set_xAdvance_mA8475DBF8792908481A5DA5FFE7C7FAD3E596A35_AdjustorThunk },
	{ 0x0600008B, GlyphValueRecord_get_yAdvance_m1379AA10FCCFFABEAF43E767F8BFBF32CA76B5B6_AdjustorThunk },
	{ 0x0600008C, GlyphValueRecord__ctor_m01488AC8714CC329C1A23B034C1ABF4B52F0740A_AdjustorThunk },
	{ 0x0600008E, GlyphValueRecord_GetHashCode_m9A2BFC7780FBD61A4B7E0091F8FE87DA15081B60_AdjustorThunk },
	{ 0x0600008F, GlyphValueRecord_Equals_m5DC74E9C597D8F27754444C057F819ECB24CB8B6_AdjustorThunk },
	{ 0x06000090, GlyphValueRecord_Equals_mB5F45CBE745D1C5BAF7944989DF4239FDC78D972_AdjustorThunk },
	{ 0x06000091, GlyphAdjustmentRecord_get_glyphIndex_mB1C51945CA4FF019A74BC98C01C8883A396CBFA9_AdjustorThunk },
	{ 0x06000092, GlyphAdjustmentRecord_get_glyphValueRecord_m83866DCE07A22F903D4BA417476E64114625BDD7_AdjustorThunk },
	{ 0x06000093, GlyphAdjustmentRecord__ctor_m54853163D58AD883E19A7C0D795A4CF73FCAE28C_AdjustorThunk },
	{ 0x06000094, GlyphAdjustmentRecord_GetHashCode_m8E0D84D4458A732085FCCA04AAE0A0B16CB3226C_AdjustorThunk },
	{ 0x06000095, GlyphAdjustmentRecord_Equals_mEF9EBAA5A7ED2B136EA25A2AD26353406884F47D_AdjustorThunk },
	{ 0x06000096, GlyphAdjustmentRecord_Equals_m2F5908F3B2BB2F6596E40E6A74E5F2120BCDA535_AdjustorThunk },
	{ 0x06000097, GlyphPairAdjustmentRecord_get_firstAdjustmentRecord_m867469548F17B298F893B78EE2F93D34E4A6C39C_AdjustorThunk },
	{ 0x06000098, GlyphPairAdjustmentRecord_set_firstAdjustmentRecord_mA19E5BFDFB5B5EF6B82BE195BC407390E3754296_AdjustorThunk },
	{ 0x06000099, GlyphPairAdjustmentRecord_get_secondAdjustmentRecord_mFDFECB1F7A38E22BD2388FFE9C71E732F6B44D91_AdjustorThunk },
	{ 0x0600009A, GlyphPairAdjustmentRecord_get_featureLookupFlags_m08DA76766FDE949068B881DBEA29955C9C43E8A9_AdjustorThunk },
	{ 0x0600009B, GlyphPairAdjustmentRecord__ctor_m2B1BCCA950399944824CFB79D74C39A7A4F87A6A_AdjustorThunk },
	{ 0x0600009C, GlyphPairAdjustmentRecord_GetHashCode_mC253F24FFD3BCE5EEB44CA6CDE1BE19336E0A5F5_AdjustorThunk },
	{ 0x0600009D, GlyphPairAdjustmentRecord_Equals_m0F49F5D76C114BB660B7619A93247591AE323CFD_AdjustorThunk },
	{ 0x0600009E, GlyphPairAdjustmentRecord_Equals_m2DADFD15A4DFF37570EA51D9EAEBA30DF0007689_AdjustorThunk },
	{ 0x0600009F, GlyphAnchorPoint_get_xCoordinate_mCD33464763911ECB78DEB1965970A916FA27DD1C_AdjustorThunk },
	{ 0x060000A0, GlyphAnchorPoint_set_xCoordinate_m6820F49984BD779C6274A656AE013575C2E121FA_AdjustorThunk },
	{ 0x060000A1, GlyphAnchorPoint_get_yCoordinate_m2683C19C6A3D750E4D6C536307313E55589909D6_AdjustorThunk },
	{ 0x060000A2, GlyphAnchorPoint_set_yCoordinate_m3DC770A8B68515510EC83140E7CC2164880EDA97_AdjustorThunk },
	{ 0x060000A3, MarkPositionAdjustment_get_xPositionAdjustment_m5ACBB4C515357320C12597CAE5E4D409BA298765_AdjustorThunk },
	{ 0x060000A4, MarkPositionAdjustment_set_xPositionAdjustment_mBD0304ADF7B91F788755A319868513C1D5C7020C_AdjustorThunk },
	{ 0x060000A5, MarkPositionAdjustment_get_yPositionAdjustment_m1F5F7DBBFEB0B52CCC772F68664D06B11D6A9F2C_AdjustorThunk },
	{ 0x060000A6, MarkPositionAdjustment_set_yPositionAdjustment_m80D07753C658004FD58CD20B4B0D70EFB791836E_AdjustorThunk },
	{ 0x060000A7, MarkToBaseAdjustmentRecord_get_baseGlyphID_mD6A0DD18DBE69E0E68ACA6AABF47B1EA61B633A5_AdjustorThunk },
	{ 0x060000A8, MarkToBaseAdjustmentRecord_set_baseGlyphID_mC8151EF01F0FCFFA0611EB65EC03099B6562ABAC_AdjustorThunk },
	{ 0x060000A9, MarkToBaseAdjustmentRecord_get_baseGlyphAnchorPoint_mCBF57932B7A89C532B0EF750DFD81F8FE389EE08_AdjustorThunk },
	{ 0x060000AA, MarkToBaseAdjustmentRecord_set_baseGlyphAnchorPoint_m1C79E0AD92B197313FCD2273FD57CB6CA3B3E379_AdjustorThunk },
	{ 0x060000AB, MarkToBaseAdjustmentRecord_get_markGlyphID_mB4BB0291733ECFC2433F5C2837F0B28EB05CAF5C_AdjustorThunk },
	{ 0x060000AC, MarkToBaseAdjustmentRecord_set_markGlyphID_mCA5B1438023B89A5FDE7B6F8F1C20F7CEB8A8C09_AdjustorThunk },
	{ 0x060000AD, MarkToBaseAdjustmentRecord_get_markPositionAdjustment_m570715D1D0F84361A90564D4A958394453E1F9AB_AdjustorThunk },
	{ 0x060000AE, MarkToBaseAdjustmentRecord_set_markPositionAdjustment_m585228E1ADB46FEB33EF9400B3A477FEE6559AFC_AdjustorThunk },
	{ 0x060000AF, MarkToMarkAdjustmentRecord_get_baseMarkGlyphID_mA0EAEA751467A8841F9D20D122C4998905A508CC_AdjustorThunk },
	{ 0x060000B0, MarkToMarkAdjustmentRecord_set_baseMarkGlyphID_m3F60246B56DB78EF3754421041378A08E9FC018F_AdjustorThunk },
	{ 0x060000B1, MarkToMarkAdjustmentRecord_get_baseMarkGlyphAnchorPoint_mB87ADA10491B42650BAD4DB7330771061827ACAB_AdjustorThunk },
	{ 0x060000B2, MarkToMarkAdjustmentRecord_set_baseMarkGlyphAnchorPoint_mF99F7E7CBCB4B3C36D87BC6F6B92232EEE63B17B_AdjustorThunk },
	{ 0x060000B3, MarkToMarkAdjustmentRecord_get_combiningMarkGlyphID_m84613A2A27F87AA21CAEDD08759031302C9A8FBF_AdjustorThunk },
	{ 0x060000B4, MarkToMarkAdjustmentRecord_set_combiningMarkGlyphID_mABD6E44107C367EE21EB258DB5681707C4B34075_AdjustorThunk },
	{ 0x060000B5, MarkToMarkAdjustmentRecord_get_combiningMarkPositionAdjustment_mC109ECEDB4AD314A25C0EB1F6F6151AE611DE15C_AdjustorThunk },
	{ 0x060000B6, MarkToMarkAdjustmentRecord_set_combiningMarkPositionAdjustment_mFAB0D9C53F43D866DD2DA6F3B85A5E1AE23897EC_AdjustorThunk },
	{ 0x060000B7, LigatureSubstitutionRecord_get_componentGlyphIDs_m3BBDC9421E3A7369B198379F5433FBB13ADCE628_AdjustorThunk },
	{ 0x060000B8, LigatureSubstitutionRecord_set_componentGlyphIDs_mDD8D76279FF545B516D4CE689568AA513A95EF63_AdjustorThunk },
	{ 0x060000B9, LigatureSubstitutionRecord_get_ligatureGlyphID_m5FD629E204026FB8B6279498CDE5CAB1D23827EC_AdjustorThunk },
	{ 0x060000BA, LigatureSubstitutionRecord_set_ligatureGlyphID_mDB4F809745EF0536182FAF90AFB276F389C35EB6_AdjustorThunk },
	{ 0x060000BB, LigatureSubstitutionRecord_Equals_m3CC62722B819D61147F694E4BF1891FF7A752BF1_AdjustorThunk },
	{ 0x060000BC, LigatureSubstitutionRecord_Equals_mD986B72ED54907EFBDF421099D75E99A6198D814_AdjustorThunk },
	{ 0x060000BD, LigatureSubstitutionRecord_GetHashCode_m1CBBE1E99AD03EA162ED8AE2C1EC3EAC15A0B238_AdjustorThunk },
};
static const int32_t s_InvokerIndices[190] = 
{
	12323,
	12380,
	12380,
	12471,
	12471,
	9591,
	12323,
	9453,
	12471,
	12471,
	12471,
	9591,
	12471,
	9591,
	12471,
	12471,
	12471,
	12471,
	12471,
	12471,
	12471,
	12471,
	12471,
	9591,
	12471,
	12323,
	12323,
	12323,
	12323,
	21747,
	1361,
	12323,
	6668,
	6575,
	21856,
	12471,
	12471,
	12471,
	12471,
	12471,
	746,
	12323,
	6668,
	6573,
	12544,
	9664,
	12280,
	9412,
	12282,
	9414,
	12471,
	9591,
	12323,
	9453,
	12563,
	9411,
	749,
	20200,
	20200,
	15721,
	15711,
	15721,
	15711,
	15719,
	15717,
	21757,
	21757,
	15551,
	15551,
	21740,
	20187,
	21775,
	21775,
	17756,
	20538,
	15570,
	15570,
	15570,
	15570,
	20664,
	12942,
	12874,
	12940,
	12855,
	21775,
	20356,
	20342,
	20342,
	17395,
	20200,
	20342,
	17395,
	21775,
	20342,
	20342,
	17395,
	20183,
	21775,
	20342,
	20342,
	17395,
	20184,
	21775,
	20342,
	20342,
	17395,
	20185,
	18333,
	-1,
	-1,
	20677,
	21856,
	20187,
	15661,
	15698,
	15654,
	15506,
	12873,
	12854,
	17358,
	17358,
	20662,
	17358,
	20187,
	20662,
	17358,
	20187,
	20662,
	17358,
	20187,
	20675,
	15689,
	12471,
	9591,
	12471,
	9591,
	12471,
	9591,
	12471,
	1516,
	17303,
	12323,
	6668,
	6576,
	12544,
	12283,
	4885,
	12323,
	6668,
	6571,
	12276,
	9408,
	12276,
	12323,
	3927,
	12323,
	6668,
	6574,
	12471,
	9591,
	12471,
	9591,
	12471,
	9591,
	12471,
	9591,
	12544,
	9664,
	12278,
	9410,
	12544,
	9664,
	12354,
	9481,
	12544,
	9664,
	12278,
	9410,
	12544,
	9664,
	12354,
	9481,
	12380,
	9510,
	12544,
	9664,
	6632,
	6668,
	12323,
	17124,
};
static const Il2CppTokenRangePair s_rgctxIndices[2] = 
{
	{ 0x0600006D, { 0, 8 } },
	{ 0x0600006E, { 8, 3 } },
};
extern const uint32_t g_rgctx_List_1U26_t7426D5EB55FE6CBFC77194E5A84AA31237C491DC;
extern const uint32_t g_rgctx_List_1_tAF861B916DD21383F06D73A5F0F2132D57F5A2E5;
extern const uint32_t g_rgctx_List_1_get_Count_mAFBAA494C44EA10C5165ABCAF3C7D9948C9776BB;
extern const uint32_t g_rgctx_TU5BU5DU26_tC514DD32B78B2B0D4C149D2FBD65B54EA4723659;
extern const uint32_t g_rgctx_TU5BU5D_t04DF92FC5A6ED2E137925B2D4AAF396747A65B9D;
extern const uint32_t g_rgctx_Array_Resize_TisT_tFFAFDE9DC7B542A3A54F814950A84AAEEA084017_mC449C792316CB2FED9221E20AB9071D2651433EE;
extern const uint32_t g_rgctx_List_1_get_Item_mFBCECC084098088A5E18C51A686368A5839C7EF3;
extern const uint32_t g_rgctx_T_tFFAFDE9DC7B542A3A54F814950A84AAEEA084017;
extern const uint32_t g_rgctx_TU5BU5DU26_tDB8D3F81015DC6570941CF27000F6AA4B87251A2;
extern const uint32_t g_rgctx_TU5BU5D_t8A34B43F2CD5F32F323C003A2FDB6F9C9A6C977A;
extern const uint32_t g_rgctx_Array_Resize_TisT_t201BA83724B99DBE4155ED3CCD796A8773447525_mE500A00B5DE2E8E6605EF95ECC80FCBF77FD2DDC;
static const Il2CppRGCTXDefinition s_rgctxValues[11] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1U26_t7426D5EB55FE6CBFC77194E5A84AA31237C491DC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tAF861B916DD21383F06D73A5F0F2132D57F5A2E5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_mAFBAA494C44EA10C5165ABCAF3C7D9948C9776BB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_tC514DD32B78B2B0D4C149D2FBD65B54EA4723659 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t04DF92FC5A6ED2E137925B2D4AAF396747A65B9D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_tFFAFDE9DC7B542A3A54F814950A84AAEEA084017_mC449C792316CB2FED9221E20AB9071D2651433EE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_mFBCECC084098088A5E18C51A686368A5839C7EF3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFFAFDE9DC7B542A3A54F814950A84AAEEA084017 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_tDB8D3F81015DC6570941CF27000F6AA4B87251A2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t8A34B43F2CD5F32F323C003A2FDB6F9C9A6C977A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_t201BA83724B99DBE4155ED3CCD796A8773447525_mE500A00B5DE2E8E6605EF95ECC80FCBF77FD2DDC },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_TextCoreFontEngineModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_TextCoreFontEngineModule_CodeGenModule = 
{
	"UnityEngine.TextCoreFontEngineModule.dll",
	190,
	s_methodPointers,
	98,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	2,
	s_rgctxIndices,
	11,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
