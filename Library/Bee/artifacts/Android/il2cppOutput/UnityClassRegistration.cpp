extern "C" void RegisterStaticallyLinkedModulesGranular()
{
	void RegisterModule_SharedInternals();
	RegisterModule_SharedInternals();

	void RegisterModule_Core();
	RegisterModule_Core();

	void RegisterModule_AndroidJNI();
	RegisterModule_AndroidJNI();

	void RegisterModule_Animation();
	RegisterModule_Animation();

	void RegisterModule_Audio();
	RegisterModule_Audio();

	void RegisterModule_GraphicsStateCollectionSerializer();
	RegisterModule_GraphicsStateCollectionSerializer();

	void RegisterModule_HierarchyCore();
	RegisterModule_HierarchyCore();

	void RegisterModule_IMGUI();
	RegisterModule_IMGUI();

	void RegisterModule_Input();
	RegisterModule_Input();

	void RegisterModule_InputLegacy();
	RegisterModule_InputLegacy();

	void RegisterModule_InputForUI();
	RegisterModule_InputForUI();

	void RegisterModule_JSONSerialize();
	RegisterModule_JSONSerialize();

	void RegisterModule_Physics();
	RegisterModule_Physics();

	void RegisterModule_Physics2D();
	RegisterModule_Physics2D();

	void RegisterModule_Properties();
	RegisterModule_Properties();

	void RegisterModule_RuntimeInitializeOnLoadManagerInitializer();
	RegisterModule_RuntimeInitializeOnLoadManagerInitializer();

	void RegisterModule_Subsystems();
	RegisterModule_Subsystems();

	void RegisterModule_Terrain();
	RegisterModule_Terrain();

	void RegisterModule_TextRendering();
	RegisterModule_TextRendering();

	void RegisterModule_TextCoreFontEngine();
	RegisterModule_TextCoreFontEngine();

	void RegisterModule_TextCoreTextEngine();
	RegisterModule_TextCoreTextEngine();

	void RegisterModule_UI();
	RegisterModule_UI();

	void RegisterModule_UIElements();
	RegisterModule_UIElements();

	void RegisterModule_UnityAnalyticsCommon();
	RegisterModule_UnityAnalyticsCommon();

	void RegisterModule_VFX();
	RegisterModule_VFX();

	void RegisterModule_XR();
	RegisterModule_XR();

	void RegisterModule_VR();
	RegisterModule_VR();

}

template <typename T> void RegisterUnityClass(const char*);
template <typename T> void RegisterStrippedType(int, const char*, const char*);

void InvokeRegisterStaticallyLinkedModuleClasses()
{
	// Do nothing (we're in stripping mode)
}

class Animator; template <> void RegisterUnityClass<Animator>(const char*);
class AnimatorController; template <> void RegisterUnityClass<AnimatorController>(const char*);
class AnimatorOverrideController; template <> void RegisterUnityClass<AnimatorOverrideController>(const char*);
class RuntimeAnimatorController; template <> void RegisterUnityClass<RuntimeAnimatorController>(const char*);
class AudioBehaviour; template <> void RegisterUnityClass<AudioBehaviour>(const char*);
class AudioClip; template <> void RegisterUnityClass<AudioClip>(const char*);
class AudioListener; template <> void RegisterUnityClass<AudioListener>(const char*);
class AudioManager; template <> void RegisterUnityClass<AudioManager>(const char*);
class AudioResource; template <> void RegisterUnityClass<AudioResource>(const char*);
class SampleClip; template <> void RegisterUnityClass<SampleClip>(const char*);
class Behaviour; template <> void RegisterUnityClass<Behaviour>(const char*);
class BuildSettings; template <> void RegisterUnityClass<BuildSettings>(const char*);
class Camera; template <> void RegisterUnityClass<Camera>(const char*);
namespace Unity { class Component; } template <> void RegisterUnityClass<Unity::Component>(const char*);
class ComputeShader; template <> void RegisterUnityClass<ComputeShader>(const char*);
class Cubemap; template <> void RegisterUnityClass<Cubemap>(const char*);
class CubemapArray; template <> void RegisterUnityClass<CubemapArray>(const char*);
class DelayedCallManager; template <> void RegisterUnityClass<DelayedCallManager>(const char*);
class EditorExtension; template <> void RegisterUnityClass<EditorExtension>(const char*);
class GameManager; template <> void RegisterUnityClass<GameManager>(const char*);
class GameObject; template <> void RegisterUnityClass<GameObject>(const char*);
class GlobalGameManager; template <> void RegisterUnityClass<GlobalGameManager>(const char*);
class GraphicsSettings; template <> void RegisterUnityClass<GraphicsSettings>(const char*);
class InputManager; template <> void RegisterUnityClass<InputManager>(const char*);
class LODGroup; template <> void RegisterUnityClass<LODGroup>(const char*);
class LevelGameManager; template <> void RegisterUnityClass<LevelGameManager>(const char*);
class Light; template <> void RegisterUnityClass<Light>(const char*);
class LightProbeProxyVolume; template <> void RegisterUnityClass<LightProbeProxyVolume>(const char*);
class LightProbes; template <> void RegisterUnityClass<LightProbes>(const char*);
class LightingSettings; template <> void RegisterUnityClass<LightingSettings>(const char*);
class LightmapSettings; template <> void RegisterUnityClass<LightmapSettings>(const char*);
class LowerResBlitTexture; template <> void RegisterUnityClass<LowerResBlitTexture>(const char*);
class Material; template <> void RegisterUnityClass<Material>(const char*);
class Mesh; template <> void RegisterUnityClass<Mesh>(const char*);
class MeshFilter; template <> void RegisterUnityClass<MeshFilter>(const char*);
class MeshRenderer; template <> void RegisterUnityClass<MeshRenderer>(const char*);
class MonoBehaviour; template <> void RegisterUnityClass<MonoBehaviour>(const char*);
class MonoManager; template <> void RegisterUnityClass<MonoManager>(const char*);
class MonoScript; template <> void RegisterUnityClass<MonoScript>(const char*);
class NamedObject; template <> void RegisterUnityClass<NamedObject>(const char*);
class Object; template <> void RegisterUnityClass<Object>(const char*);
class PlayerSettings; template <> void RegisterUnityClass<PlayerSettings>(const char*);
class PreloadData; template <> void RegisterUnityClass<PreloadData>(const char*);
class QualitySettings; template <> void RegisterUnityClass<QualitySettings>(const char*);
class RayTracingShader; template <> void RegisterUnityClass<RayTracingShader>(const char*);
namespace UI { class RectTransform; } template <> void RegisterUnityClass<UI::RectTransform>(const char*);
class ReflectionProbe; template <> void RegisterUnityClass<ReflectionProbe>(const char*);
class RenderSettings; template <> void RegisterUnityClass<RenderSettings>(const char*);
class RenderTexture; template <> void RegisterUnityClass<RenderTexture>(const char*);
class Renderer; template <> void RegisterUnityClass<Renderer>(const char*);
class ResourceManager; template <> void RegisterUnityClass<ResourceManager>(const char*);
class RuntimeInitializeOnLoadManager; template <> void RegisterUnityClass<RuntimeInitializeOnLoadManager>(const char*);
class Shader; template <> void RegisterUnityClass<Shader>(const char*);
class ShaderNameRegistry; template <> void RegisterUnityClass<ShaderNameRegistry>(const char*);
class Skybox; template <> void RegisterUnityClass<Skybox>(const char*);
class SortingGroup; template <> void RegisterUnityClass<SortingGroup>(const char*);
class Sprite; template <> void RegisterUnityClass<Sprite>(const char*);
class SpriteAtlas; template <> void RegisterUnityClass<SpriteAtlas>(const char*);
class SpriteRenderer; template <> void RegisterUnityClass<SpriteRenderer>(const char*);
class TagManager; template <> void RegisterUnityClass<TagManager>(const char*);
class TextAsset; template <> void RegisterUnityClass<TextAsset>(const char*);
class Texture; template <> void RegisterUnityClass<Texture>(const char*);
class Texture2D; template <> void RegisterUnityClass<Texture2D>(const char*);
class Texture2DArray; template <> void RegisterUnityClass<Texture2DArray>(const char*);
class Texture3D; template <> void RegisterUnityClass<Texture3D>(const char*);
class TimeManager; template <> void RegisterUnityClass<TimeManager>(const char*);
class Transform; template <> void RegisterUnityClass<Transform>(const char*);
class Collider; template <> void RegisterUnityClass<Collider>(const char*);
class PhysicsManager; template <> void RegisterUnityClass<PhysicsManager>(const char*);
class Physics2DSettings; template <> void RegisterUnityClass<Physics2DSettings>(const char*);
class Terrain; template <> void RegisterUnityClass<Terrain>(const char*);
class TerrainData; template <> void RegisterUnityClass<TerrainData>(const char*);
namespace TextRendering { class Font; } template <> void RegisterUnityClass<TextRendering::Font>(const char*);
namespace UI { class Canvas; } template <> void RegisterUnityClass<UI::Canvas>(const char*);
namespace UI { class CanvasGroup; } template <> void RegisterUnityClass<UI::CanvasGroup>(const char*);
namespace UI { class CanvasRenderer; } template <> void RegisterUnityClass<UI::CanvasRenderer>(const char*);
class UIRenderer; template <> void RegisterUnityClass<UIRenderer>(const char*);
class VFXManager; template <> void RegisterUnityClass<VFXManager>(const char*);
class VisualEffect; template <> void RegisterUnityClass<VisualEffect>(const char*);
class VisualEffectAsset; template <> void RegisterUnityClass<VisualEffectAsset>(const char*);
class VisualEffectObject; template <> void RegisterUnityClass<VisualEffectObject>(const char*);

void RegisterAllClasses()
{
void RegisterBuiltinTypes();
RegisterBuiltinTypes();
	//Total: 81 non stripped classes
	//0. Animator
	RegisterUnityClass<Animator>("Animation");
	//1. AnimatorController
	RegisterUnityClass<AnimatorController>("Animation");
	//2. AnimatorOverrideController
	RegisterUnityClass<AnimatorOverrideController>("Animation");
	//3. RuntimeAnimatorController
	RegisterUnityClass<RuntimeAnimatorController>("Animation");
	//4. AudioBehaviour
	RegisterUnityClass<AudioBehaviour>("Audio");
	//5. AudioClip
	RegisterUnityClass<AudioClip>("Audio");
	//6. AudioListener
	RegisterUnityClass<AudioListener>("Audio");
	//7. AudioManager
	RegisterUnityClass<AudioManager>("Audio");
	//8. AudioResource
	RegisterUnityClass<AudioResource>("Audio");
	//9. SampleClip
	RegisterUnityClass<SampleClip>("Audio");
	//10. Behaviour
	RegisterUnityClass<Behaviour>("Core");
	//11. BuildSettings
	RegisterUnityClass<BuildSettings>("Core");
	//12. Camera
	RegisterUnityClass<Camera>("Core");
	//13. Component
	RegisterUnityClass<Unity::Component>("Core");
	//14. ComputeShader
	RegisterUnityClass<ComputeShader>("Core");
	//15. Cubemap
	RegisterUnityClass<Cubemap>("Core");
	//16. CubemapArray
	RegisterUnityClass<CubemapArray>("Core");
	//17. DelayedCallManager
	RegisterUnityClass<DelayedCallManager>("Core");
	//18. EditorExtension
	RegisterUnityClass<EditorExtension>("Core");
	//19. GameManager
	RegisterUnityClass<GameManager>("Core");
	//20. GameObject
	RegisterUnityClass<GameObject>("Core");
	//21. GlobalGameManager
	RegisterUnityClass<GlobalGameManager>("Core");
	//22. GraphicsSettings
	RegisterUnityClass<GraphicsSettings>("Core");
	//23. InputManager
	RegisterUnityClass<InputManager>("Core");
	//24. LODGroup
	RegisterUnityClass<LODGroup>("Core");
	//25. LevelGameManager
	RegisterUnityClass<LevelGameManager>("Core");
	//26. Light
	RegisterUnityClass<Light>("Core");
	//27. LightProbeProxyVolume
	RegisterUnityClass<LightProbeProxyVolume>("Core");
	//28. LightProbes
	RegisterUnityClass<LightProbes>("Core");
	//29. LightingSettings
	RegisterUnityClass<LightingSettings>("Core");
	//30. LightmapSettings
	RegisterUnityClass<LightmapSettings>("Core");
	//31. LowerResBlitTexture
	RegisterUnityClass<LowerResBlitTexture>("Core");
	//32. Material
	RegisterUnityClass<Material>("Core");
	//33. Mesh
	RegisterUnityClass<Mesh>("Core");
	//34. MeshFilter
	RegisterUnityClass<MeshFilter>("Core");
	//35. MeshRenderer
	RegisterUnityClass<MeshRenderer>("Core");
	//36. MonoBehaviour
	RegisterUnityClass<MonoBehaviour>("Core");
	//37. MonoManager
	RegisterUnityClass<MonoManager>("Core");
	//38. MonoScript
	RegisterUnityClass<MonoScript>("Core");
	//39. NamedObject
	RegisterUnityClass<NamedObject>("Core");
	//40. Object
	//Skipping Object
	//41. PlayerSettings
	RegisterUnityClass<PlayerSettings>("Core");
	//42. PreloadData
	RegisterUnityClass<PreloadData>("Core");
	//43. QualitySettings
	RegisterUnityClass<QualitySettings>("Core");
	//44. RayTracingShader
	RegisterUnityClass<RayTracingShader>("Core");
	//45. RectTransform
	RegisterUnityClass<UI::RectTransform>("Core");
	//46. ReflectionProbe
	RegisterUnityClass<ReflectionProbe>("Core");
	//47. RenderSettings
	RegisterUnityClass<RenderSettings>("Core");
	//48. RenderTexture
	RegisterUnityClass<RenderTexture>("Core");
	//49. Renderer
	RegisterUnityClass<Renderer>("Core");
	//50. ResourceManager
	RegisterUnityClass<ResourceManager>("Core");
	//51. RuntimeInitializeOnLoadManager
	RegisterUnityClass<RuntimeInitializeOnLoadManager>("Core");
	//52. Shader
	RegisterUnityClass<Shader>("Core");
	//53. ShaderNameRegistry
	RegisterUnityClass<ShaderNameRegistry>("Core");
	//54. Skybox
	RegisterUnityClass<Skybox>("Core");
	//55. SortingGroup
	RegisterUnityClass<SortingGroup>("Core");
	//56. Sprite
	RegisterUnityClass<Sprite>("Core");
	//57. SpriteAtlas
	RegisterUnityClass<SpriteAtlas>("Core");
	//58. SpriteRenderer
	RegisterUnityClass<SpriteRenderer>("Core");
	//59. TagManager
	RegisterUnityClass<TagManager>("Core");
	//60. TextAsset
	RegisterUnityClass<TextAsset>("Core");
	//61. Texture
	RegisterUnityClass<Texture>("Core");
	//62. Texture2D
	RegisterUnityClass<Texture2D>("Core");
	//63. Texture2DArray
	RegisterUnityClass<Texture2DArray>("Core");
	//64. Texture3D
	RegisterUnityClass<Texture3D>("Core");
	//65. TimeManager
	RegisterUnityClass<TimeManager>("Core");
	//66. Transform
	RegisterUnityClass<Transform>("Core");
	//67. Collider
	RegisterUnityClass<Collider>("Physics");
	//68. PhysicsManager
	RegisterUnityClass<PhysicsManager>("Physics");
	//69. Physics2DSettings
	RegisterUnityClass<Physics2DSettings>("Physics2D");
	//70. Terrain
	RegisterUnityClass<Terrain>("Terrain");
	//71. TerrainData
	RegisterUnityClass<TerrainData>("Terrain");
	//72. Font
	RegisterUnityClass<TextRendering::Font>("TextRendering");
	//73. Canvas
	RegisterUnityClass<UI::Canvas>("UI");
	//74. CanvasGroup
	RegisterUnityClass<UI::CanvasGroup>("UI");
	//75. CanvasRenderer
	RegisterUnityClass<UI::CanvasRenderer>("UI");
	//76. UIRenderer
	RegisterUnityClass<UIRenderer>("UIElements");
	//77. VFXManager
	RegisterUnityClass<VFXManager>("VFX");
	//78. VisualEffect
	RegisterUnityClass<VisualEffect>("VFX");
	//79. VisualEffectAsset
	RegisterUnityClass<VisualEffectAsset>("VFX");
	//80. VisualEffectObject
	RegisterUnityClass<VisualEffectObject>("VFX");

}
