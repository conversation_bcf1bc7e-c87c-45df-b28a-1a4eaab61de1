{ "pid": 30139, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "<PERSON><PERSON>ink<PERSON>" } },
{ "pid": 30139, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 30139, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen2" } },
{ "pid": 30139, "tid": 12884901888, "ts": 1751913057618970, "dur": 7666, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Blocking GC"} },
{ "pid": 30139, "tid": 12884901888, "ts": 1751913060907101, "dur": 357776, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Background GC"} },
{ "pid": 30139, "tid": 1, "ts": 1751913062169507, "dur": 2125, "ph": "X", "name": "GC - Gen2", "args": {} },
{ "pid": 30139, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen1" } },
{ "pid": 30139, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen0" } },
{ "pid": 30139, "tid": 4294967296, "ts": 1751913060908447, "dur": 156895, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 30139, "tid": 1, "ts": 1751913062171635, "dur": 18, "ph": "X", "name": "GC - Gen0", "args": {} },
{ "pid": 30139, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 30139, "tid": 1, "ts": 1751913057124312, "dur": 5014526, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 30139, "tid": 1, "ts": 1751913057125733, "dur": 45921, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057131106, "dur": 22750, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057171656, "dur": 9904, "ph": "X", "name": "RegisterRuntimeEventListeners", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057197211, "dur": 33476, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057232461, "dur": 139740, "ph": "X", "name": "SetupAndRegisterUnityEngineSteps", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057372235, "dur": 42337, "ph": "X", "name": "ResolveUnityEngine", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057414601, "dur": 39581, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057454201, "dur": 134722, "ph": "X", "name": "InitializeEngineStrippingStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057588941, "dur": 3911, "ph": "X", "name": "ResolveForEngineModuleStrippingEnabledStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057592858, "dur": 7984, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057600848, "dur": 1405, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057602255, "dur": 2399, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057604656, "dur": 264, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057604932, "dur": 22665, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057627614, "dur": 447, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057628062, "dur": 118, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057628181, "dur": 179, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057628361, "dur": 126, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057628487, "dur": 283, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057628771, "dur": 427, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057629206, "dur": 3663, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057632885, "dur": 10923, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057643815, "dur": 6464, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057650290, "dur": 8845, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057659143, "dur": 2176, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057661321, "dur": 1096, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057662419, "dur": 1032, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057663469, "dur": 1522, "ph": "X", "name": "UnityLoadReferencesStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057664999, "dur": 1032, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps2", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057666038, "dur": 696, "ph": "X", "name": "SetupI18N", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057666740, "dur": 4068, "ph": "X", "name": "ResolveFromDescriptorsStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057670822, "dur": 149, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057670972, "dur": 2116, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057673090, "dur": 90, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057673181, "dur": 1822, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057675006, "dur": 372, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057675388, "dur": 33718, "ph": "X", "name": "UnityBlacklistStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057709113, "dur": 13257, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057722388, "dur": 108186, "ph": "X", "name": "DynamicDependencyLookupStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057830598, "dur": 541, "ph": "X", "name": "EarlyReportGenerationStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057831145, "dur": 557, "ph": "X", "name": "ResolveTestsStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057831707, "dur": 2034, "ph": "X", "name": "LoadI18nAssemblies", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057833835, "dur": 2046, "ph": "X", "name": "ResolveMonoBehaviourItselfStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057835897, "dur": 1262, "ph": "X", "name": "ResolveFromAllUserMonoBehaviours", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057837178, "dur": 6739, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057843924, "dur": 18, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057843954, "dur": 91023, "ph": "X", "name": "ResolveFromPreserveAttribute", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057935014, "dur": 12006, "ph": "X", "name": "EngineStrippingAnnotationStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913057947042, "dur": 159473, "ph": "X", "name": "UnityTypeMapStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913058106539, "dur": 347, "ph": "X", "name": "BeforeMarkReportGenerationStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913058106890, "dur": 100945, "ph": "X", "name": "BeforeMarkAnalyticsStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913058207858, "dur": 18648, "ph": "X", "name": "RemoveSecurityStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913058226526, "dur": 5771, "ph": "X", "name": "RemoveSecurityFromCopyAssemblies", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913058232319, "dur": 14321, "ph": "X", "name": "RemoveFeaturesStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913058246661, "dur": 15624, "ph": "X", "name": "RemoveUnreachableBlocksStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913058262314, "dur": 3044080, "ph": "X", "name": "UnityMarkStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913061306432, "dur": 622, "ph": "X", "name": "ValidateVirtualMethodAnnotationsStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913061307061, "dur": 4026, "ph": "X", "name": "ProcessWarningsStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913061311102, "dur": 62213, "ph": "X", "name": "UnitySweepStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913061373347, "dur": 10105, "ph": "X", "name": "UnityCodeRewriterStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913061383478, "dur": 12564, "ph": "X", "name": "CleanStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913061396063, "dur": 6259, "ph": "X", "name": "StubifyStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913061402352, "dur": 3331, "ph": "X", "name": "AddUnresolvedStubsStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913061405699, "dur": 2625, "ph": "X", "name": "BeforeOutputAnalyticsStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913061408336, "dur": 121, "ph": "X", "name": "BeforeOutputReportGenerationStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913061408461, "dur": 337, "ph": "X", "name": "ProcessCopiedAssembliesRequiringSecurityAttributesRemoved", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913061408804, "dur": 356, "ph": "X", "name": "SealerStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913061409164, "dur": 689124, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913062098303, "dur": 23820, "ph": "X", "name": "LinkerToEditorDataGenerationStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913062122136, "dur": 174, "ph": "X", "name": "ReportGenerationStep", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913062124344, "dur": 14387, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913062138841, "dur": 20894, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913062171653, "dur": 102, "ph": "X", "name": "", "args": {} },
{ "pid": 30139, "tid": 1, "ts": 1751913062168937, "dur": 3213, "ph": "X", "name": "Write chrome-trace events", "args": {} },
