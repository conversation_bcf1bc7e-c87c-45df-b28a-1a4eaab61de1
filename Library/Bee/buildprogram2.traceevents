{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1751913082949890, "dur": 542928, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913082950635, "dur": 91390, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913083051966, "dur": 407752, "ph": "X", "name": "RunBuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913083052408, "dur": 353088, "ph": "X", "name": "PlayerBuildProgramBase.SetupPlayerBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913083052818, "dur": 30392, "ph": "X", "name": "SetupDataFiles", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913083083517, "dur": 971, "ph": "X", "name": "SetupCopyPlugins", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913083085992, "dur": 83573, "ph": "X", "name": "SetupUnityLinker", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913083170037, "dur": 227446, "ph": "X", "name": "SetupIl2CppBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913083170522, "dur": 30319, "ph": "X", "name": "SetupIl2Cpp", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913083244962, "dur": 98805, "ph": "X", "name": "SetupSpecificConfigImpl libil2cpp", "args": {"info": "master_Android_arm32"} },
{ "pid": 35942, "tid": 1, "ts": 1751913083250141, "dur": 64612, "ph": "X", "name": "SetupSpecificConfigImpl il2cpp", "args": {"info": "master_Android_arm32"} },
{ "pid": 35942, "tid": 1, "ts": 1751913083250978, "dur": 33826, "ph": "X", "name": "SetupSpecificConfigImpl bdwgc", "args": {"info": "master_Android_arm32"} },
{ "pid": 35942, "tid": 1, "ts": 1751913083284835, "dur": 1985, "ph": "X", "name": "SetupSpecificConfigImpl zlib", "args": {"info": "master_Android_arm32"} },
{ "pid": 35942, "tid": 1, "ts": 1751913083353877, "dur": 39651, "ph": "X", "name": "SetupSpecificConfigImpl libil2cpp", "args": {"info": "master_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1751913083353911, "dur": 16196, "ph": "X", "name": "SetupSpecificConfigImpl il2cpp", "args": {"info": "master_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1751913083354010, "dur": 305, "ph": "X", "name": "SetupSpecificConfigImpl bdwgc", "args": {"info": "master_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1751913083354331, "dur": 666, "ph": "X", "name": "SetupSpecificConfigImpl zlib", "args": {"info": "master_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1751913083398100, "dur": 7395, "ph": "X", "name": "SetupCopyDataIl2cpp", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913083465575, "dur": 1496, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913083467073, "dur": 25744, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913083467587, "dur": 21965, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913083497552, "dur": 1384, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913083497305, "dur": 1773, "ph": "X", "name": "Write chrome-trace events", "args": {} },
