{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1751913069140643, "dur": 418877, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913069141323, "dur": 77663, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913069227978, "dur": 309498, "ph": "X", "name": "RunBuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913069228422, "dur": 267939, "ph": "X", "name": "PlayerBuildProgramBase.SetupPlayerBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913069228839, "dur": 29310, "ph": "X", "name": "SetupDataFiles", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913069258436, "dur": 893, "ph": "X", "name": "SetupCopyPlugins", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913069260456, "dur": 84835, "ph": "X", "name": "SetupUnityLinker", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913069345745, "dur": 148284, "ph": "X", "name": "SetupIl2CppBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913069346165, "dur": 24129, "ph": "X", "name": "SetupIl2Cpp", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913069394077, "dur": 72435, "ph": "X", "name": "SetupSpecificConfigImpl libil2cpp", "args": {"info": "master_Android_arm32"} },
{ "pid": 35942, "tid": 1, "ts": 1751913069399286, "dur": 59094, "ph": "X", "name": "SetupSpecificConfigImpl il2cpp", "args": {"info": "master_Android_arm32"} },
{ "pid": 35942, "tid": 1, "ts": 1751913069400110, "dur": 31669, "ph": "X", "name": "SetupSpecificConfigImpl bdwgc", "args": {"info": "master_Android_arm32"} },
{ "pid": 35942, "tid": 1, "ts": 1751913069431799, "dur": 1673, "ph": "X", "name": "SetupSpecificConfigImpl zlib", "args": {"info": "master_Android_arm32"} },
{ "pid": 35942, "tid": 1, "ts": 1751913069475437, "dur": 14305, "ph": "X", "name": "SetupSpecificConfigImpl libil2cpp", "args": {"info": "master_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1751913069475456, "dur": 14031, "ph": "X", "name": "SetupSpecificConfigImpl il2cpp", "args": {"info": "master_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1751913069475527, "dur": 232, "ph": "X", "name": "SetupSpecificConfigImpl bdwgc", "args": {"info": "master_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1751913069475770, "dur": 563, "ph": "X", "name": "SetupSpecificConfigImpl zlib", "args": {"info": "master_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1751913069494420, "dur": 1941, "ph": "X", "name": "SetupCopyDataIl2cpp", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913069542904, "dur": 917, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913069543822, "dur": 15697, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913069544305, "dur": 13829, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913069564112, "dur": 1427, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751913069563867, "dur": 1812, "ph": "X", "name": "Write chrome-trace events", "args": {} },
