Current local time and date: Mon Jul  7 23:31:09 2025
Annotation Lib_Android_arm32 Library/Bee/artifacts/Android/06gmu/il2cpp.a


Cold hash ingredients (anything below this line is hashed and must not change to get a cache hit):
BEE_CACHE_SALT: none
requested node: Lib_Android_arm32 Library/Bee/artifacts/Android/06gmu/il2cpp.a

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/08c6fj025ppx.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/08c6fj025ppx.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/a5thui1leh8q5.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/08c6fj025ppx.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/0mp6h84o55fj.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/0mp6h84o55fj.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/bf1uvk9mea414.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/0mp6h84o55fj.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/0wz6hi1lxiev.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/0wz6hi1lxiev.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/a9ave6cxgdkj1.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/0wz6hi1lxiev.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/1yb76e0qqo08.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/1yb76e0qqo08.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/fast_log.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/1yb76e0qqo08.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/2i92xrt1ukle.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/2i92xrt1ukle.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/gzy0bw66bko01.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/2i92xrt1ukle.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/2lp0sebdkwvw.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/2lp0sebdkwvw.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/metablock.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/2lp0sebdkwvw.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/2nyv0cmcxte2.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/2nyv0cmcxte2.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/codegen/il2cpp-codegen.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/2nyv0cmcxte2.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/30935fnu9k9q.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/30935fnu9k9q.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Runtime/RuntimeImports.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/30935fnu9k9q.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/3fapjqzptyup.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/3fapjqzptyup.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/a9ave6cxgdkj2.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/3fapjqzptyup.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/3k2qhe0gms9w.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/3k2qhe0gms9w.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec/huffman.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/3k2qhe0gms9w.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/3lsxy4wn7ern.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/3lsxy4wn7ern.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Runtime.Versioning/VersioningHelper.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/3lsxy4wn7ern.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/3rnohry6155r.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/3rnohry6155r.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/a5thui1leh8q6.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/3rnohry6155r.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/3xvnqmcy6hni.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/3xvnqmcy6hni.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Runtime.Remoting/RemotingServices.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/3xvnqmcy6hni.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/4sv3sqyrcgsp.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/4sv3sqyrcgsp.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/8y0afnful2n10.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/4sv3sqyrcgsp.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/4y9zvn8b0ka8.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/4y9zvn8b0ka8.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/ufzqt6cf7puy0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/4y9zvn8b0ka8.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/5l5kq01q9xtk.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/5l5kq01q9xtk.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/fvrppzrz6kse0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/5l5kq01q9xtk.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/5ms26x7furuw.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/5ms26x7furuw.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/ta0nb6fs1d620.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/5ms26x7furuw.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/5v4sgtcgwlq7.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/5v4sgtcgwlq7.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/block_splitter.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/5v4sgtcgwlq7.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/6ppcwyg0b7xe.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/6ppcwyg0b7xe.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/gs207xplhi4t0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/6ppcwyg0b7xe.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/6qe3amy4auww.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/6qe3amy4auww.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common/constants.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/6qe3amy4auww.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/6zn32e2h3c5g.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/6zn32e2h3c5g.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/Mono.Unity/UnityTls.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/6zn32e2h3c5g.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/75huhiu7zq5m.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/75huhiu7zq5m.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/zd2o2rpalvza0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/75huhiu7zq5m.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/7eyugoa6mwdz.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/7eyugoa6mwdz.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/zw5n29wg3y0z0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/7eyugoa6mwdz.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/7oty4p1zgyrg.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/7oty4p1zgyrg.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec/state.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/7oty4p1zgyrg.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/7oxa7yjlh8b0.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/7oxa7yjlh8b0.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/6awuv9uxvtck0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/7oxa7yjlh8b0.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/7wjcogaen2ar.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/7wjcogaen2ar.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/kjbo9kny2fhr2.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/7wjcogaen2ar.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/8bbigdsh5gen.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/8bbigdsh5gen.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/jjen6oekxq8z1.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/8bbigdsh5gen.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/8du3d4ai6a9f.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/8du3d4ai6a9f.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/entropy_encode.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/8du3d4ai6a9f.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/8hk5ax3k0rcn.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/8hk5ax3k0rcn.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/backward_references_hq.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/8hk5ax3k0rcn.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/8juz3pizpsol.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/8juz3pizpsol.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/mbtg7kmmqugy0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/8juz3pizpsol.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/8p2c7597j0g9.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/8p2c7597j0g9.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/bf1uvk9mea410.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/8p2c7597j0g9.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/8p427xa062ch.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/8p427xa062ch.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/p2ck6wy99bek0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/8p427xa062ch.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/92hd5lnuhj59.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/92hd5lnuhj59.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/WindowsGames/Win32ApiWindowsGamesEmulation.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/92hd5lnuhj59.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/9de1g6xvwpzz.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/9de1g6xvwpzz.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/16h2jw5emcxq0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/9de1g6xvwpzz.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/9mn8j9d48kig.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/9mn8j9d48kig.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/encoder_dict.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/9mn8j9d48kig.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/9plzeitkuqq2.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/9plzeitkuqq2.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/Mono.Security.Cryptography/KeyPairPersistence.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/9plzeitkuqq2.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/9py90qfp6boy.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/9py90qfp6boy.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/bh389mx5j50d0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/9py90qfp6boy.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/a9rywwtpuivi.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/a9rywwtpuivi.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/mono/MonoPosixHelper.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/a9rywwtpuivi.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/aexf4w2yvem7.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/aexf4w2yvem7.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Runtime.Remoting.Activation/ActivationServices.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/aexf4w2yvem7.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/afe7e16fsnhw.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/afe7e16fsnhw.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/ivovnzs4ui830.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/afe7e16fsnhw.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/ajaqukd76a0n.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/ajaqukd76a0n.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/bilc0w3x5djq0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/ajaqukd76a0n.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/b0g5th6o5mkc.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/b0g5th6o5mkc.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/debugger/il2cpp-api-debugger.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/b0g5th6o5mkc.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/b1tjmdl02sv7.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/b1tjmdl02sv7.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/compress_fragment.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/b1tjmdl02sv7.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/bv0u6o3pg06h.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/bv0u6o3pg06h.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/a5thui1leh8q2.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/bv0u6o3pg06h.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/bvl0hlql1wuh.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/bvl0hlql1wuh.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/jjen6oekxq8z2.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/bvl0hlql1wuh.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/c6zou0iaitrs.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/c6zou0iaitrs.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/ffr6im7eog9y0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/c6zou0iaitrs.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/cg0r5rh2ip0f.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/cg0r5rh2ip0f.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/wtxphr9u99lw0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/cg0r5rh2ip0f.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/cxojiewzs5fu.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/cxojiewzs5fu.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/16h2jw5emcxq1.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/cxojiewzs5fu.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/d8expksyfjnm.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/d8expksyfjnm.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Runtime.Remoting.Contexts/Context.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/d8expksyfjnm.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/d8mlpunh663l.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/d8mlpunh663l.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/vm-utils/icalls/mscorlib/System.Threading/Interlocked.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/d8mlpunh663l.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/dfn7o7snavbi.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/dfn7o7snavbi.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/dictionary_hash.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/dfn7o7snavbi.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/dzht24zk4rnt.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/dzht24zk4rnt.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec/bit_reader.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/dzht24zk4rnt.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/e0d188aojavj.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/e0d188aojavj.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/jlnmxh699yae0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/e0d188aojavj.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/e9qsibg5dqu9.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/e9qsibg5dqu9.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/encode.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/e9qsibg5dqu9.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/eebd8w1tcgnc.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/eebd8w1tcgnc.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/a5thui1leh8q3.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/eebd8w1tcgnc.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/eewijtopxcbc.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/eewijtopxcbc.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/jjen6oekxq8z3.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/eewijtopxcbc.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/eh6c0p6adovr.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/eh6c0p6adovr.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec/decode.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/eh6c0p6adovr.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/f6r4794pyd3i.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/f6r4794pyd3i.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/fbynxzxtl1hu0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/f6r4794pyd3i.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/fgz1jmu3olwo.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/fgz1jmu3olwo.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/16h2jw5emcxq2.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/fgz1jmu3olwo.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/gm53hvqikf6b.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/gm53hvqikf6b.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/Interop.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/gm53hvqikf6b.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/gmpkytm2vgmd.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/gmpkytm2vgmd.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/ohdyi08m6bmi0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/gmpkytm2vgmd.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/gvu3k8cbx0li.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/gvu3k8cbx0li.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/cluster.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/gvu3k8cbx0li.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/hrspz8carqbc.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/hrspz8carqbc.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Runtime.CompilerServices/RuntimeHelpers.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/hrspz8carqbc.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/hxlv94zx8v36.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/hxlv94zx8v36.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/a5thui1leh8q4.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/hxlv94zx8v36.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/ik3fw8elfta5.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/ik3fw8elfta5.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/d9vy36lxkuid0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/ik3fw8elfta5.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/ip2m9h3tutjd.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/ip2m9h3tutjd.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/fbynxzxtl1hu1.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/ip2m9h3tutjd.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/ipa58vk5gcgn.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/ipa58vk5gcgn.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/backward_references.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/ipa58vk5gcgn.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/iz9jlus7k1cj.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/iz9jlus7k1cj.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/16h2jw5emcxq3.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/iz9jlus7k1cj.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/j3yl8326h42s.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/j3yl8326h42s.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/wh4v266qj8nw0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/j3yl8326h42s.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/j7hsrdtwxqzn.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/j7hsrdtwxqzn.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/gzy0bw66bko00.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/j7hsrdtwxqzn.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/jdygbc34jz3b.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/jdygbc34jz3b.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/System/System.Threading/Semaphore.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/jdygbc34jz3b.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/ksxpra9pcvad.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/ksxpra9pcvad.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/fnbm05jnwzon0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/ksxpra9pcvad.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/l2hjgub5ts85.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/l2hjgub5ts85.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/kjbo9kny2fhr0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/l2hjgub5ts85.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/l63nb7prz46k.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/l63nb7prz46k.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/memory.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/l63nb7prz46k.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/layjup714n2t.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/layjup714n2t.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/command.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/layjup714n2t.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/lpmeuvjp48ki.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/lpmeuvjp48ki.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/static_dict.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/lpmeuvjp48ki.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/myz8lix2e4hv.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/myz8lix2e4hv.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/System.Core/System.IO.MemoryMappedFiles/MemoryMapImpl.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/myz8lix2e4hv.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/n8mvqmtcnili.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/n8mvqmtcnili.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/mc4i9rq99g8o0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/n8mvqmtcnili.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/ols1h298p8o0.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/ols1h298p8o0.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/kjbo9kny2fhr1.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/ols1h298p8o0.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/p0j79yrc8msw.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/p0j79yrc8msw.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/jjen6oekxq8z0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/p0j79yrc8msw.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/p0y1y24gnq4w.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/p0y1y24gnq4w.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/a5thui1leh8q0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/p0y1y24gnq4w.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/p2em7eo4wx4a.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/p2em7eo4wx4a.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/System/System.Net/Dns.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/p2em7eo4wx4a.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/pgpfulj57fx7.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/pgpfulj57fx7.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/literal_cost.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/pgpfulj57fx7.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/qmmstleww4b2.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/qmmstleww4b2.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common/context.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/qmmstleww4b2.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/qqrhwann3b8t.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/qqrhwann3b8t.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Runtime.Remoting.Proxies/RealProxy.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/qqrhwann3b8t.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/qrxdsurgjy1c.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/qrxdsurgjy1c.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/mc4i9rq99g8o1.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/qrxdsurgjy1c.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/qz2qhkx0muy2.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/qz2qhkx0muy2.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/kzae63toetmc0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/qz2qhkx0muy2.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/r1tndkadhu2z.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/r1tndkadhu2z.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/bf1uvk9mea411.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/r1tndkadhu2z.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/ravji19u3gkr.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/ravji19u3gkr.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/xamarin-android/xamarin_getifaddrs.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/ravji19u3gkr.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/sc1jxywr9f88.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/sc1jxywr9f88.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/System/System.IO/FileSystemWatcher.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/sc1jxywr9f88.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/sj9j0a3kj6kr.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/sj9j0a3kj6kr.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/a5thui1leh8q1.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/sj9j0a3kj6kr.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/t277gbz0w6hy.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/t277gbz0w6hy.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/c25k2q0dbzv50.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/t277gbz0w6hy.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/ta8vt2pkfei7.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/ta8vt2pkfei7.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/mc4i9rq99g8o2.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/ta8vt2pkfei7.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/tllqg5o3eflk.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/tllqg5o3eflk.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/brotli_bit_stream.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/tllqg5o3eflk.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/tsm130q0c8ac.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/tsm130q0c8ac.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/System/Mono.Net.Security/MonoTlsProviderFactory.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/tsm130q0c8ac.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/tu100ydc44le.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/tu100ydc44le.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/bilc0w3x5djq1.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/tu100ydc44le.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/u40zh2wsj17x.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/u40zh2wsj17x.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common/platform.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/u40zh2wsj17x.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/uii4cote56vs.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/uii4cote56vs.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/histogram.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/uii4cote56vs.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/uk45es8gdaju.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/uk45es8gdaju.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/bf1uvk9mea412.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/uk45es8gdaju.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/ulni3cbiybdh.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/ulni3cbiybdh.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/utf8_util.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/ulni3cbiybdh.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/ux6v7bok24tu.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/ux6v7bok24tu.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/compress_fragment_two_pass.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/ux6v7bok24tu.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/vll8qfva2bxm.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/vll8qfva2bxm.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/Std/ThreadImpl.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/vll8qfva2bxm.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/w71kwjakovl9.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/w71kwjakovl9.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/System/Microsoft.Win32/NativeMethods.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/w71kwjakovl9.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/wcwthi6ly8sa.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/wcwthi6ly8sa.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Security.Policy/Evidence.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/wcwthi6ly8sa.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/wdcj26bg0k28.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/wdcj26bg0k28.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/bilc0w3x5djq2.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/wdcj26bg0k28.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/wwwtgmct6kpr.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/wwwtgmct6kpr.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/Emscripten/SocketBridge.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/wwwtgmct6kpr.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/x0001uareexm.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/x0001uareexm.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common/dictionary.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/x0001uareexm.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/x3eog06k9pzo.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/x3eog06k9pzo.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/bf1uvk9mea413.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/x3eog06k9pzo.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/xdpoga3h12x0.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/xdpoga3h12x0.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "Library/Bee/artifacts/Android/06gmu/a9ave6cxgdkj0.lump.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/xdpoga3h12x0.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/xeeyh0dwa99q.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/xeeyh0dwa99q.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Security.Cryptography/RNGCryptoServiceProvider.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/xeeyh0dwa99q.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/yca4imf5spt7.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -std=c++11 -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -fno-rtti -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/yca4imf5spt7.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c++ "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/Microsoft.Win32/NativeMethods.cpp"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/yca4imf5spt7.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/z2vyyaer7m15.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/z2vyyaer7m15.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc/bit_cost.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/z2vyyaer7m15.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/06gmu/zrbu7kh09rxg.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DBASELIB_INLINE_NAMESPACE=il2cpp_baselib -DIL2CPP_MONO_DEBUGGER_DISABLED -DRUNTIME_IL2CPP -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DGC_NOT_DLL -DIL2CPP_DEFAULT_DATA_DIR_PATH=Data -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/pch" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/baselib/Platforms/Android/Include" -o "Library/Bee/artifacts/Android/06gmu/zrbu7kh09rxg.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common/transform.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/zrbu7kh09rxg.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/b9kys/tuz190w0bejz.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DGC_NOT_DLL -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DHAVE_BOEHM_GC -DDEFAULT_GC_NAME="BDWGC" -DALL_INTERIOR_POINTERS=1 -DGC_GCJ_SUPPORT=1 -DJAVA_FINALIZATION=1 -DNO_EXECUTE_PERMISSION=1 -DGC_NO_THREADS_DISCOVERY=1 -DIGNORE_DYNAMIC_LOADING=1 -DGC_DONT_REGISTER_MAIN_STATIC_DATA=1 -DGC_VERSION_MAJOR=7 -DGC_VERSION_MINOR=7 -DGC_VERSION_MICRO=0 -DGC_THREADS=1 -DUSE_MMAP=1 -DUSE_MUNMAP=1 -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/libatomic_ops/src" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -o "Library/Bee/artifacts/Android/b9kys/tuz190w0bejz.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/extra/gc.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/b9kys/tuz190w0bejz.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/b9kys/ypwk9iw1evh9.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -mthumb -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fexceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DGC_NOT_DLL -DIL2CPP_ENABLE_WRITE_BARRIERS=1 -DIL2CPP_INCREMENTAL_TIME_SLICE=3 -DHAVE_BOEHM_GC -DDEFAULT_GC_NAME="BDWGC" -DALL_INTERIOR_POINTERS=1 -DGC_GCJ_SUPPORT=1 -DJAVA_FINALIZATION=1 -DNO_EXECUTE_PERMISSION=1 -DGC_NO_THREADS_DISCOVERY=1 -DIGNORE_DYNAMIC_LOADING=1 -DGC_DONT_REGISTER_MAIN_STATIC_DATA=1 -DGC_VERSION_MAJOR=7 -DGC_VERSION_MINOR=7 -DGC_VERSION_MICRO=0 -DGC_THREADS=1 -DUSE_MMAP=1 -DUSE_MUNMAP=1 -DNDEBUG -I"." -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/libatomic_ops/src" -I"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/include" -o "Library/Bee/artifacts/Android/b9kys/ypwk9iw1evh9.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/bdwgc/extra/krait_signal_handler.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/b9kys/ypwk9iw1evh9.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/lwpt2/1ut5jbjfc98j.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fno-exceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DNDEBUG -I"." -o "Library/Bee/artifacts/Android/lwpt2/1ut5jbjfc98j.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/zlib/adler32.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/lwpt2/1ut5jbjfc98j.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/lwpt2/36pkhwykefkl.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fno-exceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DNDEBUG -I"." -o "Library/Bee/artifacts/Android/lwpt2/36pkhwykefkl.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/zlib/inflate.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/lwpt2/36pkhwykefkl.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/lwpt2/60suyy6i7hvz.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fno-exceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DNDEBUG -I"." -o "Library/Bee/artifacts/Android/lwpt2/60suyy6i7hvz.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/zlib/inftrees.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/lwpt2/60suyy6i7hvz.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/lwpt2/7ml20drftbri.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fno-exceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DNDEBUG -I"." -o "Library/Bee/artifacts/Android/lwpt2/7ml20drftbri.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/zlib/deflate.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/lwpt2/7ml20drftbri.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/lwpt2/8q4wv01mzyx9.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fno-exceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DNDEBUG -I"." -o "Library/Bee/artifacts/Android/lwpt2/8q4wv01mzyx9.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/zlib/zutil.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/lwpt2/8q4wv01mzyx9.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/lwpt2/dcgvmykcs1p5.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fno-exceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DNDEBUG -I"." -o "Library/Bee/artifacts/Android/lwpt2/dcgvmykcs1p5.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/zlib/trees.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/lwpt2/dcgvmykcs1p5.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/lwpt2/dkpfqqx2wqgc.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fno-exceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DNDEBUG -I"." -o "Library/Bee/artifacts/Android/lwpt2/dkpfqqx2wqgc.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/zlib/gzclose.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/lwpt2/dkpfqqx2wqgc.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/lwpt2/hdggmcrujl55.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fno-exceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DNDEBUG -I"." -o "Library/Bee/artifacts/Android/lwpt2/hdggmcrujl55.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/zlib/inffast.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/lwpt2/hdggmcrujl55.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/lwpt2/mx06swneun1r.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fno-exceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DNDEBUG -I"." -o "Library/Bee/artifacts/Android/lwpt2/mx06swneun1r.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/zlib/gzlib.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/lwpt2/mx06swneun1r.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/lwpt2/nida94x6rr10.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fno-exceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DNDEBUG -I"." -o "Library/Bee/artifacts/Android/lwpt2/nida94x6rr10.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/zlib/gzread.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/lwpt2/nida94x6rr10.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/lwpt2/oeu637gt93xs.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fno-exceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DNDEBUG -I"." -o "Library/Bee/artifacts/Android/lwpt2/oeu637gt93xs.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/zlib/infback.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/lwpt2/oeu637gt93xs.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/lwpt2/q2c3j9foaihh.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fno-exceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DNDEBUG -I"." -o "Library/Bee/artifacts/Android/lwpt2/q2c3j9foaihh.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/zlib/uncompr.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/lwpt2/q2c3j9foaihh.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/lwpt2/wp6dpf7mq57y.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fno-exceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DNDEBUG -I"." -o "Library/Bee/artifacts/Android/lwpt2/wp6dpf7mq57y.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/zlib/gzwrite.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/lwpt2/wp6dpf7mq57y.o

annotation: C_Android_arm32 Library/Bee/artifacts/Android/lwpt2/zipx2g6k3yao.o
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" -march=armv7-a -mfloat-abi=softfp -mfpu=neon-fp16 -marm -D__ARM_ARCH_7__ -D__ARM_ARCH_7A__ -D__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__ -faddrsig -DANDROID -DHAVE_INTTYPES_H -no-canonical-prefixes -fomit-frame-pointer -Wswitch -Wno-trigraphs -Wno-tautological-compare -Wno-invalid-offsetof -Wno-implicitly-unsigned-literal -Wno-integer-overflow -Wno-shift-negative-value -Wno-unknown-attributes -Wno-implicit-function-declaration -Wno-null-conversion -Wno-missing-declarations -Wno-unused-value -Wno-pragma-once-outside-header -Wno-unknown-warning-option -Wno-undef-prefix -fvisibility=hidden -fno-exceptions -funwind-tables -g -Os -fPIC -fno-strict-overflow -ffunction-sections -fdata-sections -fstack-protector -fmessage-length=0 -pipe -DNDEBUG -I"." -o "Library/Bee/artifacts/Android/lwpt2/zipx2g6k3yao.o" -fcolor-diagnostics -target armv7a-linux-androideabi23 -mllvm --dse-memoryssa-defs-per-block-limit=0 -fstrict-aliasing -fdiagnostics-format=msvc -c -x c "/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/zlib/crc32.c"
write file payload, length: 0
output: Library/Bee/artifacts/Android/lwpt2/zipx2g6k3yao.o

annotation: Lib_Android_arm32 Library/Bee/artifacts/Android/06gmu/il2cpp.a
action: "/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" rcsu "Library/Bee/artifacts/Android/06gmu/il2cpp.a" "Library/Bee/artifacts/Android/06gmu/gmpkytm2vgmd.o" "Library/Bee/artifacts/Android/06gmu/2nyv0cmcxte2.o" "Library/Bee/artifacts/Android/06gmu/b0g5th6o5mkc.o" "Library/Bee/artifacts/Android/06gmu/7eyugoa6mwdz.o" "Library/Bee/artifacts/Android/06gmu/gm53hvqikf6b.o" "Library/Bee/artifacts/Android/06gmu/yca4imf5spt7.o" "Library/Bee/artifacts/Android/06gmu/9plzeitkuqq2.o" "Library/Bee/artifacts/Android/06gmu/6zn32e2h3c5g.o" "Library/Bee/artifacts/Android/06gmu/8p427xa062ch.o" "Library/Bee/artifacts/Android/06gmu/ksxpra9pcvad.o" "Library/Bee/artifacts/Android/06gmu/qz2qhkx0muy2.o" "Library/Bee/artifacts/Android/06gmu/5l5kq01q9xtk.o" "Library/Bee/artifacts/Android/06gmu/l2hjgub5ts85.o" "Library/Bee/artifacts/Android/06gmu/ols1h298p8o0.o" "Library/Bee/artifacts/Android/06gmu/7wjcogaen2ar.o" "Library/Bee/artifacts/Android/06gmu/hrspz8carqbc.o" "Library/Bee/artifacts/Android/06gmu/cg0r5rh2ip0f.o" "Library/Bee/artifacts/Android/06gmu/aexf4w2yvem7.o" "Library/Bee/artifacts/Android/06gmu/d8expksyfjnm.o" "Library/Bee/artifacts/Android/06gmu/t277gbz0w6hy.o" "Library/Bee/artifacts/Android/06gmu/qqrhwann3b8t.o" "Library/Bee/artifacts/Android/06gmu/3xvnqmcy6hni.o" "Library/Bee/artifacts/Android/06gmu/3lsxy4wn7ern.o" "Library/Bee/artifacts/Android/06gmu/30935fnu9k9q.o" "Library/Bee/artifacts/Android/06gmu/xeeyh0dwa99q.o" "Library/Bee/artifacts/Android/06gmu/wcwthi6ly8sa.o" "Library/Bee/artifacts/Android/06gmu/5ms26x7furuw.o" "Library/Bee/artifacts/Android/06gmu/7oxa7yjlh8b0.o" "Library/Bee/artifacts/Android/06gmu/e0d188aojavj.o" "Library/Bee/artifacts/Android/06gmu/9de1g6xvwpzz.o" "Library/Bee/artifacts/Android/06gmu/cxojiewzs5fu.o" "Library/Bee/artifacts/Android/06gmu/fgz1jmu3olwo.o" "Library/Bee/artifacts/Android/06gmu/iz9jlus7k1cj.o" "Library/Bee/artifacts/Android/06gmu/myz8lix2e4hv.o" "Library/Bee/artifacts/Android/06gmu/w71kwjakovl9.o" "Library/Bee/artifacts/Android/06gmu/tsm130q0c8ac.o" "Library/Bee/artifacts/Android/06gmu/ik3fw8elfta5.o" "Library/Bee/artifacts/Android/06gmu/sc1jxywr9f88.o" "Library/Bee/artifacts/Android/06gmu/afe7e16fsnhw.o" "Library/Bee/artifacts/Android/06gmu/j3yl8326h42s.o" "Library/Bee/artifacts/Android/06gmu/p2em7eo4wx4a.o" "Library/Bee/artifacts/Android/06gmu/jdygbc34jz3b.o" "Library/Bee/artifacts/Android/06gmu/n8mvqmtcnili.o" "Library/Bee/artifacts/Android/06gmu/qrxdsurgjy1c.o" "Library/Bee/artifacts/Android/06gmu/ta8vt2pkfei7.o" "Library/Bee/artifacts/Android/06gmu/a9rywwtpuivi.o" "Library/Bee/artifacts/Android/06gmu/9py90qfp6boy.o" "Library/Bee/artifacts/Android/06gmu/c6zou0iaitrs.o" "Library/Bee/artifacts/Android/06gmu/j7hsrdtwxqzn.o" "Library/Bee/artifacts/Android/06gmu/2i92xrt1ukle.o" "Library/Bee/artifacts/Android/06gmu/4sv3sqyrcgsp.o" "Library/Bee/artifacts/Android/06gmu/f6r4794pyd3i.o" "Library/Bee/artifacts/Android/06gmu/ip2m9h3tutjd.o" "Library/Bee/artifacts/Android/06gmu/wwwtgmct6kpr.o" "Library/Bee/artifacts/Android/06gmu/ajaqukd76a0n.o" "Library/Bee/artifacts/Android/06gmu/tu100ydc44le.o" "Library/Bee/artifacts/Android/06gmu/wdcj26bg0k28.o" "Library/Bee/artifacts/Android/06gmu/6ppcwyg0b7xe.o" "Library/Bee/artifacts/Android/06gmu/p0j79yrc8msw.o" "Library/Bee/artifacts/Android/06gmu/8bbigdsh5gen.o" "Library/Bee/artifacts/Android/06gmu/bvl0hlql1wuh.o" "Library/Bee/artifacts/Android/06gmu/eewijtopxcbc.o" "Library/Bee/artifacts/Android/06gmu/vll8qfva2bxm.o" "Library/Bee/artifacts/Android/06gmu/8p2c7597j0g9.o" "Library/Bee/artifacts/Android/06gmu/r1tndkadhu2z.o" "Library/Bee/artifacts/Android/06gmu/uk45es8gdaju.o" "Library/Bee/artifacts/Android/06gmu/x3eog06k9pzo.o" "Library/Bee/artifacts/Android/06gmu/0mp6h84o55fj.o" "Library/Bee/artifacts/Android/06gmu/92hd5lnuhj59.o" "Library/Bee/artifacts/Android/06gmu/4y9zvn8b0ka8.o" "Library/Bee/artifacts/Android/06gmu/xdpoga3h12x0.o" "Library/Bee/artifacts/Android/06gmu/0wz6hi1lxiev.o" "Library/Bee/artifacts/Android/06gmu/3fapjqzptyup.o" "Library/Bee/artifacts/Android/06gmu/8juz3pizpsol.o" "Library/Bee/artifacts/Android/06gmu/d8mlpunh663l.o" "Library/Bee/artifacts/Android/06gmu/75huhiu7zq5m.o" "Library/Bee/artifacts/Android/06gmu/p0y1y24gnq4w.o" "Library/Bee/artifacts/Android/06gmu/sj9j0a3kj6kr.o" "Library/Bee/artifacts/Android/06gmu/bv0u6o3pg06h.o" "Library/Bee/artifacts/Android/06gmu/eebd8w1tcgnc.o" "Library/Bee/artifacts/Android/06gmu/hxlv94zx8v36.o" "Library/Bee/artifacts/Android/06gmu/08c6fj025ppx.o" "Library/Bee/artifacts/Android/06gmu/3rnohry6155r.o" "Library/Bee/artifacts/Android/06gmu/ravji19u3gkr.o" "Library/Bee/artifacts/Android/06gmu/6qe3amy4auww.o" "Library/Bee/artifacts/Android/06gmu/qmmstleww4b2.o" "Library/Bee/artifacts/Android/06gmu/x0001uareexm.o" "Library/Bee/artifacts/Android/06gmu/u40zh2wsj17x.o" "Library/Bee/artifacts/Android/06gmu/zrbu7kh09rxg.o" "Library/Bee/artifacts/Android/06gmu/dzht24zk4rnt.o" "Library/Bee/artifacts/Android/06gmu/eh6c0p6adovr.o" "Library/Bee/artifacts/Android/06gmu/3k2qhe0gms9w.o" "Library/Bee/artifacts/Android/06gmu/7oty4p1zgyrg.o" "Library/Bee/artifacts/Android/06gmu/ipa58vk5gcgn.o" "Library/Bee/artifacts/Android/06gmu/8hk5ax3k0rcn.o" "Library/Bee/artifacts/Android/06gmu/z2vyyaer7m15.o" "Library/Bee/artifacts/Android/06gmu/5v4sgtcgwlq7.o" "Library/Bee/artifacts/Android/06gmu/tllqg5o3eflk.o" "Library/Bee/artifacts/Android/06gmu/gvu3k8cbx0li.o" "Library/Bee/artifacts/Android/06gmu/layjup714n2t.o" "Library/Bee/artifacts/Android/06gmu/b1tjmdl02sv7.o" "Library/Bee/artifacts/Android/06gmu/ux6v7bok24tu.o" "Library/Bee/artifacts/Android/06gmu/dfn7o7snavbi.o" "Library/Bee/artifacts/Android/06gmu/e9qsibg5dqu9.o" "Library/Bee/artifacts/Android/06gmu/9mn8j9d48kig.o" "Library/Bee/artifacts/Android/06gmu/8du3d4ai6a9f.o" "Library/Bee/artifacts/Android/06gmu/1yb76e0qqo08.o" "Library/Bee/artifacts/Android/06gmu/uii4cote56vs.o" "Library/Bee/artifacts/Android/06gmu/pgpfulj57fx7.o" "Library/Bee/artifacts/Android/06gmu/l63nb7prz46k.o" "Library/Bee/artifacts/Android/06gmu/2lp0sebdkwvw.o" "Library/Bee/artifacts/Android/06gmu/lpmeuvjp48ki.o" "Library/Bee/artifacts/Android/06gmu/ulni3cbiybdh.o" "Library/Bee/artifacts/Android/b9kys/tuz190w0bejz.o" "Library/Bee/artifacts/Android/b9kys/ypwk9iw1evh9.o" "Library/Bee/artifacts/Android/lwpt2/1ut5jbjfc98j.o" "Library/Bee/artifacts/Android/lwpt2/36pkhwykefkl.o" "Library/Bee/artifacts/Android/lwpt2/60suyy6i7hvz.o" "Library/Bee/artifacts/Android/lwpt2/7ml20drftbri.o" "Library/Bee/artifacts/Android/lwpt2/8q4wv01mzyx9.o" "Library/Bee/artifacts/Android/lwpt2/dcgvmykcs1p5.o" "Library/Bee/artifacts/Android/lwpt2/dkpfqqx2wqgc.o" "Library/Bee/artifacts/Android/lwpt2/hdggmcrujl55.o" "Library/Bee/artifacts/Android/lwpt2/mx06swneun1r.o" "Library/Bee/artifacts/Android/lwpt2/nida94x6rr10.o" "Library/Bee/artifacts/Android/lwpt2/oeu637gt93xs.o" "Library/Bee/artifacts/Android/lwpt2/q2c3j9foaihh.o" "Library/Bee/artifacts/Android/lwpt2/wp6dpf7mq57y.o" "Library/Bee/artifacts/Android/lwpt2/zipx2g6k3yao.o" --format=gnu
write file payload, length: 0
output: Library/Bee/artifacts/Android/06gmu/il2cpp.a

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/16h2jw5emcxq0.lump.cpp
action: (null)
write file payload, length: 456
output: Library/Bee/artifacts/Android/06gmu/16h2jw5emcxq0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/16h2jw5emcxq1.lump.cpp
action: (null)
write file payload, length: 978
output: Library/Bee/artifacts/Android/06gmu/16h2jw5emcxq1.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/16h2jw5emcxq2.lump.cpp
action: (null)
write file payload, length: 710
output: Library/Bee/artifacts/Android/06gmu/16h2jw5emcxq2.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/16h2jw5emcxq3.lump.cpp
action: (null)
write file payload, length: 1338
output: Library/Bee/artifacts/Android/06gmu/16h2jw5emcxq3.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/6awuv9uxvtck0.lump.cpp
action: (null)
write file payload, length: 342
output: Library/Bee/artifacts/Android/06gmu/6awuv9uxvtck0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/8y0afnful2n10.lump.cpp
action: (null)
write file payload, length: 1058
output: Library/Bee/artifacts/Android/06gmu/8y0afnful2n10.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/a5thui1leh8q0.lump.cpp
action: (null)
write file payload, length: 827
output: Library/Bee/artifacts/Android/06gmu/a5thui1leh8q0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/a5thui1leh8q1.lump.cpp
action: (null)
write file payload, length: 931
output: Library/Bee/artifacts/Android/06gmu/a5thui1leh8q1.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/a5thui1leh8q2.lump.cpp
action: (null)
write file payload, length: 1026
output: Library/Bee/artifacts/Android/06gmu/a5thui1leh8q2.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/a5thui1leh8q3.lump.cpp
action: (null)
write file payload, length: 1368
output: Library/Bee/artifacts/Android/06gmu/a5thui1leh8q3.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/a5thui1leh8q4.lump.cpp
action: (null)
write file payload, length: 926
output: Library/Bee/artifacts/Android/06gmu/a5thui1leh8q4.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/a5thui1leh8q5.lump.cpp
action: (null)
write file payload, length: 727
output: Library/Bee/artifacts/Android/06gmu/a5thui1leh8q5.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/a5thui1leh8q6.lump.cpp
action: (null)
write file payload, length: 386
output: Library/Bee/artifacts/Android/06gmu/a5thui1leh8q6.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/a9ave6cxgdkj0.lump.cpp
action: (null)
write file payload, length: 502
output: Library/Bee/artifacts/Android/06gmu/a9ave6cxgdkj0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/a9ave6cxgdkj1.lump.cpp
action: (null)
write file payload, length: 845
output: Library/Bee/artifacts/Android/06gmu/a9ave6cxgdkj1.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/a9ave6cxgdkj2.lump.cpp
action: (null)
write file payload, length: 761
output: Library/Bee/artifacts/Android/06gmu/a9ave6cxgdkj2.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/bf1uvk9mea410.lump.cpp
action: (null)
write file payload, length: 642
output: Library/Bee/artifacts/Android/06gmu/bf1uvk9mea410.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/bf1uvk9mea411.lump.cpp
action: (null)
write file payload, length: 619
output: Library/Bee/artifacts/Android/06gmu/bf1uvk9mea411.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/bf1uvk9mea412.lump.cpp
action: (null)
write file payload, length: 1558
output: Library/Bee/artifacts/Android/06gmu/bf1uvk9mea412.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/bf1uvk9mea413.lump.cpp
action: (null)
write file payload, length: 755
output: Library/Bee/artifacts/Android/06gmu/bf1uvk9mea413.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/bf1uvk9mea414.lump.cpp
action: (null)
write file payload, length: 879
output: Library/Bee/artifacts/Android/06gmu/bf1uvk9mea414.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/bh389mx5j50d0.lump.cpp
action: (null)
write file payload, length: 719
output: Library/Bee/artifacts/Android/06gmu/bh389mx5j50d0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/bilc0w3x5djq0.lump.cpp
action: (null)
write file payload, length: 425
output: Library/Bee/artifacts/Android/06gmu/bilc0w3x5djq0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/bilc0w3x5djq1.lump.cpp
action: (null)
write file payload, length: 1114
output: Library/Bee/artifacts/Android/06gmu/bilc0w3x5djq1.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/bilc0w3x5djq2.lump.cpp
action: (null)
write file payload, length: 881
output: Library/Bee/artifacts/Android/06gmu/bilc0w3x5djq2.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/c25k2q0dbzv50.lump.cpp
action: (null)
write file payload, length: 387
output: Library/Bee/artifacts/Android/06gmu/c25k2q0dbzv50.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/d9vy36lxkuid0.lump.cpp
action: (null)
write file payload, length: 632
output: Library/Bee/artifacts/Android/06gmu/d9vy36lxkuid0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/fbynxzxtl1hu0.lump.cpp
action: (null)
write file payload, length: 937
output: Library/Bee/artifacts/Android/06gmu/fbynxzxtl1hu0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/fbynxzxtl1hu1.lump.cpp
action: (null)
write file payload, length: 818
output: Library/Bee/artifacts/Android/06gmu/fbynxzxtl1hu1.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/ffr6im7eog9y0.lump.cpp
action: (null)
write file payload, length: 535
output: Library/Bee/artifacts/Android/06gmu/ffr6im7eog9y0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/fnbm05jnwzon0.lump.cpp
action: (null)
write file payload, length: 487
output: Library/Bee/artifacts/Android/06gmu/fnbm05jnwzon0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/fvrppzrz6kse0.lump.cpp
action: (null)
write file payload, length: 590
output: Library/Bee/artifacts/Android/06gmu/fvrppzrz6kse0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/gs207xplhi4t0.lump.cpp
action: (null)
write file payload, length: 513
output: Library/Bee/artifacts/Android/06gmu/gs207xplhi4t0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/gzy0bw66bko00.lump.cpp
action: (null)
write file payload, length: 506
output: Library/Bee/artifacts/Android/06gmu/gzy0bw66bko00.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/gzy0bw66bko01.lump.cpp
action: (null)
write file payload, length: 736
output: Library/Bee/artifacts/Android/06gmu/gzy0bw66bko01.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/ivovnzs4ui830.lump.cpp
action: (null)
write file payload, length: 394
output: Library/Bee/artifacts/Android/06gmu/ivovnzs4ui830.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/jjen6oekxq8z0.lump.cpp
action: (null)
write file payload, length: 744
output: Library/Bee/artifacts/Android/06gmu/jjen6oekxq8z0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/jjen6oekxq8z1.lump.cpp
action: (null)
write file payload, length: 767
output: Library/Bee/artifacts/Android/06gmu/jjen6oekxq8z1.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/jjen6oekxq8z2.lump.cpp
action: (null)
write file payload, length: 754
output: Library/Bee/artifacts/Android/06gmu/jjen6oekxq8z2.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/jjen6oekxq8z3.lump.cpp
action: (null)
write file payload, length: 1309
output: Library/Bee/artifacts/Android/06gmu/jjen6oekxq8z3.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/jlnmxh699yae0.lump.cpp
action: (null)
write file payload, length: 1188
output: Library/Bee/artifacts/Android/06gmu/jlnmxh699yae0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/kjbo9kny2fhr0.lump.cpp
action: (null)
write file payload, length: 780
output: Library/Bee/artifacts/Android/06gmu/kjbo9kny2fhr0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/kjbo9kny2fhr1.lump.cpp
action: (null)
write file payload, length: 1206
output: Library/Bee/artifacts/Android/06gmu/kjbo9kny2fhr1.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/kjbo9kny2fhr2.lump.cpp
action: (null)
write file payload, length: 791
output: Library/Bee/artifacts/Android/06gmu/kjbo9kny2fhr2.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/kzae63toetmc0.lump.cpp
action: (null)
write file payload, length: 784
output: Library/Bee/artifacts/Android/06gmu/kzae63toetmc0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/mbtg7kmmqugy0.lump.cpp
action: (null)
write file payload, length: 877
output: Library/Bee/artifacts/Android/06gmu/mbtg7kmmqugy0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/mc4i9rq99g8o0.lump.cpp
action: (null)
write file payload, length: 1053
output: Library/Bee/artifacts/Android/06gmu/mc4i9rq99g8o0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/mc4i9rq99g8o1.lump.cpp
action: (null)
write file payload, length: 682
output: Library/Bee/artifacts/Android/06gmu/mc4i9rq99g8o1.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/mc4i9rq99g8o2.lump.cpp
action: (null)
write file payload, length: 695
output: Library/Bee/artifacts/Android/06gmu/mc4i9rq99g8o2.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/ohdyi08m6bmi0.lump.cpp
action: (null)
write file payload, length: 737
output: Library/Bee/artifacts/Android/06gmu/ohdyi08m6bmi0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/p2ck6wy99bek0.lump.cpp
action: (null)
write file payload, length: 727
output: Library/Bee/artifacts/Android/06gmu/p2ck6wy99bek0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/ta0nb6fs1d620.lump.cpp
action: (null)
write file payload, length: 538
output: Library/Bee/artifacts/Android/06gmu/ta0nb6fs1d620.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/ufzqt6cf7puy0.lump.cpp
action: (null)
write file payload, length: 1002
output: Library/Bee/artifacts/Android/06gmu/ufzqt6cf7puy0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/wh4v266qj8nw0.lump.cpp
action: (null)
write file payload, length: 346
output: Library/Bee/artifacts/Android/06gmu/wh4v266qj8nw0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/wtxphr9u99lw0.lump.cpp
action: (null)
write file payload, length: 528
output: Library/Bee/artifacts/Android/06gmu/wtxphr9u99lw0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/zd2o2rpalvza0.lump.cpp
action: (null)
write file payload, length: 332
output: Library/Bee/artifacts/Android/06gmu/zd2o2rpalvza0.lump.cpp
flags: 0

annotation: MakeLump Library/Bee/artifacts/Android/06gmu/zw5n29wg3y0z0.lump.cpp
action: (null)
write file payload, length: 728
output: Library/Bee/artifacts/Android/06gmu/zw5n29wg3y0z0.lump.cpp
flags: 0

annotation: WriteText Library/Bee/artifacts/Android/06gmu/_dummy_for_header_discovery
action: (null)
write file payload, length: 28
output: Library/Bee/artifacts/Android/06gmu/_dummy_for_header_discovery
flags: 0

annotation: WriteText Library/Bee/artifacts/Android/b9kys/_dummy_for_header_discovery
action: (null)
write file payload, length: 28
output: Library/Bee/artifacts/Android/b9kys/_dummy_for_header_discovery
flags: 0

annotation: WriteText Library/Bee/artifacts/Android/lwpt2/_dummy_for_header_discovery
action: (null)
write file payload, length: 28
output: Library/Bee/artifacts/Android/lwpt2/_dummy_for_header_discovery
flags: 0
Resulting Offline Hash: 29bace51a4f540d5546779a833bc08ad
explicitLeafInput: f1f23b3c2767fb25f8bb89eb5b25f4ec /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.interopservices/marshal.cpp
explicitLeafInput: 165b2ef11d6362feeddf55cda83c7904 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/file.cpp
explicitLeafInput: 6b2d8bbf80fb3d23b7abf2a1f174bf7f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.interopservices/gchandle.cpp
explicitLeafInput: 7a52f4edfa038670c99e7361ae82d832 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/memory.c
explicitLeafInput: b021641476e3b4d47cc6c3ac9152e823 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/winrt/win32apiwinrtemulation.cpp
explicitLeafInput: 202b7addd8e810b3336b605cbedc9217 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/trees.c
explicitLeafInput: c52c04d17628ff4f325da0aa6f4d3f2b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/trees.h
explicitLeafInput: 993a63ef35d81ed5303775c1d33b38c4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/brotli_bit_stream.c
explicitLeafInput: 294e321be0ee9bb83d68f8ddb823772c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/time.cpp
explicitLeafInput: a8a4947d4308e7db08539837387d9f16 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/winrt/initialize.cpp
explicitLeafInput: 206d9df3fe33c28927a23fa123eec82f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/thread.cpp
explicitLeafInput: a60b6ac7a832d1ea7dda8af2bf0ea354 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/conditionvariableimpl.cpp
explicitLeafInput: 9bbbe662c35375c6d88f8b40a7729e5d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/process.cpp
explicitLeafInput: d9442afe9c154bd680aac6c8c75fa9ba /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/image.cpp
explicitLeafInput: 7a72a8088ff3a74ab9d9b0da19e601dd /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/mono/threadpool/threadpoolmonitorthread.cpp
explicitLeafInput: 420a93eb8fb9b5baf0c74c90eaa2aaab /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/libraryloader.cpp
explicitLeafInput: c825d6f67cf6bc81788cf7555d3daf42 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/zconf.h
explicitLeafInput: 0ff319b08cebc2d2ecef90f76af7159e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/runtimemodule.cpp
explicitLeafInput: 1f68de19ca2ec64b7364de5e09ab7952 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/utf8_util.c
explicitLeafInput: 9e3c8c266fec015df47557dcb25a28e7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/sha1.cpp
explicitLeafInput: 42a098d5dc77aecef0116d9b8c533f9c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/socketimpl.cpp
explicitLeafInput: fe9e6059df9f60aae601bed591e852fe /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.diagnostics/stacktrace.cpp
explicitLeafInput: 893919cb86b2616c49555403bc59be35 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/interop.cpp
explicitLeafInput: 69d44d00d6c0b6dfd0d4d5b971803ff4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/valuetype.cpp
explicitLeafInput: 60d53bfebfa4b85dc636dc578384b377 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/inffixed.h
explicitLeafInput: c9d0c0e8f6c4f372f3e2410cb653c3a5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/libraryloader.cpp
explicitLeafInput: ae1099f1c3504ccae8315f91abc6c4ef /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/dictionary_hash.c
explicitLeafInput: 397dcd2b24cabd4a3281f39a16e8530e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/waitobject.cpp
explicitLeafInput: 7564da2449e70b73a3990adaec2c2971 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.diagnostics/stackframe.cpp
explicitLeafInput: 98ac05ad21619122dfbddc9eafddd3cd /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cppgenericclasshash.cpp
explicitLeafInput: 9d2e4abfaf567e240e28ebf636069be6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/mono.security.cryptography/keypairpersistence.cpp
explicitLeafInput: 581f6deeee9e94799b15c13ef96cf848 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/gzguts.h
explicitLeafInput: 51cc54dd80d99a63901735b774e662f5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/number.cpp
explicitLeafInput: b614b3fed9f1804257fe0a0b23539cfd /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.interopservices/runtimeinformation.cpp
explicitLeafInput: 369355491986011a1c77af2401fbcfca /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/crashhelpers.cpp
explicitLeafInput: e4b45224d3dfb8b64057abd36fe77805 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/memory.cpp
explicitLeafInput: b436acecf6b8754764ff6bde6c616b90 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/mono-structs.cpp
explicitLeafInput: b60aa37cbcb1bad3e3b49a389052f8f0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/cryptography.cpp
explicitLeafInput: 64122098d1a1a6bf0b1a46e6d2bb517d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/thread.cpp
explicitLeafInput: 94b832523c5218ff57d9198b93a79bde /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/command.c
explicitLeafInput: b5a0ef699729937eba1cdd3448262bce /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-runtime-stats.cpp
explicitLeafInput: 6b3fc6726aebb5ab727a96a8630646b3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/dec/state.c
explicitLeafInput: d3d8c8cc061691727ba34c619e8b13e2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/uncompr.c
explicitLeafInput: 00000000000000000000000000000000 /applications/unity/hub/editor/6000.0.45f1/playbackengines/androidplayer/ndk/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++
explicitLeafInput: 1093acdf0ad9b359e5b7d573c8c66575 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/runtimepropertyinfo.cpp
explicitLeafInput: 5b7c877612c86c9583861fae2dd6a642 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/android/initialize.cpp
explicitLeafInput: 56e973bb38a3fa990b407601fb5b4123 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/common/constants.c
explicitLeafInput: 1472083b725d74962039401fc53f8903 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/stacktrace.cpp
explicitLeafInput: a55019c83fda51e6382aa97ca2c2970f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/error.cpp
explicitLeafInput: c4bdb617e8a220e7b4f43340d5bf046f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/nativesymbol.cpp
explicitLeafInput: 32ca21188ad278e432cc009946692548 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/customattributecreator.cpp
explicitLeafInput: eff7748922b34a86657b931f163b99ce /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/dllmain.cpp
explicitLeafInput: 80c76db509ad36e03d437c506124cf30 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/microsoft.win32/nativemethods.cpp
explicitLeafInput: 144d50569e7175548fa40a7cec40095f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.net.sockets/socketexception.cpp
explicitLeafInput: 6104da421e001c8f04437e44a978394d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/thread.cpp
explicitLeafInput: 3aa595bcc037e3ff2f1e6c40f0dffd87 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/nativemethods.cpp
explicitLeafInput: c5e6470c51490fcd5ee9a2fc9b16aeb5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/initialize.cpp
explicitLeafInput: 5beec21ad5734541daaae710eb14564b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.globalization/cultureinfo.cpp
explicitLeafInput: 8fa44695a826637095d0332438ffcd3a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/visualizerhelpers.cpp
explicitLeafInput: 1eff9c2785e27173131ae29376e0a876 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/semaphore.cpp
explicitLeafInput: 6e115e537b89bd208cf9b35d1ffda3ba /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/monitor.cpp
explicitLeafInput: ecaad267564c3a9511969f561b7480a2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/clrconfig.cpp
explicitLeafInput: e2a86953f0efbaf61e86a5f4138afaf8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/appdomain.cpp
explicitLeafInput: bd78d4cda49a7fae411370b67ed71c78 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/environment.cpp
explicitLeafInput: 5d1f5a8e2e79e2d90686b6694dbbe8c6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/path.cpp
explicitLeafInput: b722fe6e76a883566dc2f0d3f619e6cd /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/windowsruntime.cpp
explicitLeafInput: 3c18beae6005edd96f9fef3bd38c487d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/marshalalloc.cpp
explicitLeafInput: 1893de3a0af915586fa855dd5ba63012 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/memorymappedfile.cpp
explicitLeafInput: f2973e4014d50d54f6c9dfa133c503ba /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/output.cpp
explicitLeafInput: 8b2eca725f30289f5ef5b3410a8b436c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.security.principal/windowsimpersonationcontext.cpp
explicitLeafInput: 18fdef45f6c59b9f9f9ebc7ca6973398 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/gc/nullgc.cpp
explicitLeafInput: 1941035481dacefa401c40dfb0f51f84 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/mono/threadpool/threadpool-ms.cpp
explicitLeafInput: e1536d18da9fbcecd4939cd8d511df80 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/il2cpphstringreference.cpp
explicitLeafInput: e1c006703a22978befa107d2665f06f5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/environment.cpp
explicitLeafInput: 9574ffa47fe4b76c79141c07148cd809 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/debugger/il2cpp-api-debugger.cpp
explicitLeafInput: 1fd1724c6105bd98e2316b30a6238cc0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/currentsystemtimezone.cpp
explicitLeafInput: 126ec98f4e5fdca424c938f2aa959a06 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/logging.cpp
explicitLeafInput: 1e0487a41c49785695ee7225ad34fa16 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/gc/garbagecollector.cpp
explicitLeafInput: 20bed87eff8335a7c6311119621af520 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/marshalstringalloc.cpp
explicitLeafInput: 9f978096971b11842d89fd67ea1afb17 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/conditionvariable.cpp
explicitLeafInput: b38d4f188e0bcb819b6a02a02c09b60e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/exception.cpp
explicitLeafInput: efbb130bad3f4098f3eb76962d336072 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/genericclass.cpp
explicitLeafInput: ddb99162ba3cbc56bf1cf1999a4ec2e6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/console.cpp
explicitLeafInput: 13e939863f26eb57cf2de1c5ff3c115e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime/runtimeimports.cpp
explicitLeafInput: 888f3b4841b5a40e3a6674ecf58cf251 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/cluster.c
explicitLeafInput: 857d70fb3c9c5073257a214d968e5c76 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.versioning/versioninghelper.cpp
explicitLeafInput: 5a3cf3b4544eb70891786b5ae4dff691 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/icalls/mscorlib/system.threading/interlocked.cpp
explicitLeafInput: 1e47d46d30b2624250a2f4e8096f0adb /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.diagnostics/defaulttracelistener.cpp
explicitLeafInput: 5c9b7fda9960f652661b42a6cd150145 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/runtimeconstructorinfo.cpp
explicitLeafInput: fc796c2a18fd4761e6222315cf368601 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/zutil.c
explicitLeafInput: 5d29b8d7c41cd6fcb415c57af7b6aa34 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/socket.cpp
explicitLeafInput: fb128ee29bf92a0fd0706e438761cb6e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/zutil.h
explicitLeafInput: ff13da1e023ebefd27ba36c92e27bd7b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/char-conversions.cpp
explicitLeafInput: c9f4c1f49d7eeecc285409c4bb752484 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.threading/waithandle.cpp
explicitLeafInput: ac9267a465deaf248f202295a4f568a6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/memorymappedfile.cpp
explicitLeafInput: f9c45b0903fb09525a84a82d4a6875fa /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/cpuinfo.cpp
explicitLeafInput: 9fe80a816823650fab3f1df075a1536a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/std/threadimpl.cpp
explicitLeafInput: 6ba2f5193bd3f4fa829b71f58b675c33 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.compilerservices/runtimehelpers.cpp
explicitLeafInput: e12626055aae6cbc681ccf67dc8f3ee2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/mono/runtimeclasshandle.cpp
explicitLeafInput: 8fa2a16a3288363c79e3d2c33c86455a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.threading/osspecificsynchronizationcontext.cpp
explicitLeafInput: 30ee45e35eae5e8d3c1cb2fa5fc24f09 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.net/dns.cpp
explicitLeafInput: 15fd17bc89e6f50b810b71cc4f0ad533 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/gc/writebarrier.cpp
explicitLeafInput: b55f53adae89919fa385da36658715b3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/android/consoleextension.cpp
explicitLeafInput: d6057e197d68ce3bf02d88b06122a9dd /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/locale.cpp
explicitLeafInput: ec96998c89800de8eba3267282751e77 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/threadpoolms.cpp
explicitLeafInput: 45c9ccb2a619641adb84b990f1169e92 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/class.cpp
explicitLeafInput: ac66f155cc6bf294f8185dc01edbe207 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/handle.cpp
explicitLeafInput: 92dc3163fc3f3cd99021484d085c9d2b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/dec/bit_reader.c
explicitLeafInput: 26213a980c5f66ecf6650b23251f5537 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.globalization/regioninfo.cpp
explicitLeafInput: f51b70165d93dd73d933a6f41e4a43b8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/il2cpperror.cpp
explicitLeafInput: 18b5bc462307121350b68f4e23f39534 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/vmstringutils.cpp
explicitLeafInput: 0df1011c6a963d397b2e7815e945816b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/mono/runtimegptrarrayhandle.cpp
explicitLeafInput: f7b2acc40cfd444e3eabf350ca84b401 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/customattributedata.cpp
explicitLeafInput: 0bb94b978041de9944957f328f936f7a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/stringutils.cpp
explicitLeafInput: e64ed9906decf96292ef8a9b9ae6784a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/com.cpp
explicitLeafInput: b8ab7ac7f51f467e868e8a8269114626 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/scopedthreadattacher.cpp
explicitLeafInput: 1c2d917109ccc62764d6b56f0a8e6141 /applications/unity/hub/editor/6000.0.45f1/playbackengines/androidplayer/variations/il2cpp/release/staticlibs/armeabi-v7a/baselib.a
explicitLeafInput: 8b2a57109060cba47156a562395dc086 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/memorymappedfile.cpp
explicitLeafInput: b3436be925db5a9d9454c65b6298538c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/cpuinfo.cpp
explicitLeafInput: 4188b51b89ba4c56222fcb6bde19e5df /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.globalization/compareinfo.cpp
explicitLeafInput: d6c7e5ff5e0c7517402846910fe9db21 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/osx/systemcertificates.cpp
explicitLeafInput: 24feb750a10e245072a2b4c75ebd8511 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/profiler.cpp
explicitLeafInput: d43e314d9cae190c853cb3545f275cee /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/property.cpp
explicitLeafInput: 84ac31cc5616cd51c5b39c210e7690b2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/gzclose.c
explicitLeafInput: 87e923e128907b49d612fda353e3bbff /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/genericsharing.cpp
explicitLeafInput: d54ab451332401adfa4102ee6c9b0f17 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/pal_io.cpp
explicitLeafInput: e369891ef19b01b0aa8339ebd46adf78 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/backward_references_hq.c
explicitLeafInput: dd1c3c1111ffb4576aa36d4a2b931fca /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/locale.cpp
explicitLeafInput: ffb017775db2a776dff5ae16206e7fbb /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-benchmark-support.cpp
explicitLeafInput: f2d4994ea63d68c2c4979cbec320b378 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/systemcertificates.cpp
explicitLeafInput: 38a5f3c11aa4272b1d0b74d0747c0487 /applications/unity/hub/editor/6000.0.45f1/playbackengines/androidplayer/ndk/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar
explicitLeafInput: 86180d88511bd8da914537a8593fa214 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.net.sockets/socket.cpp
explicitLeafInput: 2a09fa4100b37fcb2de74c1207dc7892 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/directory.cpp
explicitLeafInput: 77391d295c7bb3b6c147d92bfbaa7444 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/runtimetype.cpp
explicitLeafInput: 2389180f936efa09b9c618649a06466e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/extra/gc.c
explicitLeafInput: 8d8700f2c865e34961d07bab78267502 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/mono/runtime.cpp
explicitLeafInput: ae5889378adbd14408f6bfa169ef9083 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/winrt/brokeredfilesystem.cpp
explicitLeafInput: ea0b3e35a6d291e2e9b84e8a7f2d4552 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/metadatacache.cpp
explicitLeafInput: 2c96bb289f0c25c588b207ecd419bac5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/messages.cpp
explicitLeafInput: f99e550b1fb6bae422f0680ddd13b49a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/pal_networking.cpp
explicitLeafInput: 1543b070abbd30473d1f62e56b2ef995 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/memory.cpp
explicitLeafInput: ******************************** /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.threading/monitor.cpp
explicitLeafInput: 0da2d57dd86e5cf6312109d47ba77aa0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/time.cpp
explicitLeafInput: 6cafa5e013dbc0df5f3ec23cec1cf989 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cppgenericmethodhash.cpp
explicitLeafInput: 7a037988781d4b7056123293ba6c1c0d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/debug.cpp
explicitLeafInput: 69ec3b72f7a13583a4640067fd5aafd8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/filesystemwatcher.cpp
explicitLeafInput: 614afb0803f81a6a39cd4b9397a71c40 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.remoting.proxies/realproxy.cpp
explicitLeafInput: d4881ea1457896c707ce60c9d900bddb /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/synchronizationcontext.cpp
explicitLeafInput: 487630f4db1387fb67fe89102ca86765 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.io/driveinfo.cpp
explicitLeafInput: a90e8c4f2d743a1fede5aa5362d159b1 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/microsoft.win32/nativemethods.cpp
explicitLeafInput: 1580a359f54ddc6de9e79c82202f480d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/com.cpp
explicitLeafInput: 8c21fa644be9fc1ce313593d373f0c7f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/lasterror.cpp
explicitLeafInput: 7a493b4b5ecdda0ce9067f92b00a026d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.io/monoio.cpp
explicitLeafInput: cab2d9ffe89e9ffaf3b8ee5cec754ceb /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/timezone.cpp
explicitLeafInput: ffe6bdcf94c0a7b25ea792915a15b3d2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/weakreference.cpp
explicitLeafInput: 72607c33ea89908e84d81baa62146e2e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/metadataloader.cpp
explicitLeafInput: 51cc54dd80d99a63901735b774e662f5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/parameterinfo.cpp
explicitLeafInput: 72bc6c301b452c3ba423542c2cb18e2b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.diagnostics/debugger.cpp
explicitLeafInput: 06f455c231f6fc01ed7f77af0420abf7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/memoryread.cpp
explicitLeafInput: ab70be9c95af5b07f12acea16fd9f8a2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/metablock.c
explicitLeafInput: a1743abeeac836101f6297624255f7d3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cppgenericmethodcompare.cpp
explicitLeafInput: 76642b1a3704501fb284d603c67ea3bc /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/codegen/il2cpp-codegen.cpp
explicitLeafInput: 494e8405cb10c3142d648c4031fc9d63 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/datetime.cpp
explicitLeafInput: af56227355036315c3edd8296af6c46a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/encode.c
explicitLeafInput: 7f99ecb7b3a85d5de5fe98cde2e7c3ed /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/methodbase.cpp
explicitLeafInput: 52ffb3b7fa1a9464f3da496cac5268fd /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/dec/huffman.c
explicitLeafInput: 48410eb5c5a63599598f7f23a02ae65b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/genericmetadata.cpp
explicitLeafInput: 6cc2e1c8af6f0e085cd7efd1e3a37591 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/runtimeparameterinfo.cpp
explicitLeafInput: d825821579f0d2f9e51512a9f28137c9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/file.cpp
explicitLeafInput: f0e85c2787f40efe79ad0b230f2d2b33 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/image.cpp
explicitLeafInput: 67fdd3d64d04d0be133819ec6c6fb77a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/windowsruntime.cpp
explicitLeafInput: 3785ce067cb9b3dbe1e41b58c0ab0469 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/monocustomattrs.cpp
explicitLeafInput: 5134842344aa3ce2a257355db44a147c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/environment.cpp
explicitLeafInput: b4bdcd418ceb60e115285fed22af52f8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/readerwriterlock.cpp
explicitLeafInput: 51cc54dd80d99a63901735b774e662f5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/monomethod.cpp
explicitLeafInput: 705c766f83a88d9b8ad9f6a585d0cc93 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cppgenericinsthash.cpp
explicitLeafInput: 2d674b0f304db0058b47a8737ad8846a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/posixhelpers.cpp
explicitLeafInput: e2750a72b705c3fead2b1e0d8d8d5dee /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/classinlines.cpp
explicitLeafInput: 8e420b1969119b76b98ed38ecbfc9cd6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/blobreader.cpp
explicitLeafInput: 51cc54dd80d99a63901735b774e662f5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/module.cpp
explicitLeafInput: f260abade4e959c57106c68cb88a4053 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/marshalalloc.cpp
explicitLeafInput: b3adbe6ad373fe5d6ae35b4b82e8cefb /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/windowsruntime.cpp
explicitLeafInput: ed7ba601e1707354fb147fe928abbab2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/socketimpl.cpp
explicitLeafInput: 19d5930cfe696ca361296ba66941158b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/socketimpl.cpp
explicitLeafInput: fad7b3a9fc4d86295c6ffd7ff79c08a4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/common/platform.c
explicitLeafInput: 78983e296f3feb65a322bb8d947586d2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/entropy_encode.c
explicitLeafInput: 24456277856d10e834f99edb9685cb14 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.threading/mutex.cpp
explicitLeafInput: eb78bbca18707b2bf56514cf9758b82c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/file.cpp
explicitLeafInput: ba2690f19a1e68b9db6f69878d758cfd /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/globalmetadata.cpp
explicitLeafInput: 7eec67f343c7e20144b566f1088ba5b4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/domain.cpp
explicitLeafInput: 2aec2a8f62ded54c607b472d8e7a05c8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/allocator.cpp
explicitLeafInput: b55e15b4ea93b6ad9ff96bc21ed78f3b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/mono/threadpool/threadpool-ms-io.cpp
explicitLeafInput: aa2a0f26d12b31b96339c9608fcad0bd /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/genericmethod.cpp
explicitLeafInput: f120b3c297ee621773f2ff929631f168 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.remoting.messaging/monomethodmessage.cpp
explicitLeafInput: 8526940e8a8247c639e797cee444d1da /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/runtime.cpp
explicitLeafInput: 71db22b84bca888520e16b19ead8a039 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/backward_references.c
explicitLeafInput: d18997fd6407677a939d9c08b4193891 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.threading/timer.cpp
explicitLeafInput: a8636d750243d59f4b14c567231a7a01 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/random.cpp
explicitLeafInput: e9026c82e5cddb9a4666560d2079a8ca /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/crashhelpers.cpp
explicitLeafInput: 29c1b95f8269da198a0d57142d883181 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/gc/writebarriervalidation.cpp
explicitLeafInput: 25274ee6b770b13e0ad651abe40a47d0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/crashhelpers.cpp
explicitLeafInput: 4e1aa606a433c7e34e34cd7ced1b0367 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/gzlib.c
explicitLeafInput: 7f2e8fe37fd29d0e29545942684a4c33 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/threadimpl.cpp
explicitLeafInput: 96e03dffdf2c14414eae092725557100 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/buffer.cpp
explicitLeafInput: 56964f77f02c31c9cf614acf9515c813 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/cryptography.cpp
explicitLeafInput: 2892f663312534052a3868d121371e6b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/android/stacktrace.cpp
explicitLeafInput: 4e2ec16fb4f928352359ca0007867944 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/pal_uid.cpp
explicitLeafInput: f4a4ae66013fd9b3d3c784dfb8861e8d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/windowsgames/win32apiwindowsgamesemulation.cpp
explicitLeafInput: ******************************** /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/customattributedatareader.cpp
explicitLeafInput: 07c686425ed25d5bf04a9a33a0bc46a2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/mono/threadpool/threadpoolworkerthread.cpp
explicitLeafInput: 83d67698ec31c9d91b6e016bcf92a27e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/exception.cpp
explicitLeafInput: 0bac40f3b9cc8fc2feb602943ea21479 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/fast_log.c
explicitLeafInput: 33da37385e809308288b632e90750626 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/runtimeassembly.cpp
explicitLeafInput: 8f5e17b0301fb1c94987b9d1efd78b87 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/comobjectbase.cpp
explicitLeafInput: f449e9555e8325dacc56b2a276402785 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/winrt/locale.cpp
explicitLeafInput: 75d28a36cb39548302234e18836f8d68 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/gc/boehmgc.cpp
explicitLeafInput: 26f3b75f767a8fd40efb9dcb2185b319 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/stacktrace.cpp
explicitLeafInput: e367aba9fa6373bf1d32feca1ac3d374 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.io/filesystemwatcher.cpp
explicitLeafInput: 088e6842bb741e6fec883ff17e9a691d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/libraryloader.cpp
explicitLeafInput: 33f01724380d196f99158d83e788b565 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/runtimeeventinfo.cpp
explicitLeafInput: a6851ee939039aea621b68402584d196 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/encoding.cpp
explicitLeafInput: 85ca39871c41afa9310f37c4b219ca7f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/thread.cpp
explicitLeafInput: 238c76d081df3f78187f3afe0c8b0f67 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/nativemethods.cpp
explicitLeafInput: 343d225165f75d002a42b4732d35235b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/runtimemethodhandle.cpp
explicitLeafInput: e9ee65fb84d80614c952415c9959f97d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/deflate.c
explicitLeafInput: ******************************** /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/androidruntime.cpp
explicitLeafInput: beb6dc2097a51b5325d62ec94fa872d4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/deflate.h
explicitLeafInput: 9d3cc432dd4b47bf76d49e78bb6633a0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/metadataalloc.cpp
explicitLeafInput: a91aa47ba7cd0ccd440daed0f7033feb /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/gzread.c
explicitLeafInput: 23d688beed2b7cf7db2573340e9d4c59 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cpptypehash.cpp
explicitLeafInput: ef4b9670c74a776984bde4a06d2b7c0e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/path.cpp
explicitLeafInput: 9a2e5a76a8e15979d6018dd91fb54902 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/path.cpp
explicitLeafInput: 9ea77ddaac5e064bf7924c0385444879 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/marshalalloc.cpp
explicitLeafInput: 7e12c5c677456baa5bd206e5d8411517 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/winrt/win32apisharedemulation.cpp
explicitLeafInput: dc2d6daf2222ddf7014a6546ceaad671 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/socket.cpp
explicitLeafInput: 06133ea4a18b341a2514c49d2468b0ee /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/parameter.cpp
explicitLeafInput: 2a8bedf08d0084f29993b67f6d074f06 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/assemblyname.cpp
explicitLeafInput: d5804859212328ac56896f3d891fc4d2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/runtime.cpp
explicitLeafInput: 31c767c2b7547ea1fc6b42ca301adfe0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.diagnostics/fileversioninfo.cpp
explicitLeafInput: 5997f5c17dc6f294969d5ddd804eca70 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/type.cpp
explicitLeafInput: ed173db5b8e939c16719a010ef6fcc48 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/assert.cpp
explicitLeafInput: 39ccd7ad5cf1680bfe03d46c6a6cf8fb /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/brokeredfilesystem.cpp
explicitLeafInput: 0cb5a470621fc2328a7d214745532d9e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/memorypooladdresssanitizer.cpp
explicitLeafInput: a034efe9a42eefc10cfa3f484ac11a9c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cppgenericclasscompare.cpp
explicitLeafInput: f2caddcfc484fe19ee77573e3803bd34 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/error.cpp
explicitLeafInput: 2c5e01f1e616bb2dc730c3c392410fd4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/stacktrace.cpp
explicitLeafInput: 087dd7d12bd0681132850c25e6da9461 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/finally.cpp
explicitLeafInput: d0c21308c7d8bb12222180da83e31c2b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.globalization/culturedata.cpp
explicitLeafInput: a55a121a40e0f6d682c746223a8a9df6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/comentrypoints.cpp
explicitLeafInput: 60551cf3d9b1be79e75461cbdfecb9e9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/monomethodinfo.cpp
explicitLeafInput: 333eb1338d713a09e2afaad256d7e8bb /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/histogram.c
explicitLeafInput: 794f2daecd0711a1e60007dd02ac6990 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/common/transform.c
explicitLeafInput: cbdcc8b6925c9b6f0800d9035ed06d63 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/environment.cpp
explicitLeafInput: 70b9a09c61f0b78f069de95676c3f864 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/liveness.cpp
explicitLeafInput: 5ff9d18cf2ec78e1f43e49dfdb8580b8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/time.cpp
explicitLeafInput: 68339135af24f1aaaa5d6c999d2c8bc3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/conditionvariableimpl.cpp
explicitLeafInput: 390e9a476d3a79be6baf24fcd2c545f9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/process.cpp
explicitLeafInput: cc5a004435393991eb2f5f4d9dce21d6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cppgenericcontexthash.cpp
explicitLeafInput: 6ccc89741db9a38e35c856c00618c3b5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/mono/runtimemarshal.cpp
explicitLeafInput: 78b037c2ddb87e9bfe8a62539f45189b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.security.cryptography/rngcryptoserviceprovider.cpp
explicitLeafInput: bc774c4368a8141f95cd78aaf9229b3f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/inffast.c
explicitLeafInput: 27b1b03f4108a127502292d8d630763e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/inffast.h
explicitLeafInput: dca614d15d653c105b86cf9b49149bb7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.globalization/calendardata.cpp
explicitLeafInput: 55483ddb4f624a5ca086cbc74c11ceae /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/pathutils.cpp
explicitLeafInput: 0dd0219ba7c2200e73f728f3af000c38 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/platforminvoke.cpp
explicitLeafInput: 3c89d7bbc5225ce1869a0d748be3d842 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/icalls/mscorlib/system/mathf.cpp
explicitLeafInput: 4dd6caed9d3173f84c58ff75f5f45151 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/encoder_dict.c
explicitLeafInput: 61dd14f4eb21a9a2ce625a2c57945479 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.net.networkinformation/macosipinterfaceproperties.cpp
explicitLeafInput: 53c26114266a9508c0e8e346e1eb7963 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.security.policy/evidence.cpp
explicitLeafInput: bb313f0ef041137e0625b7d5b2cf7ce9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.remoting/remotingservices.cpp
explicitLeafInput: c95d003825f5a0b9ed510b3de1e6c347 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/event.cpp
explicitLeafInput: 7d892372caeb6f9282e35dc3facaaf88 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/typedreference.cpp
explicitLeafInput: 51cc54dd80d99a63901735b774e662f5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/pal_sizecheck.cpp
explicitLeafInput: 3d3f2fcb2c6392afd3406543f4f595c4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/libraryloader.cpp
explicitLeafInput: 1876fb08f42722d6095b251088d14e4d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/fieldlayout.cpp
explicitLeafInput: 11cfdcdd4a4ecad6f982dc59af0c51f3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/com.cpp
explicitLeafInput: 336d964b723e34dd93da344917907de6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.remoting.activation/activationservices.cpp
explicitLeafInput: 55e1e0878b1b258408a4ed7a40827be7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/inflate.c
explicitLeafInput: 4702331b5f0751b26e1823532fcc17c4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/argiterator.cpp
explicitLeafInput: f9c38efdf2377ce01e34269276cc53af /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/inflate.h
explicitLeafInput: 4799d8a85e1e9474141a8999dec0e667 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/arraymetadata.cpp
explicitLeafInput: 26d6c4402993b60a4e3adc940b700546 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/lasterror.cpp
explicitLeafInput: 22586cb4ba9834c91259340e47a40ee9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/winrt/file.cpp
explicitLeafInput: 945af8712ef17fe07d802b84efafe4d7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/field.cpp
explicitLeafInput: 36d58b3ee91239583d882e93814b53fd /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/reflection.cpp
explicitLeafInput: ba375ec27d65ec4692af9848ec169954 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/bit_cost.c
explicitLeafInput: 465b14252fab7a65dfaff8badf3f7315 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/consoledriver.cpp
explicitLeafInput: 49fcaefb314f1aa941bbe4e4a65c5a69 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/cpuinfo.cpp
explicitLeafInput: 2548301e3771965389b7fbe16ff3f294 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/mono.net.security/monotlsproviderfactory.cpp
explicitLeafInput: 6ec26456bf0d8a35977273663beed1c9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/runtimefieldinfo.cpp
explicitLeafInput: e419e29e63bd052e0dfb0990deafaa1e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/memorypool.cpp
explicitLeafInput: 9e457672149e512ee798af6850047801 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.threading/semaphore.cpp
explicitLeafInput: b4d47e7ee03af9b9aeeb6bc7b3c10fb5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/marshalingutils.cpp
explicitLeafInput: 93328ba870d5a5245ecfeb1e04533501 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/locale.cpp
explicitLeafInput: c2c0aa847d43b4159aaea4c2dfc6fe1e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/winrt/environment.cpp
explicitLeafInput: 743b3f75351e9b59de442b9918e89b57 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/mono.unity/unitytls.cpp
explicitLeafInput: 2fa9f3b611441aaa02309fe635550218 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/systemcertificates.cpp
explicitLeafInput: 06363f5744f2eb49f376ee3027de876f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/debug.cpp
explicitLeafInput: 4eabd995e97acd0f04457383ccd7c733 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/debugsymbolreader.cpp
explicitLeafInput: bcd9d3ca52dc91a2d8f6b4151af28b99 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/directory.cpp
explicitLeafInput: 09e83de098022e3d7b61871cf4e52599 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/file.cpp
explicitLeafInput: 89a52856369ba39822f28482f9ae7c5a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/array.cpp
explicitLeafInput: 2e8b25b0208313255a5a946d8fa849c5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/genericcontainer.cpp
explicitLeafInput: e3414ef58ab24cd07a2dc163e8f250e4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.diagnostics/process.cpp
explicitLeafInput: ef9b752f95a5034847fb952bc58a4dc0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-mono-api.cpp
explicitLeafInput: 8fd9b4e59f1ddbde52c9517f79770f3f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/common/context.c
explicitLeafInput: b5b0a65ac4359201ff60bac1dc450f10 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/mutex.cpp
explicitLeafInput: 7eebde3b488b8c857e5d42bdafba4ccf /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/initialize.cpp
explicitLeafInput: 0ac17ac79af72ea3ba5ed038e094ccef /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system.core/system.io.memorymappedfiles/memorymapimpl.cpp
explicitLeafInput: 949b24f75a014bc2273ff939a6be399b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/thread.cpp
explicitLeafInput: 54c09b1102963e6959f69f9e39a12b0f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/memory.cpp
explicitLeafInput: f0e8af6bff530cb92325745ecf83b8b3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/exception.cpp
explicitLeafInput: 2ddf5b12f3fb52d37980905b0a1d6fa4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/internalcalls.cpp
explicitLeafInput: 959f1cef6ce2589227f86ca35bb0c2c5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/delegate.cpp
explicitLeafInput: b6413d0261ae9e780fc101ccd0f39d1c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/pal_errno.cpp
explicitLeafInput: a576292f72654b5eb644076f6da03110 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/eventinfo.cpp
explicitLeafInput: a820e3bae5f8e0d7e25f427351790db2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/emscripten/socketbridge.cpp
explicitLeafInput: 72f84dbd35eba95f5979ea4d7d5dc0c9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/mono/threadpool/threadpool-ms-io-poll.cpp
explicitLeafInput: cd5df2ba54da6c33df42f0822549c95a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/lasterror.cpp
explicitLeafInput: 56239ec4247ba66c6586a5b97f90d2d9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/method.cpp
explicitLeafInput: a7e9dbe20723368653d2c0daf26e9807 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/type.cpp
explicitLeafInput: 5122ae043ddcc2a6eec172182ab8aedf /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/object.cpp
explicitLeafInput: b4d35b5da969ef41d3c80c8805bc155f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/timezone.cpp
explicitLeafInput: befd8279ef8ee25bfa78c090196984c5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.net.networkinformation/linuxnetworkinterface.cpp
explicitLeafInput: 10329f790b48f9ac0a051f788eae062e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/infback.c
explicitLeafInput: 1ca80929b442ab4eb8085cc19d38b857 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.remoting.messaging/asyncresult.cpp
explicitLeafInput: 434b61db349f436405d3e92620e453f4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cpptypecompare.cpp
explicitLeafInput: bae85a21a8dd1351b26a4926a4eab142 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-api.cpp
explicitLeafInput: cdc405db7ba109feda3e13aa09601b25 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/windowshelpers.cpp
explicitLeafInput: c3efd27a51d6a5bf9cc660959941ab19 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/assembly.cpp
explicitLeafInput: 8a759beeb7544c04019310aee214de0b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/image.cpp
explicitLeafInput: 78fc0804793b48f446803449dfd9f386 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/pal_unused.cpp
explicitLeafInput: 0665f622a805f73233f0e605d455b580 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/enum.cpp
explicitLeafInput: 2f5e6e8b6b2192f7a9bc9c0b2f33a48f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/assembly.cpp
explicitLeafInput: 8985408fc190768993d407891447d6af /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.text/encodinghelper.cpp
explicitLeafInput: e6c0f75c8372bf98d8e2b6d668850d1a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.text/normalization.cpp
explicitLeafInput: f542fa6820576efc39f14176225082b9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/assert.cpp
explicitLeafInput: 0541cd7396f7953a58377299c8f9a9a3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/console.cpp
explicitLeafInput: f3e56b11e0ee32ce3ea8a98017c54712 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.security.principal/windowsprincipal.cpp
explicitLeafInput: 7ade753c84a652c51397631a05c6a95f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/android/locale.cpp
explicitLeafInput: 6f9a581f7b1c7bbf950843a99dcb9d10 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/image.cpp
explicitLeafInput: 7efbb36c1138b12d3c31b8194d0647c2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/module.cpp
explicitLeafInput: 2791c4dac8d3601c5120d3e6a60ffaef /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/path.cpp
explicitLeafInput: f0ac3549b5f6d351ad71151ffda7cd3c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.threading/nativeeventcalls.cpp
explicitLeafInput: 1813b88d73852fdb618b587fd4a2f7ac /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/crashhelpers.cpp
explicitLeafInput: 42158022d8191290299d04e0f809e2c8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/environment.cpp
explicitLeafInput: 8745d8baf1ee7505f79e4f15acb08298 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/block_splitter.c
explicitLeafInput: 43100b0d1e1705361db776514f16ea34 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/gzwrite.c
explicitLeafInput: e1b1f1a2b7b8a8781086d7d112e688fa /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/gc.cpp
explicitLeafInput: 9987222a01032cdb9d575ad9f1521f91 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/common/dictionary.c
explicitLeafInput: 4cc42ff2db60b66d355cd407533961e4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/osx/thread.cpp
explicitLeafInput: c06b26c9250f0ec6cd1bfcc3150db718 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/assemblyname.cpp
explicitLeafInput: 294522efaf3fdff7f792bb95d1370f71 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/debugger.cpp
explicitLeafInput: 6ceb92df3b7328fa39b4e0f3afb49a38 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cppgenericinstcompare.cpp
explicitLeafInput: 291fa0b7ad5162f1276e473b2cc40404 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/extra/krait_signal_handler.c
explicitLeafInput: 97bdf21f0abd1e15db6d6882d785ed69 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/compress_fragment.c
explicitLeafInput: e2b99c25c340c1b4728f588160780a5a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cppsignature.cpp
explicitLeafInput: a2525fa38fec8f87be97bc3d1ea78664 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/runtimemethodinfo.cpp
explicitLeafInput: a947aa737a8c191c6d2209bd07e458a7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/crc32.c
explicitLeafInput: ae342b0d2eba1d8f9da0b67b74ca8444 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/directory.cpp
explicitLeafInput: b7d9f9690afaaa6e9084e29c1bff006d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.threading/thread.cpp
explicitLeafInput: f892c5d2595974d3c08c8e8313b53741 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/unityadsstubs.cpp
explicitLeafInput: fdec8a3cec51b631ea2a4db1f148deab /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cppgenericcontextcompare.cpp
explicitLeafInput: e3a6b845823d4af4a208527be3eca714 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/crc32.h
explicitLeafInput: 0f64ca58e1e11b1e3b0ffb8359626114 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/array.cpp
explicitLeafInput: 9338b27689de5c7cd76087d1c2def416 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/dec/decode.c
explicitLeafInput: 06b6e9133975816b488c5f16705c5fbe /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/object.cpp
explicitLeafInput: 68848cfbdd3754d2d343ab86940183de /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/winrt/process.cpp
explicitLeafInput: 52c1088803a22e39690f14b84c382c37 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/mono/safestringmarshal.cpp
explicitLeafInput: 5ebffce6529c37f763719160863fe837 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/inftrees.c
explicitLeafInput: 4b7a9b68ea99998c9ff30c11e02c8062 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/inftrees.h
explicitLeafInput: 0f98a1705de4c6cd21140dd301089f78 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/marshalstringalloc.cpp
explicitLeafInput: 0dd2461a81a302159d129f51f92afbf4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/literal_cost.c
explicitLeafInput: 57d2d9da546cfcc075a8017e1392027c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/pal_time.cpp
explicitLeafInput: 091b5e1daf4535e00e02c8cd0f327607 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/rcw.cpp
explicitLeafInput: 669df7225461e1a5d53f7173aac6e899 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/memorymappedfile.cpp
explicitLeafInput: b369551041f1f6c1f48fd35467cc2c6b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/event.cpp
explicitLeafInput: f8bc882c220ad8231f5e6b2766079609 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/path.cpp
explicitLeafInput: aaa9490bdc41c8626b965b3ee626f518 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/classlibrarypal.cpp
explicitLeafInput: 8650b2290b05803073c91c50c5e7193e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/xamarin-android/xamarin_getifaddrs.c
explicitLeafInput: 6c100000edf135a3a792ff375da4db8d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/string.cpp
explicitLeafInput: 41541d919396616692e21b1abf639929 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/vmthreadutils.cpp
explicitLeafInput: 561e565eab6d687999ab4e1f14aa1ee6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.threading/internalthread.cpp
explicitLeafInput: db5bbddda80f9a5d8c2d9d7b6e9066c3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/numberformatter.cpp
explicitLeafInput: 06195aa12c00f01a91c7fccf02936a66 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/socketbridge.cpp
explicitLeafInput: 6a3ee7c3cdc76b9b85b7c22395c980c4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.io/path.cpp
explicitLeafInput: 459ca4e5780ee018623b5f33bb704a7f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/runtimefieldhandle.cpp
explicitLeafInput: 197a647e4a2d96568c7037c6202084ee /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/osx/process.cpp
explicitLeafInput: 81f20a56f867ca3989847f7fb5872f09 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/error.cpp
explicitLeafInput: 987332310f5a1cd6325ea26cbf168e4c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/threadimpl.cpp
explicitLeafInput: 116c2f6988f51f4177d8b4cea0cc8ae9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/pal_random.cpp
explicitLeafInput: 0452e1ee1bcbfe31a5f9f21171940bf2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/fastreaderreaderwriterlock.cpp
explicitLeafInput: a206969180196511fef240f79aaf3745 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/fieldinfo.cpp
explicitLeafInput: 95c9ad6de4ec053ba37f1a9b19c9a76f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.io/brokeredfilesystem.cpp
explicitLeafInput: 7b11a58b992d8e97c2e24a6f88116cef /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/icalls/mscorlib/system/math.cpp
explicitLeafInput: 03f43441a71d5a4012270dd3faadfc1b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.diagnostics/stopwatch.cpp
explicitLeafInput: 41c2b8cb4c4a82453f74a3e488f03a7e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/static_dict.c
explicitLeafInput: e06cf571e34346c9e8006af2d53c590c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/thread.cpp
explicitLeafInput: 73b1b0e46a2f125c8eb132aea3229d57 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/runtimetypehandle.cpp
explicitLeafInput: b729e3c0e3fd28abe5581eb52af539ad /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/stacktrace.cpp
explicitLeafInput: 8f40ce6c153fb8f16305ba2597a77f97 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/filesystemwatcher.cpp
explicitLeafInput: 5e8c552fbad21312c0c37d874c8fc04b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/directoryutils.cpp
explicitLeafInput: 6c373120e22754e85001399de03d90ac /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.remoting.contexts/context.cpp
explicitLeafInput: 02fa9e8461bc22044fecb84545fa0309 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/zlib.h
explicitLeafInput: a94dd1f31fe04f3a921ad67765b2bf18 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/encoding.cpp
explicitLeafInput: 3ffbd395d87f31b1192c6a2f0ab20ac9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/ccwbase.cpp
explicitLeafInput: 558481d126f0c9ee9734f580ebffc2c7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/zlib/adler32.c
explicitLeafInput: 20a970ed672a5022e10545c3edc81211 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/ccw.cpp
explicitLeafInput: f699411d825bd3665a4a84240466b597 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/waithandle.cpp
explicitLeafInput: 244944416d816b1e02bace5c1e658a38 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/systemcertificates.cpp
explicitLeafInput: 76900abb5496b7769b36676fb30879dc /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/osx/image.cpp
explicitLeafInput: c2599befc49e1632439947bf865b4726 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/enum.cpp
explicitLeafInput: 6e5946c3a9b9a8d2361fb728518784fb /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/string.cpp
explicitLeafInput: 6d87773a2749a168f30ecaf814f671e5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/gc/gchandle.cpp
explicitLeafInput: 133c4fc6eadd9d826ba0be468f167ecc /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.security.principal/windowsidentity.cpp
explicitLeafInput: 01d640f5f0ad37df37ee6c21ad3dd85f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/memoryinformation.cpp
explicitLeafInput: e20e0363a4eb9d51577fd99ffe4bf90b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/environment.cpp
explicitLeafInput: 9a3be291aee2b506b20b0bc029854a21 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/mono/monoposixhelper.cpp
explicitLeafInput: e165227333128960c9e783b8506718c7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/compress_fragment_two_pass.c
implicitLeafInput: 509091e51d7243264136c6c8e8222c56 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/compress_fragment_two_pass.h
implicitLeafInput: 2f263c2bed6f62cd2029271955898aa2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/threadimpl.h
implicitLeafInput: c29ca5306db5a45a54272696f7e34310 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/error.h
implicitLeafInput: e3b8aa313476c7f0163a1139ce3a15d3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/gc_mark.h
implicitLeafInput: 327edbdeb9c47df178a65634f417c29f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/networkaccesshandlerstub.h
implicitLeafInput: 0709fcc81e8a21660c4ae06791403ab2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/reclaim.c
implicitLeafInput: 3175a77767e22489e9ca82af77d40904 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/external/utfcpp/source/utf8.h
implicitLeafInput: 70eb1960ea3deefa90bff7c72691577f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/dllmain.h
implicitLeafInput: 5187c26a671d91a58cc4dd7c35d1610b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-mono-api.h
implicitLeafInput: a2ee711990883ed42691db08f477582e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-api-functions.h
implicitLeafInput: e74c92702264f86a3b10b667b5166fce /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/memory.h
implicitLeafInput: 0c725017668728d9708da0086d694d9e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/ibmc/powerpc.h
implicitLeafInput: 82f5e2ea81827c32e3fc84498e99c63d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/internal/baselib_semaphore_futexbased.inl.h
implicitLeafInput: 8d4d5d2824c564bf0fbcf306016a5e99 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/internal/platformdetection.h
implicitLeafInput: 86ecad15ab57cbb7e3c165194151fd41 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/include/brotli/types.h
implicitLeafInput: b697a2e83fb98c954e18ffcdf8b432ec /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/google/sparsehash/template_util.h
implicitLeafInput: 7ff5399d187356fa5a165d81e099602d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/hpc/hppa.h
implicitLeafInput: 6ffd34a439102f96b45dc81c10eea6a7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.globalization/generated/cultureinfotablesnet_4_0.h
implicitLeafInput: ecfc2990bcb2d3b8b6130e667b8f2ea4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/vmmethod.h
implicitLeafInput: da950de83a53f258140683d4c330dc12 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/brotli_bit_stream.h
implicitLeafInput: ******************************** /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/vector_mlc.c
implicitLeafInput: 29289ab7752f9b120ad93816309c7b31 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/internal/baselib_lock_semaphorebased.inl.h
implicitLeafInput: 66ab811ec0fc6898a90da8b77607fadc /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_sourcelocation.h
implicitLeafInput: dfe8e5d8ffdc1c32634232d9b86c9f51 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/cluster_inc.h
implicitLeafInput: 0fb1ff1fdd26d2e3bc9840f3240f773e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/com.h
implicitLeafInput: 508c918e4c0c83b9a0d08435364f6506 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.diagnostics/stacktrace.h
implicitLeafInput: 1cab9aaecffc1acc020bdfc5fea3bf47 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/semaphoreimpl.h
implicitLeafInput: 050421a622121efd55f6e5b35a840cd4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/loadstore/acquire_release_volatile.h
implicitLeafInput: d443dc0732c0db1fc3f72383448c461e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/genericclass.h
implicitLeafInput: 22a3e75c80934f06ac24f5db1b3e295d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system.core/system.io.memorymappedfiles/memorymapimpl.h
implicitLeafInput: 573ec3a202ee7cf2ac1410439c87a7ba /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/customattributedata.h
implicitLeafInput: e16dbbf1e6f81418e5ed135b76e85e41 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.security.principal/windowsprincipal.h
implicitLeafInput: 31c73ee88dcefca9d74567a528ac5c80 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/messages.h
implicitLeafInput: f699ae59244acbe009604df4e0ca7a9c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/platforms/android/include/c/baselib_errorstate.inl.h
implicitLeafInput: 51f0ab55671eec1cd756faa81f41a710 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/params.h
implicitLeafInput: a42f9fc3947f5f67dd7049f058947ea2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/internal/baselibdeprecateddefinespreinclude.h
implicitLeafInput: 4a7e17f2f1311fcc6be7e19ef263781d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/utf8_util.h
implicitLeafInput: b860896298c188ab732f468b12be6c23 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.remoting/remotingservices.h
implicitLeafInput: 967c1de7449ee585525ab3f49a435563 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/platforms/android/include/baselibplatformspecificenvironment.h
implicitLeafInput: 7cb900e903a17fd152265cd8d5e0bf88 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cpptypehash.h
implicitLeafInput: 2529ac5ff33626e5057c8b97f0444826 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-number-formatter.h
implicitLeafInput: 15a2ae7fb0f7bead0dfbec06c80718ff /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/consoledriver.h
implicitLeafInput: 217253b76b0ab6bdf402f0fe198efd6a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/new.h
implicitLeafInput: 4c15bcf04062800ccf0ee93f2da57e06 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/mono/threadpool/threadpool-ms-io.h
implicitLeafInput: 683565759795a7bab7cb87cd801c3e61 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/gcc/sh.h
implicitLeafInput: 05166e7c66a9480df5c5908ec1faf631 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/internal/baselib_cappedsemaphore_futexbased.inl.h
implicitLeafInput: 2bfb950500f638bee3dcee2b92ac1b29 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/hash_longest_match64_inc.h
implicitLeafInput: c6b5e964d92dfab5c7f78119c26260e8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/methodbase.h
implicitLeafInput: 5f4a404ad194856ae17d1b9a53396dfb /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/collections.h
implicitLeafInput: 9b2d18285e441c2b7658b98889b9735e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/dictionary_hash.h
implicitLeafInput: 800d8279161d7cb418f83563aad8b279 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-sanitizers.h
implicitLeafInput: 0b3c6bc41e2931628bcf2b8f45bb7c5c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/weakreference.h
implicitLeafInput: 13c53c92bb40b7b8684e90bbcb635399 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/loadstore/char_atomic_load.h
implicitLeafInput: d53780aa53b14f725d65cfd99ba43702 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.globalization/cultureinfo.h
implicitLeafInput: 44e9458e88c93c44d73edc852de50ed2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/runtimeconstructorinfo.h
implicitLeafInput: 8acb44ab9a99c02fb2fe7cef392c652c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/msftc/x86_64.h
implicitLeafInput: 65d42482b26af0a4408e5c247de688f9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/msftc/common32_defs.h
implicitLeafInput: f0b1039885cb9e10fa2729267f85e237 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/pal_mirror_structs.h
implicitLeafInput: cb3ed92d1fccecf2344569381b74a6cd /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/debug.h
implicitLeafInput: 7a73bcb543b1ffd4b3e68274b4783391 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.threading/mutex.h
implicitLeafInput: 0b0cd135181c466c87d26c2263df47ad /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.globalization/culturedata.h
implicitLeafInput: f6293000779e70e34659423e3f3ea312 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/comentrypoints.h
implicitLeafInput: eccc1e16be0d873cb0785ad4651e2f4f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/cpp/reentrantlock.h
implicitLeafInput: 3268a754f82a0f9c79092639b3606a30 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/mono/threadpool/threadpoolworkerthread.h
implicitLeafInput: 2764c260f019e51e7a9116b8fff736a8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/socketimpl.h
implicitLeafInput: ebaa6ac957cf41518e5fb6c6b4e0881c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/memorypool.h
implicitLeafInput: 3765808faac780b96c6261223927d366 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/private/gc_locks.h
implicitLeafInput: 77531bf0b1f1b1bf7c4eb47efbb718f3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/readerwriterlockimpl.h
implicitLeafInput: 17294f277c3601e28698989190b12550 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/test_and_set_t_is_char.h
implicitLeafInput: dc0e0d9b7a5f5a3bf0f945a3757f1501 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/initialize.h
implicitLeafInput: c6e246ada25ce9eae7a57fe834c3c913 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/mutex.h
implicitLeafInput: fcd341337ecee478ad869878acf25d97 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/gcc/s390.h
implicitLeafInput: 9f5b9dbdb98e4700948bd85a888e95b7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/mono/runtimegptrarrayhandle.h
implicitLeafInput: 566aa4c8dd0a14ecc3d00500bd430530 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/metadatacache.h
implicitLeafInput: fb27fc80ca46f31b205182c72f0c2ca5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/command.h
implicitLeafInput: 7189452e2e9d4bd31f22e587e5e35ab3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/memory.h
implicitLeafInput: 39ef08244ee3a978bbf2fd063119ee79 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/gc_config_macros.h
implicitLeafInput: baf53b18b3018bb18a34f97cd25f4eca /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/atomic.h
implicitLeafInput: 90e24f10cebd19670206bc4b1e19187b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/file.h
implicitLeafInput: ffc253ec6da26217d35bc6d09b32c4eb /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/runtimefieldhandle.h
implicitLeafInput: 37a279a3effe0b2ad57f658d240fe369 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/classinlines.h
implicitLeafInput: 915f3a534b47821cadeb96a483d56bd1 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/private/darwin_semaphore.h
implicitLeafInput: aa4b69cb49b66588b30e628728dc0fa7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/metadataalloc.h
implicitLeafInput: 67f2c5e0836d44201eeaf52e4459ccb1 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/libraryloader.h
implicitLeafInput: 562b108face19a2b153d6f7acd4e872b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cppgenericcontexthash.h
implicitLeafInput: b43e9b26ee103d993becd44de8b25c3c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/windowsruntime.h
implicitLeafInput: f72d2cc25848f82158b8dbfe2e77ea5f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/memorymappedfile.h
implicitLeafInput: 1dac3e32047233669492157af410a83d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/dec/state.h
implicitLeafInput: 71ae856be1da7d090b5c565efc45d9db /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/directory.h
implicitLeafInput: 45b88da70b738133792fdb2970a44f9d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/external/utfcpp/source/utf8/core.h
implicitLeafInput: 5b71750d32711746cb2ea0ed1e8a0d63 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/allocator.h
implicitLeafInput: 385e5871b60cf116d260e31c83e0d9ad /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/il2cpperror.h
implicitLeafInput: c7cbd51d86034ed4738603eca9e37fc4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/image.h
implicitLeafInput: 15ab6b386b91958eb3d2021db478496c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/gcc/powerpc.h
implicitLeafInput: b318e2b3dd13082e4f3e1d3b4fc4ed68 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/mono/safestringmarshal.h
implicitLeafInput: 6084d123163abe276debf23565756535 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/common/constants.h
implicitLeafInput: be09853e751de9e381619f98ce4f82e7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/private/msvc_dbg.h
implicitLeafInput: 229218901892e0af9f394e767605671f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/hash_to_binary_tree_inc.h
implicitLeafInput: 2de6503f3fe2dd4c8639c08237c200c8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-pinvoke-support.h
implicitLeafInput: 4155f441d66c88a11c05afb5bbbdfdc0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/blacklst.c
implicitLeafInput: d62ff6bfd6ff6d7fd1d60edeea134c09 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/dynamic_array.h
implicitLeafInput: a1598678cb721a283eea457558199a4a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/ccwbase.h
implicitLeafInput: ba0180f778add239352812e3103a3e32 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops.h
implicitLeafInput: 15a4366e8e0b33fda24eefd66fe5cf70 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/scopedthreadattacher.h
implicitLeafInput: 64a03838efb452b8ca698bbf5e5f517d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/platforms/android/include/c/baselib_dynamiclibrary.inl.h
implicitLeafInput: 890daffdc7bb3e881a4149624c40071f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/handle.h
implicitLeafInput: d17e7bf202543a28f6c9ef73e50e817b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/mark.c
implicitLeafInput: 8fbcb13bfc0624ab9e051e7504ef4af1 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/gc/gc_wrapper.h
implicitLeafInput: 7af8193f1113279117adc765febff2d9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/environment.h
implicitLeafInput: f10d34afb36c737a4b652441897b9e2f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/windowsgames/win32apiwindowsgamesemulation.h
implicitLeafInput: 3854e7f71a13600e9a8c1ae1405e3013 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/internal/compiler/baselib_atomic_gcc_patch_postinclude.h
implicitLeafInput: a7749c69381148770a94016f43c13222 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/memorymappedfile.h
implicitLeafInput: b9a3741d21f71c20aef5b7f74834e907 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/method.h
implicitLeafInput: 56a6c9a791bde5c2924d443e8788576a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/hash_forgetful_chain_inc.h
implicitLeafInput: 6aaf84cd75a525cae6ec77955156ebf7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/debugger.h
implicitLeafInput: 80faebb7431d7cbedf9b9e9bc9244995 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/checksums.c
implicitLeafInput: bf4334fd7bb58f5e6eeedccbad731c6c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.remoting.activation/activationservices.h
implicitLeafInput: 9f9a0cf51e27e976c98c06d4c264dbae /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/exception.h
implicitLeafInput: 4dd68120ac2982bb54ec071b8f631dcc /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_cappedsemaphore.h
implicitLeafInput: 5f6d00845c290bd890decf7ec0c153ca /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/histogram_inc.h
implicitLeafInput: fdc42f10fb8b46c0b506770973f864c2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/process.h
implicitLeafInput: 7430b4a09ba30dbdbd031eb650dce73e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.remoting.messaging/asyncresult.h
implicitLeafInput: e828651fd0b2d785ae85590eb39c1865 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/google/sparsehash/internal/sparsehashtable.h
implicitLeafInput: 3f227087d0a07fd8236f099adcab0a6d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/mono/threadpool/threadpoolmonitorthread.h
implicitLeafInput: d59a27ff8578d4cfcd10755b40a59d9e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/backward_references_inc.h
implicitLeafInput: 95616fd447befabfb7a34e33bcef8060 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/monocustomattrs.h
implicitLeafInput: 952c433b7cb2d1964706d0d2cae9ebb3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/socket-c-api.h
implicitLeafInput: ae0b4e24df6d3723926d79cc62991b33 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/block_encoder_inc.h
implicitLeafInput: 37f2bd020f96f4d137a57f5b6ac8e399 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.diagnostics/stackframe.h
implicitLeafInput: bddf041ebaa913333cbf22bbf6ff5dbf /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/os_dep.c
implicitLeafInput: bef8a0262de957bc15ff7fcd1e1d96a3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/array.h
implicitLeafInput: 17f5dca058601a67ba799f444229ea08 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.globalization/regioninfo.h
implicitLeafInput: fb9a13f38f24bb4db4c07ff337b1525c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/metablock_inc.h
implicitLeafInput: fb2129ce9b44a51e9a2a9a32c44b2181 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/cpp/internal/conditionvariabledata_futexbased.inl.h
implicitLeafInput: e1218e96a1f55f41191d949a66825e95 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/socketimpl.h
implicitLeafInput: 8837d74382a00f3b96dded6d531790f9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/alloc.c
implicitLeafInput: 6918fd0ea01957ab2e6e7bddd1df5632 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/pathutils.h
implicitLeafInput: fdbc243daf39a7651f14e0e5c738843d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/thread.h
implicitLeafInput: d1f677545af9332b2361d7244b291dd6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/win32_threads.c
implicitLeafInput: 1d00d66b86e61816982c138a9f0bd4d0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-string-types.h
implicitLeafInput: cc40f3caad2c6e64af002c4d84152f26 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/directory-c-api.h
implicitLeafInput: 2f2e4aec4e783a5707894a230fbf3844 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/stacktrace.h
implicitLeafInput: 77be75680116ef126a03d5f7cb5c2708 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/runtimetypehandle.h
implicitLeafInput: bc021aa0105e23e85794ff4d7194d8da /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/gcc/aarch64.h
implicitLeafInput: 9fff8af30b5eb50468896793dbe7552d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/debugsymbolreader.h
implicitLeafInput: 2c956c24604f6fe0a4bc2cd2a2d95714 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/array.h
implicitLeafInput: 47b865e7cd51b8fdf8d59b27f58dcfdd /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/cluster.h
implicitLeafInput: 4c841759b3d1ae5ec9d40614d26d1339 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/all_atomic_only_load.h
implicitLeafInput: 472a337edf8289eaf8e1d668a65030e2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/allchblk.c
implicitLeafInput: 71ad7b1d8329f5b6a982bc8c03333e56 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/mono/threadpool/threadpool-ms-io-poll.h
implicitLeafInput: f7ceff8217c416d511aef7d4207742e0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/winrt/synchronousoperation.h
implicitLeafInput: e1196485ea9932e2cc81ae841f8e8885 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/assemblyname.h
implicitLeafInput: 46c981ece141c16dd1c40b4ef3149f21 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/backgraph.c
implicitLeafInput: b852e8de0b2aa5b084f365d81f83dec9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/cpp/time.h
implicitLeafInput: 50c7134e124c1b652d00023a73e081da /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/loadstore/char_acquire_release_volatile.h
implicitLeafInput: f1e75dbcc0407bb9f4698e58b59b4c07 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/write_bits.h
implicitLeafInput: 438136cfa5a8c6fa0d913038a35ca94e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/errorcodes.h
implicitLeafInput: d8fbb012197f5954cdc8b9e6401dda6c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/internal/baselib_cappedsemaphore_semaphorebased.inl.h
implicitLeafInput: 6cbf694c2e70875acd3feb986bc46e18 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/generic_pthread.h
implicitLeafInput: 6969190d91841cc832bedef630697be0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/thread_local_alloc.c
implicitLeafInput: 2c004ddbd091919e5bc293c154186bbc /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/gcc/hppa.h
implicitLeafInput: 88cfa0e1112da8c0c18f88de5e514244 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/windowsruntime.h
implicitLeafInput: f9f3750731b68833ce74f72d8385ca16 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/delegate.h
implicitLeafInput: 0378bea46d6e3584945579b47a38b4b4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/threadlocalvalueimpl.h
implicitLeafInput: 6d3a138fb7c4ee5a9f9ce61d0c6f68ce /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-normalization-tables.h
implicitLeafInput: b01693b5c8d17071f129f7ecf96e3892 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/directoryutils.h
implicitLeafInput: 6f4ee38efade6ec4eb97b4999403cc32 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/cpp/semaphore.h
implicitLeafInput: c87799eb2809256481d357b1a118177e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-api.h
implicitLeafInput: 7bb30becf2befa8ff943c3d459c473b7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/windowshelpers.h
implicitLeafInput: 00b7773f57fef11c9d1ee855dbf5c857 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/waitstatus-c-api.h
implicitLeafInput: 2b4cef6554c9a384ce1dda7fa7e6d3dd /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/android/pal_android.h
implicitLeafInput: bd62dc712c73ad7eef73eaee086690da /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/mono/runtimeclasshandle.h
implicitLeafInput: d99332fa1b4eff6be8cbd80efa312577 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/mach_dep.c
implicitLeafInput: ce53c9bc96e9e0ccc35521bf322e19b8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/google/sparsehash/type_traits.h
implicitLeafInput: 858ab51205cc48149716443610691731 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/error-c-api.h
implicitLeafInput: 62cbd5220c9efe6277fee41e74966464 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/exception.h
implicitLeafInput: 4141df70612517a40de5b0d876c94930 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/dec/bit_reader.h
implicitLeafInput: 4cd3671ac86e9401831333090d94c5ad /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/internal/baselib_atomic_extensions.h
implicitLeafInput: 8e9f9e647d12476364fe57be026aab38 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/stringview.h
implicitLeafInput: adf33c3152609aa83c13d78835b91e13 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/quality.h
implicitLeafInput: 820785964387ec2b2ed25dd70e82cfb3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/initonce.h
implicitLeafInput: f744165ba3208ecaae07711c30755bbf /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/error.h
implicitLeafInput: a6d0005b515b47e32feb326966a19d69 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/linux/pal_linux.h
implicitLeafInput: 7c89d369f46543dddd9e16b0d2abeeeb /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/google/sparsehash/internal/hashtable-common.h
implicitLeafInput: 78a333074042cb695ee8c14fb15959d6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/winrt/winrtvector.h
implicitLeafInput: e1df1e4b717767328dff5276156357d9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/emul_cas.h
implicitLeafInput: 7de37bd49ddd069ff8b6d9cefd7ad14e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/marshalalloc.h
implicitLeafInput: d20f216d25d259e1c337f358b7fdcf11 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/runtimemodule.h
implicitLeafInput: 3ed67575e6c62276644bdddbc5ac4ad6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/mono.net.security/monotlsproviderfactory.h
implicitLeafInput: e94721a01a7baa3f7c8c049a0c216d4e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/loadstore/short_atomic_store.h
implicitLeafInput: b5e57d2c54eeee63a1e1e6cf42cdcf98 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/microsoft.win32/nativemethods.h
implicitLeafInput: b63f33a01816701ae6e2373be776a8e7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/event.h
implicitLeafInput: 8f9d2f0a7588fc0ab76d14b3c4c21d31 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.remoting.contexts/context.h
implicitLeafInput: 6abb12d7c231bef7088f9020c1e0e7c2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/runtime.h
implicitLeafInput: 7cd83e1fde29ef8ec46d4b5fde2ef14d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/hash_composite_inc.h
implicitLeafInput: f4d5611469a584fbe037cc84a2bbd7ca /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/environment.h
implicitLeafInput: 68a5906feb56d7515f1b63b7dac13bf0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/all_acquire_release_volatile.h
implicitLeafInput: b8fe3e88e19212cc34f2e7d758b46108 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/internal/compiler/compilerenvironmentclang.h
implicitLeafInput: 09b97b829a3abd075af2b001e4343e18 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/assert.h
implicitLeafInput: 918fec9148ef22385e634a41b575f4ca /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/google/sparsehash/sparsetable.h
implicitLeafInput: dac235fce38448dba829d549be27add9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/pthread_stop_world.c
implicitLeafInput: 21c348040e5b24ed5e230b221da68630 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/standard_ao_double_t.h
implicitLeafInput: d004935d6b3b1023121771cccfb99f2c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/headers.c
implicitLeafInput: 57875d374c0d204f4827b821efdc6b17 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/typd_mlc.c
implicitLeafInput: 091de864f1061893ea76ee1834409b0f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/threadlocalvalue.h
implicitLeafInput: a921023db7fe716fe59cefa2c4c33a6e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_staticassert.h
implicitLeafInput: e7dcb47031a103b9f85f61dde2f76315 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/gc/writebarriervalidation.h
implicitLeafInput: eabc4a9a6114ba2213a879f3062eb45b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/muteximpl.h
implicitLeafInput: 51f39862302136621b270555a379dfb8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/internal/baselib_spinloop.h
implicitLeafInput: 8cdcc9ed8d4ab8552d524451fbd1810e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/memory.h
implicitLeafInput: a4f0e189da85fd3f7831882c4926736d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/internalcalls.h
implicitLeafInput: 10ed1e3c9e3ba9da0918449f3ffebd19 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/backward_references_hq.h
implicitLeafInput: dc0df92d114798419b94349678b3c39c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/sunc/x86.h
implicitLeafInput: 5db401cfb47ab8bcd34290299194073e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/expected.h
implicitLeafInput: ae33b198d626915635b89c8b4d43ed32 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/datetime.h
implicitLeafInput: f4053a22c5c836342102eb34e100fbdf /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/loadstore/int_atomic_load.h
implicitLeafInput: dac7bf6081702a1c151bcab9ff914add /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/private/dbg_mlc.h
implicitLeafInput: 832543739823dddc9a1908e6335df249 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/il2cpp-config-platforms.h
implicitLeafInput: 0edbbad5decfe018a1fa5b6629676f9e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/readerwriterlockimpl.h
implicitLeafInput: a360cdd4fb52d5e3ca5c64fe616666f1 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/numberformatter.h
implicitLeafInput: 9c6e2202b0dc40e11bf844a2433c5c5e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/baselib.h
implicitLeafInput: 4b6fc0fb0f94757962616b405d1d2a36 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/dec/prefix.h
implicitLeafInput: 2792fbce45b66cd6768ee2da637e33d6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/typedreference.h
implicitLeafInput: 48c6c1c9e92b56c41682b2de90bc78f6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.security.cryptography/rngcryptoserviceprovider.h
implicitLeafInput: 16a1a411ed57bb97998accf0c54f889b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/gc.h
implicitLeafInput: c2341a5d4a9a683060869fefcb6331c5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-metadata.h
implicitLeafInput: 334f3ee03feec270407c0c72dd1fd52e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/noncopyable.h
implicitLeafInput: 36eb5eda1b4fab9de13d0462728c4a3b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/fastreaderreaderwriterlockimpl.h
implicitLeafInput: 9ee3ea2c4e2e0dc427facf819e0cb6cb /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/assembly.h
implicitLeafInput: ac0dd077070516446e07ae317435c0d0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/test_and_set_t_is_ao_t.h
implicitLeafInput: ebb8f818abd0caf6bc3056e98146b218 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.diagnostics/fileversioninfo.h
implicitLeafInput: 0fa1014731311b1d10ce711cb6730397 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/internal/baselib_lock_futexbased.inl.h
implicitLeafInput: fad383639490c9771e4bb5391376a406 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/il2cpphstringreference.h
implicitLeafInput: 094af9a229ffd30a869dafe0ef122a6a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/malloc.c
implicitLeafInput: 1221796505ce5954dbb8b85b7b04a386 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/type.h
implicitLeafInput: ade27fb2bd40f89f449cf0fb95cc402f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.net.networkinformation/linuxnetworkinterface.h
implicitLeafInput: 1e7176aae356adf69616a60f64e942f3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/socketbridge.h
implicitLeafInput: c2440bdac52a0109ec44ecb239f71684 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/runtimemethodinfo.h
implicitLeafInput: 5ecd861629b557c0b451266d44e10b50 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.diagnostics/debugger.h
implicitLeafInput: b679c20a30d19aad6ce3a9b399f97575 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/waithandle.h
implicitLeafInput: 3d843f3c70e5d2aa31f21e9cb341ccfa /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_dynamiclibrary.h
implicitLeafInput: ******************************** /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/gc.h
implicitLeafInput: 1715777758fd65d0b8f0a14951a52fad /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/runtimemethodhandle.h
implicitLeafInput: 5d36ea417df4618adc94dafa4e2c2462 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/sunc/sparc.h
implicitLeafInput: 3d68f72e70723c863b6c830fc59f1884 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/internal/baselib_semaphore_semaphorebased.inl.h
implicitLeafInput: 60672b10f48673b772356a7176750bde /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/path.h
implicitLeafInput: 8dc8756b5526d3ad77da11e8f66908d2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/generalize.h
implicitLeafInput: 957c8d4a35bf34e83db2750412f0576f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/static_dict_lut.h
implicitLeafInput: a101175abf86493c6b458c402fe00201 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/loadstore/int_atomic_store.h
implicitLeafInput: f6a7ae9ce96ad4ce6de4c976853fb3a5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/lasterror.h
implicitLeafInput: 334c6421c9fdcb1fbaaf829620def565 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/codegen/il2cpp-codegen.h
implicitLeafInput: 9817dee4b56a803bcde480e8ba71fc26 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/exception.h
implicitLeafInput: c48bc652fa91b87d7c14285110a1c576 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-runtime-stats.h
implicitLeafInput: e212ff6f8a1eb25cb74e5bcf04cc875d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/metablock.h
implicitLeafInput: 659aa56700fc3af5060dba4c57a63237 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.compilerservices/runtimehelpers.h
implicitLeafInput: cf33f30e4cd5cf7fbdbb48c7dcdd732c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/object.h
implicitLeafInput: 788e55aed8f03dabfb7c645d1af3ec9a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_cpu.h
implicitLeafInput: b9871bf55dc30188c32a1ca891434fcc /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/il2cpp-config-api-platforms.h
implicitLeafInput: e2d62a27a9eae97de406c3e1c728550f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/extra/amigaos.c
implicitLeafInput: 4411c4bb74ff3059ade880dcfe64fd5e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/argiterator.h
implicitLeafInput: 50ff7a29b4dccf50a069dceab592680b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/msftc/x86.h
implicitLeafInput: 288e90cf60f2c532bc6bdd9156c1237f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/cpp/conditionvariable.h
implicitLeafInput: d086a63d421d1f64eaa0ca80a2869681 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/stacktrace.h
implicitLeafInput: cdfccd4232fb6301131d689833c858de /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cppgenericmethodcompare.h
implicitLeafInput: 27612680636ed5d9e40fbc0e63083234 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_lock.h
implicitLeafInput: f6e7f46e438a66607186c9c670a9288a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/dec/huffman.h
implicitLeafInput: 9dc175867909b361d6107839178d2c1e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.threading/waithandle.h
implicitLeafInput: 2840645210875e14a36c2130e93c2c9c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/internal/coremacros.h
implicitLeafInput: 5a0c875c9b82afa97970f16a93fdc4de /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/ao_version.h
implicitLeafInput: c70b2244bf3659a669f29146d1c3c14e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/loadstore/double_atomic_load_store.h
implicitLeafInput: 9601ba3fe337e1d861ad3413ad3fff14 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/vmstringutils.h
implicitLeafInput: 875079c62f72ce72f36187033f9f40a3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/mono/runtime.h
implicitLeafInput: c1fe014227d127f79a82c329ac9751ec /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/comobjectbase.h
implicitLeafInput: 3a8f700ba682c0bb7173173455622458 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/valuetype.h
implicitLeafInput: f78bc590bd4a8ba6943abc7da7ea642e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/mono/threadpool/threadpool-ms.h
implicitLeafInput: 524694693fcfabf91256826465cb42ed /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/find_match_length.h
implicitLeafInput: 5fddc063d5a389b2f2903a075065a51e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/heapsections.c
implicitLeafInput: d7d508549e197ff9359a672c8d4096c0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/specific.c
implicitLeafInput: b89ca35157336a6e41eb26c5bfffdcd1 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/loadstore/char_atomic_store.h
implicitLeafInput: 65b152aed0689684bf2ab71b71dfb917 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/rcw.h
implicitLeafInput: 81e4a2bac75d75916f08b441b2da05f5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/gc_gcj.h
implicitLeafInput: 28ca3e6557f3a3b8cc69ba672d1dedc3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/external/utfcpp/source/utf8/unchecked.h
implicitLeafInput: 92afb74105d4a307c265c77ac1747673 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/internal/verifyplatformenvironment.h
implicitLeafInput: 83f59ab03cb33c62f8ee6c30733991a3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/memoryinformation.h
implicitLeafInput: b2800ac03cb1e99fff6bc006273b3414 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/functional.h
implicitLeafInput: e2078f2a5db14e0c0ba00a10d5607917 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/gcc/arm.h
implicitLeafInput: 01e7af571a53bba0be52165a1d62c819 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.globalization/calendardata.h
implicitLeafInput: 32dfa118f0d15a5a0d1e032e2bd9f372 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cppgenericinstcompare.h
implicitLeafInput: a088963fa235f5dfac54099073f4b36b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.threading/internalthread.h
implicitLeafInput: 66a588573be3cdb24120a831d5419951 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/fastreaderreaderwriterlockimpl.h
implicitLeafInput: 8ae8533e356c749db8a04cf45657c3ed /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/private/darwin_stop_world.h
implicitLeafInput: a0be2de6b20abfba09722cbc777c793b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.net.sockets/socketexception.h
implicitLeafInput: b904671a1bc36bed28ebbf20a7f78725 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/thread.h
implicitLeafInput: 9801288b6a785048dd856106143fd127 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/loadstore/short_acquire_release_volatile.h
implicitLeafInput: 6cdbd6ebbe2e032126632321deb01d20 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/genericmetadata.h
implicitLeafInput: 465e077a849a50611b59e3dcce0bc0cd /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cppgenericclasscompare.h
implicitLeafInput: 28554ab2b6fff056b192c2cded7b92d3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/debugger/il2cpp-api-debugger.h
implicitLeafInput: 032b48f393903d32318bfd5691e740ae /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/il2cpphashset.h
implicitLeafInput: 9e93c28a6075edbc01ad122cd2334f8e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/mono/threadpool/threadpoolmacros.h
implicitLeafInput: 756133bb1caca27f05737806bd3e9ac6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/common/platform.h
implicitLeafInput: c5c414877e0261006c63caf22a55556f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/exceptionsupportstack.h
implicitLeafInput: daf94a2f30526e07406092dad483a117 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/methoddefinitionkey.h
implicitLeafInput: e8e194c89e817df9f595736389cefe41 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/common/version.h
implicitLeafInput: 3bf6402edcf2b6d892f8bb69741a819f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/mono/runtimemarshal.h
implicitLeafInput: 82c9b6c3afb7819a9454b8a6b3b597e3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/entropy_encode.h
implicitLeafInput: b1b560fe36b1accc9190a58ac9d376af /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/mono.security.cryptography/keypairpersistence.h
implicitLeafInput: 39e855526ae8aff7cc3a2130564cca6d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/hpc/ia64.h
implicitLeafInput: 833d2b029776eff8e8f5d23db34213bb /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_alignment.h
implicitLeafInput: bf3bebcd2921fc724f35e0f471f1f859 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/enum.h
implicitLeafInput: 73643db8173c546455d8966d2a8a2b89 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/loadstore/ordered_stores_only.h
implicitLeafInput: 8b4ef711de7cb2648b614fc44bd1fe28 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.io/brokeredfilesystem.h
implicitLeafInput: bf5f1e7783114f81a7e9bedb22b0fda6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/icalls/mscorlib/system/mathf.h
implicitLeafInput: e6c442e5eab00e6b1fd9c8d41ba204c7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.threading/osspecificsynchronizationcontext.h
implicitLeafInput: 5301540e9c4004f8295349a23a4d4f19 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_wakeupfallbackstrategy.h
implicitLeafInput: 218d151a2ae196219d14228198948f77 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_systemfutex.h
implicitLeafInput: 3137d2ac7c824ded51f2c4a2b0b4650a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-object-internals.h
implicitLeafInput: c15767b5541d6a7ed04c1e5fac26a8d8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/pal_platform.h
implicitLeafInput: f0bc71f3be7cc8606b5a786a5f0b50a5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/private/gc_hdrs.h
implicitLeafInput: dde263baf9cb4a0587a62e87de53d953 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/ccw.h
implicitLeafInput: ae393d678d776821260d18a62c7b2454 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/internal/baselib_reentrantlock.inl.h
implicitLeafInput: f2641c24b357a3e5808be1e21a1caf83 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/semaphore.h
implicitLeafInput: 39852b46eacb2347cccec4a2fea9e544 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/field.h
implicitLeafInput: 35b6a09da4d1b8a3fb69e1d5f3a68ebd /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_timer.h
implicitLeafInput: 7adcb816d670a06c8fbdf87cafe402fc /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/mono-structs.h
implicitLeafInput: 3e0a21f134d3b61a1441ca081d4cbc55 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.interopservices/gchandle.h
implicitLeafInput: 40d89cf09a773fabe60aa91a7c654ea1 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/class.h
implicitLeafInput: aafd5b59ccca3a2ad8c71ab497a2d27d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/backward_references.h
implicitLeafInput: 8a66e9847abec1d9614b3272e0925f10 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/javaxfc.h
implicitLeafInput: 7148892524fc6aa0c261014d36a7a638 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/gc/gchandle.h
implicitLeafInput: aa5f531266e31e4c0f2904e4e4cc7087 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/socketimpl.h
implicitLeafInput: 9b611bd87bde08d874d1d676adc51c9d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/threadsafefreelist.h
implicitLeafInput: 30e92a42d476a4877a1ad11e1f9f0a18 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/internal/basictypes.h
implicitLeafInput: 597b897d89b783426daa3907166c3dc8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/genericsharing.h
implicitLeafInput: 3acd79f5a7ccde1e4f66df4f6268d6b4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/vmthreadutils.h
implicitLeafInput: 04ffd2dee316a246f4f165d41ad13c17 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/new_hblk.c
implicitLeafInput: ce601f76422bbea0f6a140b4c6ab7ddc /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/dyn_load.c
implicitLeafInput: 4a693edf8d23f54e2d6f845f6fd72da8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/timezone.h
implicitLeafInput: 6b721c0bcbd1d1af23f566ca11e6a697 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/string.h
implicitLeafInput: 07b4aaa17b789a21a338be8a3405893e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/readerwriterlockimpl.h
implicitLeafInput: eb79adef1106cf1422a4a0396469eead /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/brokeredfilesystem.h
implicitLeafInput: 0a9201c66f15584033e01bf74ce6f911 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/osglobalenums.h
implicitLeafInput: 1f64f169a2cebf3cb8bc18bad7196dff /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-windowsruntime-types.h
implicitLeafInput: bc7214758bb3e47bc433c62479c49165 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/stringutils.h
implicitLeafInput: b61f3abcb48e585b2b06edaab7530d53 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/hash_longest_match_inc.h
implicitLeafInput: c6f87c11071400a5b4dfa8b9f412f786 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/memorypooladdresssanitizer.h
implicitLeafInput: 226aec89bfb3f39c3c63c11556902ae1 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/runtimepropertyinfo.h
implicitLeafInput: 3ebb849637b7dd4035f2a22ffb2566fd /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-config-api.h
implicitLeafInput: f12c189d1b318663414745c0b6d97209 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/environment.h
implicitLeafInput: 2bb6884af31cec6417badac616608b12 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/environment-c-api.h
implicitLeafInput: 1be04fe0056f7374242b8a2418d2155e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/fast_log.h
implicitLeafInput: 62571f5571e18f55520421fe63e96bbe /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/event.h
implicitLeafInput: 4e68d12c38c06279511230d26871a5db /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/all_atomic_load_store.h
implicitLeafInput: 35971efd8b5f3eb86a96d32e10a6f093 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-tokentype.h
implicitLeafInput: acf9c5a780c801e0483a5cd090653eb4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/buffer.h
implicitLeafInput: 2522f33af0bc517c55c8ffd78609eb87 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/genericmethod.h
implicitLeafInput: 63b51e1498e601b03841094b5785d631 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/runtimeeventinfo.h
implicitLeafInput: 2a624952b51f46db54dddb13c57293c6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cppgenericclasshash.h
implicitLeafInput: 7cdec3f34b3d3f234a33d4297d0d48a4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_memory.h
implicitLeafInput: d52c3181e62484ecf4af53eaef3b1cec /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_atomic.h
implicitLeafInput: d92a6a0c704db50983a9e13256cd723c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/cpp/cappedsemaphore.h
implicitLeafInput: 8a53465160c83bc1fa3309f8a8d68c38 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_atomic_macros.h
implicitLeafInput: 6d22b8fee53a31abe6c31d8751ce7e4e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/platforminvoke.h
implicitLeafInput: 9b0f737f6e252f3a7e608ac82f2d5aa0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/metadataloader.h
implicitLeafInput: 82d18b1ed1528e1b67e8f473da959f24 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/google/sparsehash/dense_hash_set.h
implicitLeafInput: 9e552daf2336f9717ee98338868945d6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/conditionvariableimpl.h
implicitLeafInput: c5574bcf6390a80ea9eb9ae0be3b45be /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/internal/compiler/baselib_cpu_gcc.inl.h
implicitLeafInput: f9e777b88737f8ab28c373ddcd90f76d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-blob.h
implicitLeafInput: 4c9e13ffc0193053ba5a7aaeda9929f9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/pthread_support.c
implicitLeafInput: 41e8a7f5a3bb8fb7b5be3d3a06841da8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/module.h
implicitLeafInput: 3e012398cadc8ce24f04c4744f9553e7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/filesystemwatcher.h
implicitLeafInput: cc16f95e473be4fdd840371d880b0653 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/object.h
implicitLeafInput: 772990b1a7f280f821cf39c7cc7cf31c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/fastreaderreaderwriterlock.h
implicitLeafInput: 5539bf80903b86c552c94480a74d5786 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/all_aligned_atomic_load_store.h
implicitLeafInput: ac634ba65fe2582118a2d65318aab6d6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/internal/baselibdeprecateddefinespostinclude.h
implicitLeafInput: 9d0ac2461212c6e6bf583c66111e9b71 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.security.principal/windowsimpersonationcontext.h
implicitLeafInput: 1b80f64110cf3d419c6dbc180bc08251 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/globalmetadatafileinternals.h
implicitLeafInput: d2bd20d34a39cf8762feb35c89152cc0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/std/threadimpl.h
implicitLeafInput: 3c6448f6906b32d227980f86309a8b42 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/property.h
implicitLeafInput: 494491d14f001e15fa6974edde174a74 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/classlibrarypal.h
implicitLeafInput: 4f5f67e3317ba940c68ba12da6ad596b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/marshalstringalloc.h
implicitLeafInput: 80077701453416e6dc6339750f62932c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/google/sparsehash/internal/sparseconfig.h
implicitLeafInput: c7955a08671f70dae5328cdd631623a2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/cpp/internal/scopedacquiremixin.h
implicitLeafInput: 9d191ee967e6053946d70d8b01e04524 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/block_splitter_inc.h
implicitLeafInput: 94b8e8d2c9210477127010f63877676a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/gc_alloc_ptrs.h
implicitLeafInput: 0e343df6556707938188d67159dd58d7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/fnlz_mlc.c
implicitLeafInput: 3822a1efa6f8384d4b9124257982bb11 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.globalization/generated/cultureinfointernalsnet_4_0.h
implicitLeafInput: 09682650f7d278ab1dde8f1d0ab85f19 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/gcc/sparc.h
implicitLeafInput: b120c746e3d0ade8420a9fc305a37fc4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.net.sockets/socket.h
implicitLeafInput: 4fa759044b0270e688b4cf297d757bcc /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/cryptography.h
implicitLeafInput: 846e11e67cdb48e1f38e19d52f6e1244 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/threadimpl.h
implicitLeafInput: 6c7b2c84811b274ca4236ff2c58e126d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/path.h
implicitLeafInput: af75f381a625e9e9923bb99e52daabb2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/char-conversions.h
implicitLeafInput: 327ba83346a20bdf6a6758a042988f94 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.remoting.proxies/realproxy.h
implicitLeafInput: ******************************** /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/readerwriterlock.h
implicitLeafInput: ab3e4e065879d1b6ffd44c11322bfbda /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/xamarin-android/logger.h
implicitLeafInput: 291bc2425732e9ffee14c5b62ac24942 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/fieldlayout.h
implicitLeafInput: da4256a53e0563f359fe65a1d3c8dca1 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-api-types.h
implicitLeafInput: ab690605b2b0ce8a22bab3f5c6f62703 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.diagnostics/process.h
implicitLeafInput: ae906be92d7d77f3d6b7f1cb15b62906 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/cpp/internal/conditionvariable_semaphorebased.inl.h
implicitLeafInput: 4cd14ad7f6c744811e98c9b5413e42fe /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/gc_typed.h
implicitLeafInput: 6bd856fa953b6fb030b9ad10e0dee3b4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/ordered.h
implicitLeafInput: b547a751d14309ca47885e8d2ee79373 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/assemblyname.h
implicitLeafInput: 22a2d03da6eed8a0a114dc9ce2997aa2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/external/utfcpp/source/utf8/cpp11.h
implicitLeafInput: def8548583d7cba327be6e23017cea66 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/gc_tiny_fl.h
implicitLeafInput: f57dc4fd4ff5b7609cf3004f159a3573 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/loadstore/int_acquire_release_volatile.h
implicitLeafInput: 525de50d8b039fde99b6ccfb6ecb5292 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/internal/compiler/baselib_atomic_msvcintrinsics.h
implicitLeafInput: f5a844f763c2bdbafcbd198afd58be8d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/cpp/internal/typetraits.h
implicitLeafInput: d80f8d3842350999a823cb61f9117e8c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/histogram.h
implicitLeafInput: 213cb22decdba0709fa60fd625c612b5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/common/transform.h
implicitLeafInput: 697a4456059fed0893c4f6d5004cc3e9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/finally.h
implicitLeafInput: 68c57ec57e1e90f4e2f37b690c64be4e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/gcj_mlc.c
implicitLeafInput: 58b3d6331fc649fab2c6408abd57cc00 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/threadpoolms.h
implicitLeafInput: 1f97b4bbd25334595663516925fbeb66 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/customattributedatareader.h
implicitLeafInput: c6499e5060ed204834090fee04427dac /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/sha1.h
implicitLeafInput: 72d15bf2f558606596d071eda6f327b4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/output.h
implicitLeafInput: 231b1375af29d94594915a731c416e95 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/runtimefieldinfo.h
implicitLeafInput: 0716fe95eea6a65f53f76d4cac3b1d68 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/cpp/baselib_dynamiclibrary.h
implicitLeafInput: 27000455016b308151cae5b520131630 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/type.h
implicitLeafInput: 253ca39acb42129f336f66e8c1de4873 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/profiler.h
implicitLeafInput: 64ab1c82e292ef5365395810e0fb851c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.globalization/compareinfo.h
implicitLeafInput: 9c8a801f67ba522cb07eaecdeff411ff /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/cachedccwbase.h
implicitLeafInput: 907ee6ee22c38efef7f17ac1415e75cc /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_errorstate.h
implicitLeafInput: d8bbd34762499c9a116d0ec2a9919568 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/nativemethods.h
implicitLeafInput: 02ca1b2306987a11bb7ff137d9bd262c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/systemcertificates.h
implicitLeafInput: b9c84b68bc5abfe3a7b06118ceac1a90 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/ordered_except_wr.h
implicitLeafInput: 8bada4d55124e9140370aeb7a570ff5a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/google/sparsehash/internal/libc_allocator_with_realloc.h
implicitLeafInput: 7351032d202d95ccdedaee5d34c8d3c9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.io/path.h
implicitLeafInput: 68ecc9bd441adf916ec1daccb5f6a3a5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/private/pthread_support.h
implicitLeafInput: 246e6a7827007d45481ca7963ced797e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cppgenericinsthash.h
implicitLeafInput: 9aefb91e4c43a49032621626252213f7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/posixhelpers.h
implicitLeafInput: 6869aeed6604f576d0f8fb194464d0c7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/gcc/m68k.h
implicitLeafInput: 0ea365c8749bea0dace480659e1a09c8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/thread-c-api.h
implicitLeafInput: 7b18ea2f760d1e1360780b03fb07e6af /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/loadstore/short_atomic_load.h
implicitLeafInput: 4effde2e32d347464cb2522aa0ec8b5d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_atomic_llsc.h
implicitLeafInput: 9aa787bae87b14c32b236f08529194fe /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/threadlocalvalueimpl.h
implicitLeafInput: b09b679c8a3e4fe151d565ec1a604376 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/domain.h
implicitLeafInput: 8deb4a04ec6dff95b7839150e8043303 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/google/sparsehash/sparse_hash_set.h
implicitLeafInput: eaa768c0aa50d117990246c8cab67532 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/encoder_dict.h
implicitLeafInput: 67a1c1019de0bee9f18dcd18cce7414b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/customattributecreator.h
implicitLeafInput: 1bb037a250888bf30d72c1b0f58547ef /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/cpp/scopedacquire.h
implicitLeafInput: 1b57182380cd2a24124ba84e295a15f7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/genericcontainer.h
implicitLeafInput: e4ae797f784b4d09987c18f0176e081d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/ringbuffer.h
implicitLeafInput: 602bcd5ffeda2b3cd1a300cbc5229a1e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/private/gc_priv.h
implicitLeafInput: c937f8f5cdf950d9c02849585e35a17b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.threading/timer.h
implicitLeafInput: baefb0e9d561ceee9c0db61507270bb2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/private/gc_atomic_ops.h
implicitLeafInput: 9c08b627ce6a1aa32a0148ea1438f2ac /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-config.h
implicitLeafInput: 60457f489a0f08d01d15e645236f6acb /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/cpp/internal/conditionvariabledata_semaphorebased.inl.h
implicitLeafInput: 24678a066ab7222dd51797c533034339 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/lasterror.h
implicitLeafInput: d26dd557c06e2ed569647d35ad9d6d03 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/arraymetadata.h
implicitLeafInput: ce026c79ad4b0f2f3162d87612b227be /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/internal/compiler/compilerenvironmentmsvc.h
implicitLeafInput: 36dfa11be9cd64bbbed2717ca8899de5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/loadstore/atomic_store.h
implicitLeafInput: de243eb8f317fdbcff48371b99a99c26 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/internal/compiler/baselib_cpu_msvc.inl.h
implicitLeafInput: c9d34bcf7346dbb3861e786e3b6e7b95 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/ptr_chck.c
implicitLeafInput: 25c537a21680a772b4b26627c6ba0f3b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cpptypecompare.h
implicitLeafInput: c117e340fe6c25f2611491be4025ccd4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/cpuinfo.h
implicitLeafInput: bc8d6ff17772dbabcafa9a6212f87ce7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/synchronizationcontext.h
implicitLeafInput: ef66ff58b5d135c09063fe8a96a3945d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/string.h
implicitLeafInput: c93448fc39ccdb02ea820e8fc24063d2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-class-internals.h
implicitLeafInput: ab2a874149a7ad979720e51de3f1ff85 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/cpp/mpmc_node_stack.h
implicitLeafInput: a8c9e569700de24c270252b33aa9c0fd /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/cpp/lock.h
implicitLeafInput: f96568381613a5d9e9c14a753a96aafc /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/gc_inline.h
implicitLeafInput: 526dc22b072d5a528e9d710fffd95431 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_reentrantlock.h
implicitLeafInput: c44e8da73f9aae08740076cdbfd93e41 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/read_ordered.h
implicitLeafInput: 545b86209839bcedb307a2c46e4e2ae6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/xamarin-android/monodroid.h
implicitLeafInput: 0ad84d2dfeeb8e6231aa3560510ba995 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-mono-api-functions.h
implicitLeafInput: 429adc5dee82692d0eec1659395198cb /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_cpu_features.h
implicitLeafInput: 7d17566bcb69cbd4ebb0da93b5513f8d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/bit_cost.h
implicitLeafInput: d658813da2967669a024a1c9f99c9ce9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/private/thread_local_alloc.h
implicitLeafInput: acbdf1019fec2d53936bdb0200183322 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/internal/compiler/compilerenvironmentgcc.h
implicitLeafInput: fb65d38fdfdbe85745c11ef7701a8b0e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/gcc/ia64.h
implicitLeafInput: ed88ce47dbf201439d243af6ac06e252 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/memoryread.h
implicitLeafInput: a956e81f154ef8b0e4cfb7cb02b101aa /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/generalize-arithm.h
implicitLeafInput: 647ad4000cb36471e1a2e713d0c6603c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/osx/pal_darwin.h
implicitLeafInput: 2d96fb5793ee09aade77ab00bc8223ec /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/appdomain.h
implicitLeafInput: edb27ab5c5f78592fd892bf4dc86e8cb /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/gcc/cris.h
implicitLeafInput: 5b5507aae332cf6edd85201487e93c28 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/gc_vector.h
implicitLeafInput: 9557eb92db8abcf3662c2515f8bfc627 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/nativesymbol.h
implicitLeafInput: 6a7011e905e62dbc06c4c29337905676 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/assembly.h
implicitLeafInput: 8d5b148de358ed888f577913c794427f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/gcc/generic.h
implicitLeafInput: 3868f77e58b25183fcdeb1b6e00b1be8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.globalization/compareoptions.h
implicitLeafInput: 06d2f5f38cec223154c85559d02edc1f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_errorcode.h
implicitLeafInput: be512b79ac20795220231883cbd93e5d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/gc_pthread_redirects.h
implicitLeafInput: 5652d86a8ddb1f7f49179d277988a2ab /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/mallocx.c
implicitLeafInput: 00b3bda5cf8ade8063422a13060dbc14 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/internal/architecturedetection.h
implicitLeafInput: c2e61163597aa5403f1ebc91a490a741 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/console.h
implicitLeafInput: fa532c825747289adf99a87e4c1c6916 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/loadstore/ordered_loads_only.h
implicitLeafInput: d426d6af57e5b36fbb1936cf0a812417 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.diagnostics/defaulttracelistener.h
implicitLeafInput: 2b76cf200fce4700e75723cf9ccada68 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/com.h
implicitLeafInput: e161f67393973133c490144520daf70a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/internal/compiler/baselib_atomic_gcc.h
implicitLeafInput: 9b0fd14fb2154d86f0719a158b0994cc /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/gc/allocator.h
implicitLeafInput: 939df7312d22ddd5b86d75b0c38a379d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/metadatalock.h
implicitLeafInput: 4b1a7a258dae1daa1a056cf23c31962c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/cpp/internal/conditionvariable_futexbased.inl.h
implicitLeafInput: 0835917555ae8adae58b95aa080de9f3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/common/context.h
implicitLeafInput: b843015a30c0cc060fbc140afe492d80 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cppgenericcontextcompare.h
implicitLeafInput: 22ef7fdc467f1ed734dbecc9a77a1a34 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/enum.h
implicitLeafInput: c3f3760095ed2a8491dddeea8591ed36 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/private/pthread_stop_world.h
implicitLeafInput: a0aeb36b593cacb0e6f81b80be33fc6a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/gc_backptr.h
implicitLeafInput: ffe3c1b397dee1ed3a2d6657e0ee0cc6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_countdowntimer.h
implicitLeafInput: 9cae7601a43456c7ea9a233a4d76a27c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/microsoft.win32/nativemethods.h
implicitLeafInput: 268d987e9892049ffdce8b00ac305fb4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/liveness.h
implicitLeafInput: 489f69048048203da7d6691a2495b4ab /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/conditionvariable.h
implicitLeafInput: 2dd96064db99986833abbd069fa65464 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/time.h
implicitLeafInput: 402319e1abbe4e11503e3d1947fee410 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/stringviewutils.h
implicitLeafInput: 7123b647e41fe5405e0157851e188842 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/winrt/referencecounter.h
implicitLeafInput: 79b15a618356bb4e1e27405ff640e94b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/il2cpphashmap.h
implicitLeafInput: 4e8f0e5e9b7965cd0427bccb85768f43 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.net.networkinformation/macosipinterfaceproperties.h
implicitLeafInput: 71c2d03cdd892493c3329683e7eaf4cc /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.security.policy/evidence.h
implicitLeafInput: 6cedafb6cbed854fc6f1965a8b442639 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/gcc/mips.h
implicitLeafInput: 44d2d9656c5649101b193235c2fcdf73 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/hash_longest_match_quickly_inc.h
implicitLeafInput: 632970714922bc7bfd52e4258f32ca7a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/private/gc_pmark.h
implicitLeafInput: ebeb2107c0b79c8fa6642ac003c1ebc7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/windowsheaders.h
implicitLeafInput: fa5c3f376ce92f261329778a7334a338 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/clrconfig.h
implicitLeafInput: 00b0319884c603e2641cab94bfad2053 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/reflection.h
implicitLeafInput: bc7f3a42f66cf48f24e07942d64e66e2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.threading/nativeeventcalls.h
implicitLeafInput: 85135f3ca79370f115a410c71ba5c972 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/c-api/file-c-api.h
implicitLeafInput: dbff92dab22b9639fcb9e0ac2ba81b21 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/internal/compiler/baselib_atomic_gcc_patch.h
implicitLeafInput: 01f1f6fe079bce1034d328889d67775e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.io/filesystemwatcher.h
implicitLeafInput: 0640b7446774f0622f78d5034b0bd926 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/waitobject.h
implicitLeafInput: 0d0368428553c1893ae46ba31ab817eb /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/templateutils.h
implicitLeafInput: 53ca34b6308ae541dfdfa6a032f4777b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/loadstore/atomic_load.h
implicitLeafInput: 0f2c1bccbceae3c761de4cb5a2ba7763 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/cpp/mpmc_node.h
implicitLeafInput: ebcebf0be6b66471d5df371d1bc10161 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_semaphore.h
implicitLeafInput: c9cfc17c54aaca72e020d44da22010e8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/filehandle.h
implicitLeafInput: e7ddf35e620bbd1a0c4e383e9d0b0606 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-tabledefs.h
implicitLeafInput: 07b5b094de73027e573dab40ae116002 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/consoleextension.h
implicitLeafInput: 7a43ef73dfc9a50fdaf8f8add39c20ce /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/cpp/atomic.h
implicitLeafInput: 0f55b200e18dcd5205f83777400cee9a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/external/utfcpp/source/utf8/cpp17.h
implicitLeafInput: 1e88d1d3e70fc33802e8286292c6ee99 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/icalls/mscorlib/system.threading/interlocked.h
implicitLeafInput: 647728d169899663e8d490e047ab1654 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.versioning/versioninghelper.h
implicitLeafInput: f438aff92f28dc8472c9e9e6f420671b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.threading/monitor.h
implicitLeafInput: 1a81a1468f5013223bc01790e9f27c50 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/internal/baselib_enumsizecheck.h
implicitLeafInput: 1e731832fac8a8a3a615996db4adeed9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/internal/assert.h
implicitLeafInput: 92b80edd2b82ff7a7eca6cad68683101 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/gc_disclaim.h
implicitLeafInput: 44fb6817e2197eec90acbb780c2d50d0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.threading/thread.h
implicitLeafInput: 31fc0e434b428de7a980932c468a6fba /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/finalize.c
implicitLeafInput: 3c93157e956dead1311b2625caca0927 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/generalize-small.h
implicitLeafInput: cf1babaede6ddb23a175492afa1d8a29 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.io/driveinfo.h
implicitLeafInput: bd2f4577ef5bb3cead357430f6f942f7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/hashutils.h
implicitLeafInput: 4228a171cec38f5d512ba4e06020d4a4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.remoting.messaging/monomethodmessage.h
implicitLeafInput: 48e20be957040a42d4f936154146aa26 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/internal/compiler/baselib_atomic_llsc_gcc.inl.h
implicitLeafInput: ff93ea772cfa324bdeb317c65683df84 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/encoding.h
implicitLeafInput: c7d56c023506ac22f0212a0a5aad4034 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/bit_cost_inc.h
implicitLeafInput: 5abe6374c151bb00662da29dee4bb1d6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.text/encodinghelper.h
implicitLeafInput: 6a3f1ce6053eeb5ce4c82025e8ee7f44 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.text/normalization.h
implicitLeafInput: 5df5d8833a1461555d16c51a70a699df /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/gc/appendonlygchashmap.h
implicitLeafInput: ac649de8c19aee4f2fa2a3e30cc7e144 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.globalization/cultureinfointernals.h
implicitLeafInput: e12d9a617420044f06c384b74eed4956 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/block_splitter.h
implicitLeafInput: 5dac9e4bf3a19fdbe399b69818157db7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/mono.unity/unitytls.h
implicitLeafInput: 27b5c200e92eb76e3c9a71c20425d9a1 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/common/dictionary.h
implicitLeafInput: b3afcf1e93eaa543005accac4e449a39 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/crashhelpers.h
implicitLeafInput: 7e6375e606a2082dd6e01a7824d9ece3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/androidruntime.h
implicitLeafInput: eb2642a46a6a008109f67402928e7f21 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/monitor.h
implicitLeafInput: c5c394ac8f89031eaf59eb29a830b52a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/baselib/fastreaderreaderwriterlockimpl.h
implicitLeafInput: 227c012b06b5ac3a8c9f931918440628 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/include/brotli/encode.h
implicitLeafInput: 9fab74ffb995b5f9e432f82e75fa4d6c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_systemsemaphore.h
implicitLeafInput: ceb6682284a7638853b0ec806ba0b3ce /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/muteximpl.h
implicitLeafInput: 5cc56a0db1666c58dea9ef09fd49e053 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/globalmetadata.h
implicitLeafInput: 550e2b400c87b3db57471a1d648f29c4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/compress_fragment.h
implicitLeafInput: 9b054858e73157b5bf5665d8277fea33 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/entropy_encode_static.h
implicitLeafInput: e3ee5c29fafbbf1172eb3df5fc44c4aa /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/mono/monoposixhelper.h
implicitLeafInput: cfc30023cc83a28082f5800420c620ad /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/mono/threadpool/threadpooldatastructures.h
implicitLeafInput: 28b6eb61c87fe2cb98e3ac6daad10a3b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/armcc/arm_v6.h
implicitLeafInput: 77bd69179e6a06d0172827a5f31d2c3c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/dbg_mlc.c
implicitLeafInput: 877dbb3322cc607b41ced6ccb9e97c12 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/private/specific.h
implicitLeafInput: 3d24597421649db23e734046c0040391 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/hash_rolling_inc.h
implicitLeafInput: 6a34fb0be9d073d5b23e803263ba2323 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/google/sparsehash/dense_hash_map.h
implicitLeafInput: 9b6bdb44eed61657bb53ba7710e5fd18 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/fastreaderreaderwriterlockimpl.h
implicitLeafInput: be295cf06dc940a96793d9d94f8194e7 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/locale.h
implicitLeafInput: 1ef7c283ce3ca6382c47cd16e6c9a969 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.diagnostics/stopwatch.h
implicitLeafInput: 1008079696eba2189f7cc8025f0c4291 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/gc_dlopen.c
implicitLeafInput: 3b07d972cef8e4ccb540c147ebf94791 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/callonce.h
implicitLeafInput: 2df76ab264c8b9f98b5b09ef38b684e8 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/blobreader.h
implicitLeafInput: 512e58446488549474d1f3ca250c7a29 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/winrt/win32apisharedemulation.h
implicitLeafInput: cd06b3ef3aa2c48e5dc65b447861d2f9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/eventinfo.h
implicitLeafInput: faa281bd78a32123736e2530c698c62b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/hash.h
implicitLeafInput: 8c2bdb38f5a8e696d0d36396f2b1d0c3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/literal_cost.h
implicitLeafInput: 8b7243f746d504f0ec49ce8a3c08286a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_thread.h
implicitLeafInput: 8ade752f1ad1e774535a6a25f42460c0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/include/brotli/decode.h
implicitLeafInput: 3ee91a2b3039a97b9b1ed3028fe9545f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/mark_rts.c
implicitLeafInput: c2222bf7e33e0d07240c2bfa1c0041a5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/google/sparsehash/internal/densehashtable.h
implicitLeafInput: 1e001c3a0654535a0267972432a2c927 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/include/brotli/port.h
implicitLeafInput: 38a2fded888c2594ccd2f529eb74b654 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/gcc/generic-small.h
implicitLeafInput: 4480827556d8b0d26d78309a9402bb24 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_debug.h
implicitLeafInput: 92529adc588a88c8698e0103d359dd2d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/gcc/avr32.h
implicitLeafInput: d65b522a04fc6bfc1e2ba905e02107e2 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/pthread_start.c
implicitLeafInput: beb2d55444e13ef95ac3c21c1719621c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/cpp/countdowntimer.h
implicitLeafInput: 517aec7c383ccef75b8c3c4776328e7a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/gc/writebarrier.h
implicitLeafInput: db540c73241918f357b83f4c9e279350 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/random.h
implicitLeafInput: e60fd0d4c49ce0ac788559beb740d30d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/image.h
implicitLeafInput: bd1b4244fd57ca13fb5adc201b732ecc /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/baselib_atomic_typesafe.h
implicitLeafInput: 33cfaf01979201f3a34da6f61ae50ae4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/win32/muteximpl.h
implicitLeafInput: a4abf6e7c47034465dbe7aeb0f8ba523 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/socket.h
implicitLeafInput: 2b7039bcfdd32a8f0a2f79b2e64f41c3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/xamarin-android/xamarin_getifaddrs.h
implicitLeafInput: b1bf76e3fc9e076f15cff5799b37b583 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/gcc/x86.h
implicitLeafInput: 68eb1e7ae5b1caed8ce5651640d75eb3 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/winrt/win32apiwinrtemulation.h
implicitLeafInput: c3944728c9ea94e27696671d9ad4ea12 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/gc/garbagecollector.h
implicitLeafInput: d018ab242e635888584e37482196af34 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/waitstatus.h
implicitLeafInput: e066b98cce3c8e48e973bc6bba1e63b9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/obj_map.c
implicitLeafInput: a93ef979a8202402851af0fb33c58854 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/prefix.h
implicitLeafInput: d0de9787dc8b7a5a5bfd831dc4f88646 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/internal/platformenvironment.h
implicitLeafInput: e5ab80c2c99924b74e538b456d9d5d4e /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/gcc/hexagon.h
implicitLeafInput: 230deac40e69ce472b7df688e591815f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/ao_t_is_int.h
implicitLeafInput: 5a333970550f1c9cbf421b0d210a4c4a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime/runtimeimports.h
implicitLeafInput: 093dfedc98762cfd458e4ccfe46f21b0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/runtimeparameterinfo.h
implicitLeafInput: f04245192af498b1ef06aba9b4255533 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.interopservices/marshal.h
implicitLeafInput: 4d1ca690b546f5d70b4c3767701effc9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/currentsystemtimezone.h
implicitLeafInput: f43535059a05eb341b79c4f2da7566b5 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/marshalalloc.h
implicitLeafInput: 3a410148f813264baece3edffc68519a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/msftc/arm.h
implicitLeafInput: 40870316289616018080741b586459be /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/classlibrarypal/brotli/enc/static_dict.h
implicitLeafInput: 529729a4f51f8f5aaed50207970b364f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/gc_version.h
implicitLeafInput: 377caa70e13fb48654cfc3dc412518dc /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/misc.c
implicitLeafInput: 72070fa7afc2796a154e9ad0412deee0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm-utils/icalls/mscorlib/system/math.h
implicitLeafInput: d2c634836c3c1f52d90fd54daa2f8a8b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/runtimeassembly.h
implicitLeafInput: afdf8461c56baeb778d910d473cd9df1 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/darwin_stop_world.c
implicitLeafInput: 9ea39ac2b89fa90801b2a75072a01ea0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/runtime.h
implicitLeafInput: f4edc125db0e378f2edb4171314f2706 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/c/internal/compiler/baselib_atomic_msvc.h
implicitLeafInput: b468bbae32c61bebaca1e3b1bb13c600 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.net/dns.h
implicitLeafInput: 4f390094f8f8c317135d91177ddd0c9d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cppsignature.h
implicitLeafInput: 20ec98f3682f2cae9213734b7a5563f6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/generic/eventimpl.h
implicitLeafInput: faa32ecb649ccd361f42e1112bb8ef6d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/posix/conditionvariableimpl.h
implicitLeafInput: 77a37c1dba6ef355c4317460e00863c4 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.runtime.interopservices/runtimeinformation.h
implicitLeafInput: ******************************** /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/include/private/gcconfig.h
implicitLeafInput: dc9fbca5878825ec109c198f3a186f3d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/metadata/il2cppgenericmethodhash.h
implicitLeafInput: dd28b8ff2b1d1919477e869a1a1202a9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/google/sparsehash/sparse_hash_map.h
implicitLeafInput: 73503feaa4f44ef1a5ba26963cfcbde9 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/monomethodinfo.h
implicitLeafInput: d38c44cd913182cdd6a50cef2764597d /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/logging.h
implicitLeafInput: 9a29491846c1f6e963fd5bdd22561a66 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/il2cpp-runtime-metadata.h
implicitLeafInput: 86ccb7396f87627f96cad5dc85bf196f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/keywrapper.h
implicitLeafInput: 1d0131adbc6068ed76ad78d62b9de977 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system/runtimetype.h
implicitLeafInput: ddfe98f7d090a88b78c83ddbb717fe03 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.globalization/cultureinfotables.h
implicitLeafInput: 92164d9ee1c4b59efebfaa65012fa23a /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.reflection/fieldinfo.h
implicitLeafInput: 94af80be1a1b52934fe69ad79547fda6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.security.principal/windowsidentity.h
implicitLeafInput: 6e866225c429ef7754dfe8ed87da167b /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/interop.h
implicitLeafInput: baf1ef1cf5df1889a142f814a8c61737 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/vm/parameter.h
implicitLeafInput: 9b57876e1780cd879387a72e35cc4e51 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/system/system.threading/semaphore.h
implicitLeafInput: 8505d855971a2f1b8f45f997109c58b6 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/utils/marshalingutils.h
implicitLeafInput: 9c1ffe123427e43dbed4a2ae17786421 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/gcc/alpha.h
implicitLeafInput: 4457c483d2a0f8297310edee52308d6f /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/icalls/mscorlib/system.io/monoio.h
implicitLeafInput: 193640afef0e876e2eac074596c1bcf0 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/codegen/il2cpp-codegen-metadata.h
implicitLeafInput: a8e1f6bffb0193643f2f91f7e76bb0bd /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/gcc/generic-arithm.h
implicitLeafInput: 55312d5672a2702867ff26aeb128f976 /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/baselib/include/external/utfcpp/source/utf8/checked.h
implicitLeafInput: cb1ba6b4529bdf8a7f195878679f3ffd /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/external/bdwgc/libatomic_ops/src/atomic_ops/sysdeps/icc/ia64.h
implicitLeafInput: bebb5ffb569daf9313e8a3e092b2902c /applications/unity/hub/editor/6000.0.45f1/unity.app/contents/il2cpp/libil2cpp/os/emscripten/pal_emscripten.h
Resulting Combined Hash: b1bd870809710aa99d5dbe64c3368bf0
