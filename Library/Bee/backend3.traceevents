{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1751913069702563, "dur":24337, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751913069726904, "dur":342, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751913069727309, "dur":98, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751913069727413, "dur":3430, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751913069730845, "dur":13106350, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751913082837426, "dur":2649, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1751913069727365, "dur":3485, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751913069731055, "dur":82, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer" }}
,{ "pid":12345, "tid":1, "ts":1751913069731137, "dur":222, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/Apk" }}
,{ "pid":12345, "tid":1, "ts":1751913069731359, "dur":192, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/Apk" }}
,{ "pid":12345, "tid":1, "ts":1751913069731551, "dur":196, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/Apk" }}
,{ "pid":12345, "tid":1, "ts":1751913069731785, "dur":78, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/Source/GameActivity" }}
,{ "pid":12345, "tid":1, "ts":1751913069731904, "dur":963, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/build/deploy" }}
,{ "pid":12345, "tid":1, "ts":1751913069732934, "dur":58, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1751913069733044, "dur":50, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1751913069733117, "dur":61, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1751913069733178, "dur":3352, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp" }}
,{ "pid":12345, "tid":1, "ts":1751913069736819, "dur":54, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1751913069736874, "dur":92, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1751913069736966, "dur":53, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1751913069737019, "dur":50, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1751913069737069, "dur":141, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1751913069737361, "dur":134, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1751913069737495, "dur":53, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1751913069730856, "dur":6748, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751913069737604, "dur":6070, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"UnityLinker /Users/<USER>/Desktop/WebBrowser/Library/Bee/artifacts/unitylinker_xy1a.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1751913069743674, "dur":456, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751913069746949, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Mono.Security.dll" }}
,{ "pid":12345, "tid":1, "ts":1751913069747062, "dur":551, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/mscorlib.dll" }}
,{ "pid":12345, "tid":1, "ts":1751913069747694, "dur":177, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/System.dll" }}
,{ "pid":12345, "tid":1, "ts":1751913069747928, "dur":138, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Unity.Burst.dll" }}
,{ "pid":12345, "tid":1, "ts":1751913069748091, "dur":130, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Unity.Collections.dll" }}
,{ "pid":12345, "tid":1, "ts":1751913069748243, "dur":309, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":1, "ts":1751913069748577, "dur":93, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":1, "ts":1751913069748761, "dur":474, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":1, "ts":1751913069749240, "dur":231, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1751913069749506, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1751913069749582, "dur":213, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Unity.RenderPipelines.Universal.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1751913069749799, "dur":128, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":1, "ts":1751913069750029, "dur":170, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":1, "ts":1751913069745490, "dur":4714, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"IL2CPP_CodeGen /Users/<USER>/Desktop/WebBrowser/Library/Bee/artifacts/il2cpp_conv_l4eq.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1751913069750206, "dur":118, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751913076845631, "dur":1249, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751913069750777, "dur":7096208, "ph":"X", "name": "IL2CPP_CodeGen",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/artifacts/il2cpp_conv_l4eq.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1751913076849066, "dur":5988128, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751913069727369, "dur":3489, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751913069730861, "dur":994, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751913069731855, "dur":938, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751913069732793, "dur":938, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751913069733731, "dur":952, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751913069734683, "dur":158, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751913069734842, "dur":2794, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751913069738043, "dur":259, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/Unity.TextMeshPro-FeaturesChecked.txt_c7od.info" }}
,{ "pid":12345, "tid":2, "ts":1751913069738353, "dur":329, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/System.Xml-FeaturesChecked.txt_wnnz.info" }}
,{ "pid":12345, "tid":2, "ts":1751913069738850, "dur":156, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/Unity.InputSystem-FeaturesChecked.txt_ivdb.info" }}
,{ "pid":12345, "tid":2, "ts":1751913069739061, "dur":218, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.SpriteShapeModule-FeaturesChecked.txt_zrrh.info" }}
,{ "pid":12345, "tid":2, "ts":1751913069739401, "dur":279, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt_z4m3.info" }}
,{ "pid":12345, "tid":2, "ts":1751913069739707, "dur":274, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt_4rfo.info" }}
,{ "pid":12345, "tid":2, "ts":1751913069740128, "dur":238, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.Physics2DModule-FeaturesChecked.txt_45hf.info" }}
,{ "pid":12345, "tid":2, "ts":1751913069740368, "dur":78, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751913069740448, "dur":200, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt_rzjr.info" }}
,{ "pid":12345, "tid":2, "ts":1751913069740649, "dur":1132, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ActionGenerateProjectFiles Library/Bee/artifacts/Android/IntermediateFiles.txt (+44 others)" }}
,{ "pid":12345, "tid":2, "ts":1751913069741817, "dur":1227, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751913069743047, "dur":2465, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751913069745512, "dur":353, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.RenderPipeline.Universal.ShaderLibrary-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1751913069745899, "dur":321, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751913069746326, "dur":116425, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/Unity.RenderPipeline.Universal.ShaderLibrary-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1751913069864272, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1751913069864379, "dur":128034, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1751913069994825, "dur":329, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1751913069995242, "dur":416548, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1751913070607342, "dur":74, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751913070411963, "dur":195463, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/Unity.TextMeshPro-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1751913070607630, "dur":264869, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.UI-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1751913070872703, "dur":111219, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1751913071045642, "dur":988, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071046686, "dur":344, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/System.Core.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071047072, "dur":199, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipelines.Universal.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071047276, "dur":458, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Unity.RenderPipelines.GPUDriven.Runtime.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071047271, "dur":464, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipelines.GPUDriven.Runtime.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071047740, "dur":574, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipelines.GPUDriven.Runtime.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071048323, "dur":1793, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Unity.Collections.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071048316, "dur":1800, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Collections.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071050486, "dur":273, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Collections.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071050772, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Unity.InputSystem.ForUI.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071050761, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.InputSystem.ForUI.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071051552, "dur":210, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.InputSystem.ForUI.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071051770, "dur":171, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071051948, "dur":272, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/System.Xml.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071052234, "dur":222, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/System.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071052464, "dur":322, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipeline.Universal.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071052795, "dur":283, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UnityAnalyticsCommonModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071053083, "dur":100, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Unity.Burst.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071053079, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Burst.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071053401, "dur":162, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751913071053191, "dur":376, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Burst.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071053600, "dur":186, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AnimationModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071053820, "dur":186, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AnimationModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071054038, "dur":162, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VFXModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071054206, "dur":238, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VFXModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071055548, "dur":240, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071055834, "dur":168, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071056041, "dur":152, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AIModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071056223, "dur":133, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VRModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071056362, "dur":143, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VRModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071056510, "dur":139, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071056696, "dur":159, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.XRModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071056861, "dur":248, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AIModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071057117, "dur":248, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.XRModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071057405, "dur":157, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SharedInternalsModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071057568, "dur":235, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputLegacyModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071057808, "dur":198, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.SpriteShapeModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071057803, "dur":203, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SpriteShapeModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071058015, "dur":7438, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SpriteShapeModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071065482, "dur":11482, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.JSONSerializeModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071076976, "dur":544, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.GridModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071077595, "dur":258, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.CoreModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071077858, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.GridModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071077855, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.GridModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071078671, "dur":224, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.GridModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071078906, "dur":308, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreFontEngineModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071079223, "dur":265, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreTextEngineModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071079493, "dur":932, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TextCoreFontEngineModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071079489, "dur":937, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreFontEngineModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071080453, "dur":209, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreFontEngineModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071080667, "dur":720, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TextCoreTextEngineModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071080662, "dur":725, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreTextEngineModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071081393, "dur":834, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreTextEngineModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071082240, "dur":64, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AudioModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071082230, "dur":75, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AudioModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071082325, "dur":382, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AudioModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071082716, "dur":286, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.IMGUIModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071083011, "dur":291, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071083308, "dur":312, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AudioModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071083663, "dur":364, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SubsystemsModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071084082, "dur":429, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AndroidJNIModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071084517, "dur":392, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputForUIModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071084915, "dur":369, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.PropertiesModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071085299, "dur":312, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AndroidJNIModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071085617, "dur":292, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIElementsModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071085948, "dur":707, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UnityAnalyticsCommonModule.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071086665, "dur":289, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071086962, "dur":161, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/System.Configuration.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071087130, "dur":152, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071087286, "dur":2366, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Unity.Mathematics.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071087283, "dur":2369, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Mathematics.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071089654, "dur":403, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751913071090060, "dur":229, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Mathematics.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071090299, "dur":174, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Mono.Security.dll" }}
,{ "pid":12345, "tid":2, "ts":1751913071090481, "dur":206, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityLinkerToEditorData.json" }}
,{ "pid":12345, "tid":2, "ts":1751913071090692, "dur":52, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071090687, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071090752, "dur":1742, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071092498, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071092564, "dur":362, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751913071092926, "dur":629041, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751913071722226, "dur":88888, "ph":"X", "name": "Stripping",  "args": { "detail":"Library/Bee/artifacts/Android/libunity/armeabi-v7a/unstripped/libunity.so -> Library/Bee/artifacts/Android/libunity/armeabi-v7a/stripped/libunity.so Library/Bee/artifacts/Android/libunity/armeabi-v7a/stripped/libunity.so" }}
,{ "pid":12345, "tid":2, "ts":1751913071811131, "dur":5037998, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751913076849154, "dur":5988001, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751913069727370, "dur":3496, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751913069730870, "dur":992, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751913069731862, "dur":943, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751913069732805, "dur":937, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751913069733742, "dur":950, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751913069734692, "dur":151, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751913069734843, "dur":2783, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751913069738661, "dur":209, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/System.Configuration-FeaturesChecked.txt_vfp9.info" }}
,{ "pid":12345, "tid":3, "ts":1751913069739011, "dur":204, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.AudioModule-FeaturesChecked.txt_tbfa.info" }}
,{ "pid":12345, "tid":3, "ts":1751913069739275, "dur":194, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.VRModule-FeaturesChecked.txt_nz9x.info" }}
,{ "pid":12345, "tid":3, "ts":1751913069739569, "dur":243, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.UI-FeaturesChecked.txt_v0bu.info" }}
,{ "pid":12345, "tid":3, "ts":1751913069740010, "dur":279, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.SharedInternalsModule-FeaturesChecked.txt_sm9d.info" }}
,{ "pid":12345, "tid":3, "ts":1751913069740293, "dur":229, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.InputForUIModule-FeaturesChecked.txt_awp5.info" }}
,{ "pid":12345, "tid":3, "ts":1751913069740673, "dur":190, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/IntermediateLauncherManifestDiag.txt_4oyd.info" }}
,{ "pid":12345, "tid":3, "ts":1751913069740863, "dur":79, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/symbols/arm64-v8a/libmain.so" }}
,{ "pid":12345, "tid":3, "ts":1751913069740943, "dur":93, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/symbols/arm64-v8a/libmain.so" }}
,{ "pid":12345, "tid":3, "ts":1751913069741068, "dur":78, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/styles.xml" }}
,{ "pid":12345, "tid":3, "ts":1751913069741191, "dur":84, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v30/freeformwindow.xml" }}
,{ "pid":12345, "tid":3, "ts":1751913069741275, "dur":95, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v30/freeformwindow.xml" }}
,{ "pid":12345, "tid":3, "ts":1751913069741405, "dur":63, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_background.png" }}
,{ "pid":12345, "tid":3, "ts":1751913069741468, "dur":131, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_background.png" }}
,{ "pid":12345, "tid":3, "ts":1751913069741642, "dur":84, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/RuntimeInitializeOnLoads.json" }}
,{ "pid":12345, "tid":3, "ts":1751913069741762, "dur":3735, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751913069745497, "dur":367, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ClassRegistrationGenerator /Users/<USER>/Desktop/WebBrowser/Library/Bee/artifacts/Android/il2cppOutput/UnityClassRegistration.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751913069745869, "dur":99, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751913069746657, "dur":222605, "ph":"X", "name": "ClassRegistrationGenerator",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/artifacts/Android/il2cppOutput/UnityClassRegistration.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751913069969395, "dur":60, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Release/StaticLibs/arm64-v8a/compile.rsp" }}
,{ "pid":12345, "tid":3, "ts":1751913069969393, "dur":101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Compile UnityClassRegistration Library/Bee/artifacts/Android/libunity/arm64-v8a/UnityClassRegistration.o" }}
,{ "pid":12345, "tid":3, "ts":1751913070008247, "dur":63, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751913069969630, "dur":38696, "ph":"X", "name": "Compile",  "args": { "detail":"UnityClassRegistration Library/Bee/artifacts/Android/libunity/arm64-v8a/UnityClassRegistration.o" }}
,{ "pid":12345, "tid":3, "ts":1751913070008360, "dur":84, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1751913070008487, "dur":170642, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1751913070179322, "dur":317675, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1751913070497206, "dur":114787, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.TerrainModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1751913070612272, "dur":64, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.InputModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1751913070612389, "dur":243545, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.InputModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1751913070856106, "dur":109993, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1751913070966356, "dur":319075, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.AnimationModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1751913071285885, "dur":5563304, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751913076849194, "dur":5987936, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751913069727372, "dur":3501, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751913069730876, "dur":1012, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751913069731888, "dur":929, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751913069732817, "dur":941, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751913069733758, "dur":963, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751913069734721, "dur":78, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751913069734799, "dur":2835, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751913069737911, "dur":224, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.Universal.Runtime-FeaturesChecked.txt_7fk5.info" }}
,{ "pid":12345, "tid":4, "ts":1751913069738279, "dur":198, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/System.Core-FeaturesChecked.txt_k2yd.info" }}
,{ "pid":12345, "tid":4, "ts":1751913069738601, "dur":151, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/Unity.Burst.Unsafe-FeaturesChecked.txt_9x4w.info" }}
,{ "pid":12345, "tid":4, "ts":1751913069738907, "dur":235, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.AnimationModule-FeaturesChecked.txt_3sdh.info" }}
,{ "pid":12345, "tid":4, "ts":1751913069739295, "dur":280, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/Unity.RenderPipeline.Universal.ShaderLibrary-FeaturesChecked.txt_erxn.info" }}
,{ "pid":12345, "tid":4, "ts":1751913069739661, "dur":206, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.TextRenderingModule-FeaturesChecked.txt_ighi.info" }}
,{ "pid":12345, "tid":4, "ts":1751913069740055, "dur":295, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.PropertiesModule-FeaturesChecked.txt_m9z5.info" }}
,{ "pid":12345, "tid":4, "ts":1751913069740423, "dur":167, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.IMGUIModule-FeaturesChecked.txt_ufk2.info" }}
,{ "pid":12345, "tid":4, "ts":1751913069740590, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/libs/unity-classes.jar" }}
,{ "pid":12345, "tid":4, "ts":1751913069740656, "dur":132, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/libs/unity-classes.jar" }}
,{ "pid":12345, "tid":4, "ts":1751913069740834, "dur":96, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/symbols/armeabi-v7a/libmain.so" }}
,{ "pid":12345, "tid":4, "ts":1751913069740930, "dur":88, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/symbols/armeabi-v7a/libmain.so" }}
,{ "pid":12345, "tid":4, "ts":1751913069741054, "dur":85, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v31/styles.xml" }}
,{ "pid":12345, "tid":4, "ts":1751913069741187, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/strings.xml" }}
,{ "pid":12345, "tid":4, "ts":1751913069741263, "dur":81, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/strings.xml" }}
,{ "pid":12345, "tid":4, "ts":1751913069741391, "dur":83, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/app_icon.png" }}
,{ "pid":12345, "tid":4, "ts":1751913069741474, "dur":118, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/app_icon.png" }}
,{ "pid":12345, "tid":4, "ts":1751913069741656, "dur":1614, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751913069743271, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GuidGenerator /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/unity_app_guid" }}
,{ "pid":12345, "tid":4, "ts":1751913069743352, "dur":2162, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751913069745515, "dur":738, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1751913069746253, "dur":99, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751913069746577, "dur":222331, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1751913069969086, "dur":110571, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.VRModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1751913070079836, "dur":249293, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/System.Xml-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1751913070439277, "dur":137, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751913070330117, "dur":109307, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1751913070439645, "dur":236421, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/Unity.Collections-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1751913070866069, "dur":110, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751913070676264, "dur":189921, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/Unity.InputSystem.ForUI-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1751913070866384, "dur":116719, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1751913070983352, "dur":627, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/PackageCache/com.unity.burst@7a907cf5a459/.Runtime/bcl.exe" }}
,{ "pid":12345, "tid":4, "ts":1751913070983243, "dur":736, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/Android/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":4, "ts":1751913070983980, "dur":10438, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751913071016762, "dur":11820283, "ph":"X", "name": "GenerateNativePluginsForAssemblies",  "args": { "detail":"Library/Bee/artifacts/Android/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":5, "ts":1751913069727373, "dur":3509, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751913069730886, "dur":1041, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751913069731927, "dur":935, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751913069732862, "dur":944, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751913069733807, "dur":945, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751913069734752, "dur":2893, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751913069738004, "dur":242, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.GPUDriven.Runtime-FeaturesChecked.txt_gz0s.info" }}
,{ "pid":12345, "tid":5, "ts":1751913069738353, "dur":243, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/System-FeaturesChecked.txt_v90b.info" }}
,{ "pid":12345, "tid":5, "ts":1751913069738777, "dur":168, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/Unity.Collections.LowLevel.ILSupport-FeaturesChecked.txt_orex.info" }}
,{ "pid":12345, "tid":5, "ts":1751913069739056, "dur":319, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.Core.Runtime.Shared-FeaturesChecked.txt_024x.info" }}
,{ "pid":12345, "tid":5, "ts":1751913069739517, "dur":278, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.UIModule-FeaturesChecked.txt_zatd.info" }}
,{ "pid":12345, "tid":5, "ts":1751913069739921, "dur":135, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/Unity.Mathematics-FeaturesChecked.txt_bwkn.info" }}
,{ "pid":12345, "tid":5, "ts":1751913069740239, "dur":186, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.JSONSerializeModule-FeaturesChecked.txt_vjsc.info" }}
,{ "pid":12345, "tid":5, "ts":1751913069740522, "dur":197, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.CoreModule-FeaturesChecked.txt_uimb.info" }}
,{ "pid":12345, "tid":5, "ts":1751913069740720, "dur":81, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/armeabi-v7a/libmain.so" }}
,{ "pid":12345, "tid":5, "ts":1751913069740802, "dur":99, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/armeabi-v7a/libmain.so" }}
,{ "pid":12345, "tid":5, "ts":1751913069740976, "dur":85, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":5, "ts":1751913069741110, "dur":100, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/colors.xml" }}
,{ "pid":12345, "tid":5, "ts":1751913069741216, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/freeformwindow.xml" }}
,{ "pid":12345, "tid":5, "ts":1751913069741296, "dur":92, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/freeformwindow.xml" }}
,{ "pid":12345, "tid":5, "ts":1751913069741424, "dur":97, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/libs/lightwebviewunity-release.aar" }}
,{ "pid":12345, "tid":5, "ts":1751913069741522, "dur":58, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/libs/lightwebviewunity-release.aar" }}
,{ "pid":12345, "tid":5, "ts":1751913069741652, "dur":80, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":5, "ts":1751913069741769, "dur":3735, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751913069745504, "dur":603, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1751913069746108, "dur":240, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751913069746485, "dur":102433, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1751913069946681, "dur":130, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751913069849088, "dur":97737, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/System.Core-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1751913069947256, "dur":115919, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1751913070063306, "dur":82635, "ph":"X", "name": "Compile",  "args": { "detail":"UnityICallRegistration Library/Bee/artifacts/Android/libunity/armeabi-v7a/UnityICallRegistration.o" }}
,{ "pid":12345, "tid":5, "ts":1751913070145968, "dur":83, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1751913070146092, "dur":236595, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1751913070382881, "dur":302283, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Universal.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1751913070685319, "dur":131198, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.AudioModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1751913070816840, "dur":106623, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/System-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1751913070923728, "dur":231150, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.GridModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1751913071155079, "dur":678188, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751913071833340, "dur":232, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/symbols/arm64-v8a/libunity.so" }}
,{ "pid":12345, "tid":5, "ts":1751913071833574, "dur":5015608, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751913076849186, "dur":5987947, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751913069727387, "dur":3502, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751913069730891, "dur":1032, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751913069731923, "dur":942, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751913069732865, "dur":936, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751913069733801, "dur":944, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751913069734746, "dur":2887, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751913069737634, "dur":74476, "ph":"X", "name": "ComputeLeafInputSignature",  "args": { "detail":"Lib_Android_arm32 Library/Bee/artifacts/Android/06gmu/il2cpp.a" }}
,{ "pid":12345, "tid":6, "ts":1751913069812180, "dur":142124, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/Unity.Burst-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1751913069954478, "dur":108935, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.VFXModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1751913070063711, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Burst.Unsafe-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1751913070063846, "dur":280558, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/Unity.Burst.Unsafe-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1751913070344576, "dur":155178, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/Unity.Collections.LowLevel.ILSupport-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1751913070499909, "dur":96795, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.PhysicsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1751913070596889, "dur":236073, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1751913070833150, "dur":111408, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1751913070944809, "dur":311096, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime.Shared-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1751913071256048, "dur":5593026, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751913076849093, "dur":5988089, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751913069727389, "dur":3505, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751913069730897, "dur":1041, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751913069731938, "dur":933, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751913069732871, "dur":938, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751913069733809, "dur":981, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751913069734790, "dur":2857, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751913069738429, "dur":265, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/Assembly-CSharp-FeaturesChecked.txt_ki5r.info" }}
,{ "pid":12345, "tid":7, "ts":1751913069738861, "dur":168, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.AIModule-FeaturesChecked.txt_jr17.info" }}
,{ "pid":12345, "tid":7, "ts":1751913069739179, "dur":204, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt_9rh7.info" }}
,{ "pid":12345, "tid":7, "ts":1751913069739401, "dur":243, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.UIElementsModule-FeaturesChecked.txt_aabo.info" }}
,{ "pid":12345, "tid":7, "ts":1751913069739701, "dur":236, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt_el85.info" }}
,{ "pid":12345, "tid":7, "ts":1751913069740093, "dur":302, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.PhysicsModule-FeaturesChecked.txt_2l1t.info" }}
,{ "pid":12345, "tid":7, "ts":1751913069740486, "dur":192, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine-FeaturesChecked.txt_x1ug.info" }}
,{ "pid":12345, "tid":7, "ts":1751913069740678, "dur":82, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/java/com/unity3d/player/UnityPlayerGameActivity.java" }}
,{ "pid":12345, "tid":7, "ts":1751913069740760, "dur":107, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/java/com/unity3d/player/UnityPlayerGameActivity.java" }}
,{ "pid":12345, "tid":7, "ts":1751913069740904, "dur":90, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/resources/META-INF/com.android.games.engine.build_fingerprint" }}
,{ "pid":12345, "tid":7, "ts":1751913069741043, "dur":83, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v21/styles.xml" }}
,{ "pid":12345, "tid":7, "ts":1751913069741167, "dur":117, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/values/strings.xml" }}
,{ "pid":12345, "tid":7, "ts":1751913069741308, "dur":90, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon_round.xml" }}
,{ "pid":12345, "tid":7, "ts":1751913069741398, "dur":104, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon_round.xml" }}
,{ "pid":12345, "tid":7, "ts":1751913069741535, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_foreground.png" }}
,{ "pid":12345, "tid":7, "ts":1751913069741595, "dur":74, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_foreground.png" }}
,{ "pid":12345, "tid":7, "ts":1751913069741696, "dur":3798, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751913069745850, "dur":192, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.CoreModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1751913069746307, "dur":50, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.IMGUIModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1751913069746674, "dur":57, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.PropertiesModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1751913069747101, "dur":243, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TextCoreTextEngineModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1751913069747443, "dur":510, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UIElementsModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1751913069748024, "dur":424, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.0.45f1/Unity.app/Contents/Tools/InternalCallRegistrationWriter/InternalCallRegistrationWriter.exe" }}
,{ "pid":12345, "tid":7, "ts":1751913069745494, "dur":2955, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ICallRegistrationGenerator /Users/<USER>/Desktop/WebBrowser/Library/Bee/artifacts/Android/il2cppOutput/UnityICallRegistration.cpp" }}
,{ "pid":12345, "tid":7, "ts":1751913069748485, "dur":294595, "ph":"X", "name": "ICallRegistrationGenerator",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/artifacts/Android/il2cppOutput/UnityICallRegistration.cpp" }}
,{ "pid":12345, "tid":7, "ts":1751913070043115, "dur":1882, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/artifacts/Android/il2cppOutput/UnityICallRegistration.cpp" }}
,{ "pid":12345, "tid":7, "ts":1751913070043102, "dur":1898, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Compile UnityICallRegistration Library/Bee/artifacts/Android/libunity/arm64-v8a/UnityICallRegistration.o" }}
,{ "pid":12345, "tid":7, "ts":1751913070045027, "dur":83309, "ph":"X", "name": "Compile",  "args": { "detail":"UnityICallRegistration Library/Bee/artifacts/Android/libunity/arm64-v8a/UnityICallRegistration.o" }}
,{ "pid":12345, "tid":7, "ts":1751913070128356, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Link libunity Library/Bee/artifacts/Android/libunity/arm64-v8a/unstripped/libunity.so" }}
,{ "pid":12345, "tid":7, "ts":1751913070128515, "dur":1587872, "ph":"X", "name": "Link",  "args": { "detail":"libunity Library/Bee/artifacts/Android/libunity/arm64-v8a/unstripped/libunity.so" }}
,{ "pid":12345, "tid":7, "ts":1751913071716477, "dur":116760, "ph":"X", "name": "Generate",  "args": { "detail":"Library/Bee/artifacts/Android/libunity/arm64-v8a/libunity.dbg.so from Library/Bee/artifacts/Android/libunity/arm64-v8a/unstripped/libunity.so Library/Bee/artifacts/Android/libunity/arm64-v8a/libunity.dbg.so" }}
,{ "pid":12345, "tid":7, "ts":1751913071833278, "dur":160069, "ph":"X", "name": "Generate",  "args": { "detail":"Library/Bee/artifacts/Android/libunity/arm64-v8a/libunity.sym.so from Library/Bee/artifacts/Android/libunity/arm64-v8a/libunity.dbg.so Library/Bee/artifacts/Android/libunity/arm64-v8a/libunity.sym.so" }}
,{ "pid":12345, "tid":7, "ts":1751913071993453, "dur":38350, "ph":"X", "name": "Adding",  "args": { "detail":"note Library/Bee/artifacts/Android/libunity/arm64-v8a/stripped/libunity.so -> /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libunity.so /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libunity.so" }}
,{ "pid":12345, "tid":7, "ts":1751913072031827, "dur":4817271, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751913076849106, "dur":5988068, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751913069727424, "dur":3475, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751913069730900, "dur":978, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751913069731878, "dur":934, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751913069732812, "dur":956, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751913069733768, "dur":960, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751913069734729, "dur":2886, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751913069738174, "dur":188, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/Mono.Security-FeaturesChecked.txt_pcel.info" }}
,{ "pid":12345, "tid":8, "ts":1751913069738433, "dur":283, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/Unity.Burst-FeaturesChecked.txt_5voi.info" }}
,{ "pid":12345, "tid":8, "ts":1751913069738870, "dur":281, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.AndroidJNIModule-FeaturesChecked.txt_x3ln.info" }}
,{ "pid":12345, "tid":8, "ts":1751913069739278, "dur":224, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.XRModule-FeaturesChecked.txt_xks3.info" }}
,{ "pid":12345, "tid":8, "ts":1751913069739516, "dur":258, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.TilemapModule-FeaturesChecked.txt_xct4.info" }}
,{ "pid":12345, "tid":8, "ts":1751913069739944, "dur":139, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.SubsystemsModule-FeaturesChecked.txt_vzqa.info" }}
,{ "pid":12345, "tid":8, "ts":1751913069740087, "dur":293, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.InputModule-FeaturesChecked.txt_xblj.info" }}
,{ "pid":12345, "tid":8, "ts":1751913069740507, "dur":190, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.GridModule-FeaturesChecked.txt_5p9t.info" }}
,{ "pid":12345, "tid":8, "ts":1751913069740698, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751913069740774, "dur":114, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libmain.so" }}
,{ "pid":12345, "tid":8, "ts":1751913069740888, "dur":85, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libmain.so" }}
,{ "pid":12345, "tid":8, "ts":1751913069741002, "dur":103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/ids.xml" }}
,{ "pid":12345, "tid":8, "ts":1751913069741106, "dur":77, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/ids.xml" }}
,{ "pid":12345, "tid":8, "ts":1751913069741228, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon.xml" }}
,{ "pid":12345, "tid":8, "ts":1751913069741304, "dur":100, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon.xml" }}
,{ "pid":12345, "tid":8, "ts":1751913069741495, "dur":92, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/libs/lightwebviewsdk-release.aar" }}
,{ "pid":12345, "tid":8, "ts":1751913069741690, "dur":86, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/data.unity3d" }}
,{ "pid":12345, "tid":8, "ts":1751913069741783, "dur":3721, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751913069745508, "dur":359, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1751913069745869, "dur":106, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751913069745977, "dur":51, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751913069746264, "dur":116476, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1751913069864230, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1751913069864369, "dur":127244, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1751913069991743, "dur":254, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Compile UnityClassRegistration Library/Bee/artifacts/Android/libunity/armeabi-v7a/UnityClassRegistration.o" }}
,{ "pid":12345, "tid":8, "ts":1751913069992001, "dur":1448, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751913069994493, "dur":417039, "ph":"X", "name": "Compile",  "args": { "detail":"UnityClassRegistration Library/Bee/artifacts/Android/libunity/armeabi-v7a/UnityClassRegistration.o" }}
,{ "pid":12345, "tid":8, "ts":1751913070411571, "dur":99, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Link libunity Library/Bee/artifacts/Android/libunity/armeabi-v7a/unstripped/libunity.so" }}
,{ "pid":12345, "tid":8, "ts":1751913070411761, "dur":1310119, "ph":"X", "name": "Link",  "args": { "detail":"libunity Library/Bee/artifacts/Android/libunity/armeabi-v7a/unstripped/libunity.so" }}
,{ "pid":12345, "tid":8, "ts":1751913071722000, "dur":138045, "ph":"X", "name": "Generate",  "args": { "detail":"Library/Bee/artifacts/Android/libunity/armeabi-v7a/libunity.dbg.so from Library/Bee/artifacts/Android/libunity/armeabi-v7a/unstripped/libunity.so Library/Bee/artifacts/Android/libunity/armeabi-v7a/libunity.dbg.so" }}
,{ "pid":12345, "tid":8, "ts":1751913071860083, "dur":155547, "ph":"X", "name": "Generate",  "args": { "detail":"Library/Bee/artifacts/Android/libunity/armeabi-v7a/libunity.sym.so from Library/Bee/artifacts/Android/libunity/armeabi-v7a/libunity.dbg.so Library/Bee/artifacts/Android/libunity/armeabi-v7a/libunity.sym.so" }}
,{ "pid":12345, "tid":8, "ts":1751913072015717, "dur":33149, "ph":"X", "name": "Adding",  "args": { "detail":"note Library/Bee/artifacts/Android/libunity/armeabi-v7a/stripped/libunity.so -> /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/armeabi-v7a/libunity.so /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/armeabi-v7a/libunity.so" }}
,{ "pid":12345, "tid":8, "ts":1751913072048887, "dur":4800226, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751913076849125, "dur":5988032, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751913069727425, "dur":3498, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751913069730927, "dur":1034, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751913069731961, "dur":942, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751913069732903, "dur":936, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751913069733840, "dur":956, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751913069734796, "dur":2836, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751913069737633, "dur":72721, "ph":"X", "name": "ComputeLeafInputSignature",  "args": { "detail":"Lib_Android_arm64 Library/Bee/artifacts/Android/vf0d7/il2cpp.a" }}
,{ "pid":12345, "tid":9, "ts":1751913069810445, "dur":99599, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1751913069910208, "dur":108942, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1751913070169962, "dur":5013, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751913070019332, "dur":155652, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1751913070181067, "dur":64, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1751913070181189, "dur":210577, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1751913070391945, "dur":301129, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/Unity.InputSystem-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1751913070693275, "dur":189460, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.GPUDriven.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1751913070882966, "dur":120479, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.CoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1751913071045642, "dur":277, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071045971, "dur":158, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipelines.Core.Runtime.Shared.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071046132, "dur":140, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Unity.TextMeshPro.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071046129, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.TextMeshPro.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071046284, "dur":715, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.TextMeshPro.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071047060, "dur":303, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751913071047366, "dur":271, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071047642, "dur":534, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Unity.RenderPipelines.Universal.Runtime.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071047637, "dur":539, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipelines.Universal.Runtime.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071048186, "dur":321, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipelines.Universal.Runtime.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071048519, "dur":182, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Collections.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071048707, "dur":227, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Collections.LowLevel.ILSupport.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071048939, "dur":197, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/mscorlib.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071049168, "dur":149, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/analytics.json" }}
,{ "pid":12345, "tid":9, "ts":1751913071049323, "dur":207, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071049535, "dur":1285, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Unity.InputSystem.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071049530, "dur":1325, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.InputSystem.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071052057, "dur":336, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.InputSystem.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071052400, "dur":61, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Unity.RenderPipeline.Universal.ShaderLibrary.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071052395, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipeline.Universal.ShaderLibrary.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071052471, "dur":243, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipeline.Universal.ShaderLibrary.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071052720, "dur":214, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Burst.Unsafe.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071052940, "dur":265, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Burst.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071053426, "dur":147, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751913071053215, "dur":361, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.Physics2DModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071053577, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.Physics2DModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071053635, "dur":1075, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.Physics2DModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071055540, "dur":917, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UI.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071054711, "dur":1747, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UI.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071056803, "dur":313, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UI.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071057127, "dur":271, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SharedInternalsModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071057440, "dur":410, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SpriteShapeModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071057855, "dur":220, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.InputLegacyModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071057850, "dur":225, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputLegacyModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071058129, "dur":3135, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputLegacyModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071061265, "dur":6811, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.HierarchyCoreModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071073112, "dur":307, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.HierarchyCoreModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071073430, "dur":148, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextRenderingModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071073631, "dur":136, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.HierarchyCoreModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071073790, "dur":144, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.JSONSerializeModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071073957, "dur":131, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextRenderingModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071074094, "dur":127, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.PhysicsModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071074227, "dur":198, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TerrainModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071074435, "dur":354, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TilemapModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071074826, "dur":283, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.PhysicsModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071075146, "dur":283, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TerrainModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071075462, "dur":333, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TilemapModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071075800, "dur":4828, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.CoreModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071075795, "dur":4833, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.CoreModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071081019, "dur":181, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751913071081205, "dur":292, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.CoreModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071081531, "dur":208, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071081744, "dur":85, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.IMGUIModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071081739, "dur":90, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.IMGUIModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071081837, "dur":1621, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.IMGUIModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071083508, "dur":311, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputForUIModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071083870, "dur":239, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.PropertiesModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071084113, "dur":214, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UIElementsModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071084109, "dur":219, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIElementsModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071084333, "dur":273, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIElementsModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071084612, "dur":1363, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SubsystemsModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071085980, "dur":3624, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/artifacts/Android/ManagedStripped/Unity.RenderPipelines.Core.Runtime.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071085976, "dur":3629, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipelines.Core.Runtime.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071089884, "dur":1042, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751913071089623, "dur":1322, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipelines.Core.Runtime.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751913071090953, "dur":1544, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071092532, "dur":309, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":9, "ts":1751913071092841, "dur":623657, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751913071812045, "dur":107, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751913071716632, "dur":95577, "ph":"X", "name": "Stripping",  "args": { "detail":"Library/Bee/artifacts/Android/libunity/arm64-v8a/unstripped/libunity.so -> Library/Bee/artifacts/Android/libunity/arm64-v8a/stripped/libunity.so Library/Bee/artifacts/Android/libunity/arm64-v8a/stripped/libunity.so" }}
,{ "pid":12345, "tid":9, "ts":1751913071812228, "dur":5036934, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751913076849168, "dur":5988083, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751913069727426, "dur":3504, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751913069730930, "dur":1033, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751913069731963, "dur":945, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751913069732908, "dur":934, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751913069733842, "dur":937, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751913069734779, "dur":2832, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751913069738353, "dur":209, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/mscorlib-FeaturesChecked.txt_pwj8.info" }}
,{ "pid":12345, "tid":10, "ts":1751913069738677, "dur":234, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/Unity.Collections-FeaturesChecked.txt_imay.info" }}
,{ "pid":12345, "tid":10, "ts":1751913069739014, "dur":222, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/Unity.InputSystem.ForUI-FeaturesChecked.txt_1qsl.info" }}
,{ "pid":12345, "tid":10, "ts":1751913069739388, "dur":375, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.VFXModule-FeaturesChecked.txt_r8mm.info" }}
,{ "pid":12345, "tid":10, "ts":1751913069739990, "dur":223, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.TerrainModule-FeaturesChecked.txt_wybo.info" }}
,{ "pid":12345, "tid":10, "ts":1751913069740289, "dur":213, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/UnityEngine.InputLegacyModule-FeaturesChecked.txt_y5my.info" }}
,{ "pid":12345, "tid":10, "ts":1751913069740504, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751913069740625, "dur":2626, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/Android/boot.config" }}
,{ "pid":12345, "tid":10, "ts":1751913069743302, "dur":2201, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751913069745504, "dur":362, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1751913069745867, "dur":302, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751913069746246, "dur":116526, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.UIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1751913069864259, "dur":72, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1751913069864371, "dur":127408, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1751913069995014, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1751913069995151, "dur":416053, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.AIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1751913070411379, "dur":272417, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/Unity.Mathematics-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1751913070684050, "dur":162998, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1751913070847342, "dur":96, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1751913070847469, "dur":102801, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1751913070950457, "dur":295899, "ph":"X", "name": "ExtractUsedFeatures",  "args": { "detail":"Library/Bee/artifacts/Android/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1751913071246498, "dur":613654, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751913071860215, "dur":278, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/symbols/armeabi-v7a/libunity.so" }}
,{ "pid":12345, "tid":10, "ts":1751913071860495, "dur":4988677, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751913076849179, "dur":5987962, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751913082841847, "dur":332, "ph":"X", "name": "ProfilerWriteOutput" }
,