                        -H/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-<PERSON><PERSON><PERSON>OID_PLATFORM=android-23
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK
-DCMAKE_ANDROID_NDK=/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK
-DCMAKE_TOOLCHAIN_FILE=/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/NDK/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/215e5a2o/obj/armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/215e5a2o/obj/armeabi-v7a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-DCMAKE_FIND_ROOT_PATH=/Users/<USER>/Desktop/WebBrowser/.utmp/RelWithDebInfo/215e5a2o/prefab/armeabi-v7a/prefab
-B/Users/<USER>/Desktop/WebBrowser/.utmp/RelWithDebInfo/215e5a2o/armeabi-v7a
-GNinja
-DANDROID_STL=c++_shared
-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
                        Build command args: []
                        Version: 2