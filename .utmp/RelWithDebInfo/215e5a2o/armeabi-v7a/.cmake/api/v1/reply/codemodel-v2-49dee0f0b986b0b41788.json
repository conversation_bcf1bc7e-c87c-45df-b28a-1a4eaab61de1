{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-RelWithDebInfo-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}, {"build": "GameActivity", "jsonFile": "directory-GameActivity-RelWithDebInfo-f368e6d506ea21fe2607.json", "minimumCMakeVersion": {"string": "3.4.1"}, "parentIndex": 0, "projectIndex": 1, "source": "GameActivity", "targetIndexes": [0]}], "name": "RelWithDebInfo", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "Unity"}, {"directoryIndexes": [1], "name": "game", "parentIndex": 0, "targetIndexes": [0]}], "targets": [{"directoryIndex": 1, "id": "game::@d02bb112ea9f9c2ed29f", "jsonFile": "target-game-RelWithDebInfo-b1c5adc7471054fbf5ed.json", "name": "game", "projectIndex": 1}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Desktop/WebBrowser/.utmp/RelWithDebInfo/215e5a2o/armeabi-v7a", "source": "/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp"}, "version": {"major": 2, "minor": 3}}