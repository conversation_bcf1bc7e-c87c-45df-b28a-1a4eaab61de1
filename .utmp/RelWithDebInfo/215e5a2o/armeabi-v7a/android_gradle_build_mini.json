{"buildFiles": ["/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/CMakeLists.txt", "/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/CMakeLists.txt"], "cleanCommandsComponents": [["/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/WebBrowser/.utmp/RelWithDebInfo/215e5a2o/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Applications/Unity/Hub/Editor/6000.0.45f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/WebBrowser/.utmp/RelWithDebInfo/215e5a2o/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"game::@d02bb112ea9f9c2ed29f": {"artifactName": "game", "abi": "armeabi-v7a", "output": "/Users/<USER>/Desktop/WebBrowser/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/215e5a2o/obj/armeabi-v7a/libgame.so", "runtimeFiles": []}}}