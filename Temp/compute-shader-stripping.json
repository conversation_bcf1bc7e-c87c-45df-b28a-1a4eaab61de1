{"totalVariantsIn": 2, "totalVariantsOut": 2, "shaders": [{"inputVariants": 2, "outputVariants": 2, "name": "ProbeVolumeSamplingDebugPositionNormal", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputePositionNormal", "stripTimeMs": 0.005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputePositionNormal", "stripTimeMs": 0.0002}]}]}]}