{"player_build_data": [{"type": "il2cppData", "msg": {"build_event_id": "7249eb3fb9330480388297a7096e2b2d", "node_executed": true, "attribute_total_count_eager_static_constructor": 82, "attribute_total_count_set_option": 0, "attribute_total_count_generate_into_own_cpp_file": 0, "attribute_total_count_ignore_by_deep_profiler": 44, "extra_types_total_count": 0, "option_extra_types_file_count": 0, "option_debug_assembly_name_count": 0, "option_additional_cpp_count": 0, "option_emit_null_checks": true, "option_enable_stacktrace": false, "option_enable_deep_profiler": false, "option_enable_stats": false, "option_enable_array_bounds_check": true, "option_enable_divide_by_zero_check": false, "option_emit_comments": false, "option_disable_generic_sharing": false, "option_maximum_recursive_generic_depth": -1, "option_generic_virtual_method_iterations": -1, "option_code_generation_option": ["EnableInlining"], "option_file_generation_option": [], "option_generics_option": [], "option_feature": [], "option_diagnostic_option": [], "option_convert_to_cpp": true, "option_compile_cpp": false, "option_development_mode": false, "option_enable_debugger": false, "option_generate_usym_file": false, "option_jobs": 10}, "version": 1}, {"type": "playerBuildTimingData", "msg": {"build_event_id": "7249eb3fb9330480388297a7096e2b2d", "build_player": 482024, "preprocess_player": 221, "produce_player_script_assemblies": 22309, "build_scripts_dlls": 16, "writing_asset_files": 28700, "postprocess_built_player": 332365, "node_summary_table": [{"name": "Csc", "duration": 178}, {"name": "Csc", "duration": 2977}, {"name": "Csc", "duration": 1749}, {"name": "Csc", "duration": 1843}, {"name": "Csc", "duration": 4148}, {"name": "Csc", "duration": 2493}, {"name": "Csc", "duration": 2477}, {"name": "Csc", "duration": 1749}, {"name": "Csc", "duration": 1797}, {"name": "Csc", "duration": 1825}, {"name": "Csc", "duration": 2045}, {"name": "Csc", "duration": 3008}, {"name": "Csc", "duration": 1664}, {"name": "Csc", "duration": 2985}, {"name": "Csc", "duration": 2022}, {"name": "Csc", "duration": 1790}, {"name": "Csc", "duration": 2078}, {"name": "Csc", "duration": 1752}, {"name": "Csc", "duration": 1483}, {"name": "Csc", "duration": 2978}, {"name": "Csc", "duration": 1749}, {"name": "Csc", "duration": 1823}, {"name": "Csc", "duration": 1630}, {"name": "Csc", "duration": 1942}, {"name": "Csc", "duration": 2063}, {"name": "Csc", "duration": 3554}, {"name": "Csc", "duration": 2409}, {"name": "Csc", "duration": 1672}, {"name": "Csc", "duration": 2018}, {"name": "Csc", "duration": 2015}, {"name": "Csc", "duration": 3398}, {"name": "Csc", "duration": 2617}, {"name": "Csc", "duration": 1944}, {"name": "Csc", "duration": 1551}, {"name": "Csc", "duration": 2985}, {"name": "Csc", "duration": 178}, {"name": "Csc", "duration": 178}, {"name": "il2cpp.exe", "duration": 6913}, {"name": "UnityLinker.exe", "duration": 5014}]}, "version": 1}, {"type": "unityLinkerData", "msg": {"build_event_id": "7249eb3fb9330480388297a7096e2b2d", "node_executed": true, "attribute_marked_count_always_link_assembly": 1, "attribute_swept_count_always_link_assembly": 0, "attribute_total_count_always_link_assembly": 1, "attribute_marked_count_preserve": 56, "attribute_total_count_preserve": 50, "attribute_total_count_preserve_body": 0, "attribute_marked_count_required_member": 252, "attribute_swept_count_required_member": 25, "attribute_total_count_required_member": 277, "attribute_marked_count_require_derived": 0, "attribute_swept_count_require_derived": 0, "attribute_total_count_require_derived": 0, "attribute_marked_count_require_implementors": 0, "attribute_swept_count_require_implementors": 0, "attribute_total_count_require_implementors": 0, "attribute_total_count_required_interface": 0, "attribute_marked_count_require_attribute_usages": 698, "attribute_total_count_require_attribute_usages": 2, "attribute_total_count_dynamic_dependency": 0, "attribute_marked_count_dynamic_dependency": 11, "attribute_swept_count_dynamic_dependency": -11, "assembly_counts_total_in": 105, "assembly_counts_link": 36, "assembly_counts_copy": 15, "assembly_counts_delete": 54, "assembly_counts_total_out": 51, "unresolved_stubbing_total_count": 0, "unresolved_stubbing_missing_interface_method_count": 0, "unresolved_stubbing_missing_abstract_class_method_count": 0, "unresolved_stubbing_missing_type_count": 0, "unresolved_stubbing_missing_method_count": 0, "unrecognized_reflection_access_total_count": 330, "unrecognized_reflection_access_core_count": 158, "unrecognized_reflection_access_unity_count": 172, "unrecognized_reflection_access_user_count": 0, "recognized_reflection_access_total_count": 47, "recognized_reflection_access_core_count": 29, "recognized_reflection_access_unity_count": 18, "recognized_reflection_access_user_count": 0, "link_xml_total_count": 11, "link_xml_embedded_count": 1, "link_xml_embedded_unity_count": 0, "link_xml_embedded_user_count": 0, "link_xml_file_count": 10, "link_xml_assembly_preserve_all_total_count": 0, "link_xml_assembly_preserve_all_unity_count": 0, "link_xml_assembly_preserve_all_core_count": 0, "link_xml_assembly_preserve_all_user_count": 0, "engine_module_total_in": 68, "engine_module_deleted": 41, "engine_module_total_out": 27, "option_rule_set": "Minimal", "option_enable_report": false, "option_enable_snapshot": false, "option_enable_engine_module_stripping": true, "option_unity_root_strategy": "AllNonEngineAndNonClassLibraries", "option_enable_ildump": false}, "version": 1}]}