{"totalVariantsIn": 2957, "totalVariantsOut": 1777, "shaders": [{"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Universal Render Pipeline/Sampling", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "BoxDownsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.365200000000001}]}]}, {"inputVariants": 22, "outputVariants": 22, "name": "Hidden/Universal Render Pipeline/ScreenSpaceAmbientOcclusion", "pipelines": [{"inputVariants": 22, "outputVariants": 22, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "SSAO_Occlusion (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0689}, {"inputVariants": 2, "outputVariants": 2, "variantName": "SSAO_Bilateral_HorizontalBlur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0499}, {"inputVariants": 2, "outputVariants": 2, "variantName": "SSAO_Bilateral_VerticalBlur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.049600000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "SSAO_Bilateral_FinalBlur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.047900000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "SSAO_Bilateral_FinalBlur_AfterOpaque (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.047900000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "SSAO_Gaussian_HorizontalBlur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.048}, {"inputVariants": 2, "outputVariants": 2, "variantName": "SSAO_Gaussian_VerticalBlur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0472}, {"inputVariants": 2, "outputVariants": 2, "variantName": "SSAO_Gaussian_VerticalBlur_AfterOpaque (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.045700000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "SSA<PERSON><PERSON><PERSON>e (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.049}, {"inputVariants": 2, "outputVariants": 2, "variantName": "SSAO_<PERSON><PERSON><PERSON>_AfterOpaque (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.046200000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Universal Render Pipeline/Stop NaN", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Stop NaN (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0497}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CoreSRP/CoreCopy", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.051800000000000006}, {"inputVariants": 2, "outputVariants": 2, "variantName": "CopyMS (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0502}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/Universal Render Pipeline/CameraMotionBlur", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Camera Motion Blur - Low Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1106}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Camera Motion Blur - Medium Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0454}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Camera Motion Blur - High Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0482}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Camera And Object Motion Blur - Low Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.047}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Camera And Object Motion Blur - Medium Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0463}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Camera And Object Motion Blur - High Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.046200000000000005}]}]}, {"inputVariants": 10, "outputVariants": 8, "name": "Hidden/TerrainEngine/Details/UniversalPipeline/BillboardWavingDoublePass", "pipelines": [{"inputVariants": 10, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "ForwardLit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0665}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.20470000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.035}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthNormals (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0682}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "Hidden/Universal Render Pipeline/BokehDepthOfField", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field CoC (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0521}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field Prefilter (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0476}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field Blur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0473}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field Post Blur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0475}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field Composite (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.052700000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field CoC (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0455}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field Prefilter (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0634}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field Blur (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.055900000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field Post Blur (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.054400000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bokeh Depth Of Field Composite (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0526}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/FallbackError", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0499}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/Universal Render Pipeline/XR/XROcclusionMesh", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1222}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Universal Render Pipeline/Edge Adaptive Spatial Upsampling", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "EASU (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0327}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Universal/HDRDebugView", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0517}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0466}]}]}, {"inputVariants": 14, "outputVariants": 14, "name": "Hidden/Universal Render Pipeline/GaussianDepthOfField", "pipelines": [{"inputVariants": 14, "outputVariants": 14, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Gaussian Depth Of Field CoC (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.050800000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Gaussian Depth Of Field Prefilter (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0536}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Gaussian Depth Of Field Blur Horizontal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0489}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Gaussian Depth Of Field Blur Vertical (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0476}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Gaussian Depth Of Field Composite (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0568}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/Universal Render Pipeline/Bloom", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Bloom Prefilter (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0594}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bloom Blur Horizontal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0574}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bloom Blur Vertical (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0519}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Bloom Upsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0529}]}]}, {"inputVariants": 18, "outputVariants": 18, "name": "Hidden/Universal Render Pipeline/SubpixelMorphologicalAntialiasing", "pipelines": [{"inputVariants": 18, "outputVariants": 18, "pipeline": "", "variants": [{"inputVariants": 6, "outputVariants": 6, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0659}, {"inputVariants": 6, "outputVariants": 6, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0613}, {"inputVariants": 6, "outputVariants": 6, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0656}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Universal Render Pipeline/LutBuilderLdr", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "LutBuilderLdr (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0514}]}]}, {"inputVariants": 66, "outputVariants": 66, "name": "Hidden/Universal Render Pipeline/LensFlareDataDriven", "pipelines": [{"inputVariants": 66, "outputVariants": 66, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "LensFlareAdditive (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0893}, {"inputVariants": 16, "outputVariants": 16, "variantName": "LensFlareScreen (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1057}, {"inputVariants": 16, "outputVariants": 16, "variantName": "LensFlarePremultiply (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.09190000000000001}, {"inputVariants": 16, "outputVariants": 16, "variantName": "LensFlareLerp (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1213}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareOcclusion (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.067}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/Universal Render Pipeline/LensFlareScreenSpace", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareScreenSpac Prefilter (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.054}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareScreenSpace Downsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.047400000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareScreenSpace Upsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.054900000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareScreenSpace Composition (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0594}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareScreenSpace Write to BloomTexture (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.050100000000000006}]}]}, {"inputVariants": 58, "outputVariants": 58, "name": "Hidden/Universal/CoreBlit", "pipelines": [{"inputVariants": 58, "outputVariants": 58, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Nearest (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.059500000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Bilinear (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.061000000000000006}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NearestQuad (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0673}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BilinearQuad (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0507}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NearestQuadPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0524}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BilinearQuadPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0599}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NearestQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.055200000000000006}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BilinearQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06910000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BilinearQuadPaddingOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0492}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NearestQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06760000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BilinearQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0585}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NearestQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0563}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BilinearQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.048600000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BilinearQuadPaddingAlphaBlendOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0473}, {"inputVariants": 2, "outputVariants": 2, "variantName": "CubeToOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0589}, {"inputVariants": 2, "outputVariants": 2, "variantName": "CubeToOctahedralLuminance (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0538}, {"inputVariants": 2, "outputVariants": 2, "variantName": "CubeToOctahedralAlpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.050100000000000006}, {"inputVariants": 2, "outputVariants": 2, "variantName": "CubeToOctahedralRed (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0514}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BilinearQuadLuminance (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0589}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BilinearQuadAlpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.053500000000000006}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BilinearQuadRed (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06720000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0516}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0709}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0521}]}]}, {"inputVariants": 14, "outputVariants": 14, "name": "Hidden/Universal Render Pipeline/TemporalAA", "pipelines": [{"inputVariants": 14, "outputVariants": 14, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Accumulate - Quality Very Low (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0611}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Accumulate - Quality Low (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.069}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Accumulate - Quality Medium (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0616}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Accumulate - Quality High (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0738}, {"inputVariants": 4, "outputVariants": 4, "variantName": "TemporalAA - Accumulate - Quality Very High (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0557}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Copy History (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0575}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Universal Render Pipeline/PaniniProjection", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Panini Projection (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0572}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "Hidden/Universal/BlitHDROverlay", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 0, "variantName": "BilinearDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1194}, {"inputVariants": 2, "outputVariants": 0, "variantName": "NearestDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.033600000000000005}]}]}, {"inputVariants": 256, "outputVariants": 192, "name": "Hidden/Universal Render Pipeline/FinalPost", "pipelines": [{"inputVariants": 256, "outputVariants": 192, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 128, "outputVariants": 96, "variantName": "FinalPost (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.4111}, {"inputVariants": 128, "outputVariants": 96, "variantName": "FinalPost (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.46380000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Universal Render Pipeline/CameraMotionVectors", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Camera Motion Vectors (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07350000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Universal Render Pipeline/Debug/DebugReplacement", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Vertex Attributes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0555}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Universal/CoreBlitColorAndDepth", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ColorOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.045700000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ColorAndDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0543}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "Hidden/Universal Render Pipeline/XR/XRMirrorView", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0671}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/Universal Render Pipeline/CopyDepth", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "CopyDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0969}]}]}, {"inputVariants": 32, "outputVariants": 12, "name": "Hidden/TerrainEngine/Details/UniversalPipeline/WavingDoublePass", "pipelines": [{"inputVariants": 32, "outputVariants": 12, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 24, "outputVariants": 8, "variantName": "ForwardLit (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1736}, {"inputVariants": 4, "outputVariants": 0, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0618}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0398}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthNormalsOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06330000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Universal Render Pipeline/FallbackError", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0675}]}]}, {"inputVariants": 1920, "outputVariants": 864, "name": "Hidden/Universal Render Pipeline/UberPost", "pipelines": [{"inputVariants": 1920, "outputVariants": 864, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1920, "outputVariants": 864, "variantName": "UberPost (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.3754}]}]}, {"inputVariants": 12, "outputVariants": 8, "name": "Hidden/Universal Render Pipeline/Scaling Setup", "pipelines": [{"inputVariants": 12, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 12, "outputVariants": 8, "variantName": "ScalingSetup (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0903}]}]}, {"inputVariants": 20, "outputVariants": 0, "name": "Hidden/Universal Render Pipeline/StencilDeferred", "pipelines": [{"inputVariants": 20, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 0, "variantName": "Stencil Volume (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.055}, {"inputVariants": 4, "outputVariants": 0, "variantName": "Deferred Punctual Light (Lit) (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0292}, {"inputVariants": 4, "outputVariants": 0, "variantName": "Deferred Punctual Light (SimpleLit) (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0307}, {"inputVariants": 4, "outputVariants": 0, "variantName": "Deferred Directional Light (Lit) (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0262}, {"inputVariants": 4, "outputVariants": 0, "variantName": "Deferred Directional Light (SimpleLit) (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0315}, {"inputVariants": 1, "outputVariants": 0, "variantName": "ClearStencilPartial (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0352}, {"inputVariants": 1, "outputVariants": 0, "variantName": "SSAOOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029900000000000003}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Universal Render Pipeline/LutBuilderHdr", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 6, "outputVariants": 6, "variantName": "LutBuilderHdr (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06760000000000001}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/Universal Render Pipeline/DBufferClear", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 0, "variantName": "DBufferClear (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.057100000000000005}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "Hidden/TerrainEngine/Details/UniversalPipeline/Vertexlit", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "TerrainDetailVertex (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.061900000000000004}, {"inputVariants": 8, "outputVariants": 8, "variantName": "TerrainDetailVertex - GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07490000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.051500000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthNormals (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0618}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/Universal Render Pipeline/XR/XRMotionVector", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 0, "variantName": "XR Camera MotionVectors (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0461}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "TextMeshPro/Sprite", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0949}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Skybox/Procedural", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 6, "outputVariants": 6, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06280000000000001}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "TextMeshPro/Mobile/Distance Field", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 24, "outputVariants": 24, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.14750000000000002}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Legacy Shaders/VertexLit", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 6, "outputVariants": 6, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0751}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (VertexLM) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.057}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.069}]}]}, {"inputVariants": 34, "outputVariants": 34, "name": "Legacy Shaders/Diffuse", "pipelines": [{"inputVariants": 34, "outputVariants": 34, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1081}, {"inputVariants": 10, "outputVariants": 10, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0825}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0737}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-StencilWrite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0616}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "Hidden/Internal-DepthNormalsTexture", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0609}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0517}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0489}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.0557}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.0596}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.047900000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0687}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.0546}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.0792}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.0648}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "Hidden/Internal-ScreenSpaceShadows", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0813}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0839}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0981}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.08750000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-CombineDepthNormals", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0734}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0707}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0664}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ConvertTexture", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.048100000000000004}]}]}, {"inputVariants": 54, "outputVariants": 54, "name": "Hidden/Internal-DeferredShading", "pipelines": [{"inputVariants": 54, "outputVariants": 54, "pipeline": "", "variants": [{"inputVariants": 52, "outputVariants": 52, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2253}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0601}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-DeferredReflections", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.060500000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0695}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-MotionVectors", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0482}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0618}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.054}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-Flare", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0461}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-Halo", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.065}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyWithDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0526}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitToDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0613}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitToDepth_MSAA", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.060500000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyHDRTonemap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0565}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-DebugPattern", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Target Color and DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0584}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Target only Color (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0438}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Target only DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058600000000000006}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureClip", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0424}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.057100000000000005}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureClipText", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.059000000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.041600000000000005}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITexture", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0577}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0521}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureBlit", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.053200000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0426}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUIRoundedRect", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0723}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.055400000000000005}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUIRoundedRectWithColorPerBorder", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.044000000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.062}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-UIRDefault", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0717}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-UIRAtlasBlitCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0658}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-UIRDefaultWorld", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.045700000000000005}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Sprites/Default", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0805}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Sprites/Mask", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0637}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "UI/Default", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0766}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "UI/DefaultETC1", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0814}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeBlur", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0454}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0577}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeCopy", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.053000000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0665}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeBlend", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0448}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0636}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VR/BlitTexArraySlice", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0431}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "Hidden/Internal-ODSWorldTexture", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.057300000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.06420000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0533}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.0675}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.0534}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.054}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.048}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.0459}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.0539}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.0533}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-CubemapToEquirect", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0674}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/VR/BlitFromTex2DToTexArraySlice", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0358}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0553}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VideoComposite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0611}]}]}, {"inputVariants": 30, "outputVariants": 30, "name": "Hidden/VideoDecode", "pipelines": [{"inputVariants": 30, "outputVariants": 30, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "YCbCr_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0429}, {"inputVariants": 4, "outputVariants": 4, "variantName": "YCbCrA_To_RGBAFull (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0654}, {"inputVariants": 4, "outputVariants": 4, "variantName": "YCbCrA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.059500000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Flip_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0511}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Flip_RGBASplit_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0758}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Flip_NV12_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0627}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Flip_NV12_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06380000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_P010_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0639}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/VideoDecodeAndroid", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "RGBAExternal_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0489}, {"inputVariants": 2, "outputVariants": 2, "variantName": "RGBASplitExternal_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.045700000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Compositing", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Mix_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0402}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TextCore/Distance Field SSD", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07010000000000001}]}]}]}